# Documentazione API - Sistema HACCP Management

## Panoramica
Questa documentazione descrive tutti gli endpoint API disponibili nel sistema HACCP Management. L'applicazione è basata su Express.js con autenticazione tramite Passport.js e gestione sessioni PostgreSQL.

## Autenticazione

### Base URL
- **Sviluppo**: `http://localhost:5000`
- **Produzione**: `https://[domain].replit.app`

### Sistema di Autenticazione
Il sistema utilizza sessioni cookie-based con middleware Passport.js. Tutti gli endpoint protetti richiedono autenticazione tramite cookie di sessione.

#### Utenti di Default
- **Admin**: `admin` / `admin123`
- **User**: `user` / `user123`

---

## Endpoint di Autenticazione

### POST /api/auth/login
**Descrizione**: Effettua il login dell'utente e crea una sessione
**Autenticazione**: Nessuna
**Metodo**: POST
**Content-Type**: application/json

**Payload**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**Risposta di successo (200)**:
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "isAdmin": true
}
```

**Errori**:
- `401`: Credenziali non valide
- `500`: Errore interno del server

---

### GET /api/auth/me
**Descrizione**: Recupera le informazioni dell'utente attualmente autenticato
**Autenticazione**: Richiesta
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>", 
  "isAdmin": true,
  "role": "admin"
}
```

**Errori**:
- `401`: Non autenticato

---

### POST /api/auth/logout
**Descrizione**: Effettua il logout distruggendo la sessione
**Autenticazione**: Richiesta
**Metodo**: POST

**Risposta di successo (200)**:
```json
{
  "message": "Logged out successfully"
}
```

---

## Gestione Utenti

### GET /api/users
**Descrizione**: Recupera la lista di tutti gli utenti
**Autenticazione**: Admin richiesto
**Metodo**: GET

**Risposta di successo (200)**:
```json
[
  {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "isAdmin": true,
    "role": "admin",
    "createdAt": "2025-06-11T08:00:00.000Z"
  }
]
```

---

### GET /api/auth/check-impersonation
**Descrizione**: Verifica se l'utente corrente è in modalità impersonificazione
**Autenticazione**: Richiesta
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "isImpersonated": false,
  "originalAdminId": null,
  "currentUser": {
    "id": 1,
    "username": "admin"
  }
}
```

---

## Impostazioni Utente

### GET /api/user/settings
### GET /api/users/settings
**Descrizione**: Recupera le impostazioni personali dell'utente autenticato
**Autenticazione**: Richiesta
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "userId": 1,
  "cameraFacingMode": "environment",
  "cameraResolution": "fullhd",
  "showScanner": false,
  "vibrationFeedback": true,
  "soundFeedback": true,
  "confirmScans": false,
  "lightFeedback": true,
  "aiProvider": "claude"
}
```

---

### POST /api/users/settings
**Descrizione**: Aggiorna le impostazioni personali dell'utente
**Autenticazione**: Richiesta
**Metodo**: POST
**Content-Type**: application/json

**Payload**:
```json
{
  "cameraFacingMode": "user",
  "vibrationFeedback": false,
  "aiProvider": "gemini"
}
```

**Campi consentiti**:
- `cameraFacingMode`: "environment" | "user"
- `cameraResolution`: "hd" | "fullhd" | "4k"
- `showScanner`: boolean
- `vibrationFeedback`: boolean
- `soundFeedback`: boolean
- `confirmScans`: boolean
- `lightFeedback`: boolean
- `aiProvider`: "claude" | "gemini"
- `claudeModel`: string
- `geminiModel`: string
- `offlineMode`: boolean
- `dataPersistence`: boolean
- `autoSync`: boolean
- `cacheManagement`: boolean
- `backgroundSync`: boolean

**Risposta di successo (200)**:
```json
{
  "success": true,
  "settings": {
    "userId": 1,
    "cameraFacingMode": "user",
    "vibrationFeedback": false,
    "aiProvider": "gemini"
  }
}
```

---

## Sistema AI

### GET /api/ai/settings
**Descrizione**: Recupera le impostazioni AI globali del sistema
**Autenticazione**: Admin richiesto
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "defaultProvider": "claude",
  "claudeProvider": {
    "models": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"],
    "defaultModel": "claude-3-5-sonnet-20241022"
  },
  "geminiProvider": {
    "models": ["gemini-1.5-flash", "gemini-1.5-pro"],
    "defaultModel": "gemini-1.5-flash"
  },
  "defaultPrompts": {
    "ddt": 1,
    "label": 2,
    "general": 3
  }
}
```

---

### POST /api/ai/settings
**Descrizione**: Aggiorna le impostazioni AI globali
**Autenticazione**: Admin richiesto
**Metodo**: POST
**Content-Type**: application/json

**Payload**:
```json
{
  "defaultProvider": "gemini",
  "claudeProvider": {
    "defaultModel": "claude-3-haiku-20240307"
  }
}
```

---

### GET /api/ai/config
**Descrizione**: Recupera la configurazione AI corrente per l'utente autenticato
**Autenticazione**: Richiesta
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "provider": "claude",
  "model": "claude-3-5-sonnet-20241022",
  "availableProviders": ["claude", "gemini"]
}
```

---

### GET /api/ai/models
**Descrizione**: Recupera tutti i modelli AI disponibili
**Autenticazione**: Richiesta
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "claude": {
    "models": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"],
    "defaultModel": "claude-3-5-sonnet-20241022"
  },
  "gemini": {
    "models": ["gemini-1.5-flash", "gemini-1.5-pro"],
    "defaultModel": "gemini-1.5-flash"
  },
  "currentProvider": "claude"
}
```

---

## Gestione Container

### PATCH /api/containers/:id/capacity
**Descrizione**: Aggiorna la capacità massima di un container specifico
**Autenticazione**: Richiesta
**Metodo**: PATCH
**Content-Type**: application/json
**Parametri URL**: `id` (integer) - ID del container

**Payload**:
```json
{
  "maxItems": 50
}
```

**Risposta di successo (200)**:
```json
{
  "id": 1,
  "name": "Container A",
  "maxItems": 50,
  "currentItems": 25,
  "updatedAt": "2025-06-11T08:00:00.000Z"
}
```

**Errori**:
- `400`: ID non valido o capacità inferiore agli elementi attuali
- `404`: Container non trovato

---

## Impostazioni di Sistema

### GET /api/system/settings
**Descrizione**: Recupera le impostazioni di sistema
**Autenticazione**: Admin richiesto
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "id": 1,
  "appName": "HACCP Management",
  "version": "1.2.17",
  "maintenanceMode": false,
  "allowRegistration": false
}
```

---

### GET /api/system/global-settings
**Descrizione**: Recupera le impostazioni globali combinate (AI + PWA)
**Autenticazione**: Admin richiesto
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "id": 1,
  "defaultAiProvider": "claude",
  "defaultClaudeModel": "claude-3-5-sonnet-20241022",
  "defaultGeminiModel": "gemini-1.5-flash",
  "defaultDdtPromptId": 1,
  "defaultLabelPromptId": 2,
  "defaultGeneralPromptId": 3,
  "pwaOfflineMode": true,
  "pwaDataPersistence": true,
  "pwaAutoSync": true,
  "pwaCacheManagement": false,
  "pwaBackgroundSync": true
}
```

---

## Strumenti di Sviluppo

### GET /api/dev/status
**Descrizione**: Verifica lo stato dei strumenti di sviluppo
**Autenticazione**: Nessuna (solo in sviluppo)
**Metodo**: GET

**Risposta di successo (200)**:
```json
{
  "environment": "development",
  "timestamp": 1707645012000,
  "fileChanges": [
    {
      "path": "/client/src/App.tsx", 
      "mtime": 1707645010000
    }
  ]
}
```

---

## Codici di Stato HTTP

### Successo
- **200 OK**: Richiesta completata con successo
- **201 Created**: Risorsa creata con successo

### Errori Client
- **400 Bad Request**: Dati di input non validi
- **401 Unauthorized**: Autenticazione richiesta
- **403 Forbidden**: Permessi insufficienti
- **404 Not Found**: Risorsa non trovata

### Errori Server
- **500 Internal Server Error**: Errore interno del server

---

## Note sulla Sicurezza

1. **Sessioni**: Le sessioni sono gestite con cookie httpOnly e sameSite=lax
2. **CORS**: Configurato per domini autorizzati
3. **Validazione**: Tutti gli input sono validati tramite Zod schemas
4. **Rate Limiting**: Implementato per endpoint sensibili
5. **Logging**: Tutte le operazioni amministrative sono logged

---

## Cache e Performance

⚠️ **IMPORTANTE**: Il sistema di caching è completamente disabilitato per massimizzare le performance. Tutti i dati vengono recuperati direttamente dal server ad ogni richiesta.

- **React Query**: `cacheTime: 0`, `staleTime: 0`
- **PWA Cache**: Disabilitato
- **Offline Storage**: Disabilitato
- **Service Worker**: Solo per notifiche push

---

## Esempi di Utilizzo

### Login e Recupero Dati Utente
```bash
# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  -c cookies.txt

# Verifica autenticazione
curl -X GET http://localhost:5000/api/auth/me \
  -b cookies.txt
```

### Aggiornamento Impostazioni
```bash
curl -X POST http://localhost:5000/api/users/settings \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"aiProvider":"gemini","vibrationFeedback":false}'
```

---

## Troubleshooting

### Errore 401 Unauthorized
- Verificare che il cookie di sessione sia presente
- Effettuare nuovamente il login se la sessione è scaduta

### Errore 403 Forbidden
- Verificare i permessi dell'utente
- Alcuni endpoint richiedono privilegi amministrativi

### Errore 500 Internal Server Error
- Controllare i log del server per dettagli specifici
- Verificare la connessione al database PostgreSQL