import { pgTable, text, serial, integer, boolean, timestamp, jsonb, primaryKey, uuid, index } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Definizione enum per i ruoli utente
export const UserRole = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user'
} as const;

// Tipo per i ruoli utente
export type UserRoleType = typeof UserRole[keyof typeof UserRole];

// Definizione enum per i tipi di tenant
export const TenantType = {
  RESTAURANT: 'restaurant',
  FOOD_SERVICE: 'food_service',
  CATERING: 'catering',
  OTHER: 'other'
} as const;

export type TenantTypeType = typeof TenantType[keyof typeof TenantType];

// Definizione enum per lo stato del tenant
export const TenantStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  TRIAL: 'trial'
} as const;

export type TenantStatusType = typeof TenantStatus[keyof typeof TenantStatus];

// ============================================================================
// TABELLE MULTI-TENANT
// ============================================================================

// Tabella per la gestione dei tenant (ristoranti/aziende)
export const tenants = pgTable("tenants", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(), // Nome del ristorante/azienda
  code: text("code").notNull().unique(), // Codice identificativo univoco
  type: text("type").default(TenantType.RESTAURANT).notNull(),
  status: text("status").default(TenantStatus.ACTIVE).notNull(),
  
  // Informazioni aziendali
  vatNumber: text("vat_number"),
  address: text("address"),
  city: text("city"),
  postalCode: text("postal_code"),
  country: text("country").default("IT"),
  phone: text("phone"),
  email: text("email"),
  website: text("website"),
  
  // Configurazioni specifiche del tenant
  maxUsers: integer("max_users").default(10),
  maxStorageGB: integer("max_storage_gb").default(5),
  features: jsonb("features").default({
    ddtProcessing: true,
    productLabeling: true,
    containerManagement: true,
    aiProcessing: true,
    advancedReports: true
  }),
  
  // Impostazioni di branding
  logo: text("logo"), // Base64 o URL del logo
  primaryColor: text("primary_color").default("#0ea5e9"),
  secondaryColor: text("secondary_color").default("#64748b"),
  
  // Metadati
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: text("created_by"), // ID dell'amministratore che ha creato il tenant
});

// Tabella per gli amministratori del sistema (super-admin)
export const systemAdmins = pgTable("system_admins", {
  id: uuid("id").primaryKey().defaultRandom(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  fullName: text("full_name"),
  isActive: boolean("is_active").default(true).notNull(),
  lastLogin: timestamp("last_login"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  username: text("username").notNull(),
  password: text("password").notNull(),
  email: text("email"),
  role: text("role").default(UserRole.USER).notNull(),
  isAdmin: boolean("is_admin").default(false).notNull(), // Mantenuto per retrocompatibilità
  isActive: boolean("is_active").default(true).notNull(),
  lastLogin: timestamp("last_login"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => {
  return {
    // CRITICAL PERFORMANCE INDEX: Optimizes tenant-user count queries (fixes N+1 problem)
    tenantIdIdx: index("users_tenant_id_idx").on(table.tenantId),
    // Search optimization for admin functions
    usernameIdx: index("users_username_idx").on(table.username),
    // Performance optimization for active user filtering
    isActiveIdx: index("users_is_active_idx").on(table.isActive),
    // Compound index for tenant + active users (optimal for dashboard queries)
    tenantActiveIdx: index("users_tenant_active_idx").on(table.tenantId, table.isActive),
  };
});

export const suppliers = pgTable("suppliers", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  companyName: text("company_name").notNull(),
  vatNumber: text("vat_number").notNull(),
  address: text("address").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
}, (table) => {
  return {
    // Multi-tenant performance index
    tenantIdIdx: index("suppliers_tenant_id_idx").on(table.tenantId),
    // VAT number search optimization
    vatNumberIdx: index("suppliers_vat_number_idx").on(table.vatNumber),
  };
});

export const ddts = pgTable("ddts", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  supplierId: uuid("supplier_id").references(() => suppliers.id),
  companyName: text("company_name").notNull(),
  vatNumber: text("vat_number").notNull(),
  address: text("address").notNull(),
  number: text("number").notNull(),
  date: text("date").notNull(),
  image: text("image"),
  qrCode: text("qr_code"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
}, (table) => {
  return {
    // Multi-tenant performance index
    tenantIdIdx: index("ddts_tenant_id_idx").on(table.tenantId),
    // DDT number search optimization
    numberIdx: index("ddts_number_idx").on(table.number),
    // Supplier relationship optimization
    supplierIdIdx: index("ddts_supplier_id_idx").on(table.supplierId),
    // Date-based queries optimization
    dateIdx: index("ddts_date_idx").on(table.date),
  };
});

export const productLabels = pgTable("product_labels", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  ddtId: uuid("ddt_id").references(() => ddts.id),
  productName: text("product_name").notNull(),
  expiryDate: text("expiry_date").notNull(),
  batchNumber: text("batch_number").notNull(),
  storageInstructions: text("storage_instructions").notNull(),
  notes: text("notes"),
  qrCode: text("qr_code"),
  image: text("image"),
  // Campi per la gestione del ritiro prodotti
  isRetired: boolean("is_retired").default(false).notNull(),
  retiredAt: timestamp("retired_at"),
  retiredBy: uuid("retired_by").references(() => users.id),
  retiredReason: text("retired_reason"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
}, (table) => {
  return {
    // Multi-tenant performance index
    tenantIdIdx: index("product_labels_tenant_id_idx").on(table.tenantId),
    // DDT relationship optimization
    ddtIdIdx: index("product_labels_ddt_id_idx").on(table.ddtId),
    // Product retirement queries optimization
    isRetiredIdx: index("product_labels_is_retired_idx").on(table.isRetired),
    // Expiry date queries optimization (for inventory management)
    expiryDateIdx: index("product_labels_expiry_date_idx").on(table.expiryDate),
    // Compound index for tenant + active products
    tenantActiveIdx: index("product_labels_tenant_active_idx").on(table.tenantId, table.isRetired),
  };
});

export const containerTypes = pgTable("container_types", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  value: text("value").notNull(),
  label: text("label").notNull(),
  description: text("description"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
});

export const containers = pgTable("containers", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  name: text("name").notNull(),
  typeId: uuid("type_id").references(() => containerTypes.id),
  type: text("type").notNull(), // Mantenuto per retrocompatibilità
  maxItems: integer("max_items").default(5).notNull(),
  currentItems: integer("current_items").default(0).notNull(),
  isArchived: boolean("is_archived").default(false).notNull(),
  qrCode: text("qr_code"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
}, (table) => {
  return {
    // Multi-tenant performance index
    tenantIdIdx: index("containers_tenant_id_idx").on(table.tenantId),
    // Container name search optimization
    nameIdx: index("containers_name_idx").on(table.name),
    // Archive status optimization
    isArchivedIdx: index("containers_is_archived_idx").on(table.isArchived),
    // Compound index for tenant + active containers
    tenantActiveIdx: index("containers_tenant_active_idx").on(table.tenantId, table.isArchived),
  };
});

export const containerProducts = pgTable("container_products", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  containerId: uuid("container_id").references(() => containers.id),
  productLabelId: uuid("product_label_id").references(() => productLabels.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by").references(() => users.id),
}, (table) => {
  return {
    // Multi-tenant performance index
    tenantIdIdx: index("container_products_tenant_id_idx").on(table.tenantId),
    // Container relationship optimization
    containerIdIdx: index("container_products_container_id_idx").on(table.containerId),
    // Product relationship optimization
    productLabelIdIdx: index("container_products_product_label_id_idx").on(table.productLabelId),
    // Compound index for container-product associations
    containerProductIdx: index("container_products_container_product_idx").on(table.containerId, table.productLabelId),
  };
});

export const activityLogs = pgTable("activity_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  userId: uuid("user_id").references(() => users.id),
  username: text("username").notNull(),
  action: text("action").notNull(),
  details: text("details").notNull(),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  metadata: jsonb("metadata"),
  // Aggiunti campi opzionali per facilitare il filtraggio
  containerId: uuid("container_id").references(() => containers.id),
  productId: uuid("product_id").references(() => productLabels.id),
});

// ============================================================================
// INSERT SCHEMAS E TIPI MULTI-TENANT
// ============================================================================

// Schema per tenant
export const insertTenantSchema = createInsertSchema(tenants, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
  createdBy: z.string().optional(),
});

export const insertSystemAdminSchema = createInsertSchema(systemAdmins, {
  id: z.string().optional(),
  lastLogin: z.date().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// Insert schemas (aggiornati per multi-tenant)
export const insertUserSchema = createInsertSchema(users, {
  id: z.string().optional(),
  email: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
  lastLogin: z.date().optional(),
});
export const insertSupplierSchema = createInsertSchema(suppliers, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
});
export const insertDDTSchema = createInsertSchema(ddts, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
  supplierId: z.string().nullable().optional(),
  image: z.string().optional(),
  qrCode: z.string().optional(),
});
export const insertProductLabelSchema = createInsertSchema(productLabels, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
  ddtId: z.string().optional(),
  notes: z.string().optional(),
  image: z.string().optional(),
  qrCode: z.string().optional(),
  // Campi per il ritiro prodotti
  isRetired: z.boolean().optional(),
  retiredAt: z.date().optional(),
  retiredBy: z.string().optional(),
  retiredReason: z.string().optional(),
});

// Schema specifico per il ritiro prodotti
export const retireProductSchema = z.object({
  reason: z.string().min(1, "La motivazione è obbligatoria"),
});
export const insertContainerSchema = createInsertSchema(containers, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
  currentItems: z.number().optional(),
  isArchived: z.boolean().optional(),
  qrCode: z.string().optional(),
  typeId: z.string().nullable().optional(),
});
export const insertContainerProductSchema = createInsertSchema(containerProducts, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
});
export const insertContainerTypeSchema = createInsertSchema(containerTypes, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  createdBy: z.string().optional(),
  description: z.string().optional(),
});

export const insertActivityLogSchema = createInsertSchema(activityLogs, {
  id: z.string().optional(),
  timestamp: z.date().optional(),
  metadata: z.any().optional(),
  containerId: z.string().optional(),
  productId: z.string().optional(),
});

// Filtri per i log di attività
export const activityLogFiltersSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  userId: z.string().optional(),
  containerId: z.string().optional(),
  action: z.string().optional(),
  exportFormat: z.enum(['csv', 'excel', 'pdf']).optional()
});

export type ActivityLogFilters = z.infer<typeof activityLogFiltersSchema>;

// ============================================================================
// TYPES MULTI-TENANT
// ============================================================================

// Tipi per tenant e system admin
export type Tenant = typeof tenants.$inferSelect;
export type InsertTenant = z.infer<typeof insertTenantSchema>;

export type SystemAdmin = typeof systemAdmins.$inferSelect;
export type InsertSystemAdmin = z.infer<typeof insertSystemAdminSchema>;

// Types esistenti (aggiornati per multi-tenant)
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Supplier = typeof suppliers.$inferSelect;
export type InsertSupplier = z.infer<typeof insertSupplierSchema>;

export type DDT = typeof ddts.$inferSelect;
export type InsertDDT = z.infer<typeof insertDDTSchema>;

export type ProductLabel = typeof productLabels.$inferSelect;
export type InsertProductLabel = z.infer<typeof insertProductLabelSchema>;
export type RetireProduct = z.infer<typeof retireProductSchema>;

export type Container = typeof containers.$inferSelect;
export type InsertContainer = z.infer<typeof insertContainerSchema>;

export type ContainerProduct = typeof containerProducts.$inferSelect;
export type InsertContainerProduct = z.infer<typeof insertContainerProductSchema>;

export type ContainerType = typeof containerTypes.$inferSelect;
export type InsertContainerType = z.infer<typeof insertContainerTypeSchema>;

export type ActivityLog = typeof activityLogs.$inferSelect;
export type InsertActivityLog = z.infer<typeof insertActivityLogSchema>;

// Tabella delle impostazioni di sistema (per tenant specifico)
export const systemSettings = pgTable("system_settings", {
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  key: text("key").notNull(),
  value: jsonb("value").notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  updatedBy: uuid("updated_by").references(() => users.id),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.tenantId, table.key] })
  };
});

export const insertSystemSettingsSchema = createInsertSchema(systemSettings, {
  updatedAt: z.date().optional(),
  updatedBy: z.string().optional(),
});

export type SystemSetting = typeof systemSettings.$inferSelect;
export type InsertSystemSetting = z.infer<typeof insertSystemSettingsSchema>;

// Tabella unificata per le impostazioni AI (per tenant specifico)
export const aiSettings = pgTable("ai_settings", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull().unique(),
  // Provider AI predefinito
  defaultAiProvider: text("default_ai_provider").default("claude").notNull(), // "claude" o "gemini"
  
  // Modelli Claude (identificatori corretti)
  defaultClaudeModel: text("default_claude_model").default("claude-3-5-sonnet-20241022").notNull(),
  availableClaudeModels: jsonb("available_claude_models").default([
    "claude-3-5-sonnet-20241022",
    "claude-3-5-haiku-20241022", 
    "claude-3-opus-20240229",
    "claude-3-sonnet-20240229",
    "claude-3-haiku-20240307"
  ]).notNull(),
  
  // Modelli Gemini
  defaultGeminiModel: text("default_gemini_model").default("gemini-1.5-pro").notNull(),
  availableGeminiModels: jsonb("available_gemini_models").default([
    "gemini-1.5-pro",
    "gemini-1.5-flash",
    "gemini-pro",
    "gemini-pro-vision"
  ]).notNull(),
  
  // Prompt AI predefiniti globali
  defaultDdtPromptId: text("default_ddt_prompt_id"),
  defaultLabelPromptId: text("default_label_prompt_id"),
  defaultGeneralPromptId: text("default_general_prompt_id"),
  
  // Metadati
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  updatedBy: uuid("updated_by").references(() => users.id),
});

// Tabella delle impostazioni globali PWA (per tenant specifico)
export const globalSettings = pgTable("global_settings", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull().unique(),
  // Funzionalità PWA globali
  pwaOfflineMode: boolean("pwa_offline_mode").default(true).notNull(),
  pwaDataPersistence: boolean("pwa_data_persistence").default(true).notNull(),
  pwaAutoSync: boolean("pwa_auto_sync").default(true).notNull(),
  pwaCacheManagement: boolean("pwa_cache_management").default(true).notNull(),
  pwaBackgroundSync: boolean("pwa_background_sync").default(false).notNull(),
  pwaPushNotifications: boolean("pwa_push_notifications").default(false).notNull(),
  
  // Metadati
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  updatedBy: uuid("updated_by").references(() => users.id),
});

// API Analytics Table for tracking API usage and performance
export const apiAnalytics = pgTable("api_analytics", {
  id: uuid("id").primaryKey().defaultRandom(),
  endpoint: text("endpoint").notNull(),
  method: text("method").notNull(), // GET, POST, PUT, DELETE, etc.
  statusCode: integer("status_code").notNull(),
  responseTime: integer("response_time").notNull(), // in milliseconds
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  userAgent: text("user_agent"),
  ipAddress: text("ip_address"),
  tenantId: uuid("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  userId: uuid("user_id").references(() => users.id, { onDelete: "set null" }),
  errorMessage: text("error_message"), // For failed requests
}, (table) => ({
  // Performance indices for analytics queries
  endpointIdx: index("api_analytics_endpoint_idx").on(table.endpoint),
  timestampIdx: index("api_analytics_timestamp_idx").on(table.timestamp),
  tenantTimestampIdx: index("api_analytics_tenant_timestamp_idx").on(table.tenantId, table.timestamp),
  statusCodeIdx: index("api_analytics_status_code_idx").on(table.statusCode),
  userAnalyticsIdx: index("api_analytics_user_idx").on(table.userId, table.timestamp),
}));

// Schema per le impostazioni AI
export const insertAiSettingsSchema = createInsertSchema(aiSettings, {
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  updatedBy: z.string().optional(),
});

export type AiSetting = typeof aiSettings.$inferSelect;
export type InsertAiSetting = z.infer<typeof insertAiSettingsSchema>;

export const insertGlobalSettingsSchema = createInsertSchema(globalSettings, {
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  updatedBy: z.string().optional(),
});

export type GlobalSetting = typeof globalSettings.$inferSelect;
export type InsertGlobalSetting = z.infer<typeof insertGlobalSettingsSchema>;

// API Analytics Types (moved after table definitions)
export const insertApiAnalyticsSchema = createInsertSchema(apiAnalytics, {
  id: z.string().optional(),
  timestamp: z.date().optional(),
  errorMessage: z.string().optional(),
});

export type ApiAnalytics = typeof apiAnalytics.$inferSelect;
export type InsertApiAnalytics = z.infer<typeof insertApiAnalyticsSchema>;

// Tabella delle impostazioni personali degli utenti
export const userSettings = pgTable("user_settings", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull().unique(),
  // Impostazioni fotocamera
  cameraFacingMode: text("camera_facing_mode").default("environment").notNull(),
  cameraResolution: text("camera_resolution").default("fullhd").notNull(),
  // Impostazioni app
  showScanner: boolean("show_scanner").default(false).notNull(),
  vibrationFeedback: boolean("vibration_feedback").default(true).notNull(),
  soundFeedback: boolean("sound_feedback").default(true).notNull(),
  confirmScans: boolean("confirm_scans").default(false).notNull(),
  lightFeedback: boolean("light_feedback").default(true).notNull(),
  // Impostazioni AI
  claudeModel: text("claude_model").default("claude-3-5-sonnet-20241022"),
  geminiModel: text("gemini_model").default("gemini-1.5-pro"),
  aiProvider: text("ai_provider").default("claude").notNull(), // "claude" o "gemini"
  // Impostazioni PWA
  offlineMode: boolean("offline_mode").default(true).notNull(),
  dataPersistence: boolean("data_persistence").default(true).notNull(),
  autoSync: boolean("auto_sync").default(true).notNull(),
  cacheManagement: boolean("cache_management").default(true).notNull(),
  backgroundSync: boolean("background_sync").default(false).notNull(),
  // Metadati
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertUserSettingsSchema = createInsertSchema(userSettings, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type UserSetting = typeof userSettings.$inferSelect;
export type InsertUserSetting = z.infer<typeof insertUserSettingsSchema>;

// Tabella per i prompt di Claude AI (per tenant specifico)
export const claudePrompts = pgTable("claude_prompts", {
  id: text("id").primaryKey(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  name: text("name").notNull(),
  content: text("content").notNull(),
  description: text("description"),
  category: text("category").notNull(), // 'ddt', 'label', 'general'
  isDefault: boolean("is_default").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Tabella per i feedback degli utenti (per tenant specifico)
export const userFeedback = pgTable("user_feedback", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  username: text("username").notNull(),
  message: text("message").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Insert schemas per le nuove tabelle
export const insertClaudePromptSchema = createInsertSchema(claudePrompts, {
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const insertUserFeedbackSchema = createInsertSchema(userFeedback, {
  id: z.string().optional(),
  createdAt: z.date().optional(),
});

// Tipi per le nuove tabelle
export type ClaudePrompt = typeof claudePrompts.$inferSelect;
export type InsertClaudePrompt = z.infer<typeof insertClaudePromptSchema>;

export type UserFeedback = typeof userFeedback.$inferSelect;
export type InsertUserFeedback = z.infer<typeof insertUserFeedbackSchema>;
