/**
 * Safe Date Utilities for HACCP Tracker
 * 
 * @description Utility sicure per il parsing e manipolazione delle date
 * che prevengono crash da array destructuring unsafe
 */

export interface DateParts {
  day: number;
  month: number;
  year: number;
}

export interface SafeDateParseResult {
  success: boolean;
  parts: DateParts | null;
  error?: string;
}

/**
 * Parsing sicuro di date in formato DD/MM/YYYY o DD/MM/YY
 * Previene crash da array destructuring non sicuro
 */
export function safeParseDateString(dateString: string | null | undefined): SafeDateParseResult {
  // Input validation
  if (!dateString || typeof dateString !== 'string') {
    return {
      success: false,
      parts: null,
      error: 'Date string is null, undefined, or not a string'
    };
  }

  const trimmed = dateString.trim();
  if (!trimmed) {
    return {
      success: false,
      parts: null,
      error: 'Date string is empty'
    };
  }

  // Split and validate array length
  const parts = trimmed.split('/');
  if (parts.length !== 3) {
    return {
      success: false,
      parts: null,
      error: `Expected 3 date parts separated by '/', got ${parts.length}`
    };
  }

  // Safe destructuring with validation
  const [dayStr, monthStr, yearStr] = parts;
  
  // Validate each part exists and is not empty
  if (!dayStr || !monthStr || !yearStr) {
    return {
      success: false,
      parts: null,
      error: 'One or more date parts are empty'
    };
  }

  // Parse numbers with validation
  const day = parseInt(dayStr, 10);
  const month = parseInt(monthStr, 10);
  let year = parseInt(yearStr, 10);

  // Validate parsed numbers
  if (isNaN(day) || isNaN(month) || isNaN(year)) {
    return {
      success: false,
      parts: null,
      error: 'One or more date parts are not valid numbers'
    };
  }

  // Handle 2-digit years (convert to 4-digit)
  if (year < 100) {
    // Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
    year = year <= 30 ? 2000 + year : 1900 + year;
  }

  // Validate date ranges
  if (day < 1 || day > 31) {
    return {
      success: false,
      parts: null,
      error: `Day must be between 1 and 31, got ${day}`
    };
  }

  if (month < 1 || month > 12) {
    return {
      success: false,
      parts: null,
      error: `Month must be between 1 and 12, got ${month}`
    };
  }

  if (year < 1900 || year > 2100) {
    return {
      success: false,
      parts: null,
      error: `Year must be between 1900 and 2100, got ${year}`
    };
  }

  return {
    success: true,
    parts: { day, month, year }
  };
}

/**
 * Conversione sicura da DateParts a oggetto Date JavaScript
 */
export function datePartsToDate(parts: DateParts): Date | null {
  try {
    // JavaScript Date constructor uses 0-based months
    const date = new Date(parts.year, parts.month - 1, parts.day);
    
    // Verify the date is valid (handles invalid dates like Feb 30)
    if (isNaN(date.getTime())) {
      return null;
    }
    
    // Verify the date components match (detects invalid dates like Feb 30 -> Mar 2)
    if (date.getFullYear() !== parts.year || 
        date.getMonth() !== parts.month - 1 || 
        date.getDate() !== parts.day) {
      return null;
    }
    
    return date;
  } catch (error) {
    return null;
  }
}

/**
 * Parsing sicuro con conversione diretta a Date object
 */
export function safeParseDateToDate(dateString: string | null | undefined): Date | null {
  const result = safeParseDateString(dateString);
  if (!result.success || !result.parts) {
    return null;
  }
  
  return datePartsToDate(result.parts);
}

/**
 * Formattazione sicura di date per display
 */
export function safeFormatDate(dateString: string | null | undefined): string {
  if (!dateString) return '';
  
  const result = safeParseDateString(dateString);
  if (!result.success || !result.parts) {
    return '';
  }
  
  const { day, month, year } = result.parts;
  return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
}

/**
 * Verifica sicura se una data è scaduta
 */
export function isDateExpired(dateString: string | null | undefined): boolean {
  const date = safeParseDateToDate(dateString);
  if (!date) {
    return false; // Se non può essere parsata, consideriamo non scaduta per sicurezza
  }
  
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset to start of day
  date.setHours(0, 0, 0, 0);
  
  return date < today;
}

/**
 * Calcolo sicuro dei giorni rimanenti alla scadenza
 */
export function getDaysUntilExpiry(dateString: string | null | undefined): number | null {
  const date = safeParseDateToDate(dateString);
  if (!date) {
    return null;
  }
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  date.setHours(0, 0, 0, 0);
  
  const diffTime = date.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Validazione avanzata di date con business rules
 */
export function validateExpiryDate(dateString: string | null | undefined): {
  isValid: boolean;
  error?: string;
  warningDays?: number;
} {
  const result = safeParseDateString(dateString);
  
  if (!result.success) {
    return {
      isValid: false,
      error: result.error
    };
  }
  
  const date = datePartsToDate(result.parts!);
  if (!date) {
    return {
      isValid: false,
      error: 'Invalid date combination'
    };
  }
  
  const daysUntilExpiry = getDaysUntilExpiry(dateString);
  if (daysUntilExpiry === null) {
    return {
      isValid: false,
      error: 'Unable to calculate expiry days'
    };
  }
  
  // Business rule: warn if expiring within 7 days
  if (daysUntilExpiry <= 7 && daysUntilExpiry >= 0) {
    return {
      isValid: true,
      warningDays: daysUntilExpiry
    };
  }
  
  return {
    isValid: true
  };
}

/**
 * Utility legacy per backward compatibility (deprecata)
 * @deprecated Use safeParseDateString instead
 */
export function unsafeArrayDestructuring(dateString: string): [string, string, string] {
  console.warn('⚠️ Using deprecated unsafeArrayDestructuring. Please migrate to safeParseDateString');
  const parts = dateString.split('/');
  return [parts[0] || '', parts[1] || '', parts[2] || ''];
}