#!/bin/bash

# Script per ripristinare un backup completo del progetto HACCP Tracker

# Colori per i messaggi
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi di errore ed uscire
error_exit() {
    echo -e "${RED}[ERRORE]${NC} $1" >&2
    exit 1
}

# Funzione per stampare messaggi informativi
info_message() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Funzione per stampare messaggi di successo
success_message() {
    echo -e "${GREEN}[SUCCESSO]${NC} $1"
}

# Funzione per stampare messaggi di avviso
warning_message() {
    echo -e "${YELLOW}[AVVISO]${NC} $1"
}

echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BLUE}  SCRIPT DI RIPRISTINO BACKUP HACCP TRACKER${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Mostra i backup disponibili
info_message "Backup del database disponibili:"
if ls db_backups/haccp_backup_*.sql* 1> /dev/null 2>&1; then
    echo
    ls -lht db_backups/haccp_backup_*.sql* | head -10 | nl
    echo
else
    warning_message "Nessun backup del database trovato nella directory db_backups/"
fi

info_message "Backup del progetto disponibili:"
if ls backups/haccp_project_backup_*.tar.gz 1> /dev/null 2>&1; then
    echo
    ls -lht backups/haccp_project_backup_*.tar.gz | head -5 | nl
    echo
else
    warning_message "Nessun backup del progetto trovato nella directory backups/"
fi

# Selezione del tipo di ripristino
echo -e "${YELLOW}Seleziona il tipo di ripristino:${NC}"
echo "1) Ripristina solo il database"
echo "2) Ripristina solo il progetto"
echo "3) Ripristina tutto (database + progetto)"
echo "4) Esci"
echo

read -p "Inserisci la tua scelta (1-4): " CHOICE

case $CHOICE in
    1)
        # Ripristino del database
        info_message "Ripristino del database selezionato"
        
        if [ -z "$DATABASE_URL" ]; then
            error_exit "La variabile DATABASE_URL non è impostata. Impossibile procedere."
        fi
        
        echo "Seleziona il file di backup del database da ripristinare:"
        select BACKUP_FILE in db_backups/haccp_backup_*.sql*; do
            if [ -n "$BACKUP_FILE" ]; then
                info_message "File selezionato: $BACKUP_FILE"
                break
            else
                warning_message "Selezione non valida, riprova."
            fi
        done
        
        warning_message "ATTENZIONE: Questo processo cancellerà tutti i dati attuali del database!"
        read -p "Sei sicuro di voler procedere? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info_message "Operazione annullata."
            exit 0
        fi
        
        info_message "Ripristino del database in corso..."
        
        # Determina se il file è compresso
        if [[ "$BACKUP_FILE" == *.gz ]]; then
            info_message "Decompressione e ripristino del backup compresso..."
            gunzip -c "$BACKUP_FILE" | psql "$DATABASE_URL" || error_exit "Errore durante il ripristino del database."
        else
            info_message "Ripristino del backup non compresso..."
            psql "$DATABASE_URL" -f "$BACKUP_FILE" || error_exit "Errore durante il ripristino del database."
        fi
        
        success_message "Database ripristinato con successo!"
        ;;
        
    2)
        # Ripristino del progetto
        info_message "Ripristino del progetto selezionato"
        
        echo "Seleziona il file di backup del progetto da ripristinare:"
        select PROJECT_BACKUP in backups/haccp_project_backup_*.tar.gz; do
            if [ -n "$PROJECT_BACKUP" ]; then
                info_message "File selezionato: $PROJECT_BACKUP"
                break
            else
                warning_message "Selezione non valida, riprova."
            fi
        done
        
        warning_message "ATTENZIONE: Questo processo sovrascriverà i file del progetto attuali!"
        read -p "Sei sicuro di voler procedere? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info_message "Operazione annullata."
            exit 0
        fi
        
        info_message "Estrazione del backup del progetto..."
        tar -xzf "$PROJECT_BACKUP" || error_exit "Errore durante l'estrazione del backup."
        
        success_message "Progetto ripristinato con successo!"
        warning_message "Ricorda di:"
        echo "- Configurare le variabili d'ambiente (.env)"
        echo "- Installare le dipendenze: npm install"
        echo "- Riavviare l'applicazione"
        ;;
        
    3)
        # Ripristino completo
        info_message "Ripristino completo selezionato"
        
        warning_message "ATTENZIONE: Questo processo cancellerà tutti i dati attuali!"
        read -p "Sei sicuro di voler procedere con il ripristino completo? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info_message "Operazione annullata."
            exit 0
        fi
        
        # Esegui prima il ripristino del progetto, poi del database
        echo "Seleziona il backup del progetto:"
        select PROJECT_BACKUP in backups/haccp_project_backup_*.tar.gz; do
            if [ -n "$PROJECT_BACKUP" ]; then
                break
            fi
        done
        
        echo "Seleziona il backup del database:"
        select DB_BACKUP in db_backups/haccp_backup_*.sql*; do
            if [ -n "$DB_BACKUP" ]; then
                break
            fi
        done
        
        info_message "Ripristino del progetto..."
        tar -xzf "$PROJECT_BACKUP" || error_exit "Errore durante l'estrazione del progetto."
        
        if [ -n "$DATABASE_URL" ]; then
            info_message "Ripristino del database..."
            if [[ "$DB_BACKUP" == *.gz ]]; then
                gunzip -c "$DB_BACKUP" | psql "$DATABASE_URL" || error_exit "Errore durante il ripristino del database."
            else
                psql "$DATABASE_URL" -f "$DB_BACKUP" || error_exit "Errore durante il ripristino del database."
            fi
        else
            warning_message "DATABASE_URL non impostata, salto il ripristino del database."
        fi
        
        success_message "Ripristino completo terminato con successo!"
        ;;
        
    4)
        info_message "Uscita dal programma."
        exit 0
        ;;
        
    *)
        error_exit "Scelta non valida."
        ;;
esac

echo
success_message "Operazione completata! Ricorda di verificare che tutto funzioni correttamente."
echo -e "${YELLOW}IMPORTANTE:${NC} Dopo il ripristino potresti dover:"
echo "1. Riconfigurare le variabili d'ambiente"
echo "2. Reinstallare le dipendenze npm"
echo "3. Riavviare l'applicazione"
echo "4. Verificare le connessioni al database"