#!/usr/bin/env node

/**
 * Script di preparazione al deployment
 * Genera una versione univoca e la imposta come variabile d'ambiente
 */

import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

// Legge la versione corrente dal file version.ts
function getCurrentAppVersion() {
  try {
    const content = readFileSync('./client/src/lib/version.ts', 'utf8');
    const match = content.match(/const APP_VERSION = "([^"]+)";/);
    return match ? match[1] : '1.2.15';
  } catch (error) {
    return '1.2.15';
  }
}

// Funzione per incrementare la versione (patch)
function incrementVersion() {
  const currentVersion = getCurrentAppVersion();
  const parts = currentVersion.split('.').map(Number);
  
  // Incrementa patch
  parts[2] = (parts[2] || 0) + 1;
  
  return parts.join('.');
}

// Funzione per aggiornare il file version.ts
function updateVersionFile(version) {
  const versionFilePath = './client/src/lib/version.ts';
  
  try {
    let content = readFileSync(versionFilePath, 'utf8');
    
    // Aggiorna la versione nel file
    content = content.replace(
      /const APP_VERSION = "[^"]+";/,
      `const APP_VERSION = "${version}";`
    );
    
    // Aggiorna il commento di build
    const buildComment = `// Build: ${new Date().toISOString().split('T')[0]}`;
    content = content.replace(
      /\/\/ Build: .*/,
      buildComment
    );
    
    writeFileSync(versionFilePath, content);
    console.log(`✅ File version.ts aggiornato con versione ${version}`);
    
  } catch (error) {
    console.error('❌ Errore aggiornamento file version.ts:', error.message);
  }
}

// Funzione per creare/aggiornare file .env con versione
function updateEnvFile(version) {
  const envPath = './.env';
  let envContent = '';
  
  try {
    envContent = readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('📝 Creazione nuovo file .env');
  }
  
  // Aggiorna o aggiunge VITE_APP_VERSION
  if (envContent.includes('VITE_APP_VERSION=')) {
    envContent = envContent.replace(
      /VITE_APP_VERSION=.*/,
      `VITE_APP_VERSION=${version}`
    );
  } else {
    envContent += `\nVITE_APP_VERSION=${version}\n`;
  }
  
  writeFileSync(envPath, envContent);
  console.log(`✅ File .env aggiornato con VITE_APP_VERSION=${version}`);
}

// Esecuzione principale
function main() {
  console.log('🚀 Preparazione deployment in corso...');
  
  const currentVersion = getCurrentAppVersion();
  const newVersion = incrementVersion();
  
  console.log(`📦 Versione corrente: ${currentVersion}`);
  console.log(`📦 Nuova versione: ${newVersion}`);
  
  updateVersionFile(newVersion);
  updateEnvFile(newVersion);
  
  console.log('✅ Preparazione deployment completata!');
  console.log(`🎯 Versione pronta per il deploy: ${newVersion}`);
  console.log('');
  console.log('Per deployare:');
  console.log('1. Esegui npm run build');
  console.log('2. Avvia il deployment su Replit');
}

main();