# Guida al Sistema di Backup HACCP Tracker

## Panoramica

Il sistema HACCP Tracker include un sistema completo di backup e ripristino che protegge sia il database che il codice sorgente del progetto.

## File di Backup Disponibili

### Database
- **Script principale**: `backup_db.sh` - Backup completo del database PostgreSQL
- **Directory**: `db_backups/` - Contiene tutti i backup del database
- **Formato**: File SQL compressi (.sql.gz)

### Progetto Completo
- **Script principale**: `backup_simple.sh` - Backup del codice sorgente
- **Directory**: `backups/` - Contiene tutti i backup del progetto
- **Formato**: Archivi tar compressi (.tar.gz)

### Ripristino
- **Script principale**: `restore_backup.sh` - Menu interattivo per il ripristino

## Come Eseguire i Backup

### 1. Backup Solo Database
```bash
./backup_db.sh
```
Questo comando:
- Crea un backup completo del database PostgreSQL
- Include struttura e dati
- Comprime automaticamente il file
- Salva il file nella directory `db_backups/`

### 2. Backup Solo Progetto
```bash
./backup_simple.sh
```
Questo comando:
- Crea un backup del codice sorgente
- Esclude automaticamente i file non necessari (node_modules, .git, etc.)
- Comprime il backup in formato tar.gz
- Salva il file nella directory `backups/`

### 3. Backup Completo Automatico
Per un backup completo di tutto:
```bash
./backup_db.sh && ./backup_simple.sh
```

## Struttura dei File di Backup

### Backup Database
```
db_backups/
├── haccp_backup_complete_20250713_215525.sql.gz  (13M)
├── haccp_backup_complete_20250713_215612.sql.gz  (13M)
└── haccp_backup_20250630_123822.sql              (16M)
```

### Backup Progetto
```
backups/
├── haccp_project_backup_20250713_215715.tar.gz   (61M)
└── haccp_project_backup_20250713_215650.tar.gz   (184M)
```

## Contenuto dei Backup

### Backup Database Include:
- ✅ Tutte le tabelle e i loro dati
- ✅ Sequenze e indici
- ✅ Vincoli e relazioni
- ✅ Permessi e ruoli
- ✅ Struttura completa del database

### Backup Progetto Include:
- ✅ Codice sorgente (client/, server/, shared/)
- ✅ File di configurazione (*.ts, *.js, *.json)
- ✅ Documentazione (*.md)
- ✅ Script di utilità (*.sh)
- ✅ Backup esistenti del database

### File Esclusi dal Backup Progetto:
- ❌ node_modules/ (dipendenze npm)
- ❌ .git/ (repository git)
- ❌ dist/, build/ (file compilati)
- ❌ .env, .env.local (variabili d'ambiente sensibili)
- ❌ *.log (file di log)
- ❌ File temporanei

## Come Ripristinare i Backup

### Ripristino Interattivo
```bash
./restore_backup.sh
```
Questo script offre un menu interattivo con le opzioni:
1. Ripristina solo il database
2. Ripristina solo il progetto
3. Ripristina tutto (database + progetto)
4. Esci

### Ripristino Manuale Database
```bash
# Per file compressi
gunzip -c db_backups/haccp_backup_complete_YYYYMMDD_HHMMSS.sql.gz | psql $DATABASE_URL

# Per file non compressi
psql $DATABASE_URL -f db_backups/haccp_backup_YYYYMMDD_HHMMSS.sql
```

### Ripristino Manuale Progetto
```bash
tar -xzf backups/haccp_project_backup_YYYYMMDD_HHMMSS.tar.gz
```

## Procedure Consigliate

### Frequenza dei Backup
- **Database**: Giornaliero o prima di modifiche importanti
- **Progetto**: Prima di cambiamenti significativi al codice
- **Completo**: Settimanalmente

### Archiviazione
- Conserva i backup in più località (locale, cloud)
- Mantieni almeno 3 generazioni di backup
- Testa periodicamente il processo di ripristino

### Prima di Modifiche Critiche
```bash
# Esegui sempre un backup prima di:
# - Modifiche al database schema
# - Aggiornamenti software importanti
# - Deployment in produzione

./backup_db.sh && ./backup_simple.sh
```

## Risoluzione Problemi

### Errore "DATABASE_URL non impostata"
Assicurati che la variabile d'ambiente DATABASE_URL sia configurata:
```bash
echo $DATABASE_URL
```

### Spazio Insufficiente
I backup possono essere voluminosi. Controlla lo spazio disponibile:
```bash
df -h
```

### Permessi Negati
Assicurati che gli script abbiano i permessi di esecuzione:
```bash
chmod +x backup_db.sh backup_simple.sh restore_backup.sh
```

## Sicurezza

### Dati Sensibili
- I backup del database contengono tutti i dati, inclusi quelli sensibili
- Le variabili d'ambiente (.env) non sono incluse nei backup per sicurezza
- Conserva i backup in luoghi sicuri con accesso limitato

### Variabili d'Ambiente da Riconfigurare Dopo il Ripristino
```bash
# Crea/aggiorna il file .env con:
DATABASE_URL=your_database_connection_string
ANTHROPIC_API_KEY=your_claude_api_key
GOOGLE_API_KEY=your_gemini_api_key
NODE_ENV=development
```

## Script Legacy

Il progetto include anche script precedenti:
- `reset_db.sh` - Reset completo del database con backup automatico
- `backup_project.sh` - Versione avanzata del backup progetto (attualmente non funzionante)

## Supporto

Per problemi con il sistema di backup:
1. Verifica i permessi degli script
2. Controlla che DATABASE_URL sia impostata
3. Verifica lo spazio disponibile
4. Consulta i log di errore per dettagli specifici