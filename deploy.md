# Deployment Guide - HACCP Tracker

## Simplified Version Management System

The application now uses a clean, semantic versioning system that automatically increments with each deployment.

### How It Works

1. **Development**: Uses semantic versioning with dev suffix (e.g., `1.2.16-dev.14`)
2. **Production**: Uses clean semantic versioning (e.g., `1.2.16`)
3. **Format**: `major.minor.patch` following semantic versioning standards

### Deployment Process

#### Automatic (Recommended)
1. Run the deployment preparation script:
   ```bash
   node deploy-prep.js
   ```
2. This will:
   - Read current version from version.ts
   - Automatically increment patch number
   - Update version file and .env
   - Prepare for production deployment

#### Manual
1. Edit `APP_VERSION` in `client/src/lib/version.ts`
2. Update `VITE_APP_VERSION` in .env
3. Build and deploy

### Version Examples

- **Development**: `1.2.16-dev.14` (changes every 10 minutes)
- **Production**: `1.2.16` (clean semantic version)
- **Next Deploy**: `1.2.17` (automatically incremented)

### Version Detection

The PWA automatically detects new versions by:
- Comparing semantic versions properly
- Handling dev vs production version transitions
- Triggering update prompts for newer versions
- Cleaning up old timestamp-based versions

### Environment Variables for Production

Set in your Replit deployment:
- `VITE_APP_VERSION`: The clean version string (e.g., "1.2.16")
- `NODE_ENV`: Set to "production"

### Verification

After deployment, check:
1. App header shows clean version number
2. Browser console shows version logs
3. PWA update detection works correctly

## Current Status

✅ Clean semantic versioning implemented (1.2.16)
✅ Automatic patch increment on deployment
✅ Development versions use readable suffix (-dev.XX)
✅ PWA update detection working with new format
✅ Backward compatibility with old versions
✅ Simplified deployment script created

## Example Deployment Flow

```bash
# Before deployment
Current version: 1.2.16

# Run deployment script
node deploy-prep.js
# Output: 
# 📦 Versione corrente: 1.2.16
# 📦 Nuova versione: 1.2.17
# ✅ Preparazione deployment completata!

# Deploy to production
# Result: Clean version 1.2.17 in production
```