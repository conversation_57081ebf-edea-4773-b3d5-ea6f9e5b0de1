#!/bin/bash

# Script per creare un backup completo del database HACCP
# Include tutti i dati e la struttura del database

# Colori per i messaggi
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi di errore ed uscire
error_exit() {
    echo -e "${RED}[ERRORE]${NC} $1" >&2
    exit 1
}

# Funzione per stampare messaggi informativi
info_message() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Funzione per stampare messaggi di successo
success_message() {
    echo -e "${GREEN}[SUCCESSO]${NC} $1"
}

# Funzione per stampare messaggi di avviso
warning_message() {
    echo -e "${YELLOW}[AVVISO]${NC} $1"
}

# Verifica che la variabile d'ambiente DATABASE_URL sia impostata
if [ -z "$DATABASE_URL" ]; then
    error_exit "La variabile DATABASE_URL non è impostata. Impossibile procedere."
fi

# Crea directory per i backup se non esiste
mkdir -p db_backups

# Nome del file di backup con timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="db_backups/haccp_backup_complete_${TIMESTAMP}.sql"
BACKUP_FILE_COMPRESSED="db_backups/haccp_backup_complete_${TIMESTAMP}.sql.gz"

info_message "Avvio backup completo del database HACCP..."
info_message "File di backup: $BACKUP_FILE"

# Esecuzione del backup completo con opzioni avanzate
warning_message "Creazione backup in corso, potrebbe richiedere alcuni minuti..."

# Opzioni per pg_dump:
# --verbose: output verboso
# --no-password: non richiedere password (usa URL)
# --format=plain: formato SQL plain text
# --inserts: usa INSERT invece di COPY per i dati
# --column-inserts: includi i nomi delle colonne negli INSERT
# --create: includi comandi per creare il database
# --clean: includi comandi per eliminare oggetti prima di ricrearli
pg_dump "$DATABASE_URL" \
    --verbose \
    --no-password \
    --format=plain \
    --inserts \
    --column-inserts \
    --create \
    --clean \
    --file="$BACKUP_FILE" || error_exit "Impossibile creare il backup del database."

# Verifica che il file di backup sia stato creato e non sia vuoto
if [ ! -f "$BACKUP_FILE" ] || [ ! -s "$BACKUP_FILE" ]; then
    error_exit "Il file di backup non è stato creato correttamente o è vuoto."
fi

# Comprimi il backup per risparmiare spazio
info_message "Compressione del backup in corso..."
gzip "$BACKUP_FILE" || warning_message "Impossibile comprimere il backup, ma il file non compresso è disponibile."

# Determina quale file mostrare (compresso o non compresso)
if [ -f "$BACKUP_FILE_COMPRESSED" ]; then
    FINAL_BACKUP_FILE="$BACKUP_FILE_COMPRESSED"
    FILE_SIZE=$(ls -lh "$BACKUP_FILE_COMPRESSED" | awk '{print $5}')
else
    FINAL_BACKUP_FILE="$BACKUP_FILE"
    FILE_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
fi

success_message "Backup completato con successo!"
echo
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${GREEN}  RIEPILOGO BACKUP COMPLETO${NC}"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BLUE}  File di backup:${NC} $FINAL_BACKUP_FILE"
echo -e "${BLUE}  Dimensione file:${NC} $FILE_SIZE"
echo -e "${BLUE}  Data creazione:${NC} $(date)"
echo -e "${BLUE}  Database:${NC} HACCP Tracker"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo

# Elenco dei backup esistenti
info_message "Backup esistenti nella directory db_backups:"
if ls db_backups/haccp_backup_*.sql* 1> /dev/null 2>&1; then
    ls -lht db_backups/haccp_backup_*.sql* | head -10
else
    warning_message "Nessun backup precedente trovato."
fi

echo
success_message "Il backup è pronto per essere scaricato o archiviato in un luogo sicuro."
echo -e "${YELLOW}IMPORTANTE:${NC} Conserva questo backup in un luogo sicuro e testane periodicamente il ripristino."