#!/bin/bash

# Script per eseguire un reset completo e sicuro del database HACCP
# Questo script eseguirà un backup prima di procedere con il reset

# Colori per i messaggi
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi di errore ed uscire
error_exit() {
    echo -e "${RED}[ERRORE]${NC} $1" >&2
    exit 1
}

# Verifica che la variabile d'ambiente DATABASE_URL sia impostata
if [ -z "$DATABASE_URL" ]; then
    error_exit "La variabile DATABASE_URL non è impostata. Impossibile procedere."
fi

# Richiedi conferma all'utente
echo -e "${YELLOW}ATTENZIONE: Questo script cancellerà permanentemente i dati dal database.${NC}"
echo -e "Verrà creato un backup prima di procedere, ma assicurati di avere altre copie di backup se necessario."
read -p "Sei sicuro di voler procedere? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Operazione annullata.${NC}"
    exit 0
fi

# Crea directory per i backup se non esiste
mkdir -p db_backups

# Nome del file di backup con timestamp
BACKUP_FILE="db_backups/haccp_backup_$(date +%Y%m%d_%H%M%S).sql"

echo -e "${YELLOW}Creazione backup in corso...${NC}"

# Esecuzione del backup
pg_dump "$DATABASE_URL" -f "$BACKUP_FILE" || error_exit "Impossibile creare il backup del database."

echo -e "${GREEN}Backup completato: $BACKUP_FILE${NC}"

# Conferma finale
echo
echo -e "${YELLOW}ULTIMA CONFERMA: Tutti i dati saranno eliminati.${NC}"
read -p "Vuoi procedere con il reset del database? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Reset annullato. Il backup è stato comunque creato.${NC}"
    exit 0
fi

echo -e "${YELLOW}Esecuzione reset database in corso...${NC}"

# Esegui lo script SQL di reset
psql "$DATABASE_URL" -f reset_database.sql || error_exit "Errore durante l'esecuzione dello script SQL."

echo -e "${GREEN}Reset del database completato con successo.${NC}"
echo -e "Se necessario, consulta il backup: ${YELLOW}$BACKUP_FILE${NC}"

# Suggerimento sul frontend
echo
echo -e "${YELLOW}NOTA:${NC} Potrebbe essere necessario riavviare l'applicazione e reimpostare"
echo -e "il contatore delle notifiche a 0 nel file: client/src/components/layout/header.tsx"
echo