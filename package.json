{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@playwright/test": "^1.54.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@tanstack/react-query-devtools": "^5.75.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/cors": "^2.8.19", "@types/express-fileupload": "^1.5.1", "@types/jest": "^30.0.0", "@types/memoizee": "^0.4.12", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.3", "@types/winston": "^2.4.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-fileupload": "^1.5.2", "express-rate-limit": "^8.0.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "html-pdf-node": "^1.0.7", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "input-otp": "^1.4.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jsqr": "^1.4.0", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "openai": "^4.103.0", "openid-client": "^6.5.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "playwright": "^1.54.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-camera-pro": "^1.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-qrcode-logo": "^3.0.0", "react-resizable-panels": "^2.1.7", "react-webcam": "^7.2.0", "recharts": "^2.15.2", "supertest": "^7.1.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.4.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "vite-plugin-pwa": "^1.0.0", "winston": "^3.17.0", "workbox-window": "^7.3.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^7.0.6"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}