/**
 * Setup globale per test Jest - Client-side (React)
 * 
 * @description Configurazione test environment per componenti React
 * Include: testing-library, mocks globali, polyfills
 * <AUTHOR> di Testing Automatizzato HACCP Tracker
 * @version 1.0.0 - Setup iniziale testing automatizzato
 */

import '@testing-library/jest-dom/jest-globals';
import { TextEncoder, TextDecoder } from 'util';

// Polyfills per test environment
Object.assign(global, { TextDecoder, TextEncoder });

// Mock per fetch API
global.fetch = jest.fn();

// Mock per window.matchMedia (utilizzato da componenti responsive)
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock per IntersectionObserver (utilizzato da lazy loading)
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock per ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock per localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock per sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock per URL.createObjectURL (utilizzato per QR codes)
global.URL.createObjectURL = jest.fn(() => 'mocked-url');

// Configurazione timeout per test asincroni
jest.setTimeout(30000);

// Mock console warnings per evitare spam nei test
const originalWarn = console.warn;
beforeAll(() => {
  console.warn = jest.fn();
});

afterAll(() => {
  console.warn = originalWarn;
});

// Reset mocks dopo ogni test
afterEach(() => {
  jest.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
});