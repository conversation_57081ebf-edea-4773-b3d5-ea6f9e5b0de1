/**
 * Setup globale per test Jest - Server-side (Express/Database)
 * 
 * @description Configurazione test environment per API e database
 * Include: database mocking, authentication mocking, API testing utilities
 * <AUTHOR> di Testing Automatizzato HACCP Tracker
 * @version 1.0.0 - Setup iniziale testing server-side
 */

import { jest } from '@jest/globals';

// Mock per variabili ambiente di test
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/haccp_test';
process.env.SESSION_SECRET = 'test-session-secret';

// Mock per moduli external che potrebbero causare problemi nei test
jest.mock('@neondatabase/serverless', () => ({
  neon: jest.fn(() => jest.fn()),
  Pool: jest.fn(() => ({
    connect: jest.fn(),
    query: jest.fn(),
    end: jest.fn(),
  })),
}));

// Mock per AI services nei test
jest.mock('@anthropic-ai/sdk', () => ({
  Anthropic: jest.fn(() => ({
    messages: {
      create: jest.fn(),
    },
  })),
}));

jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn(() => ({
    getGenerativeModel: jest.fn(() => ({
      generateContent: jest.fn(),
    })),
  })),
}));

// Configurazione timeout per test database
jest.setTimeout(30000);

// Helper per cleanup database dopo i test
export const cleanupDatabase = async () => {
  // Implementazione cleanup se necessario
  console.log('Database cleanup executed');
};

// Setup e teardown globali
beforeEach(() => {
  // Reset di tutti i mock prima di ogni test
  jest.clearAllMocks();
});

afterEach(async () => {
  // Cleanup dopo ogni test
  await cleanupDatabase();
});

// Configurazione globale per supprimere warning non critici
const originalConsoleWarn = console.warn;
beforeAll(() => {
  console.warn = jest.fn();
});

afterAll(() => {
  console.warn = originalConsoleWarn;
});