/**
 * Test Suite Completo per Sistema di Monitoring HACCP Tracker
 * 
 * @description Test di integrazione per validare:
 * - Health check endpoints funzionanti
 * - Business metrics collection accurata
 * - Sistema di alerting operativo
 * - Performance monitoring corretto
 * - Integrazione completa frontend/backend
 * 
 * <AUTHOR> di Testing Integrato HACCP Tracker
 * @version 1.0.0 - Testing monitoring completo
 * @date 2025-07-26
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';

// Configurazione per test environment
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:5000';
const TIMEOUT = 10000; // 10 secondi per operazioni di rete

// Utilities per testing HTTP
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`${BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
};

// Helper per aspettare condizioni
const waitFor = (condition: () => boolean | Promise<boolean>, timeout = 5000) => {
  return new Promise<void>((resolve, reject) => {
    const startTime = Date.now();
    
    const check = async () => {
      try {
        const result = await condition();
        if (result) {
          resolve();
          return;
        }
      } catch (error) {
        // Ignora errori temporanei
      }
      
      if (Date.now() - startTime > timeout) {
        reject(new Error('Timeout waiting for condition'));
        return;
      }
      
      setTimeout(check, 100);
    };
    
    check();
  });
};

describe('Sistema di Monitoring - Test di Integrazione', () => {
  beforeAll(async () => {
    console.log('🧪 Inizializzando test suite monitoring...');
    
    // Verifica che il server sia raggiungibile
    await waitFor(async () => {
      try {
        const response = await fetch(`${BASE_URL}/api/monitoring/status`);
        return response.ok;
      } catch {
        return false;
      }
    }, TIMEOUT);
    
    console.log('✅ Server monitoring raggiungibile');
  }, TIMEOUT);

  afterAll(() => {
    console.log('🧹 Cleanup test suite monitoring completato');
  });

  describe('Health Check Endpoints', () => {
    test('GET /api/monitoring/status - deve restituire status base', async () => {
      const response = await apiRequest('/api/monitoring/status');
      
      expect(response).toHaveProperty('status');
      expect(response).toHaveProperty('timestamp');
      expect(response).toHaveProperty('uptime');
      expect(response).toHaveProperty('version');
      
      expect(response.status).toBe('ok');
      expect(typeof response.uptime).toBe('number');
      expect(response.uptime).toBeGreaterThan(0);
      expect(response.version).toMatch(/^\d+\.\d+\.\d+/);
      
      console.log('✅ Status endpoint validato:', response.status);
    });

    test('GET /api/monitoring/health - deve restituire health check completo', async () => {
      const response = await apiRequest('/api/monitoring/health');
      
      // Struttura base
      expect(response).toHaveProperty('status');
      expect(response).toHaveProperty('timestamp');
      expect(response).toHaveProperty('uptime');
      expect(response).toHaveProperty('version');
      expect(response).toHaveProperty('checks');
      expect(response).toHaveProperty('performance');
      
      // Status deve essere uno dei valori validi
      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.status);
      
      // Checks structure
      const { checks } = response;
      expect(checks).toHaveProperty('database');
      expect(checks).toHaveProperty('external_apis');
      expect(checks).toHaveProperty('storage');
      expect(checks).toHaveProperty('memory');
      expect(checks).toHaveProperty('cpu');
      
      // Ogni check deve avere la struttura corretta
      Object.entries(checks).forEach(([component, check]: [string, any]) => {
        expect(check).toHaveProperty('status');
        expect(check).toHaveProperty('lastCheck');
        expect(['healthy', 'degraded', 'unhealthy']).toContain(check.status);
        expect(new Date(check.lastCheck)).toBeInstanceOf(Date);
        
        console.log(`✅ ${component}: ${check.status}`);
      });
      
      // Performance metrics
      const { performance } = response;
      expect(performance).toHaveProperty('avgResponseTime');
      expect(performance).toHaveProperty('requestsPerMinute');
      expect(performance).toHaveProperty('errorRate');
      
      expect(typeof performance.avgResponseTime).toBe('number');
      expect(typeof performance.requestsPerMinute).toBe('number');
      expect(typeof performance.errorRate).toBe('number');
      
      console.log('✅ Health check completo validato');
    });

    test('GET /api/monitoring/health/database - deve testare solo database', async () => {
      const response = await apiRequest('/api/monitoring/health/database');
      
      expect(response).toHaveProperty('component', 'database');
      expect(response).toHaveProperty('status');
      expect(response).toHaveProperty('lastCheck');
      
      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.status);
      
      console.log('✅ Database health check validato:', response.status);
    });

    test('GET /api/monitoring/health/apis - deve testare APIs esterne', async () => {
      const response = await apiRequest('/api/monitoring/health/apis');
      
      expect(response).toHaveProperty('component', 'external_apis');
      expect(response).toHaveProperty('status');
      expect(response).toHaveProperty('details');
      
      // Details deve contenere info su Claude e Gemini
      expect(response.details).toHaveProperty('claude');
      expect(response.details).toHaveProperty('gemini');
      
      console.log('✅ External APIs health check validato');
    });
  });

  describe('Business Metrics Collection', () => {
    test('GET /api/monitoring/metrics/business - deve raccogliere metriche business', async () => {
      const response = await apiRequest('/api/monitoring/metrics/business');
      
      expect(response).toHaveProperty('success', true);
      expect(response).toHaveProperty('data');
      expect(response).toHaveProperty('generatedAt');
      
      const { data } = response;
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('metrics');
      expect(data).toHaveProperty('trends');
      
      // Metrics structure
      const { metrics } = data;
      expect(metrics).toHaveProperty('activeUsers');
      expect(metrics).toHaveProperty('ddtProcessed');
      expect(metrics).toHaveProperty('productLabelsCreated');
      expect(metrics).toHaveProperty('containersManaged');
      expect(metrics).toHaveProperty('errorCount');
      expect(metrics).toHaveProperty('performanceScore');
      
      // Tutti i valori devono essere numerici
      Object.entries(metrics).forEach(([key, value]) => {
        expect(typeof value).toBe('number');
        expect(value).toBeGreaterThanOrEqual(0);
      });
      
      // Trends structure
      const { trends } = data;
      expect(trends).toHaveProperty('userGrowth');
      expect(trends).toHaveProperty('processingEfficiency');
      expect(trends).toHaveProperty('systemStability');
      
      // Performance score deve essere tra 0 e 100
      expect(metrics.performanceScore).toBeGreaterThanOrEqual(0);
      expect(metrics.performanceScore).toBeLessThanOrEqual(100);
      
      console.log('✅ Business metrics validati:', {
        activeUsers: metrics.activeUsers,
        performanceScore: metrics.performanceScore
      });
    });

    test('POST /api/monitoring/metrics/track - deve tracciare eventi', async () => {
      const testEvent = {
        event: 'TEST_EVENT',
        properties: {
          testData: 'integration_test',
          timestamp: new Date().toISOString()
        }
      };
      
      const response = await apiRequest('/api/monitoring/metrics/track', {
        method: 'POST',
        body: JSON.stringify(testEvent)
      });
      
      expect(response).toHaveProperty('success', true);
      expect(response).toHaveProperty('eventId');
      expect(response).toHaveProperty('timestamp');
      
      console.log('✅ Event tracking validato:', response.eventId);
    });
  });

  describe('Performance Monitoring', () => {
    test('Latency dei health checks deve essere accettabile', async () => {
      const startTime = Date.now();
      
      await apiRequest('/api/monitoring/health');
      
      const latency = Date.now() - startTime;
      
      // Health check deve rispondere entro 1 secondo
      expect(latency).toBeLessThan(1000);
      
      console.log(`✅ Health check latency: ${latency}ms`);
    });

    test('Business metrics collection deve essere performante', async () => {
      const startTime = Date.now();
      
      await apiRequest('/api/monitoring/metrics/business');
      
      const latency = Date.now() - startTime;
      
      // Business metrics deve rispondere entro 2 secondi
      expect(latency).toBeLessThan(2000);
      
      console.log(`✅ Business metrics latency: ${latency}ms`);
    });

    test('Sistema deve gestire richieste concorrenti', async () => {
      const concurrentRequests = 5;
      const promises = Array.from({ length: concurrentRequests }, () =>
        apiRequest('/api/monitoring/status')
      );
      
      const startTime = Date.now();
      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      // Tutte le richieste devono avere successo
      results.forEach(result => {
        expect(result.status).toBe('ok');
      });
      
      // Il tempo totale non deve essere eccessivo
      expect(totalTime).toBeLessThan(3000);
      
      console.log(`✅ ${concurrentRequests} richieste concorrenti completate in ${totalTime}ms`);
    });
  });

  describe('Sistema di Resilienza', () => {
    test('Health check deve riprendersi da errori temporanei', async () => {
      // Simola condizione di carico con richieste ravvicinate
      const rapidRequests = Array.from({ length: 10 }, (_, i) =>
        apiRequest('/api/monitoring/status').catch(error => ({ error: error.message, index: i }))
      );
      
      const results = await Promise.all(rapidRequests);
      
      // Almeno la maggioranza delle richieste deve avere successo
      const successCount = results.filter(r => !r.error).length;
      const successRate = successCount / results.length;
      
      expect(successRate).toBeGreaterThan(0.7); // 70% successo minimo
      
      console.log(`✅ Resilienza testata: ${successRate * 100}% successo`);
    });

    test('Business metrics deve essere stabile nel tempo', async () => {
      // Fai 3 richieste consecutive e verifica consistenza
      const requests = [];
      for (let i = 0; i < 3; i++) {
        requests.push(await apiRequest('/api/monitoring/metrics/business'));
        await new Promise(resolve => setTimeout(resolve, 100)); // 100ms pausa
      }
      
      // Verifica che i dati siano coerenti
      const performanceScores = requests.map(r => r.data.metrics.performanceScore);
      
      // La deviazione standard non deve essere eccessiva
      const avg = performanceScores.reduce((a, b) => a + b, 0) / performanceScores.length;
      const variance = performanceScores.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) / performanceScores.length;
      const stdDev = Math.sqrt(variance);
      
      expect(stdDev).toBeLessThan(10); // Deviazione standard < 10 punti
      
      console.log(`✅ Stabilità metriche: σ=${stdDev.toFixed(2)}`);
    });
  });

  describe('Integrazione Frontend-Backend', () => {
    test('Endpoints devono restituire JSON valido con CORS headers', async () => {
      const response = await fetch(`${BASE_URL}/api/monitoring/status`, {
        method: 'GET',
        headers: {
          'Origin': 'http://localhost:3000'
        }
      });
      
      expect(response.ok).toBe(true);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      expect(data).toBeInstanceOf(Object);
      
      console.log('✅ CORS e JSON response validati');
    });

    test('Errori HTTP devono essere gestiti correttamente', async () => {
      // Test endpoint inesistente
      try {
        await apiRequest('/api/monitoring/nonexistent');
        fail('Dovrebbe aver lanciato un errore');
      } catch (error) {
        expect(error.message).toContain('404');
      }
      
      console.log('✅ Error handling HTTP validato');
    });
  });
});

// Test di performance isolati
describe('Performance Benchmarks', () => {
  test('Sistema deve rispettare i target di performance', async () => {
    console.log('🚀 Eseguendo benchmark performance...');
    
    const benchmarks = {
      healthCheck: { target: 500, actual: 0 },
      businessMetrics: { target: 1500, actual: 0 },
      statusCheck: { target: 200, actual: 0 }
    };
    
    // Health check benchmark
    let start = Date.now();
    await apiRequest('/api/monitoring/health');
    benchmarks.healthCheck.actual = Date.now() - start;
    
    // Business metrics benchmark
    start = Date.now();
    await apiRequest('/api/monitoring/metrics/business');
    benchmarks.businessMetrics.actual = Date.now() - start;
    
    // Status check benchmark
    start = Date.now();
    await apiRequest('/api/monitoring/status');
    benchmarks.statusCheck.actual = Date.now() - start;
    
    // Valida tutti i benchmark
    Object.entries(benchmarks).forEach(([name, { target, actual }]) => {
      expect(actual).toBeLessThan(target);
      console.log(`✅ ${name}: ${actual}ms (target: ${target}ms)`);
    });
    
    console.log('🎯 Tutti i benchmark di performance superati!');
  });
});