/**
 * XSS Vulnerability Scanner
 * Comprehensive Cross-Site Scripting vulnerability detection
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { app } from '../../server/index';
import { JSDOM } from 'jsdom';

describe('XSS Vulnerability Scanner', () => {
  let authCookie: string;
  let adminCookie: string;

  // Comprehensive XSS payload collection
  const xssPayloads = [
    // Basic script injection
    '<script>alert("XSS")</script>',
    '<SCRIPT>alert("XSS")</SCRIPT>',
    '<script>alert(String.fromCharCode(88,83,83))</script>',
    
    // Event handler injection
    '<img src="x" onerror="alert(\'XSS\')">',
    '<svg onload="alert(\'XSS\')">',
    '<iframe onload="alert(\'XSS\')"></iframe>',
    '<body onload="alert(\'XSS\')">',
    '<div onmouseover="alert(\'XSS\')">',
    
    // JavaScript protocol
    'javascript:alert("XSS")',
    'JAVASCRIPT:alert("XSS")',
    'java\0script:alert("XSS")',
    'javascript&colon;alert("XSS")',
    
    // Data URL injection
    'data:text/html,<script>alert("XSS")</script>',
    'data:text/html;base64,PHNjcmlwdD5hbGVydCgiWFNTIik8L3NjcmlwdD4=',
    
    // CSS injection
    '<style>@import"javascript:alert(\'XSS\')";</style>',
    '<link rel="stylesheet" href="javascript:alert(\'XSS\')">',
    
    // Meta refresh injection
    '<meta http-equiv="refresh" content="0;url=javascript:alert(\'XSS\')">',
    
    // Form injection
    '<form><button formaction="javascript:alert(\'XSS\')">Click</button></form>',
    
    // Base64 encoded payloads
    'PHNjcmlwdD5hbGVydCgiWFNTIik8L3NjcmlwdD4=', // <script>alert("XSS")</script>
    
    // Unicode and encoding bypass attempts
    '\\u003cscript\\u003ealert("XSS")\\u003c/script\\u003e',
    '%3Cscript%3Ealert("XSS")%3C/script%3E',
    '&lt;script&gt;alert("XSS")&lt;/script&gt;',
    
    // Template injection attempts
    '{{alert("XSS")}}',
    '${alert("XSS")}',
    '#{alert("XSS")}',
    
    // Context breaking attempts
    '"><script>alert("XSS")</script>',
    "'><script>alert('XSS')</script>",
    '</textarea><script>alert("XSS")</script>',
    '</title><script>alert("XSS")</script>',
    
    // DOM-based XSS attempts
    'eval(alert("XSS"))',
    'setTimeout("alert(\'XSS\')",100)',
    'setInterval("alert(\'XSS\')",100)',
    
    // Filter bypass attempts
    '<svg><script>alert&#40;&#39;XSS&#39;&#41;</script></svg>',
    '<iframe src="data:text/html,&lt;script&gt;alert(&#39;XSS&#39;)&lt;/script&gt;"></iframe>',
    
    // MIME type confusion
    '<object data="data:text/html,<script>alert(\'XSS\')</script>"></object>',
    
    // Polyglot payloads
    'jaVasCript:/*-/*`/*\\`/*\'/*"/**/(/* */onerror=alert(\'XSS\') )//%0D%0A%0d%0a//</stYle/</titLe/</teXtarEa/</scRipt/--!>\\x3csVg/<sVg/oNloAd=alert(//XSS//)//>'
  ];

  beforeAll(async () => {
    // Login as regular user
    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'user',
        password: process.env.USER_DEFAULT_PASSWORD || 'userpass123'
      });
    
    authCookie = userLogin.headers['set-cookie'][0];

    // Login as admin
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'admin',
        password: process.env.ADMIN_DEFAULT_PASSWORD || 'adminpass123'
      });
    
    adminCookie = adminLogin.headers['set-cookie'][0];
  });

  describe('Input Field XSS Vulnerability Tests', () => {
    test('should prevent XSS in product label creation', async () => {
      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/product-labels')
          .set('Cookie', authCookie)
          .send({
            productName: payload,
            lotNumber: 'LOT123',
            expirationDate: '2025-12-31',
            ddtId: 1,
            conservationInstructions: payload
          });

        if (response.status === 201) {
          // If creation succeeded, verify payload was sanitized
          expect(response.body.productName).not.toContain('<script');
          expect(response.body.productName).not.toContain('javascript:');
          expect(response.body.productName).not.toContain('onerror=');
          expect(response.body.productName).not.toContain('onload=');
          
          if (response.body.conservationInstructions) {
            expect(response.body.conservationInstructions).not.toContain('<script');
            expect(response.body.conservationInstructions).not.toContain('javascript:');
          }
        } else {
          // If validation rejected it, that's also good
          expect([400, 422]).toContain(response.status);
        }
      }
    });

    test('should prevent XSS in supplier creation', async () => {
      for (const payload of xssPayloads.slice(0, 10)) { // Test subset for performance
        const response = await request(app)
          .post('/api/suppliers')
          .set('Cookie', authCookie)
          .send({
            companyName: payload,
            vatNumber: '12345678901',
            address: payload,
            city: 'Test City',
            contactPerson: payload,
            email: '<EMAIL>',
            phone: '1234567890'
          });

        if (response.status === 201) {
          expect(response.body.companyName).not.toContain('<script');
          expect(response.body.address).not.toContain('javascript:');
          expect(response.body.contactPerson).not.toContain('onerror=');
        } else {
          expect([400, 422]).toContain(response.status);
        }
      }
    });

    test('should prevent XSS in container creation', async () => {
      for (const payload of xssPayloads.slice(0, 10)) {
        const response = await request(app)
          .post('/api/containers')
          .set('Cookie', authCookie)
          .send({
            name: payload,
            type: 'freezer',
            maxItems: 10,
            description: payload
          });

        if (response.status === 201) {
          expect(response.body.name).not.toContain('<script');
          expect(response.body.name).not.toContain('javascript:');
          if (response.body.description) {
            expect(response.body.description).not.toContain('<img');
          }
        } else {
          expect([400, 422]).toContain(response.status);
        }
      }
    });
  });

  describe('Response Content XSS Tests', () => {
    test('should not reflect XSS in error messages', async () => {
      const xssInUrl = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .get(`/api/product-labels/${encodeURIComponent(xssInUrl)}`)
        .set('Cookie', authCookie);

      expect(response.status).toBe(400);
      expect(response.text).not.toContain('<script>');
      expect(response.text).not.toContain('alert(');
    });

    test('should sanitize search parameters', async () => {
      const maliciousSearch = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .get('/api/product-labels')
        .query({ search: maliciousSearch })
        .set('Cookie', authCookie);

      expect(response.status).toBe(200);
      if (response.body.searchTerm) {
        expect(response.body.searchTerm).not.toContain('<script>');
      }
    });

    test('should prevent XSS in JSON responses', async () => {
      // Create a product with potential XSS content that should be sanitized
      const response = await request(app)
        .post('/api/product-labels')
        .set('Cookie', authCookie)
        .send({
          productName: 'Safe Product Name',
          lotNumber: 'LOT123',
          expirationDate: '2025-12-31',
          ddtId: 1,
          conservationInstructions: '<img src=x onerror=alert("XSS")>'
        });

      if (response.status === 201) {
        const jsonResponse = JSON.stringify(response.body);
        expect(jsonResponse).not.toMatch(/<script|javascript:|onerror=|onload=/i);
      }
    });
  });

  describe('HTTP Header XSS Tests', () => {
    test('should prevent XSS via custom headers', async () => {
      const xssHeader = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .get('/api/product-labels')
        .set('Cookie', authCookie)
        .set('X-Custom-Header', xssHeader)
        .set('Referer', `https://evil.com/${xssHeader}`);

      expect(response.status).toBe(200);
      // Verify response doesn't reflect the malicious headers
      expect(response.text).not.toContain('<script>');
    });

    test('should have proper XSS protection headers', async () => {
      const response = await request(app).get('/');
      
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['content-security-policy']).toBeDefined();
      expect(response.headers['x-content-type-options']).toBe('nosniff');
    });
  });

  describe('DOM-based XSS Prevention Tests', () => {
    test('should prevent DOM manipulation via API responses', async () => {
      // Test that API responses don't contain executable JavaScript
      const endpoints = [
        '/api/product-labels',
        '/api/containers',
        '/api/suppliers',
        '/api/activity-logs'
      ];

      for (const endpoint of endpoints) {
        const response = await request(app)
          .get(endpoint)
          .set('Cookie', authCookie);

        if (response.status === 200) {
          const responseText = JSON.stringify(response.body);
          expect(responseText).not.toMatch(/document\.|window\.|eval\(|setTimeout\(|setInterval\(/);
        }
      }
    });

    test('should escape HTML entities in responses', async () => {
      const htmlEntitiesTest = '&lt;script&gt;alert("XSS")&lt;/script&gt;';
      
      const response = await request(app)
        .post('/api/product-labels')
        .set('Cookie', authCookie)
        .send({
          productName: htmlEntitiesTest,
          lotNumber: 'LOT123',
          expirationDate: '2025-12-31',
          ddtId: 1
        });

      if (response.status === 201) {
        // Should remain escaped, not be converted to executable script
        expect(response.body.productName).not.toBe('<script>alert("XSS")</script>');
      }
    });
  });

  describe('Context-Specific XSS Tests', () => {
    test('should prevent XSS in URL parameters', async () => {
      const xssPayload = 'javascript:alert("XSS")';
      
      const response = await request(app)
        .get(`/api/containers`)
        .query({ 
          type: xssPayload,
          name: '<script>alert("XSS")</script>'
        })
        .set('Cookie', authCookie);

      expect(response.status).toBe(200);
      expect(response.text).not.toContain('javascript:');
      expect(response.text).not.toContain('<script>');
    });

    test('should prevent XSS in file upload metadata', async () => {
      const maliciousFilename = '<script>alert("XSS")</script>.jpg';
      
      const response = await request(app)
        .post('/api/ddts')
        .set('Cookie', authCookie)
        .attach('image', Buffer.from('fake image data'), maliciousFilename);

      // Should either reject the file or sanitize the filename
      if (response.status === 201) {
        expect(JSON.stringify(response.body)).not.toContain('<script>');
      } else {
        expect([400, 415, 422]).toContain(response.status);
      }
    });
  });

  describe('Advanced XSS Vector Tests', () => {
    test('should prevent SVG-based XSS', async () => {
      const svgXss = '<svg xmlns="http://www.w3.org/2000/svg" onload="alert(\'XSS\')"><rect width="100" height="100"/></svg>';
      
      const response = await request(app)
        .post('/api/product-labels')
        .set('Cookie', authCookie)
        .send({
          productName: svgXss,
          lotNumber: 'LOT123',
          expirationDate: '2025-12-31',
          ddtId: 1
        });

      if (response.status === 201) {
        expect(response.body.productName).not.toContain('onload=');
        expect(response.body.productName).not.toContain('<svg');
      } else {
        expect([400, 422]).toContain(response.status);
      }
    });

    test('should prevent CSS-based XSS', async () => {
      const cssXss = '<style>body{background:url("javascript:alert(\'XSS\')")}</style>';
      
      const response = await request(app)
        .post('/api/suppliers')
        .set('Cookie', authCookie)
        .send({
          companyName: cssXss,
          vatNumber: '12345678901',
          address: 'Test Address',
          city: 'Test City'
        });

      if (response.status === 201) {
        expect(response.body.companyName).not.toContain('<style>');
        expect(response.body.companyName).not.toContain('javascript:');
      } else {
        expect([400, 422]).toContain(response.status);
      }
    });

    test('should prevent template injection XSS', async () => {
      const templateXss = '{{constructor.constructor("alert(\'XSS\')")()}}';
      
      const response = await request(app)
        .post('/api/containers')
        .set('Cookie', authCookie)
        .send({
          name: templateXss,
          type: 'freezer',
          maxItems: 10
        });

      if (response.status === 201) {
        expect(response.body.name).not.toContain('constructor');
        expect(response.body.name).not.toContain('{{');
      } else {
        expect([400, 422]).toContain(response.status);
      }
    });
  });

  describe('Content Security Policy Validation', () => {
    test('should enforce strict CSP headers', async () => {
      const response = await request(app).get('/');
      
      const csp = response.headers['content-security-policy'];
      expect(csp).toBeDefined();
      
      // Should not allow unsafe-inline for scripts in production
      if (process.env.NODE_ENV === 'production') {
        expect(csp).not.toContain("script-src 'unsafe-inline'");
      }
      
      // Should prevent data: URLs for scripts
      expect(csp).not.toContain("script-src data:");
    });

    test('should prevent inline event handlers', async () => {
      const response = await request(app).get('/');
      const csp = response.headers['content-security-policy'];
      
      // CSP should be configured to block inline event handlers
      expect(csp).toContain("script-src");
      // In production, should not allow unsafe-inline
      if (process.env.NODE_ENV === 'production') {
        expect(csp).not.toContain("'unsafe-inline'");
      }
    });
  });

  afterAll(async () => {
    // Cleanup any test data created during XSS tests
  });
});