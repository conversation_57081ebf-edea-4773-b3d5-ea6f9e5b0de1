/**
 * Security Penetration Testing Suite
 * Comprehensive security validation for HACCP Tracker
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { app } from '../../server/index';

describe('Security Penetration Testing', () => {
  let authCookie: string;
  let adminCookie: string;

  beforeAll(async () => {
    // Login as regular user
    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'user',
        password: process.env.USER_DEFAULT_PASSWORD || 'userpass123'
      });
    
    authCookie = userLogin.headers['set-cookie'][0];

    // Login as admin
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'admin',
        password: process.env.ADMIN_DEFAULT_PASSWORD || 'adminpass123'
      });
    
    adminCookie = adminLogin.headers['set-cookie'][0];
  });

  describe('Authentication Security Tests', () => {
    test('should block access without authentication', async () => {
      const endpoints = [
        '/api/product-labels',
        '/api/containers',
        '/api/ddts',
        '/api/suppliers',
        '/api/activity-logs'
      ];

      for (const endpoint of endpoints) {
        const response = await request(app).get(endpoint);
        expect(response.status).toBe(401);
        expect(response.body.message).toBe('Unauthorized');
      }
    });

    test('should prevent SQL injection in login', async () => {
      const sqlInjectionAttempts = [
        "admin'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin' UNION SELECT * FROM users --",
        "'; DELETE FROM users WHERE '1'='1' --"
      ];

      for (const attempt of sqlInjectionAttempts) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            username: attempt,
            password: 'password'
          });
        
        expect(response.status).toBe(401);
        expect(response.body.message).toBe('Invalid username or password');
      }
    });

    test('should enforce rate limiting on authentication', async () => {
      const attempts = [];
      
      // Attempt multiple logins rapidly
      for (let i = 0; i < 15; i++) {
        attempts.push(
          request(app)
            .post('/api/auth/login')
            .send({
              username: 'testuser',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(attempts);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should prevent session fixation attacks', async () => {
      // Attempt to use a fixed session ID
      const response = await request(app)
        .post('/api/auth/login')
        .set('Cookie', 'connect.sid=fixed-session-id')
        .send({
          username: 'admin',
          password: process.env.ADMIN_DEFAULT_PASSWORD || 'adminpass123'
        });

      expect(response.status).toBe(200);
      const newSessionCookie = response.headers['set-cookie']?.[0];
      expect(newSessionCookie).toBeDefined();
      expect(newSessionCookie).not.toContain('fixed-session-id');
    });
  });

  describe('XSS Vulnerability Tests', () => {
    test('should sanitize XSS attempts in product creation', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript:alert("XSS")',
        '<img src="x" onerror="alert(\'XSS\')">',
        '<svg onload="alert(\'XSS\')">',
        '"><script>alert("XSS")</script>'
      ];

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/product-labels')
          .set('Cookie', authCookie)
          .send({
            productName: payload,
            lotNumber: 'LOT123',
            expirationDate: '2025-12-31',
            ddtId: 1
          });

        // Should either reject with validation error or sanitize
        if (response.status === 201) {
          expect(response.body.productName).not.toContain('<script>');
          expect(response.body.productName).not.toContain('javascript:');
        } else {
          expect(response.status).toBe(400);
        }
      }
    });

    test('should prevent XSS in supplier creation', async () => {
      const xssPayload = '<script>document.cookie="stolen=true"</script>';
      
      const response = await request(app)
        .post('/api/suppliers')
        .set('Cookie', authCookie)
        .send({
          companyName: xssPayload,
          vatNumber: '12345678901',
          address: 'Test Address',
          city: 'Test City'
        });

      if (response.status === 201) {
        expect(response.body.companyName).not.toContain('<script>');
      } else {
        expect(response.status).toBe(400);
      }
    });

    test('should prevent XSS in container creation', async () => {
      const xssPayload = '<img src=x onerror=alert("XSS")>';
      
      const response = await request(app)
        .post('/api/containers')
        .set('Cookie', authCookie)
        .send({
          name: xssPayload,
          type: 'freezer',
          maxItems: 10
        });

      if (response.status === 201) {
        expect(response.body.name).not.toContain('<img');
        expect(response.body.name).not.toContain('onerror');
      } else {
        expect(response.status).toBe(400);
      }
    });
  });

  describe('Authorization Security Tests', () => {
    test('should prevent privilege escalation', async () => {
      // Regular user trying to access admin endpoints
      const adminEndpoints = [
        { method: 'get', path: '/api/users' },
        { method: 'post', path: '/api/users' },
        { method: 'delete', path: '/api/activity-logs/cleanup' }
      ];

      for (const endpoint of adminEndpoints) {
        const response = await request(app)
          [endpoint.method](endpoint.path)
          .set('Cookie', authCookie);
        
        expect([401, 403]).toContain(response.status);
      }
    });

    test('should enforce tenant isolation', async () => {
      // This test would require multiple tenants to be properly effective
      // For now, we verify that tenant context is properly validated
      const response = await request(app)
        .get('/api/product-labels')
        .set('Cookie', authCookie);

      expect(response.status).toBe(200);
      // Verify that response only contains data for the authenticated user's tenant
      expect(Array.isArray(response.body)).toBe(true);
    });

    test('should prevent CSRF attacks', async () => {
      // Attempt to make state-changing request without CSRF token
      const response = await request(app)
        .post('/api/containers')
        .set('Cookie', authCookie)
        .set('Origin', 'https://malicious-site.com')
        .send({
          name: 'Test Container',
          type: 'freezer',
          maxItems: 10
        });

      // Should be blocked by CORS or CSRF protection
      expect([403, 401]).toContain(response.status);
    });
  });

  describe('Input Validation Tests', () => {
    test('should validate and reject malformed requests', async () => {
      const malformedRequests = [
        { endpoint: '/api/product-labels', data: { productName: '', lotNumber: null } },
        { endpoint: '/api/containers', data: { name: '', maxItems: -1 } },
        { endpoint: '/api/suppliers', data: { companyName: '', vatNumber: 'invalid' } }
      ];

      for (const req of malformedRequests) {
        const response = await request(app)
          .post(req.endpoint)
          .set('Cookie', authCookie)
          .send(req.data);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Validation error');
      }
    });

    test('should prevent oversized requests', async () => {
      const largeString = 'A'.repeat(100000); // 100KB string
      
      const response = await request(app)
        .post('/api/product-labels')
        .set('Cookie', authCookie)
        .send({
          productName: largeString,
          lotNumber: 'LOT123',
          expirationDate: '2025-12-31',
          ddtId: 1,
          conservationInstructions: largeString
        });

      expect([400, 413]).toContain(response.status);
    });

    test('should validate file upload security', async () => {
      // Test with potentially malicious file content
      const maliciousContent = Buffer.from('<script>alert("XSS")</script>');
      
      const response = await request(app)
        .post('/api/ddts')
        .set('Cookie', authCookie)
        .attach('image', maliciousContent, 'malicious.html');

      expect([400, 415]).toContain(response.status);
    });
  });

  describe('Data Exposure Tests', () => {
    test('should not expose sensitive information in errors', async () => {
      const response = await request(app)
        .get('/api/nonexistent-endpoint')
        .set('Cookie', authCookie);

      expect(response.status).toBe(404);
      expect(JSON.stringify(response.body)).not.toMatch(/password|secret|key|token/i);
    });

    test('should not expose database structure in errors', async () => {
      const response = await request(app)
        .get('/api/product-labels/99999999')
        .set('Cookie', authCookie);

      expect(response.status).toBe(404);
      expect(JSON.stringify(response.body)).not.toMatch(/table|column|database|sql/i);
    });

    test('should sanitize activity log data', async () => {
      const response = await request(app)
        .get('/api/activity-logs')
        .set('Cookie', adminCookie);

      expect(response.status).toBe(200);
      
      // Check that no sensitive data is exposed in activity logs
      const logs = response.body;
      for (const log of logs) {
        expect(JSON.stringify(log)).not.toMatch(/password|secret|key|token/i);
      }
    });
  });

  describe('Security Headers Tests', () => {
    test('should include security headers', async () => {
      const response = await request(app).get('/');

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
      expect(response.headers['content-security-policy']).toBeDefined();
    });

    test('should enforce HTTPS in production headers', async () => {
      // This test would need to be run in production environment
      // For now, we verify the header logic exists
      const response = await request(app).get('/');
      expect(response.headers).toBeDefined();
    });
  });

  afterAll(async () => {
    // Cleanup any test data if needed
  });
});