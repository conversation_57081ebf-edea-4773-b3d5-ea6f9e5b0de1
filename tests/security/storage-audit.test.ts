/**
 * Storage Security Audit Suite
 * Comprehensive database and storage layer security validation
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { storage } from '../../server/storage';
import { db } from '../../server/db';
import { eq, sql } from 'drizzle-orm';
import { users, containers, productLabels, activityLogs, suppliers } from '../../shared/schema';

describe('Storage Security Audit', () => {
  const testTenantId = 'test-tenant-audit';
  const testUserId = 'test-user-audit';

  beforeAll(async () => {
    // Create test tenant and user for isolation testing
    await db.insert(users).values({
      id: testUserId,
      username: 'audit-test-user',
      passwordHash: 'test-hash',
      tenantId: testTenantId,
      isAdmin: false,
      role: 'user'
    }).onConflictDoNothing();
  });

  describe('Tenant Isolation Validation', () => {
    test('should enforce tenant isolation in getAllProductLabels', async () => {
      // Create product labels for different tenants
      const tenant1Product = await db.insert(productLabels).values({
        productName: 'Tenant1 Product',
        lotNumber: 'T1-LOT123',
        expirationDate: new Date('2025-12-31'),
        createdBy: testUserId,
        tenantId: 'tenant-1',
        ddtId: 1,
        qrCodeData: 'test-qr-1'
      }).returning();

      const tenant2Product = await db.insert(productLabels).values({
        productName: 'Tenant2 Product',
        lotNumber: 'T2-LOT123',
        expirationDate: new Date('2025-12-31'),
        createdBy: testUserId,
        tenantId: 'tenant-2',
        ddtId: 1,
        qrCodeData: 'test-qr-2'
      }).returning();

      // Test that tenant isolation works
      const tenant1Results = await db.select()
        .from(productLabels)
        .where(eq(productLabels.tenantId, 'tenant-1'));

      const tenant2Results = await db.select()
        .from(productLabels)
        .where(eq(productLabels.tenantId, 'tenant-2'));

      expect(tenant1Results).toHaveLength(1);
      expect(tenant2Results).toHaveLength(1);
      expect(tenant1Results[0].productName).toBe('Tenant1 Product');
      expect(tenant2Results[0].productName).toBe('Tenant2 Product');

      // Cleanup
      await db.delete(productLabels).where(eq(productLabels.id, tenant1Product[0].id));
      await db.delete(productLabels).where(eq(productLabels.id, tenant2Product[0].id));
    });

    test('should prevent cross-tenant data access in containers', async () => {
      // Create containers for different tenants
      const tenant1Container = await db.insert(containers).values({
        name: 'Tenant1 Container',
        type: 'freezer',
        maxItems: 10,
        currentItems: 0,
        tenantId: 'tenant-1',
        createdBy: testUserId,
        qrCodeData: 'container-qr-1'
      }).returning();

      const tenant2Container = await db.insert(containers).values({
        name: 'Tenant2 Container',
        type: 'refrigerator',
        maxItems: 15,
        currentItems: 0,
        tenantId: 'tenant-2',
        createdBy: testUserId,
        qrCodeData: 'container-qr-2'
      }).returning();

      // Verify tenant isolation
      const tenant1Containers = await db.select()
        .from(containers)
        .where(eq(containers.tenantId, 'tenant-1'));

      const tenant2Containers = await db.select()
        .from(containers)
        .where(eq(containers.tenantId, 'tenant-2'));

      expect(tenant1Containers).toHaveLength(1);
      expect(tenant2Containers).toHaveLength(1);
      expect(tenant1Containers[0].name).toBe('Tenant1 Container');
      expect(tenant2Containers[0].name).toBe('Tenant2 Container');

      // Cleanup
      await db.delete(containers).where(eq(containers.id, tenant1Container[0].id));
      await db.delete(containers).where(eq(containers.id, tenant2Container[0].id));
    });

    test('should enforce tenant isolation in user queries', async () => {
      // Create users for different tenants
      const tenant1User = await db.insert(users).values({
        id: 'tenant1-user-audit',
        username: 'tenant1-user',
        passwordHash: 'hash1',
        tenantId: 'tenant-1',
        isAdmin: false,
        role: 'user'
      }).returning();

      const tenant2User = await db.insert(users).values({
        id: 'tenant2-user-audit',
        username: 'tenant2-user',
        passwordHash: 'hash2',
        tenantId: 'tenant-2',
        isAdmin: false,
        role: 'user'
      }).returning();

      // Test secure user lookup
      const user1 = await storage.getUserSecure('tenant1-user-audit');
      const user2 = await storage.getUserSecure('tenant2-user-audit');

      expect(user1?.tenantId).toBe('tenant-1');
      expect(user2?.tenantId).toBe('tenant-2');

      // Cleanup
      await db.delete(users).where(eq(users.id, 'tenant1-user-audit'));
      await db.delete(users).where(eq(users.id, 'tenant2-user-audit'));
    });
  });

  describe('Data Validation and Sanitization', () => {
    test('should validate required fields in storage operations', async () => {
      // Test invalid product label creation
      try {
        await storage.createProductLabel({
          productName: '', // Empty required field
          lotNumber: 'LOT123',
          expirationDate: new Date('2025-12-31'),
          createdBy: testUserId,
          ddtId: 1
        });
        fail('Should have thrown validation error');
      } catch (error) {
        expect(error).toBeDefined();
      }

      // Test invalid container creation
      try {
        await storage.createContainer({
          name: '', // Empty required field
          type: 'freezer',
          maxItems: -1, // Invalid value
          createdBy: testUserId
        });
        fail('Should have thrown validation error');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should sanitize input data', async () => {
      const maliciousInput = '<script>alert("XSS")</script>';
      
      try {
        const supplier = await storage.createSupplier({
          companyName: maliciousInput,
          vatNumber: '12345678901',
          address: 'Test Address',
          city: 'Test City',
          tenantId: testTenantId,
          createdBy: testUserId
        });

        // Verify that dangerous content is not stored as-is
        expect(supplier.companyName).not.toContain('<script>');
        
        // Cleanup
        await db.delete(suppliers).where(eq(suppliers.id, supplier.id));
      } catch (error) {
        // If validation prevents creation, that's also acceptable
        expect(error).toBeDefined();
      }
    });

    test('should enforce data length limits', async () => {
      const veryLongString = 'A'.repeat(10000);
      
      try {
        await storage.createProductLabel({
          productName: veryLongString,
          lotNumber: 'LOT123',
          expirationDate: new Date('2025-12-31'),
          createdBy: testUserId,
          ddtId: 1
        });
        fail('Should have thrown validation error for oversized data');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('SQL Injection Prevention', () => {
    test('should prevent SQL injection in parameterized queries', async () => {
      const maliciousInput = "'; DROP TABLE users; --";
      
      // Test that malicious input doesn't execute SQL
      const result = await db.select()
        .from(users)
        .where(eq(users.username, maliciousInput))
        .limit(1);

      expect(result).toHaveLength(0);
      
      // Verify that users table still exists
      const userCount = await db.select({ count: sql<number>`count(*)` })
        .from(users);
      
      expect(userCount[0].count).toBeGreaterThan(0);
    });

    test('should safely handle special characters in data', async () => {
      const specialCharsInput = "Test'Name\"With\\Backslash&Ampersand";
      
      const container = await storage.createContainer({
        name: specialCharsInput,
        type: 'freezer',
        maxItems: 10,
        createdBy: testUserId
      });

      expect(container.name).toBe(specialCharsInput);
      
      // Cleanup
      await db.delete(containers).where(eq(containers.id, container.id));
    });
  });

  describe('Activity Logging Security', () => {
    test('should not log sensitive information in activity logs', async () => {
      // Create an activity log
      await storage.addActivityLog({
        tenantId: testTenantId,
        userId: testUserId,
        username: 'audit-test-user',
        action: 'LOGIN',
        details: 'User logged in with password: secretpassword123',
        timestamp: new Date()
      });

      const logs = await storage.getActivityLogs();
      const sensitiveLog = logs.find(log => 
        log.userId === testUserId && log.action === 'LOGIN'
      );

      if (sensitiveLog) {
        // Should not contain the actual password
        expect(sensitiveLog.details).not.toContain('secretpassword123');
      }
    });

    test('should enforce proper activity log retention', async () => {
      // Test that old logs can be properly cleaned up
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 2);

      await storage.addActivityLog({
        tenantId: testTenantId,
        userId: testUserId,
        username: 'audit-test-user',
        action: 'OLD_ACTION',
        details: 'Old action for testing',
        timestamp: oldDate
      });

      // Test deletion of old logs
      const deletedCount = await storage.deleteOldActivityLogs(oldDate);
      expect(typeof deletedCount).toBe('number');
      expect(deletedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Database Connection Security', () => {
    test('should use secure connection parameters', () => {
      // Verify that database URL uses SSL in production
      const dbUrl = process.env.DATABASE_URL;
      if (dbUrl && process.env.NODE_ENV === 'production') {
        expect(dbUrl).toContain('ssl=true');
      }
    });

    test('should handle connection errors gracefully', async () => {
      // Test that database errors don't expose sensitive information
      try {
        await db.execute(sql`SELECT * FROM nonexistent_table`);
        fail('Should have thrown database error');
      } catch (error) {
        const errorMessage = String(error);
        expect(errorMessage).not.toMatch(/password|secret|key/i);
      }
    });
  });

  describe('Backup and Recovery Security', () => {
    test('should not expose sensitive data in error messages', async () => {
      // Test error handling doesn't leak information
      try {
        await storage.getProductLabel(999999999);
      } catch (error) {
        const errorMessage = String(error);
        expect(errorMessage).not.toMatch(/database|table|column/i);
      }
    });

    test('should validate data integrity constraints', async () => {
      // Test foreign key constraints
      try {
        await storage.createProductLabel({
          productName: 'Test Product',
          lotNumber: 'LOT123',
          expirationDate: new Date('2025-12-31'),
          createdBy: 'nonexistent-user-id',
          ddtId: 999999999 // Non-existent DDT ID
        });
        fail('Should have thrown foreign key constraint error');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  afterAll(async () => {
    // Cleanup test data
    await db.delete(users).where(eq(users.id, testUserId));
    await db.delete(activityLogs).where(eq(activityLogs.tenantId, testTenantId));
  });
});