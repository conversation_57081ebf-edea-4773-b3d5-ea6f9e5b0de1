/**
 * Performance Impact Assessment
 * Comprehensive analysis of security implementations impact on system performance
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { app } from '../../server/index';
import { performance } from 'perf_hooks';

describe('Performance Impact Assessment', () => {
  let authCookie: string;
  let adminCookie: string;

  const performanceMetrics = {
    authenticationTime: [] as number[],
    apiResponseTimes: [] as number[],
    securityHeadersOverhead: [] as number[],
    validationOverhead: [] as number[],
    dbQueryTimes: [] as number[]
  };

  beforeAll(async () => {
    // Login and measure authentication performance
    const authStart = performance.now();
    
    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'user',
        password: process.env.USER_DEFAULT_PASSWORD || 'userpass123'
      });
    
    const authEnd = performance.now();
    performanceMetrics.authenticationTime.push(authEnd - authStart);
    authCookie = userLogin.headers['set-cookie'][0];

    // Admin login
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'admin',
        password: process.env.ADMIN_DEFAULT_PASSWORD || 'adminpass123'
      });
    
    adminCookie = adminLogin.headers['set-cookie'][0];
  });

  describe('Authentication Performance Tests', () => {
    test('should measure authentication response time under load', async () => {
      const iterations = 10;
      const authTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        await request(app)
          .get('/api/auth/me')
          .set('Cookie', authCookie);
          
        const end = performance.now();
        authTimes.push(end - start);
      }

      const avgTime = authTimes.reduce((a, b) => a + b, 0) / authTimes.length;
      const maxTime = Math.max(...authTimes);
      
      console.log(`Authentication avg: ${avgTime.toFixed(2)}ms, max: ${maxTime.toFixed(2)}ms`);
      
      // Authentication should be fast
      expect(avgTime).toBeLessThan(200); // Average under 200ms
      expect(maxTime).toBeLessThan(500); // Max under 500ms
      
      performanceMetrics.authenticationTime.push(...authTimes);
    });

    test('should measure rate limiting performance impact', async () => {
      const iterations = 50;
      const requestTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        const response = await request(app)
          .get('/api/product-labels')
          .set('Cookie', authCookie);
          
        const end = performance.now();
        requestTimes.push(end - start);

        // Break if rate limited
        if (response.status === 429) {
          break;
        }
      }

      const avgTime = requestTimes.reduce((a, b) => a + b, 0) / requestTimes.length;
      console.log(`Rate limiting check avg: ${avgTime.toFixed(2)}ms`);
      
      // Rate limiting shouldn't add significant overhead
      expect(avgTime).toBeLessThan(100);
    });

    test('should measure CSRF protection overhead', async () => {
      const iterations = 20;
      const csrfTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        await request(app)
          .post('/api/product-labels')
          .set('Cookie', authCookie)
          .send({
            productName: `Test Product ${i}`,
            lotNumber: `LOT${i}`,
            expirationDate: '2025-12-31',
            ddtId: 1
          });
          
        const end = performance.now();
        csrfTimes.push(end - start);
      }

      const avgTime = csrfTimes.reduce((a, b) => a + b, 0) / csrfTimes.length;
      console.log(`CSRF protection avg: ${avgTime.toFixed(2)}ms`);
      
      // CSRF protection should add minimal overhead
      expect(avgTime).toBeLessThan(300);
    });
  });

  describe('API Response Performance Tests', () => {
    test('should measure security headers performance impact', async () => {
      const iterations = 50;
      const headerTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        const response = await request(app)
          .get('/api/product-labels')
          .set('Cookie', authCookie);
          
        const end = performance.now();
        headerTimes.push(end - start);

        // Verify security headers are present
        expect(response.headers['x-content-type-options']).toBe('nosniff');
        expect(response.headers['x-frame-options']).toBe('DENY');
      }

      const avgTime = headerTimes.reduce((a, b) => a + b, 0) / headerTimes.length;
      console.log(`Security headers avg: ${avgTime.toFixed(2)}ms`);
      
      performanceMetrics.securityHeadersOverhead.push(...headerTimes);
      expect(avgTime).toBeLessThan(150);
    });

    test('should measure input validation performance', async () => {
      const iterations = 30;
      const validationTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        await request(app)
          .post('/api/suppliers')
          .set('Cookie', authCookie)
          .send({
            companyName: `Test Company ${i}`,
            vatNumber: `*********0${i}`,
            address: 'Test Address',
            city: 'Test City'
          });
          
        const end = performance.now();
        validationTimes.push(end - start);
      }

      const avgTime = validationTimes.reduce((a, b) => a + b, 0) / validationTimes.length;
      console.log(`Input validation avg: ${avgTime.toFixed(2)}ms`);
      
      performanceMetrics.validationOverhead.push(...validationTimes);
      expect(avgTime).toBeLessThan(250);
    });

    test('should measure XSS protection performance impact', async () => {
      const maliciousInputs = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(\'XSS\')">',
        'javascript:alert("XSS")',
        '<svg onload="alert(\'XSS\')">',
        '"><script>alert("XSS")</script>'
      ];

      const xssTimes: number[] = [];

      for (const input of maliciousInputs) {
        const start = performance.now();
        
        await request(app)
          .post('/api/containers')
          .set('Cookie', authCookie)
          .send({
            name: input,
            type: 'freezer',
            maxItems: 10
          });
          
        const end = performance.now();
        xssTimes.push(end - start);
      }

      const avgTime = xssTimes.reduce((a, b) => a + b, 0) / xssTimes.length;
      console.log(`XSS protection avg: ${avgTime.toFixed(2)}ms`);
      
      // XSS protection shouldn't significantly slow down requests
      expect(avgTime).toBeLessThan(400);
    });
  });

  describe('Database Security Performance Tests', () => {
    test('should measure tenant isolation query performance', async () => {
      const iterations = 25;
      const queryTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        await request(app)
          .get('/api/containers')
          .set('Cookie', authCookie);
          
        const end = performance.now();
        queryTimes.push(end - start);
      }

      const avgTime = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
      console.log(`Tenant isolation queries avg: ${avgTime.toFixed(2)}ms`);
      
      performanceMetrics.dbQueryTimes.push(...queryTimes);
      expect(avgTime).toBeLessThan(200);
    });

    test('should measure parameterized query performance', async () => {
      const iterations = 20;
      const paramQueryTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        await request(app)
          .get('/api/product-labels')
          .query({ search: `product${i}` })
          .set('Cookie', authCookie);
          
        const end = performance.now();
        paramQueryTimes.push(end - start);
      }

      const avgTime = paramQueryTimes.reduce((a, b) => a + b, 0) / paramQueryTimes.length;
      console.log(`Parameterized queries avg: ${avgTime.toFixed(2)}ms`);
      
      // Parameterized queries should be efficient
      expect(avgTime).toBeLessThan(180);
    });

    test('should measure activity logging performance impact', async () => {
      const iterations = 15;
      const loggingTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        // Create an action that triggers activity logging
        await request(app)
          .post('/api/product-labels')
          .set('Cookie', authCookie)
          .send({
            productName: `Logged Product ${i}`,
            lotNumber: `LOG${i}`,
            expirationDate: '2025-12-31',
            ddtId: 1
          });
          
        const end = performance.now();
        loggingTimes.push(end - start);
      }

      const avgTime = loggingTimes.reduce((a, b) => a + b, 0) / loggingTimes.length;
      console.log(`Activity logging avg: ${avgTime.toFixed(2)}ms`);
      
      // Activity logging shouldn't significantly impact performance
      expect(avgTime).toBeLessThan(350);
    });
  });

  describe('Concurrent Request Performance Tests', () => {
    test('should handle concurrent authenticated requests efficiently', async () => {
      const concurrentRequests = 20;
      const requestPromises: Promise<any>[] = [];

      const start = performance.now();

      for (let i = 0; i < concurrentRequests; i++) {
        requestPromises.push(
          request(app)
            .get('/api/product-labels')
            .set('Cookie', authCookie)
        );
      }

      const responses = await Promise.all(requestPromises);
      const end = performance.now();

      const totalTime = end - start;
      const avgTime = totalTime / concurrentRequests;

      console.log(`Concurrent requests (${concurrentRequests}) total: ${totalTime.toFixed(2)}ms, avg: ${avgTime.toFixed(2)}ms`);

      // All requests should succeed
      responses.forEach(response => {
        expect([200, 304]).toContain(response.status);
      });

      // Concurrent processing should be efficient
      expect(avgTime).toBeLessThan(100);
    });

    test('should maintain performance under security load', async () => {
      const iterations = 10;
      const loadTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const requests = [
          request(app).get('/api/product-labels').set('Cookie', authCookie),
          request(app).get('/api/containers').set('Cookie', authCookie),
          request(app).get('/api/suppliers').set('Cookie', authCookie),
          request(app).get('/api/activity-logs').set('Cookie', adminCookie)
        ];

        const start = performance.now();
        await Promise.all(requests);
        const end = performance.now();

        loadTimes.push(end - start);
      }

      const avgTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
      console.log(`Security load test avg: ${avgTime.toFixed(2)}ms`);

      // System should handle security-enhanced load efficiently
      expect(avgTime).toBeLessThan(600);
    });
  });

  describe('Memory and Resource Usage Tests', () => {
    test('should not have significant memory leaks in security operations', async () => {
      const iterations = 100;
      const initialMemory = process.memoryUsage().heapUsed;

      for (let i = 0; i < iterations; i++) {
        await request(app)
          .post('/api/product-labels')
          .set('Cookie', authCookie)
          .send({
            productName: `Memory Test ${i}`,
            lotNumber: `MEM${i}`,
            expirationDate: '2025-12-31',
            ddtId: 1
          });
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB

      console.log(`Memory increase after ${iterations} operations: ${memoryIncrease.toFixed(2)}MB`);

      // Memory increase should be reasonable
      expect(memoryIncrease).toBeLessThan(50); // Less than 50MB increase
    });

    test('should measure CPU overhead of security features', async () => {
      const iterations = 50;
      
      // Measure CPU-intensive security operations
      const cpuIntensiveStart = process.cpuUsage();
      const start = performance.now();

      for (let i = 0; i < iterations; i++) {
        // Simulate security-heavy operations
        await request(app)
          .post('/api/suppliers')
          .set('Cookie', authCookie)
          .send({
            companyName: `CPU Test Company ${i}<script>alert('test')</script>`,
            vatNumber: `*********${i}`,
            address: 'Test Address with potential XSS <img src=x>',
            city: 'Test City'
          });
      }

      const end = performance.now();
      const cpuUsage = process.cpuUsage(cpuIntensiveStart);

      const totalTime = end - start;
      const avgTime = totalTime / iterations;
      const userCpuTime = cpuUsage.user / 1000; // Convert to milliseconds
      const systemCpuTime = cpuUsage.system / 1000;

      console.log(`CPU overhead - Total: ${totalTime.toFixed(2)}ms, Avg: ${avgTime.toFixed(2)}ms`);
      console.log(`CPU usage - User: ${userCpuTime.toFixed(2)}ms, System: ${systemCpuTime.toFixed(2)}ms`);

      // Security operations should not consume excessive CPU
      expect(avgTime).toBeLessThan(300);
      expect(userCpuTime).toBeLessThan(5000); // 5 seconds total user CPU time
    });
  });

  describe('Performance Regression Tests', () => {
    test('should compare performance with and without security headers', async () => {
      const iterations = 20;
      
      // This test would ideally compare against a baseline without security features
      // For now, we ensure security-enabled performance meets standards
      const securityEnabledTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        const response = await request(app)
          .get('/api/product-labels')
          .set('Cookie', authCookie);
          
        const end = performance.now();
        securityEnabledTimes.push(end - start);

        // Verify security features are active
        expect(response.headers['x-content-type-options']).toBeDefined();
        expect(response.headers['content-security-policy']).toBeDefined();
      }

      const avgTime = securityEnabledTimes.reduce((a, b) => a + b, 0) / securityEnabledTimes.length;
      console.log(`Security-enabled avg response time: ${avgTime.toFixed(2)}ms`);
      
      // Performance should still be good with security enabled
      expect(avgTime).toBeLessThan(150);
    });

    test('should measure end-to-end transaction performance', async () => {
      const iterations = 10;
      const transactionTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        // Full transaction: create supplier, create DDT, create product, assign to container
        const supplier = await request(app)
          .post('/api/suppliers')
          .set('Cookie', authCookie)
          .send({
            companyName: `Transaction Test ${i}`,
            vatNumber: `*********${i}`,
            address: 'Test Address',
            city: 'Test City'
          });

        if (supplier.status === 201) {
          const product = await request(app)
            .post('/api/product-labels')
            .set('Cookie', authCookie)
            .send({
              productName: `Transaction Product ${i}`,
              lotNumber: `TXN${i}`,
              expirationDate: '2025-12-31',
              ddtId: 1
            });

          if (product.status === 201) {
            await request(app)
              .post('/api/containers')
              .set('Cookie', authCookie)
              .send({
                name: `Transaction Container ${i}`,
                type: 'freezer',
                maxItems: 10
              });
          }
        }
        
        const end = performance.now();
        transactionTimes.push(end - start);
      }

      const avgTime = transactionTimes.reduce((a, b) => a + b, 0) / transactionTimes.length;
      console.log(`End-to-end transaction avg: ${avgTime.toFixed(2)}ms`);
      
      // Full transactions should complete in reasonable time
      expect(avgTime).toBeLessThan(1000); // Under 1 second
    });
  });

  describe('Performance Metrics Summary', () => {
    test('should generate comprehensive performance report', () => {
      const report = {
        authentication: {
          avgTime: performanceMetrics.authenticationTime.reduce((a, b) => a + b, 0) / performanceMetrics.authenticationTime.length,
          samples: performanceMetrics.authenticationTime.length
        },
        apiResponses: {
          avgTime: performanceMetrics.apiResponseTimes.reduce((a, b) => a + b, 0) / performanceMetrics.apiResponseTimes.length || 0,
          samples: performanceMetrics.apiResponseTimes.length
        },
        securityHeaders: {
          avgTime: performanceMetrics.securityHeadersOverhead.reduce((a, b) => a + b, 0) / performanceMetrics.securityHeadersOverhead.length,
          samples: performanceMetrics.securityHeadersOverhead.length
        },
        validation: {
          avgTime: performanceMetrics.validationOverhead.reduce((a, b) => a + b, 0) / performanceMetrics.validationOverhead.length,
          samples: performanceMetrics.validationOverhead.length
        },
        database: {
          avgTime: performanceMetrics.dbQueryTimes.reduce((a, b) => a + b, 0) / performanceMetrics.dbQueryTimes.length,
          samples: performanceMetrics.dbQueryTimes.length
        }
      };

      console.log('\n=== PERFORMANCE IMPACT ASSESSMENT REPORT ===');
      console.log(`Authentication: ${report.authentication.avgTime.toFixed(2)}ms avg (${report.authentication.samples} samples)`);
      console.log(`Security Headers: ${report.securityHeaders.avgTime.toFixed(2)}ms avg (${report.securityHeaders.samples} samples)`);
      console.log(`Input Validation: ${report.validation.avgTime.toFixed(2)}ms avg (${report.validation.samples} samples)`);
      console.log(`Database Queries: ${report.database.avgTime.toFixed(2)}ms avg (${report.database.samples} samples)`);
      console.log('============================================\n');

      // All security features should maintain good performance
      expect(report.authentication.avgTime).toBeLessThan(200);
      expect(report.securityHeaders.avgTime).toBeLessThan(150);
      expect(report.validation.avgTime).toBeLessThan(250);
      expect(report.database.avgTime).toBeLessThan(200);

      // Performance impact assessment passed
      expect(true).toBe(true);
    });
  });

  afterAll(async () => {
    // Cleanup performance test data
    console.log('\nPerformance Impact Assessment completed successfully');
  });
});