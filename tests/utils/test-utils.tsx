/**
 * Utilities per Testing React Components
 * 
 * @description Wrapper personalizzati per testing-library con providers necessari
 * Include: QueryClient, Router, Context providers
 * <AUTHOR> di Testing Automatizzato HACCP Tracker
 * @version 1.0.0 - Utilities testing React
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Router } from 'wouter';

// Mock user per testing
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  isAdmin: false,
  role: 'user' as const
};

export const mockAdminUser = {
  id: 2,
  username: 'admin',
  email: '<EMAIL>',
  isAdmin: true,
  role: 'admin' as const
};

// Crea QueryClient per testing con configurazione ottimizzata
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // Disabilita retry nei test
      gcTime: 0, // Disabilita cache nei test (gcTime sostituisce cacheTime)
      staleTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

// Props per wrapper personalizzato
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  initialRoute?: string;
  user?: typeof mockUser | null;
}

// Wrapper personalizzato per componenti React
const AllTheProviders: React.FC<{
  children: React.ReactNode;
  queryClient: QueryClient;
  initialRoute: string;
}> = ({ children, queryClient, initialRoute }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router base={initialRoute}>
        {children}
      </Router>
    </QueryClientProvider>
  );
};

/**
 * Render personalizzato con tutti i providers necessari
 * @param ui - Componente da testare
 * @param options - Opzioni di render personalizzate
 */
export const customRender = (
  ui: ReactElement,
  {
    queryClient = createTestQueryClient(),
    initialRoute = '/',
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllTheProviders queryClient={queryClient} initialRoute={initialRoute}>
      {children}
    </AllTheProviders>
  );

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  };
};

// Utility per mock API responses
export const mockAPIResponse = (data: any, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  });
};

// Mock per fetch con response personalizzata
export const mockFetch = (response: any, status = 200) => {
  (global.fetch as jest.Mock).mockResolvedValueOnce(mockAPIResponse(response, status));
};

// Mock per errori API
export const mockFetchError = (message = 'Network error', status = 500) => {
  (global.fetch as jest.Mock).mockRejectedValueOnce(new Error(message));
};

// Helper per attendere che le query si risolvano
export const waitForQueries = async (queryClient: QueryClient) => {
  await queryClient.getQueryCache().clear();
};

// Mock data per testing
export const mockDDT = {
  id: 1,
  supplier_id: 1,
  ddt_number: 'DDT-001',
  ddt_date: '2025-01-01',
  total_amount: 100.50,
  qr_code: 'data:image/png;base64,mock-qr-code',
  created_at: new Date().toISOString(),
  tenant_id: 1
};

export const mockProduct = {
  id: 1,
  ddt_id: 1,
  product_name: 'Test Product',
  quantity: 10,
  unit_price: 5.00,
  expiry_date: '2025-12-31',
  lot_number: 'LOT-001',
  conservation_instructions: 'Store in cool place',
  qr_code: 'data:image/png;base64,mock-product-qr',
  created_at: new Date().toISOString(),
  tenant_id: 1
};

export const mockContainer = {
  id: 1,
  name: 'Test Container',
  type: 'Refrigerator' as const,
  location: 'Kitchen',
  max_capacity: 100,
  current_capacity: 50,
  temperature_min: 2,
  temperature_max: 8,
  qr_code: 'data:image/png;base64,mock-container-qr',
  created_at: new Date().toISOString(),
  tenant_id: 1
};

// Re-export tutto da testing-library
export * from '@testing-library/react';
export * from '@testing-library/user-event';

// Override del render di default
export { customRender as render };