{"name": "haccp-admin-dashboard", "version": "1.0.0", "description": "Dashboard amministrativa per la gestione multi-tenant di HACCP Tracker", "type": "module", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "tsc && vite build", "start": "tsx server/index.ts", "preview": "vite preview"}, "dependencies": {"@neondatabase/serverless": "^0.10.3", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@tanstack/react-query": "^5.59.8", "@tanstack/react-query-devtools": "^5.59.8", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "drizzle-orm": "^0.36.1", "drizzle-zod": "^0.5.1", "express": "^4.21.1", "express-session": "^1.18.1", "framer-motion": "^11.11.17", "lucide-react": "^0.451.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "recharts": "^2.13.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@tailwindcss/typography": "^0.5.15", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.4.15"}}