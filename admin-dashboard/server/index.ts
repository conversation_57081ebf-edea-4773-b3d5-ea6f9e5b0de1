import express from 'express'
import session from 'express-session'
import { createServer } from 'http'
import { join, resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { db } from '../../server/db.js'
import { systemAdmins, tenants, users } from '../../shared/schema.js'
import { eq, count, desc, like, or } from 'drizzle-orm'
import bcrypt from 'bcryptjs'

// Extend session data to include adminId
declare module 'express-session' {
  interface SessionData {
    adminId?: string;
  }
}

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.ADMIN_PORT || 3001

// Middleware
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// Session configuration
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'admin-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production', // HTTPS obbligatorio in produzione
      sameSite: 'strict', // Protezione CSRF più rigida
      maxAge: 2 * 60 * 60 * 1000, // 2 ore invece di 24 ore per sicurezza
      httpOnly: true
    },
  })
)

// Authentication middleware
const requireAuth = (req: any, res: any, next: any) => {
  if (!req.session.adminId) {
    return res.status(401).json({ message: 'Unauthorized' })
  }
  next()
}

// Admin authentication routes
app.post('/api/admin/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json({ message: 'Username and password required' })
    }

    // Find admin user
    const admin = await db
      .select()
      .from(systemAdmins)
      .where(eq(systemAdmins.username, username))
      .limit(1)

    if (!admin.length) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }

    const adminUser = admin[0]

    // Check if admin is active
    if (!adminUser.isActive) {
      return res.status(401).json({ message: 'Account disabled' })
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, adminUser.password)
    if (!passwordValid) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }

    // Store admin session
    req.session.adminId = adminUser.id

    // Return admin data (without password)
    const { password: _, ...adminData } = adminUser
    res.json(adminData)
  } catch (error) {
    console.error('Admin login error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.post('/api/admin/auth/logout', (req, res) => {
  req.session.destroy(() => {
    res.json({ message: 'Logged out' })
  })
})

app.get('/api/admin/auth/me', async (req, res) => {
  try {
    if (!req.session.adminId) {
      return res.status(401).json({ message: 'Unauthorized' })
    }

    const admin = await db
      .select()
      .from(systemAdmins)
      .where(eq(systemAdmins.id, req.session.adminId))
      .limit(1)

    if (!admin.length) {
      return res.status(401).json({ message: 'Admin not found' })
    }

    const { password: _, ...adminData } = admin[0]
    res.json(adminData)
  } catch (error) {
    console.error('Admin auth check error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Dashboard stats
app.get('/api/admin/dashboard/stats', requireAuth, async (req, res) => {
  try {
    const [totalTenants] = await db
      .select({ count: count() })
      .from(tenants)

    const [activeTenants] = await db
      .select({ count: count() })
      .from(tenants)
      .where(eq(tenants.status, 'active'))

    const [totalUsers] = await db
      .select({ count: count() })
      .from(users)

    const [activeUsers] = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.isActive, true))

    res.json({
      totalTenants: totalTenants.count,
      activeTenants: activeTenants.count,
      totalUsers: totalUsers.count,
      activeUsers: activeUsers.count,
    })
  } catch (error) {
    console.error('Dashboard stats error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Tenant management routes
app.get('/api/admin/tenants', requireAuth, async (req, res) => {
  try {
    const { search, status, limit } = req.query

    let query = db.select().from(tenants)

    // Apply filters
    if (search) {
      query = query.where(
        or(
          like(tenants.name, `%${search}%`),
          like(tenants.code, `%${search}%`),
          like(tenants.email, `%${search}%`)
        )
      ) as any
    }

    if (status) {
      query = query.where(eq(tenants.status, status as string)) as any
    }

    // Apply limit
    if (limit) {
      query = query.limit(parseInt(limit as string)) as any
    }

    const tenantList = await query.orderBy(desc(tenants.createdAt))

    // Get user count for each tenant
    const enrichedTenants = await Promise.all(
      tenantList.map(async (tenant) => {
        const [userCount] = await db
          .select({ count: count() })
          .from(users)
          .where(eq(users.tenantId, tenant.id))

        return {
          ...tenant,
          userCount: userCount.count,
          maxUsers: tenant.maxUsers || 50, // Default max users
        }
      })
    )

    res.json(enrichedTenants)
  } catch (error) {
    console.error('Get tenants error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.post('/api/admin/tenants', requireAuth, async (req, res) => {
  try {
    const { name, code, type, email, phone, vatNumber, maxUsers } = req.body

    if (!name || !code) {
      return res.status(400).json({ message: 'Name and code are required' })
    }

    // Check if code already exists
    const existingTenant = await db
      .select()
      .from(tenants)
      .where(eq(tenants.code, code))
      .limit(1)

    if (existingTenant.length) {
      return res.status(400).json({ message: 'Tenant code already exists' })
    }

    const newTenant = await db
      .insert(tenants)
      .values({
        name,
        code,
        type: type || 'restaurant',
        status: 'active',
        email,
        phone,
        vatNumber,
        maxUsers: maxUsers || 50,
      })
      .returning()

    res.status(201).json(newTenant[0])
  } catch (error) {
    console.error('Create tenant error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.delete('/api/admin/tenants/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params

    // Check if tenant exists
    const tenant = await db
      .select()
      .from(tenants)
      .where(eq(tenants.id, id))
      .limit(1)

    if (!tenant.length) {
      return res.status(404).json({ message: 'Tenant not found' })
    }

    // Don't allow deletion of default tenant
    if (tenant[0].code === 'default') {
      return res.status(400).json({ message: 'Cannot delete default tenant' })
    }

    // Delete tenant (this will cascade to related data)
    await db.delete(tenants).where(eq(tenants.id, id))

    res.json({ message: 'Tenant deleted successfully' })
  } catch (error) {
    console.error('Delete tenant error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  const distPath = resolve(__dirname, '../dist')
  app.use(express.static(distPath))
  
  app.get('*', (req, res) => {
    res.sendFile(join(distPath, 'index.html'))
  })
} else {
  // In development, proxy to Vite dev server
  app.get('*', (req, res) => {
    res.redirect(`http://localhost:5173${req.path}`)
  })
}

const server = createServer(app)

server.listen(PORT, () => {
  console.log(`🚀 Admin Dashboard server running on port ${PORT}`)
  console.log(`📊 Admin Dashboard URL: http://localhost:${PORT}`)
})

export default app