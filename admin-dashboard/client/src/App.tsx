import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Router, Route, Switch } from 'wouter'
import { Toaster } from '@/components/ui/toaster'
import Dashboard from '@/pages/Dashboard'
import TenantManagement from '@/pages/TenantManagement'
import SystemSettings from '@/pages/SystemSettings'
import Login from '@/pages/Login'
import Layout from '@/components/Layout'
import { AuthProvider } from '@/contexts/AuthContext'

// Crea il query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minuti
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Switch>
            <Route path="/login" component={Login} />
            <Route>
              <Layout>
                <Switch>
                  <Route path="/" component={Dashboard} />
                  <Route path="/tenants" component={TenantManagement} />
                  <Route path="/settings" component={SystemSettings} />
                  <Route>
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">
                          Pagina non trovata
                        </h2>
                        <p className="text-gray-600">
                          La pagina richiesta non esiste.
                        </p>
                      </div>
                    </div>
                  </Route>
                </Switch>
              </Layout>
            </Route>
          </Switch>
        </Router>
        <Toaster />
        <ReactQueryDevtools initialIsOpen={false} />
      </AuthProvider>
    </QueryClientProvider>
  )
}

export default App