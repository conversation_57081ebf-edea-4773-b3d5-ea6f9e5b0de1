import React from 'react'

export interface ToastProps {
  title?: string
  description?: string
  variant?: 'default' | 'destructive'
}

export function Toast({ title, description, variant = 'default' }: ToastProps) {
  return (
    <div className={`rounded-md p-4 ${
      variant === 'destructive' 
        ? 'bg-red-50 border border-red-200 text-red-800' 
        : 'bg-white border border-gray-200 text-gray-900'
    }`}>
      {title && <div className="font-medium">{title}</div>}
      {description && <div className="text-sm mt-1">{description}</div>}
    </div>
  )
}