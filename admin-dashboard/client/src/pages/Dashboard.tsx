import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  Building2, 
  Users, 
  Activity, 
  TrendingUp,
  AlertCircle,
  CheckCircle2
} from 'lucide-react'

// Tipi per i dati del dashboard
interface DashboardStats {
  totalTenants: number
  activeTenants: number
  totalUsers: number
  activeUsers: number
}

interface TenantSummary {
  id: string
  name: string
  code: string
  status: string
  userCount: number
  lastActivity: string
}

export default function Dashboard() {
  // Query per le statistiche generali
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/dashboard/stats', {
        credentials: 'include',
      })
      if (!response.ok) throw new Error('Failed to fetch stats')
      return response.json() as Promise<DashboardStats>
    },
  })

  // Query per i tenant recenti
  const { data: recentTenants, isLoading: tenantsLoading } = useQuery({
    queryKey: ['recent-tenants'],
    queryFn: async () => {
      const response = await fetch('/api/admin/tenants?limit=5', {
        credentials: 'include',
      })
      if (!response.ok) throw new Error('Failed to fetch tenants')
      return response.json() as Promise<TenantSummary[]>
    },
  })

  if (statsLoading || tenantsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Tenant Totali',
      value: stats?.totalTenants || 0,
      icon: Building2,
      description: `${stats?.activeTenants || 0} attivi`,
      color: 'bg-blue-500',
    },
    {
      title: 'Utenti Totali',
      value: stats?.totalUsers || 0,
      icon: Users,
      description: `${stats?.activeUsers || 0} attivi`,
      color: 'bg-green-500',
    },
    {
      title: 'Attività Sistema',
      value: '98.5%',
      icon: Activity,
      description: 'Uptime ultimo mese',
      color: 'bg-purple-500',
    },
    {
      title: 'Crescita',
      value: '+12%',
      icon: TrendingUp,
      description: 'Nuovi tenant questo mese',
      color: 'bg-orange-500',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Panoramica del sistema multi-tenant HACCP Tracker
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className={`${card.color} p-3 rounded-lg`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">{card.description}</p>
            </div>
          )
        })}
      </div>

      {/* Recent Tenants */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Tenant Recenti</h2>
          <p className="text-sm text-gray-600 mt-1">
            Ultimi tenant aggiunti al sistema
          </p>
        </div>
        <div className="divide-y divide-gray-200">
          {recentTenants?.length ? (
            recentTenants.map((tenant) => (
              <div key={tenant.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">
                      {tenant.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      Codice: {tenant.code} • {tenant.userCount} utenti
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  {tenant.status === 'active' ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                  )}
                  <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    tenant.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {tenant.status === 'active' ? 'Attivo' : 'Inattivo'}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="p-6 text-center text-gray-500">
              Nessun tenant trovato
            </div>
          )}
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Stato del Sistema
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Database</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">API Gateway</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Servizi AI</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Azioni Rapide
          </h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <Building2 className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  Crea Nuovo Tenant
                </span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  Gestisci Utenti
                </span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <Activity className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  Visualizza Log Sistema
                </span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}