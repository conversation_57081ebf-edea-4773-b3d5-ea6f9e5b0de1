{"compilerOptions": {"target": "ES2020", "useDefaults": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./client/src/*"], "@shared/*": ["../shared/*"], "@assets/*": ["./client/public/assets/*"]}}, "include": ["client/src", "server", "../shared"], "exclude": ["node_modules"]}