/**
 * Configurazione Jest per HACCP Tracker (ES Modules)
 * Testing completo: unit, integration, database
 * 
 * @description Configurazione ottimizzata per testing TypeScript, React e Express con ES modules
 * <AUTHOR> di Testing Automatizzato
 * @version 1.0.0 - Implementazione iniziale testing automatizzato
 */

export default {
  // Ambiente di test predefinito
  testEnvironment: 'jsdom',
  
  // Pattern per trovare i file di test
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)'
  ],
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  
  // Trasformazioni per TypeScript
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  
  // Configurazione ts-jest per ES modules
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx'
      }
    }
  },
  
  // Mapping dei moduli per supportare path aliases
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@components/(.*)$': '<rootDir>/client/src/components/$1',
    '^@lib/(.*)$': '<rootDir>/client/src/lib/$1',
    '^@hooks/(.*)$': '<rootDir>/client/src/hooks/$1',
    '^@shared/(.*)$': '<rootDir>/shared/$1',
    '^@server/(.*)$': '<rootDir>/server/$1',
    
    // Mock per file statici
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/tests/__mocks__/fileMock.js'
  },
  
  // Directory per moduli
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Estensioni file supportate
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/backups/'
  ],
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],
  
  // Coverage configuration
  collectCoverage: false, // Disabilitato per ora
  collectCoverageFrom: [
    'client/src/**/*.{ts,tsx}',
    'server/**/*.{ts,js}',
    'shared/**/*.{ts}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/__tests__/**',
    '!**/*.test.*',
    '!**/*.spec.*'
  ],
  
  // Timeout
  testTimeout: 30000,
  
  // Verbose
  verbose: true,
  
  // Mock configuration
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true
};