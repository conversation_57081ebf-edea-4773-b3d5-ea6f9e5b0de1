# CRITICAL SECURITY: Production Environment Variables
# These MUST be set for production deployment

# DATABASE CONFIGURATION
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# SESSION SECURITY (REQUIRED IN PRODUCTION)
# Must be at least 32 characters long, cryptographically secure
SESSION_SECRET=your-ultra-secure-session-secret-min-32-chars

# USER CREDENTIALS (REQUIRED IN PRODUCTION)
# Must be at least 12 characters with mixed case, numbers, and symbols
ADMIN_DEFAULT_PASSWORD=YourSecureAdminPassword123!
USER_DEFAULT_PASSWORD=YourSecureUserPassword123!

# API KEYS (Optional - Set if using AI features)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_AI_API_KEY=your-google-ai-api-key-here

# ADDITIONAL SECURITY (Optional)
ADMIN_API_KEY=your-admin-api-key-here
SYSTEM_API_KEY=your-system-api-key-here
AI_SERVICE_API_KEY=your-ai-service-api-key-here

# COOKIE SECURITY (Production Only)
COOKIE_DOMAIN=your-domain.com

# RATE LIMITING
MAX_REQUEST_SIZE=10mb
SESSION_TIMEOUT=7200

# DEVELOPMENT ONLY (Never set to true in production)
ALLOW_MOCK_TOKENS=false
ENABLE_STRICT_VALIDATION=true
REQUIRE_API_KEYS=true