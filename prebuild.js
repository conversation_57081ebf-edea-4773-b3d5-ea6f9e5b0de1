#!/usr/bin/env node

/**
 * Pre-build script che si esegue automaticamente prima del build
 * Incrementa la versione solo durante i build di produzione
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';

// Controlla se siamo in un build di produzione
function isProductionBuild() {
  return process.env.NODE_ENV === 'production' ||
         process.env.REPLIT_DEPLOYMENT === 'true' ||
         !process.env.REPL_ID; // Deployment Replit non ha REPL_ID
}

// Legge la versione corrente
function getCurrentVersion() {
  try {
    const content = readFileSync('./client/src/lib/version.ts', 'utf8');
    const match = content.match(/const APP_VERSION = "([^"]+)";/);
    return match ? match[1] : '1.2.16';
  } catch (error) {
    return '1.2.16';
  }
}

// Incrementa la versione patch
function incrementVersion(version) {
  const parts = version.split('.').map(Number);
  parts[2] = (parts[2] || 0) + 1;
  return parts.join('.');
}

// Aggiorna version.ts
function updateVersionFile(version) {
  let content = readFileSync('./client/src/lib/version.ts', 'utf8');
  
  content = content.replace(
    /const APP_VERSION = "[^"]+";/,
    `const APP_VERSION = "${version}";`
  );
  
  const buildDate = new Date().toISOString().split('T')[0];
  content = content.replace(
    /\/\/ Build: .*/,
    `// Build: ${buildDate}`
  );
  
  writeFileSync('./client/src/lib/version.ts', content);
  return true;
}

// Aggiorna .env per il build
function updateEnvFile(version) {
  const envPath = './.env';
  let envContent = '';
  
  try {
    envContent = readFileSync(envPath, 'utf8');
  } catch {
    // File non esiste
  }
  
  if (envContent.includes('VITE_APP_VERSION=')) {
    envContent = envContent.replace(
      /VITE_APP_VERSION=.*/,
      `VITE_APP_VERSION=${version}`
    );
  } else {
    envContent += `\nVITE_APP_VERSION=${version}\n`;
  }
  
  writeFileSync(envPath, envContent);
}

// Esecuzione principale
function main() {
  console.log('Prebuild: Checking for version increment...');
  
  if (isProductionBuild()) {
    const currentVersion = getCurrentVersion();
    const newVersion = incrementVersion(currentVersion);
    
    updateVersionFile(newVersion);
    updateEnvFile(newVersion);
    
    console.log(`Production build: ${currentVersion} → ${newVersion}`);
  } else {
    console.log('Development build: version unchanged');
  }
}

main();