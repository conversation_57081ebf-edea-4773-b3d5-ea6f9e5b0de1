#!/bin/bash

# Script per creare un backup completo del progetto HACCP Tracker
# Include codice sorgente, configurazioni e backup del database

# Colori per i messaggi
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi di errore ed uscire
error_exit() {
    echo -e "${RED}[ERRORE]${NC} $1" >&2
    exit 1
}

# Funzione per stampare messaggi informativi
info_message() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Funzione per stampare messaggi di successo
success_message() {
    echo -e "${GREEN}[SUCCESSO]${NC} $1"
}

# Funzione per stampare messaggi di avviso
warning_message() {
    echo -e "${YELLOW}[AVVISO]${NC} $1"
}

# Crea directory per i backup se non esiste
mkdir -p backups

# Nome del file di backup con timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PROJECT_BACKUP_DIR="backups/haccp_project_backup_${TIMESTAMP}"
PROJECT_BACKUP_FILE="backups/haccp_project_backup_${TIMESTAMP}.tar.gz"

info_message "Avvio backup completo del progetto HACCP Tracker..."
info_message "Directory temporanea: $PROJECT_BACKUP_DIR"
info_message "File finale: $PROJECT_BACKUP_FILE"

# Crea directory temporanea per il backup
mkdir -p "$PROJECT_BACKUP_DIR"

# Prima esegui il backup del database se non esiste già uno recente
info_message "Verifica backup database..."
if [ ! -f "db_backups/haccp_backup_complete_${TIMESTAMP}.sql.gz" ]; then
    warning_message "Esecuzione backup database..."
    ./backup_db.sh
fi

# Copia i file del progetto escludendo le directory non necessarie
info_message "Copia files del progetto..."

# Copia tutti i file mantenendo la struttura
cp -r . "$PROJECT_BACKUP_DIR/" || error_exit "Errore durante la copia dei file del progetto."

# Rimuovi le directory non necessarie dal backup
info_message "Rimozione file non necessari dal backup..."
rm -rf "$PROJECT_BACKUP_DIR/.git" 2>/dev/null
rm -rf "$PROJECT_BACKUP_DIR/node_modules" 2>/dev/null
rm -rf "$PROJECT_BACKUP_DIR/dist" 2>/dev/null
rm -rf "$PROJECT_BACKUP_DIR/build" 2>/dev/null
rm -rf "$PROJECT_BACKUP_DIR/.replit" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR/replit.nix" 2>/dev/null
rm -rf "$PROJECT_BACKUP_DIR/backups" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR/.env" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR/.env.local" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR"/*.log 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR/.DS_Store" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR/Thumbs.db" 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR"/*.tmp 2>/dev/null
rm -f "$PROJECT_BACKUP_DIR"/*.temp 2>/dev/null

# Crea un file di informazioni sul backup
info_message "Creazione file di informazioni sul backup..."
cat > "$PROJECT_BACKUP_DIR/BACKUP_INFO.txt" << EOF
HACCP Tracker - Backup Completo
================================

Data backup: $(date)
Versione progetto: $(cat version.txt 2>/dev/null || echo "Non disponibile")
Directory originale: $(pwd)

Contenuto del backup:
- Codice sorgente completo del progetto
- File di configurazione
- Schema del database (in shared/schema.ts)
- Script di backup e ripristino
- Documentazione (replit.md)
- Backup del database nella directory db_backups/

File esclusi dal backup:
- node_modules (dipendenze npm)
- .git (repository git)
- dist/build (file compilati)
- .env (variabili d'ambiente sensibili)
- file temporanei e log

Per ripristinare il progetto:
1. Estrai i file: tar -xzf haccp_project_backup_${TIMESTAMP}.tar.gz
2. Installa le dipendenze: npm install
3. Configura le variabili d'ambiente (.env)
4. Ripristina il database usando il file SQL nella directory db_backups/
5. Avvia l'applicazione: npm run dev

Note importanti:
- Le chiavi API e le variabili d'ambiente devono essere riconfigurate
- Il database deve essere ripristinato separatamente
- Verifica che tutte le dipendenze siano installate correttamente

EOF

# Comprimi il backup
info_message "Compressione del backup progetto..."
cd backups
tar -czf "haccp_project_backup_${TIMESTAMP}.tar.gz" "haccp_project_backup_${TIMESTAMP}" || error_exit "Errore durante la compressione del backup."
cd ..

# Rimuovi la directory temporanea
rm -rf "$PROJECT_BACKUP_DIR"

# Verifica che il file di backup sia stato creato
if [ ! -f "$PROJECT_BACKUP_FILE" ]; then
    error_exit "Il file di backup del progetto non è stato creato correttamente."
fi

# Ottieni la dimensione del file
FILE_SIZE=$(ls -lh "$PROJECT_BACKUP_FILE" | awk '{print $5}')

success_message "Backup completo del progetto completato con successo!"
echo
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${GREEN}  RIEPILOGO BACKUP COMPLETO PROGETTO${NC}"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BLUE}  File backup progetto:${NC} $PROJECT_BACKUP_FILE"
echo -e "${BLUE}  Dimensione file:${NC} $FILE_SIZE"
echo -e "${BLUE}  Data creazione:${NC} $(date)"
echo -e "${BLUE}  Progetto:${NC} HACCP Tracker - Restaurant Inventory Management"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo

# Mostra l'elenco completo dei backup
info_message "Backup esistenti:"
echo -e "${BLUE}Database:${NC}"
if ls db_backups/haccp_backup_*.sql* 1> /dev/null 2>&1; then
    ls -lht db_backups/haccp_backup_*.sql* | head -5
else
    warning_message "Nessun backup database trovato."
fi

echo
echo -e "${BLUE}Progetto completo:${NC}"
if ls backups/haccp_project_backup_*.tar.gz 1> /dev/null 2>&1; then
    ls -lht backups/haccp_project_backup_*.tar.gz | head -5
else
    warning_message "Nessun backup progetto precedente trovato."
fi

echo
success_message "Tutti i backup sono pronti per essere scaricati o archiviati."
echo -e "${YELLOW}IMPORTANTE:${NC} Conserva questi backup in un luogo sicuro e testane periodicamente il ripristino."
echo -e "${YELLOW}NOTA:${NC} I backup non includono le variabili d'ambiente sensibili (API keys, password)."