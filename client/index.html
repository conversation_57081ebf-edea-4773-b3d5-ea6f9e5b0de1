<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>HACCP Tracker</title>
    <meta name="description" content="Restaurant goods traceability management compliant with HACCP regulations" />
    
    <!-- Precaricamento risorse critiche per migliorare la performance -->
    <!-- Abbiamo rimosso il preload dei file compilati da Vite perché gestiti internamente -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- PWA setup -->
    <link rel="icon" href="favicon.ico" sizes="any" />
    <link rel="apple-touch-icon" href="apple-touch-icon.png" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#0f766e" />
    
    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="HACCP Tracker" />
    
    <!-- iOS splash screen images -->
    <link rel="apple-touch-startup-image" href="icons/icon-512x512.png" />
    
    <!-- Font ottimizzato con display=swap per rendering progressivo -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'" />
    <!-- Fallback per browser senza support per onload -->
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    </noscript>
  </head>
  <body>
    <div id="root"></div>
    <!-- App contenuto -->
    <script type="module" src="/src/main.tsx"></script>
    <!-- Banner Replit solo in development -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" defer></script>
  </body>
</html>
