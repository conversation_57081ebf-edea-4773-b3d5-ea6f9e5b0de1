# Service Worker - Note Implementative

Questo documento fornisce una panoramica delle strategie implementate nel service worker dell'applicazione HACCP Tracker.

## Strategie di cache

Il service worker implementa diverse strategie di caching in base al tipo di risorsa:

### 1. Cache-first per risorse statiche
- Utilizza: Tutti gli asset come JS, CSS, immagini, font e HTML
- Strategia: Verifica prima in cache, se non trovati passa alla rete

### 2. Network-first per le API
- Utilizza: Tutte le chiamate API (/api/*)
- Strategia: Cerca prima sulla rete, fallback alla cache in caso di errore o offline

### 3. Stale-while-revalidate per altri contenuti
- Utilizza: Altri contenuti che non rientrano nelle categorie precedenti
- Strategia: Serve dalla cache mentre aggiorna in background

## Gestione Offline

La modalità offline è gestita con diverse strategie:
- Risposte di fallback per richieste API offline
- Utilizzo di IndexedDB per la cache dei dati
- Implementazione del Background Sync quando la connessione viene ripristinata

## Note implementative

Per garantire un'esperienza ottimale, il service worker:
1. Utilizza un sistema di versionamento per la cache
2. Implementa pulizia automatica delle cache obsolete
3. Gestisce gli aggiornamenti in modo non invasivo
4. Supporta precaching degli asset critici

## Metodi di debug

Durante lo sviluppo, utilizzare la console del browser per visualizzare:
- `console.log("[ServiceWorker]")` - Log generali
- Eventi registrati come 'install', 'activate', 'fetch'
- Stato della cache delle risorse

## Attivazione/Disattivazione

Il service worker viene automaticamente disabilitato in ambiente di sviluppo per evitare problemi con hot-reload e debugging.