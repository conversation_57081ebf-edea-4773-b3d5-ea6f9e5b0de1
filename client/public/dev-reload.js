/**
 * Script per il ricaricamento manuale delle pagine in caso di problemi con l'hot reload di Vite.
 * Questo script viene caricato direttamente dall'HTML e non richiede la compilazione di Vite.
 * 
 * Il problema principale in Replit è l'errore WebSocket:
 * "Failed to construct 'WebSocket': The URL 'wss://localhost:undefined/?token=...' is invalid"
 * Questo script fornisce un modo per aggirare questo problema.
 */
(function() {
  // Verifica se siamo in ambiente di sviluppo (Replit)
  const isReplit = window.location.host.includes('replit');
  console.log('Dev-reload: Avviato in ambiente', isReplit ? 'Replit' : 'esterno');
  
  // Crea il pulsante di ricarica
  function createReloadButton() {
    const button = document.createElement('button');
    
    // Crea l'icona SVG usando DOM methods sicuri
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '20');
    svg.setAttribute('height', '20');
    svg.setAttribute('viewBox', '0 0 24 24');
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke', 'currentColor');
    svg.setAttribute('stroke-width', '2');
    svg.setAttribute('stroke-linecap', 'round');
    svg.setAttribute('stroke-linejoin', 'round');
    svg.style.marginRight = '8px';
    
    const path1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path1.setAttribute('d', 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8');
    const path2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path2.setAttribute('d', 'M21 3v5h-5');
    const path3 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path3.setAttribute('d', 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16');
    const path4 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path4.setAttribute('d', 'M3 21v-5h5');
    
    svg.appendChild(path1);
    svg.appendChild(path2);
    svg.appendChild(path3);
    svg.appendChild(path4);
    
    const text = document.createTextNode('RICARICA');
    
    button.appendChild(svg);
    button.appendChild(text);
    
    button.style.position = 'fixed';
    button.style.bottom = '20px';
    button.style.right = '20px';
    button.style.zIndex = '9999';
    button.style.backgroundColor = 'rgba(220, 38, 38, 0.9)'; // Rosso semi-trasparente
    button.style.color = 'white';
    button.style.padding = '12px 16px';
    button.style.border = 'none';
    button.style.borderRadius = '8px';
    button.style.cursor = 'pointer';
    button.style.fontSize = '16px';
    button.style.fontWeight = 'bold';
    button.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    
    // Aggiungi l'evento di click
    button.addEventListener('click', function() {
      console.log('Ricaricamento pagina via script dev-reload.js');
      window.location.reload();
    });
    
    return button;
  }
  
  // Aggiungi il pulsante al body quando il DOM è pronto
  function addReloadButton() {
    const body = document.body;
    if (body) {
      body.appendChild(createReloadButton());
      console.log('Dev-reload: Pulsante di ricarica aggiunto');
      
      // Controlla anche se c'è un file worker.js che potrebbe interferire
      const existingWorker = navigator.serviceWorker.controller;
      if (existingWorker) {
        console.log('Dev-reload: Rilevato Service Worker attivo, potrebbe essere necessario disattivarlo in sviluppo');
      }
      
      // Aggiungi un piccolo banner informativo per aiutare il debugging
      const infoDiv = document.createElement('div');
      infoDiv.style.position = 'fixed';
      infoDiv.style.top = '0';
      infoDiv.style.left = '0';
      infoDiv.style.right = '0';
      infoDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      infoDiv.style.color = 'white';
      infoDiv.style.padding = '4px 10px';
      infoDiv.style.fontSize = '12px';
      infoDiv.style.textAlign = 'center';
      infoDiv.style.zIndex = '9999';
      infoDiv.style.fontFamily = 'monospace';
      infoDiv.textContent = `Dev-reload attivo | ${new Date().toLocaleTimeString()} | HACCP Tracker`;
      body.appendChild(infoDiv);
      
      // Nascondi il div dopo 5 secondi
      setTimeout(() => {
        infoDiv.style.opacity = '0';
        infoDiv.style.transition = 'opacity 0.5s';
        setTimeout(() => infoDiv.remove(), 500);
      }, 5000);
    } else {
      // Se il body non è ancora pronto, riprova più tardi
      setTimeout(addReloadButton, 100);
    }
  }
  
  // Avvia il processo quando il documento è pronto
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addReloadButton);
  } else {
    addReloadButton();
  }
  
  // Aggiunge anche un listener per tastiera che consente di riavviare con CTRL+R
  document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
      console.log('Dev-reload: Ricaricamento tramite scorciatoia CTRL+R');
      // Non blocchiamo l'evento predefinito per permettere al browser di ricaricare normalmente
    }
  });
})();