<!DOCTYPE html>
<html>
<head>
  <title>QR Code</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      font-family: Arial, sans-serif;
    }
    .container {
      width: 50mm;
      height: auto;
      text-align: center;
    }
    .qr-image {
      width: 40mm;
      height: 40mm;
      margin: 0 auto 5mm auto;
      display: block;
    }
    .title {
      font-size: 3mm;
      font-weight: bold;
      text-transform: uppercase;
      margin: 0 0 1mm 0;
      line-height: 4mm;
    }
  </style>
</head>
<body>
  <div class="container">
    <img id="qrCodeImage" class="qr-image" src="">
    <p id="title" class="title"></p>
    <p id="subtitle" class="title"></p>
  </div>

  <script>
    function getQueryParam(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }
    
    window.onload = function() {
      // Ottieni i parametri dall'URL
      const qrValue = getQueryParam('value');
      const title = getQueryParam('title');
      const subtitle = getQueryParam('subtitle');
      
      // Crea un QR code utilizzando una API esterna semplice
      const qrImageSrc = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrValue)}`;
      
      // Imposta i valori negli elementi HTML
      document.getElementById('qrCodeImage').src = qrImageSrc;
      
      if (title) {
        document.getElementById('title').textContent = decodeURIComponent(title);
      }
      
      if (subtitle) {
        document.getElementById('subtitle').textContent = decodeURIComponent(subtitle);
      }
    };
  </script>
</body>
</html>