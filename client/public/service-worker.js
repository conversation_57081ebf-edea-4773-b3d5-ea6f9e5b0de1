// HACCP Tracker Service Worker - Ottimizzato per prestazioni
// Versione del service worker - incrementato per risolvere i problemi in produzione
const VERSION = 'v1.0.6';

// Verifica se siamo in ambiente di sviluppo (non dovrebbe eseguire questo codice in sviluppo)
const isDevMode = self.location.hostname === 'localhost' || 
                 self.location.hostname === '127.0.0.1' ||
                 self.location.hostname.includes('.replit.dev');

// In ambiente di sviluppo, disabilita completamente il service worker
if (isDevMode) {
  self.addEventListener('install', (event) => {
    self.skipWaiting();
  });
  
  self.addEventListener('activate', (event) => {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }).then(() => self.clients.claim())
    );
  });
  
  // Non intercettare alcuna richiesta in modalità sviluppo
  self.addEventListener('fetch', (event) => {
    // Non facciamo nulla, lasciamo che le richieste procedano normalmente
    return;
  });
  
  // Fine dell'inizializzazione per la modalità sviluppo
  console.log('[ServiceWorker] Inizializzato in modalità sviluppo - disabilitato');
}

// Performance optimization flags - abilita funzionalità specifiche
const ENABLE_NAVIGATION_PRELOAD = 'navigationPreload' in self.registration;
const ENABLE_AGGRESSIVE_CACHING = false; // Disabilitato per evitare problemi con le API
const ENABLE_STALE_WHILE_REVALIDATE = true;

// Funzione di log per tracciare le attività del service worker
const swLog = (message) => {
  console.log(`[ServiceWorker ${VERSION}] ${message}`);
};

// Configurazione della strategia di sincronizzazione in background
const SYNC_QUEUE_NAME = 'haccp-tracker-sync-queue';
const SYNC_DB_NAME = 'haccp-offline-storage';
const SYNC_DB_VERSION = 1;
const SYNC_STORE_NAME = 'pending-operations';

// Inizializza il database IndexedDB per operazioni offline
const initSyncDB = () => {
  return new Promise((resolve, reject) => {
    if (!('indexedDB' in self)) {
      reject('IndexedDB non supportato in questo browser');
      return;
    }
    
    const request = indexedDB.open(SYNC_DB_NAME, SYNC_DB_VERSION);
    
    request.onerror = (event) => {
      swLog(`Errore nell'apertura del database: ${event.target.error}`);
      reject(event.target.error);
    };
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      // Crea object store per operazioni in attesa
      if (!db.objectStoreNames.contains(SYNC_STORE_NAME)) {
        db.createObjectStore(SYNC_STORE_NAME, { keyPath: 'id', autoIncrement: true });
        swLog('Object store per operazioni offline creato');
      }
    };
    
    request.onsuccess = (event) => {
      swLog('Database offline inizializzato con successo');
      resolve(event.target.result);
    };
  });
};

// Registra la sincronizzazione in background se supportata
const registerSync = () => {
  if ('sync' in self.registration) {
    swLog('Background Sync supportato, registrazione...');
    self.registration.sync.register(SYNC_QUEUE_NAME)
      .then(() => swLog('Background Sync registrato con successo'))
      .catch(err => swLog(`Errore nella registrazione del Background Sync: ${err}`));
  } else {
    swLog('Background Sync non supportato in questo browser');
  }
};

// Registra la sincronizzazione periodica se supportata
const registerPeriodicSync = () => {
  if ('periodicSync' in self.registration) {
    swLog('Periodic Sync supportato, registrazione...');
    // Richiede permesso
    self.registration.periodicSync.register(SYNC_QUEUE_NAME, {
      minInterval: 24 * 60 * 60 * 1000, // Ogni 24 ore
    })
      .then(() => swLog('Periodic Sync registrato con successo'))
      .catch(err => swLog(`Errore nella registrazione del Periodic Sync: ${err}`));
  } else {
    swLog('Periodic Sync non supportato in questo browser');
  }
};

if (isDevMode) {
  swLog('Development mode detected - disabling caching');
  self.addEventListener('install', (event) => {
    swLog('Installing development service worker');
    self.skipWaiting();
  });
  self.addEventListener('activate', (event) => {
    swLog('Activating development service worker');
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            swLog(`Clearing cache in development: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );
      }).then(() => {
        swLog('Taking control of all clients');
        return self.clients.claim();
      })
    );
  });
} else {
  // Production service worker
  // Usiamo il versionamento nel nome della cache per facilitare gli aggiornamenti
  const CACHE_NAME = `haccp-tracker-${VERSION}`;
  const API_CACHE_NAME = `haccp-tracker-api-${VERSION}`;

  // Assets critici da caricare immediatamente (prioritari)
  const CRITICAL_ASSETS = [
    '/',
    '/index.html',
    '/manifest.json',
    '/favicon.ico',
    '/apple-touch-icon.png',
    '/offline.html'
  ];
  
  // Assets secondari da caricare dopo quelli critici
  const SECONDARY_ASSETS = [
    // Icone e risorse per la PWA
    '/icons/icon-72x72.png',
    '/icons/icon-96x96.png', 
    '/icons/icon-128x128.png',
    '/icons/icon-144x144.png',
    '/icons/icon-152x152.png',
    '/icons/icon-192x192.png',
    '/icons/icon-384x384.png',
    '/icons/icon-512x512.png',
    '/icons/icon.png',
    '/icons/file-icon.png',
    '/icons/shortcut-goods.png',
    '/icons/shortcut-containers.png',
    '/icons/shortcut-scan.png'
  ];
  
  // Tutti gli asset da cachare
  const STATIC_ASSETS = [...CRITICAL_ASSETS, ...SECONDARY_ASSETS];
  
  // Lista di cache da preservare al momento dell'attivazione
  // Questo è utile per aggiornamenti graduali
  const CURRENT_CACHES = [
    CACHE_NAME,
    API_CACHE_NAME
  ];
}

// Install event - cache static assets con strategia di priorità
self.addEventListener('install', (event) => {
  if (isDevMode) {
    swLog('Skipping installation in development mode');
    self.skipWaiting();
    return;
  }
  
  swLog('Installing production service worker with prioritized caching');
  
  // Utilizziamo una strategia di caching in due fasi per dare priorità agli asset critici
  event.waitUntil(
    (async () => {
      try {
        // Fase 1: Carichiamo immediatamente gli asset critici
        const cache = await caches.open(CACHE_NAME);
        swLog('Caching critical assets first');
        
        // Cache degli asset critici
        await Promise.all(
          CRITICAL_ASSETS.map(url => 
            cache.add(url).catch(err => {
              swLog(`Failed to cache critical asset ${url}: ${err}`);
              // Non blocchiamo il processo per un singolo asset fallito
              return Promise.resolve();
            })
          )
        );
        
        // Fase 2: Carichiamo gli asset secondari
        swLog('Caching secondary assets');
        await Promise.all(
          SECONDARY_ASSETS.map(url => 
            cache.add(url).catch(err => {
              swLog(`Failed to cache secondary asset ${url}: ${err}`);
              return Promise.resolve();
            })
          )
        );
        
        swLog('All assets cached successfully');
      } catch (error) {
        swLog(`Caching error: ${error}`);
        // Continuiamo comunque per evitare che il service worker si blocchi
      }
    })()
      .then(() => {
        swLog('Installation complete, skipping waiting');
        return self.skipWaiting();
      })
      .catch(error => {
        swLog(`Install error: ${error}`);
        // Forziamo comunque lo skipWaiting per evitare di bloccare il service worker
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  if (isDevMode) {
    swLog('Activating development service worker');
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            swLog(`Clearing cache in development: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );
      }).then(() => {
        swLog('Development service worker now controls all clients');
        return self.clients.claim();
      })
    );
    return;
  }
  
  swLog('Activating production service worker');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        swLog(`Found ${cacheNames.length} caches`);
        return Promise.all(
          cacheNames.filter(cacheName => {
            // Mantieni solo le cache correnti, elimina le altre
            const shouldDelete = !CURRENT_CACHES.includes(cacheName);
            if (shouldDelete) {
              swLog(`Will delete old cache: ${cacheName}`);
            }
            return shouldDelete;
          }).map(cacheName => {
            swLog(`Deleting old cache: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );
      })
      .then(() => {
        // Abilita navigation preload se supportato
        if (ENABLE_NAVIGATION_PRELOAD && self.registration.navigationPreload) {
          swLog('Enabling navigation preload for faster page navigations');
          return self.registration.navigationPreload.enable().then(() => {
            return self.clients.claim();
          }).catch(error => {
            swLog(`Navigation preload error: ${error}`);
            return self.clients.claim();
          });
        } else {
          swLog('Navigation preload not supported in this browser');
          return self.clients.claim();
        }
      })
      .catch(error => {
        swLog(`Activation error: ${error}`);
        // Continuiamo con clients.claim anche in caso di errore
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  // In development mode, just pass through all requests to the network
  if (isDevMode) {
    swLog('Development mode: bypassing fetch event');
    return; // Non interferire con le richieste in modalità sviluppo
  }
  
  // Create a safe response function to handle errors without bloccare il service worker
  const createSafeResponse = (message, status = 503) => {
    try {
      return new Response(JSON.stringify({ 
        error: 'Offline', 
        message: message 
      }), { 
        headers: { 'Content-Type': 'application/json' },
        status: status
      });
    } catch (error) {
      swLog(`Error creating safe response: ${error}`);
      // Fallback ancora più semplice in caso di problemi
      return new Response('{"error":"Offline"}', { 
        headers: { 'Content-Type': 'application/json' },
        status: status
      });
    }
  };
  
  // Funzione di sicurezza per avvolgere i respondWith
  const safeRespondWith = (promise) => {
    try {
      event.respondWith(promise);
    } catch (error) {
      swLog(`Error in respondWith: ${error}`);
      // Non facciamo nulla, lasciamo che la richiesta proceda normalmente
    }
  };
  
  try {
    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin)) {
      swLog(`Skipping cross-origin request: ${event.request.url}`);
      return;
    }
    
    // Skip richieste di service worker stesso
    if (event.request.url.includes('service-worker.js')) {
      swLog('Skipping service worker request');
      return;
    }
    
    // Skip richieste alle pagine di stampa QR code
    if (event.request.url.includes('print-qr') || event.request.url.includes('print-qr-direct.html')) {
      swLog('Skipping QR code print page request');
      return;
    }
    
    // Network-first strategy for API requests - MODIFICATO per risolvere problemi in produzione
    if (event.request.url.includes('/api/')) {
      // Non memorizzare nella cache le richieste POST/PUT/DELETE
      if (event.request.method !== 'GET') {
        swLog(`Skipping non-GET API request: ${event.request.method} ${event.request.url}`);
        return; // Lascia che la richiesta proceda normalmente senza intercettazione
      }
      
      // Passa direttamente le richieste di autenticazione, modelli LLM e impostazioni di sistema alla rete
      // Questo risolve i problemi con l'accesso ai modelli LLM e impostazioni nelle pagine di configurazione
      if (event.request.url.includes('/api/auth/') || 
          event.request.url.includes('/api/llm/') || 
          event.request.url.includes('/api/models') ||
          event.request.url.includes('/api/claude/') || 
          event.request.url.includes('/api/system/')) {
        swLog(`Passing through critical API request: ${event.request.url}`);
        return; // Lascia che la richiesta proceda normalmente alla rete
      }
      
      swLog(`Handling API request with network-first: ${event.request.url}`);
      
      // Implementazione network-first con meno interferenza
      safeRespondWith(
        (async () => {
          try {
            // Prova sempre prima la rete con la richiesta originale
            const networkResponse = await fetch(event.request.clone());
            
            if (networkResponse && networkResponse.ok) {
              swLog(`Network request successful: ${event.request.url}`);
              
              // Aggiorna la cache con la nuova risposta solo per risposte valide
              try {
                const cache = await caches.open(API_CACHE_NAME);
                await cache.put(event.request, networkResponse.clone());
              } catch (cacheError) {
                swLog(`Cache update skipped: ${cacheError}`);
                // Non blocchiamo la risposta se la cache fallisce
              }
                
              return networkResponse;
            } else {
              throw new Error(`Network response error: ${networkResponse.status}`);
            }
          } catch (error) {
            swLog(`Network request failed: ${error}, trying cache fallback`);
            
            // Solo se la rete fallisce, proviamo la cache
            try {
              const cache = await caches.open(API_CACHE_NAME);
              const cachedResponse = await cache.match(event.request);
              
              if (cachedResponse) {
                swLog(`Returning cached response as fallback for: ${event.request.url}`);
                return cachedResponse;
              }
            } catch (cacheError) {
              swLog(`Cache fallback failed: ${cacheError}`);
            }
            
            // Fallback con risposta di errore strutturata
            if (event.request.url.includes('/api/auth/me')) {
              return createSafeResponse('Modalità offline attiva, alcune funzionalità potrebbero essere limitate');
            }
            
            return createSafeResponse('Impossibile accedere ai dati: controlla la connessione di rete');
          }
        })()
      );
      return;
    }
    
    // Cache-first strategy for static assets and navigation
    swLog(`Handling asset request: ${event.request.url}`);
    safeRespondWith(
      caches.match(event.request)
        .then(response => {
          // Return cached response if found
          if (response) {
            swLog('Returning cached asset');
            return response;
          }
          
          swLog('No cached asset found, fetching from network');
          
          try {
            // Clone the request
            const fetchRequest = event.request.clone();
            
            // Make network request
            return fetch(fetchRequest)
              .then(response => {
                try {
                  // Check for valid response
                  if (!response || response.status !== 200 || response.type !== 'basic') {
                    return response;
                  }
                  
                  // Clone the response
                  const responseToCache = response.clone();
                  
                  // Cache the successful response
                  caches.open(CACHE_NAME)
                    .then(cache => {
                      cache.put(event.request, responseToCache)
                        .catch(err => swLog(`Cache put error for asset: ${err}`));
                    })
                    .catch(err => swLog(`Cache open error for asset: ${err}`));
                    
                  return response;
                } catch (error) {
                  swLog(`Error handling fetch response for asset: ${error}`);
                  return response; // Restituisci la risposta originale in caso di errore
                }
              })
              .catch(fetchError => {
                swLog(`Network error for asset request: ${fetchError}`);
                
                // For navigation, return the cached index.html
                if (event.request.mode === 'navigate') {
                  swLog('Returning cached index.html for navigation request');
                  return caches.match('/index.html')
                    .catch(indexError => {
                      swLog(`Error getting cached index.html: ${indexError}`);
                      return createSafeResponse('Pagina non disponibile offline', 404);
                    });
                }
                
                return createSafeResponse('Risorsa non disponibile offline', 404);
              });
          } catch (requestError) {
            swLog(`Error creating fetch request: ${requestError}`);
            return createSafeResponse('Errore nell\'elaborazione della richiesta', 500);
          }
        })
        .catch(cacheError => {
          swLog(`Cache match error for asset: ${cacheError}`);
          
          // Fallback to network as a last resort
          try {
            return fetch(event.request)
              .catch(() => createSafeResponse('Risorsa non disponibile', 404));
          } catch (finalError) {
            swLog(`Final fallback error: ${finalError}`);
            return createSafeResponse('Errore irreversibile', 500);
          }
        })
    );
  } catch (error) {
    swLog(`Global fetch handler error: ${error}`);
    // Non facciamo respondWith in caso di errore globale - lasciamo passare la richiesta
  }
});

// Sync event handler for offline updates
self.addEventListener('sync', (event) => {
  if (event.tag === SYNC_QUEUE_NAME) {
    swLog('Sync event ricevuto, elaborazione delle operazioni in attesa...');
    event.waitUntil(
      processPendingOperations()
    );
  }
});

// Periodic sync handler 
self.addEventListener('periodicsync', (event) => {
  if (event.tag === SYNC_QUEUE_NAME) {
    swLog('Periodic Sync event ricevuto, aggiornamento dati...');
    event.waitUntil(
      updateCachedData()
    );
  }
});

// Process pending operations from IndexedDB
async function processPendingOperations() {
  try {
    const db = await initSyncDB();
    const tx = db.transaction(SYNC_STORE_NAME, 'readwrite');
    const store = tx.objectStore(SYNC_STORE_NAME);
    
    // Ottieni tutte le operazioni in attesa
    const operations = await new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
    
    if (operations.length === 0) {
      swLog('Nessuna operazione in attesa');
      return;
    }
    
    swLog(`Elaborazione di ${operations.length} operazioni in attesa`);
    
    // Elabora ogni operazione in sequenza
    for (const op of operations) {
      try {
        // Tenta di eseguire la richiesta ora che siamo online
        const response = await fetch(op.url, {
          method: op.method,
          headers: op.headers,
          body: op.body
        });
        
        if (response.ok) {
          // Se la richiesta ha successo, rimuovi l'operazione dal database
          await new Promise((resolve, reject) => {
            const deleteRequest = store.delete(op.id);
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
          });
          
          swLog(`Operazione #${op.id} completata e rimossa dalla coda`);
          
          // Aggiorniamo anche le cache correlate
          if (op.cacheUpdate) {
            const cache = await caches.open(op.cacheUpdate.cacheName);
            await cache.delete(op.cacheUpdate.url);
            // Se l'operazione ha generato contenuti da memorizzare nella cache
            if (op.cacheUpdate.storeResponse) {
              await cache.put(op.cacheUpdate.url, response.clone());
            }
          }
        } else {
          swLog(`Operazione #${op.id} fallita: ${response.status} ${response.statusText}`);
          // Incrementa il contatore dei tentativi
          op.retries = (op.retries || 0) + 1;
          
          // Se abbiamo raggiunto il massimo numero di tentativi, contrassegna come fallita
          if (op.retries >= 5) {
            op.failed = true;
            op.failReason = `Status: ${response.status} ${response.statusText}`;
          }
          
          // Aggiorna l'operazione nel database
          await new Promise((resolve, reject) => {
            const updateRequest = store.put(op);
            updateRequest.onsuccess = () => resolve();
            updateRequest.onerror = () => reject(updateRequest.error);
          });
        }
      } catch (error) {
        swLog(`Errore nell'elaborazione dell'operazione #${op.id}: ${error}`);
        // Incrementa il contatore dei tentativi
        op.retries = (op.retries || 0) + 1;
        op.lastError = error.toString();
        
        // Aggiorna l'operazione nel database
        await new Promise((resolve, reject) => {
          const updateRequest = store.put(op);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(updateRequest.error);
        });
      }
    }
    
    // Chiudi la transazione
    await new Promise((resolve) => {
      tx.oncomplete = () => resolve();
      tx.onerror = () => {
        swLog(`Errore nella transazione: ${tx.error}`);
        resolve(); // Risolvi comunque per non bloccare
      };
    });
    
    swLog('Elaborazione delle operazioni in attesa completata');
    db.close();
  } catch (error) {
    swLog(`Errore durante l'elaborazione delle operazioni in attesa: ${error}`);
  }
}

// Update cached data (used by periodic sync)
async function updateCachedData() {
  try {
    swLog('Aggiornamento dati in cache...');
    
    // Lista di URL da aggiornare regolarmente
    const urlsToUpdate = [
      { url: '/api/auth/me', cacheName: API_CACHE_NAME },
      { url: '/api/containers', cacheName: API_CACHE_NAME },
      { url: '/api/container-types', cacheName: API_CACHE_NAME },
      { url: '/api/suppliers', cacheName: API_CACHE_NAME }
    ];
    
    for (const item of urlsToUpdate) {
      try {
        // Fetch nuovi dati
        const response = await fetch(item.url);
        
        if (response.ok) {
          // Aggiorna la cache
          const cache = await caches.open(item.cacheName);
          await cache.put(item.url, response.clone());
          swLog(`Cache aggiornata per ${item.url}`);
        }
      } catch (error) {
        swLog(`Errore nell'aggiornamento della cache per ${item.url}: ${error}`);
        // Continua con il prossimo URL
      }
    }
    
    swLog('Aggiornamento dati in cache completato');
  } catch (error) {
    swLog(`Errore durante l'aggiornamento dei dati in cache: ${error}`);
  }
}

// Push notification event handler
self.addEventListener('push', (event) => {
  let data = {};
  
  try {
    data = event.data.json();
  } catch(e) {
    data = {
      title: 'HACCP Tracker',
      body: event.data ? event.data.text() : 'Notification from HACCP Tracker'
    };
  }
  
  swLog(`Notifica push ricevuta: ${data.title}`);
  
  const options = {
    body: data.body || 'New notification',
    icon: '/logo192.svg',
    badge: '/favicon.ico',
    vibrate: [100, 50, 100],
    data: {
      url: data.url || '/',
      timestamp: Date.now()
    },
    tag: data.tag || 'default', // Tag per raggruppare notifiche simili
    renotify: data.renotify || false, // Se true, vibra anche se il tag è lo stesso di una notifica esistente
    actions: data.actions || [
      {
        action: 'view',
        title: 'Visualizza'
      },
      {
        action: 'close', 
        title: 'Chiudi'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title || 'HACCP Tracker', options)
  );
});

// Notification click event handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  // Estrai l'URL e l'azione dalla notifica
  const url = event.notification.data.url;
  const action = event.action;
  
  swLog(`Notifica cliccata: ${action || 'default'} per URL ${url}`);
  
  // Se l'azione è "close", non facciamo nulla
  if (action === 'close') {
    return;
  }
  
  // Per tutte le altre azioni, apriamo la pagina appropriata
  event.waitUntil(
    clients.matchAll({type: 'window'})
      .then(windowClients => {
        // Check if there is already a window with this URL open
        for (let client of windowClients) {
          if (client.url === url && 'focus' in client) {
            swLog('Focus su finestra esistente');
            return client.focus();
          }
        }
        
        // If not, open a new window
        if (clients.openWindow) {
          swLog('Apertura nuova finestra');
          return clients.openWindow(url);
        }
      })
  );
});

// Add event handler for message from the client
self.addEventListener('message', (event) => {
  // Verifico se event.data esiste e ottengo type e payload in modo sicuro
  const data = event.data || {};
  const type = data.type || '';
  const payload = data.payload;
  
  swLog(`Messaggio ricevuto dal client: ${type}`);
  
  // Gestione speciale per il caso in cui il messaggio sia una stringa 'SKIP_WAITING'
  if (type === 'SKIP_WAITING' || event.data === 'SKIP_WAITING') {
    swLog('Richiesta di SKIP_WAITING ricevuta, attivazione immediata');
    self.skipWaiting();
  } else if (type === 'GET_VERSION') {
    // Rispondi con la versione del service worker
    event.ports[0].postMessage({
      version: VERSION
    });
  } else if (type === 'QUEUE_OPERATION') {
    // Aggiungi un'operazione alla coda per sync
    if (payload && payload.url && payload.method) {
      swLog(`Messa in coda operazione: ${payload.method} ${payload.url}`);
      
      event.waitUntil(
        initSyncDB()
          .then(db => {
            const tx = db.transaction(SYNC_STORE_NAME, 'readwrite');
            const store = tx.objectStore(SYNC_STORE_NAME);
            
            return new Promise((resolve, reject) => {
              const request = store.add({
                url: payload.url,
                method: payload.method,
                headers: payload.headers || {},
                body: payload.body || null,
                timestamp: Date.now(),
                retries: 0,
                cacheUpdate: payload.cacheUpdate || null
              });
              
              request.onsuccess = () => {
                swLog('Operazione aggiunta alla coda con successo');
                // Registra sync se non è già registrato
                registerSync();
                resolve();
              };
              
              request.onerror = () => {
                swLog(`Errore nell'aggiunta dell'operazione alla coda: ${request.error}`);
                reject(request.error);
              };
            });
          })
          .then(() => {
            // Rispondi al client
            if (event.ports && event.ports[0]) {
              event.ports[0].postMessage({
                success: true,
                message: 'Operazione messa in coda'
              });
            }
          })
          .catch(error => {
            swLog(`Errore nella gestione dell'operazione: ${error}`);
            // Rispondi al client
            if (event.ports && event.ports[0]) {
              event.ports[0].postMessage({
                success: false,
                error: error.toString()
              });
            }
          })
      );
    }
  }
});

// Tenta di inizializzare il database delle operazioni offline e registrare background sync
self.addEventListener('activate', (event) => {
  if (!isDevMode) {
    // Inizializza background sync in produzione
    event.waitUntil(
      initSyncDB()
        .then(() => {
          registerSync();
          registerPeriodicSync();
        })
        .catch(error => {
          swLog(`Errore nell'inizializzazione del database o nella registrazione sync: ${error}`);
        })
    );
  }
});
