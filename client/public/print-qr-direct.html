<!DOCTYPE html>
<html>
<head>
  <title>Stampa QR Code</title>
  <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
  <style>
    @page {
      size: 50mm 60mm;
      margin: 0;
    }
    body {
      margin: 0;
      padding: 5mm;
      background: white;
      font-family: Arial, sans-serif;
    }
    .container {
      width: 40mm;
      height: 50mm;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .qr-image {
      width: 40mm;
      height: 40mm;
      margin-bottom: 3mm;
    }
    .title {
      font-size: 3.5mm;
      font-weight: bold;
      line-height: 4mm;
      margin: 0 0 1mm 0;
      text-align: center;
      text-transform: uppercase;
      word-break: break-word;
      max-width: 40mm;
    }
  </style>
</head>
<body>
  <div class="container">
    <div id="qr-image-container" class="qr-image"></div>
    <p id="title" class="title"></p>
    <p id="subtitle" class="title"></p>
  </div>

  <script>
    // Funzione per ottenere i parametri dall'URL
    function getQueryParam(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }
    
    // Funzione per decodificare i parametri dell'URL
    function decodeParam(param) {
      return param ? decodeURIComponent(param) : '';
    }
    
    // Funzione per generare il QR code usando la libreria
    function generateQRCode(text, container) {
      try {
        // Crea un nuovo QR code
        const qr = qrcode(4, 'M');
        qr.addData(text);
        qr.make();
        
        // Ottieni l'SVG del QR code
        const svgText = qr.createSvgTag({
          cellSize: 8,  
          margin: 4
        });
        
        // Inserisci l'SVG nel container
        container.innerHTML = svgText;
        
        // Trova l'elemento SVG e imposta la larghezza e altezza corrette
        const svg = container.querySelector('svg');
        if (svg) {
          svg.style.width = '100%';
          svg.style.height = '100%';
        }
        
        return true;
      } catch (error) {
        console.error('Errore nella generazione del QR code:', error);
        return false;
      }
    }
    
    // Funzione principale che viene eseguita al caricamento della pagina
    window.onload = function() {
      // Ottieni i parametri dall'URL
      const title = decodeParam(getQueryParam('title'));
      const subtitle = decodeParam(getQueryParam('subtitle'));
      const qrValue = decodeParam(getQueryParam('qrvalue'));
      const qrImageSrc = getQueryParam('qrimage');
      
      // Imposta i titoli
      document.getElementById('title').textContent = title;
      document.getElementById('subtitle').textContent = subtitle;
      
      // Container per il QR code
      const qrContainer = document.getElementById('qr-image-container');
      
      // Priorità 1: Usa l'immagine QR code passata come parametro
      if (qrImageSrc) {
        const img = document.createElement('img');
        img.src = qrImageSrc;
        img.alt = 'QR Code';
        img.style.width = '100%';
        img.style.height = '100%';
        qrContainer.appendChild(img);
      } 
      // Priorità 2: Genera QR code usando il valore passato e la libreria
      else if (qrValue) {
        if (!generateQRCode(qrValue, qrContainer)) {
          // Se fallisce la generazione, usa un QR code statico di fallback
          qrContainer.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="100%" height="100%">
              <rect width="200" height="200" fill="white"/>
              <rect x="40" y="40" width="120" height="120" fill="black"/>
              <rect x="52" y="52" width="96" height="96" fill="white"/>
              <rect x="64" y="64" width="72" height="72" fill="black"/>
              <rect x="76" y="76" width="48" height="48" fill="white"/>
              <rect x="88" y="88" width="24" height="24" fill="black"/>
              <rect x="40" y="40" width="8" height="8" fill="black"/>
              <rect x="152" y="40" width="8" height="8" fill="black"/>
              <rect x="40" y="152" width="8" height="8" fill="black"/>
            </svg>
          `;
        }
      }
      // Fallback finale
      else {
        qrContainer.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="100%" height="100%">
            <rect width="200" height="200" fill="white"/>
            <rect x="40" y="40" width="120" height="120" fill="black"/>
            <rect x="52" y="52" width="96" height="96" fill="white"/>
            <rect x="64" y="64" width="72" height="72" fill="black"/>
            <rect x="76" y="76" width="48" height="48" fill="white"/>
            <rect x="88" y="88" width="24" height="24" fill="black"/>
            <rect x="40" y="40" width="8" height="8" fill="black"/>
            <rect x="152" y="40" width="8" height="8" fill="black"/>
            <rect x="40" y="152" width="8" height="8" fill="black"/>
          </svg>
        `;
      }
      
      // Stampa automaticamente dopo un breve ritardo
      setTimeout(function() {
        window.print();
        setTimeout(function() {
          window.close();
        }, 500);
      }, 800);
    };
  </script>
</body>
</html>