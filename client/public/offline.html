<!DOCTYPE html>
<html lang="it">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="theme-color" content="#0f766e">
  <title>Offline - HACCP Tracker</title>
  <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" href="/apple-touch-icon.png">
  <style>
    :root {
      --primary: #0f766e;
      --primary-light: #14b8a6;
      --primary-dark: #115e59;
      --background: #f8fafc;
      --text-primary: #334155;
      --text-secondary: #64748b;
      --error: #ef4444;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      line-height: 1.5;
    }
    
    header {
      background-color: var(--primary);
      color: white;
      padding: 1rem;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 1.5rem;
    }
    
    h1 {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      color: var(--primary);
    }
    
    p {
      font-size: 1rem;
      color: var(--text-secondary);
      margin-bottom: 1.5rem;
    }
    
    .card {
      background-color: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      width: 100%;
      max-width: 500px;
      margin-bottom: 1.5rem;
    }
    
    .button {
      display: inline-block;
      background-color: var(--primary);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      border: none;
      outline: none;
    }
    
    .button:hover {
      background-color: var(--primary-dark);
    }
    
    .button.secondary {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }
    
    .button.secondary:hover {
      background-color: rgba(15, 118, 110, 0.1);
    }
    
    .icon {
      width: 24px;
      height: 24px;
      vertical-align: middle;
      margin-right: 0.5rem;
    }
    
    footer {
      padding: 1rem;
      text-align: center;
      font-size: 0.875rem;
      color: var(--text-secondary);
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .wifi-icon {
      margin-bottom: 1rem;
    }
    
    @media (max-width: 640px) {
      main {
        padding: 1rem;
      }
      
      .card {
        padding: 1rem;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>HACCP Tracker</h1>
  </header>
  
  <main>
    <div class="wifi-icon">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="1" y1="1" x2="23" y2="23"></line>
        <path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path>
        <path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path>
        <path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path>
        <path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path>
        <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
        <line x1="12" y1="20" x2="12.01" y2="20"></line>
      </svg>
    </div>
    
    <div class="card">
      <h1>Modalità Offline</h1>
      <p>Non sei connesso a Internet. Alcune funzionalità potrebbero non essere disponibili.</p>
      <p>Puoi continuare a utilizzare le funzioni offline dell'app o provare a ripristinare la connessione.</p>
      
      <button onclick="window.location.href='/'" class="button">Torna alla Home</button>
      <button onclick="window.location.reload()" class="button secondary" style="margin-left: 0.5rem;">Riprova</button>
    </div>
    
    <div>
      <p>Se hai già scaricato i dati, puoi accedere a:</p>
      <ul style="text-align: left; color: var(--text-secondary);">
        <li>Elenco container</li>
        <li>Prodotti in magazzino</li>
        <li>Documenti salvati</li>
      </ul>
    </div>
  </main>
  
  <footer>
    <p>HACCP Tracker &copy; 2025 - Versione 1.1.0</p>
  </footer>
  
  <script>
    // Controlla periodicamente lo stato della connessione
    function checkConnection() {
      if (navigator.onLine) {
        document.querySelector('.card p').textContent = 
          'La connessione è stata ripristinata. Puoi continuare a utilizzare tutte le funzionalità dell\'app.';
      }
    }
    
    // Ascolta eventi di connessione
    window.addEventListener('online', () => {
      checkConnection();
    });
    
    // Controlla inizialmente
    checkConnection();
  </script>
</body>
</html>