<!DOCTYPE html>
<html>
<head>
  <title>Stampa QR Code</title>
  <style>
    @page {
      size: 50mm 50mm;
      margin: 0;
    }
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background: white;
    }
    .qr-container {
      display: block;
      width: 45mm;
      height: 45mm;
    }
    img {
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div class="qr-container">
    <img id="qr-image" src="" alt="QR Code">
  </div>

  <script>
    // Recupera il parametro dell'URL per l'immagine QR code
    function getImageParam() {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('image');
    }
    
    window.onload = function() {
      // Imposta l'immagine
      const imgSrc = getImageParam();
      if (imgSrc) {
        document.getElementById('qr-image').src = decodeURIComponent(imgSrc);
      }
      
      // Stampa automaticamente
      setTimeout(function() {
        window.print();
        setTimeout(function() {
          window.close();
        }, 500);
      }, 200);
    }
  </script>
</body>
</html>