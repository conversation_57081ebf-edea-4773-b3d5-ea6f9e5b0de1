/**
 * Utility per gestire il hot reload e il monitoraggio delle modifiche ai file
 */

// Controlla gli aggiornamenti ogni 5 secondi
const CHECK_INTERVAL = 5000;

// Ultima volta che abbiamo controllato gli aggiornamenti
let lastCheckTime = Date.now();

// Stato del controllo
let isChecking = false;
let checkCount = 0;

// ID univoco per la sessione corrente
const SESSION_ID = Math.random().toString(36).substring(2, 15);

/**
 * Controlla se ci sono aggiornamenti al codice
 */
async function checkForUpdates() {
  if (isChecking) return;
  
  try {
    isChecking = true;
    
    // Aggiunge parametri random per evitare caching
    const response = await fetch(`/api/dev/check-updates?t=${Date.now()}&sid=${SESSION_ID}`);
    
    if (response.ok) {
      const data = await response.json();
      
      // Se c'è un aggiornamento più recente del nostro ultimo controllo, ricarica la pagina
      if (data.timestamp && data.timestamp > lastCheckTime) {
        console.log('[Hot Reload] Rilevate modifiche, ricaricamento...');
        window.location.reload();
      }
      
      // Aggiorna l'ultimo timestamp di controllo
      lastCheckTime = Date.now();
      checkCount++;
      
      // Aggiorna il contatore nel pulsante se esiste
      const countElement = document.getElementById('hot-reload-count');
      if (countElement) {
        countElement.textContent = `${checkCount}`;
      }
    }
  } catch (error) {
    console.error('[Hot Reload] Errore durante il controllo aggiornamenti:', error);
  } finally {
    isChecking = false;
    
    // Aggiorna lo stato visivo del pulsante
    const button = document.getElementById('hot-reload-button');
    if (button) {
      button.classList.remove('checking');
    }
  }
}

/**
 * Forza un ricaricamento della pagina
 */
function forceReload() {
  console.log('[Hot Reload] Forzatura ricaricamento manuale');
  window.location.reload();
}

/**
 * Aggiunge il pulsante di ricarica alla pagina
 */
function addReloadButton() {
  // Crea un elemento style per il pulsante
  const style = document.createElement('style');
  style.textContent = `
    #hot-reload-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      padding: 10px 14px;
      background-color: rgba(255, 0, 0, 0.8);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
      font-family: system-ui, sans-serif;
    }
    
    #hot-reload-button.checking {
      background-color: rgba(0, 0, 128, 0.9);
    }
    
    #hot-reload-icon {
      width: 20px;
      height: 20px;
    }
    
    #hot-reload-button.checking #hot-reload-icon {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    #hot-reload-count {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      min-width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-left: 4px;
    }
  `;
  document.head.appendChild(style);
  
  // Crea il pulsante di ricarica
  const button = document.createElement('button');
  button.id = 'hot-reload-button';
  button.title = `Hot Reload | Ultimo controllo: ${new Date().toLocaleTimeString()}`;
  button.innerHTML = `
    <svg id="hot-reload-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M23 4v6h-6"></path>
      <path d="M1 20v-6h6"></path>
      <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
      <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
    </svg>
    RICARICA
    <span id="hot-reload-count">0</span>
  `;
  button.addEventListener('click', forceReload);
  document.body.appendChild(button);
  
  console.log('[Hot Reload] Pulsante aggiunto alla pagina');
}

/**
 * Inizializza il sistema di hot reload
 */
export function initHotReload() {
  console.log('[Hot Reload] Inizializzazione...');
  
  // Aggiungi il pulsante alla pagina
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addReloadButton);
  } else {
    addReloadButton();
  }
  
  // Controlla subito gli aggiornamenti
  checkForUpdates();
  
  // Imposta un intervallo per controllare periodicamente
  setInterval(checkForUpdates, CHECK_INTERVAL);
}

export default initHotReload;