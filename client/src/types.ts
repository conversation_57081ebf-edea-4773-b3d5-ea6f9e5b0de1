import { UserRoleType } from "@shared/schema";

export interface User {
  id: number;
  username: string;
  fullName?: string;
  email?: string | null;
  isAdmin: boolean;
  role?: UserRoleType;
  tenantId?: string;
  tenant?: {
    id: string;
    name: string;
    code: string;
  };
  isImpersonated?: boolean;
  originalAdminId?: string;  
  createdAt?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface Container {
  id: number;
  name: string;
  type: string;
  typeId: number;
  maxItems: number;
  currentItems: number;
  isArchived: boolean;
  qrCode?: string;
  createdAt: string;
  createdBy: number;
}

export interface ContainerType {
  id: number;
  value: string;
  label: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  createdBy?: number;
}

export interface DDT {
  id: number;
  supplierId: number;
  companyName: string;
  vatNumber: string;
  address: string;
  number: string;
  date: string;
  createdAt: string;
  createdBy: number;
  image?: string;
}

export interface DDTFormData {
  companyName: string;
  vatNumber: string;
  address: string;
  number: string;
  date: string;
}

export interface ProductLabel {
  id: number;
  ddtId: number;
  productName: string;
  expiryDate: string;
  batchNumber: string;
  storageInstructions: string;
  notes: string;
  qrCode: string;
  isUsed: boolean;
  createdAt: string;
  createdBy: number;
}

export interface ProductLabelFormData {
  productName: string;
  expiryDate: string;
  batchNumber: string;
  storageInstructions: string;
  notes: string;
  qrCodeName: string;
}

export interface Supplier {
  id: number;
  companyName: string;
  vatNumber: string;
  address: string;
  email?: string;
  phone?: string;
  contactPerson?: string;
  createdAt: string;
  createdBy: number;
}
