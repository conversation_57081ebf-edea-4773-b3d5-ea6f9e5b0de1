/* Ottimizzazione font con preload - miglioramento performance */
/* I font sono ora precaricati nell'HTML per una visualizzazione più rapida */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fix per orientamento preview Replit */
html, body {
  transform: none !important;
  min-width: 320px;
  overflow-x: auto;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Fix per scroll nella pagina utenti */
.users-page-main {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Fix responsive per modal */
.mobile-modal-fix {
  max-height: 85vh;
  overflow-y: auto;
}

@media (max-width: 640px) {
  [data-radix-dialog-content],
  .mobile-modal-fix {
    width: 95vw !important;
    max-width: 95vw !important;
    margin: 8px auto !important;
    max-height: 85vh !important;
    overflow-y: auto !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    border-radius: 8px !important;
  }
  
  /* Specifico per modali che si tagliano */
  [data-radix-dialog-overlay] {
    position: fixed !important;
    inset: 0 !important;
    z-index: 50 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
}

@layer components {
  .glow-effect {
    position: relative;
  }
  
  .glow-effect::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .glow-effect:hover::before {
    opacity: 0.7;
  }
  
  .glow-effect-blue::before {
    box-shadow: 0 0 15px 5px rgba(59, 130, 246, 0.5);
  }
  
  .glow-effect-gray::before {
    box-shadow: 0 0 15px 5px rgba(156, 163, 175, 0.3);
  }
}

/* Posizionamento dropdown corretto */
@layer utilities {
  /* Assicura che i dropdown abbiano un buon posizionamento */
  [data-radix-dropdown-menu-content] {
    z-index: 50;
  }
  
  @media (min-width: 768px) {
    /* Fix specifico per il menu header su desktop - forza posizionamento a destra */
    header [data-radix-dropdown-menu-content],
    header div[data-radix-dropdown-menu-content],
    body header [data-radix-dropdown-menu-content],
    body:not(.impersonation-active) header [data-radix-dropdown-menu-content] {
      position: fixed !important;
      right: 24px !important;
      left: auto !important;
      transform: none !important;
      transform-origin: top right !important;
      top: 70px !important;
    }
    
    /* Specifico per impersonazione attiva */
    .impersonation-active header [data-radix-dropdown-menu-content],
    .impersonation-active header div[data-radix-dropdown-menu-content] {
      top: 126px !important; /* 56px barra + 70px normale */
    }
    
    /* Assicura che il contenitore header non interferisca */
    header .relative {
      overflow: visible !important;
    }
    
    /* Override eventuali conflitti */
    body header [data-radix-popper-content-wrapper] {
      position: static !important;
    }
    
    /* Classe specifica per il menu desktop */
    .desktop-menu-right[data-radix-dropdown-menu-content] {
      position: fixed !important;
      right: 24px !important;
      left: auto !important;
      transform: none !important;
      transform-origin: top right !important;
      top: 70px !important;
    }
    
    /* Quando impersonazione è attiva */
    .impersonation-active .desktop-menu-right[data-radix-dropdown-menu-content] {
      top: 126px !important;
    }
  }
  
  /* Layout responsive per desktop */
  @media (min-width: 768px) {
    /* Migliora il posizionamento del contenuto principale su desktop */
    main {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    /* Ottimizza l'header su desktop */
    header .max-w-md {
      max-width: 1200px;
    }
  }
  
  /* Layout flessibile senza margin-top fissi per responsiveness mobile */
  main,
  .main-content {
    /* Usa padding-top invece di margin-top per evitare overflow */
    padding-top: 0;
    margin-top: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* Permetti scroll verticale per contenuti */
    overflow-x: hidden; /* Blocca solo scroll orizzontale */
    height: 100%;
  }
  
  /* Su mobile, blocca solo layout principale ma permetti scroll contenuti */
  @media (max-width: 767px) {
    body {
      overflow: hidden !important;
      height: 100vh !important;
      height: 100dvh !important;
      overscroll-behavior: none !important;
    }
    
    /* Applica contenimento solo ai container layout principali */
    #root,
    #root > div,
    .gradient-background {
      overflow: hidden !important;
      height: 100vh !important;
      height: 100dvh !important;
    }
    
    /* Permetti scroll per main e contenuti interni */
    main,
    .main-content {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      height: 100% !important;
      max-height: 100vh !important;
      max-height: 100dvh !important;
    }
    
    /* Previeni bounce scroll ma permetti scroll normale */
    main,
    .main-content {
      overscroll-behavior: none !important;
      -webkit-overflow-scrolling: touch !important;
    }
    
    /* Forza scroll per contenuti lunghi come dashboard admin */
    .scrollable-content,
    .admin-dashboard-content {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      height: calc(100vh - 80px) !important; /* Sottrae spazio per header */
      height: calc(100dvh - 80px) !important;
    }
  }
  
  /* Solo su desktop, aggiungi spazio per header e barra impersonazione */
  @media (min-width: 768px) {
    .impersonation-active main,
    .impersonation-active .main-content {
      padding-top: 134px; /* 56px header + 56px barra + padding */
    }
    
    main:not(.impersonation-active),
    .main-content:not(.impersonation-active) {
      padding-top: 78px; /* Solo header + padding */
    }
  }
}

:root, .light {
  /* Tema Light (default) */
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 20 14.3% 4.1%;
  --radius: 0.5rem;
}

.dark {
  /* Tema Scuro */
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

.fun {
  /* Tema Divertente con colori vivaci */
  --background: 280 50% 98%;
  --foreground: 300 100% 25%;
  --muted: 305 30% 95%;
  --muted-foreground: 300 50% 40%;
  --popover: 280 50% 98%;
  --popover-foreground: 300 100% 25%;
  --card: 280 60% 98%;
  --card-foreground: 300 100% 25%;
  --border: 310 50% 80%;
  --input: 310 50% 80%;
  --primary: 271 91% 65%; /* Viola vivace */
  --primary-foreground: 0 0% 100%;
  --secondary: 340 100% 75%; /* Rosa */
  --secondary-foreground: 300 100% 25%;
  --accent: 199 100% 72%; /* Azzurro */
  --accent-foreground: 300 100% 25%;
  --destructive: 0 100% 65%;
  --destructive-foreground: 0 0% 100%;
  --ring: 271 91% 65%;
  --radius: 1rem; /* Raggi più arrotondati */
}

.ocean {
  /* Tema Oceano con sfumature blu */
  --background: 195 100% 97%;
  --foreground: 220 70% 20%;
  --muted: 200 60% 92%;
  --muted-foreground: 215 50% 35%;
  --popover: 195 100% 97%;
  --popover-foreground: 220 70% 20%;
  --card: 200 60% 96%;
  --card-foreground: 220 70% 20%;
  --border: 205 70% 85%;
  --input: 205 70% 85%;
  --primary: 205 100% 50%; /* Blu oceano */
  --primary-foreground: 0 0% 100%;
  --secondary: 180 100% 40%; /* Turchese */
  --secondary-foreground: 0 0% 100%;
  --accent: 195 80% 60%; /* Azzurro cielo */
  --accent-foreground: 0 0% 100%;
  --destructive: 0 90% 60%;
  --destructive-foreground: 0 0% 100%;
  --ring: 205 100% 50%;
  --radius: 0.5rem;
}

.forest {
  /* Tema Foresta con sfumature verdi */
  --background: 120 30% 96%;
  --foreground: 130 50% 15%;
  --muted: 150 25% 92%;
  --muted-foreground: 140 40% 30%;
  --popover: 120 30% 96%;
  --popover-foreground: 130 50% 15%;
  --card: 150 25% 96%;
  --card-foreground: 130 50% 15%;
  --border: 140 30% 80%;
  --input: 140 30% 80%;
  --primary: 145 60% 40%; /* Verde foresta */
  --primary-foreground: 0 0% 100%;
  --secondary: 95 70% 50%; /* Verde lime */
  --secondary-foreground: 0 0% 100%;
  --accent: 160 70% 40%; /* Verde smeraldo */
  --accent-foreground: 0 0% 100%;
  --destructive: 0 90% 60%;
  --destructive-foreground: 0 0% 100%;
  --ring: 145 60% 40%;
  --radius: 0.25rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    /* Evita scroll indesiderato e movimenti su mobile */
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height per mobile */
    overflow: hidden;
    position: relative;
    /* Previeni scroll ma permetti touch per interazione */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Su desktop, permetti scroll normale */
  @media (min-width: 768px) {
    body {
      height: auto;
      overflow: auto;
    }
  }
  
  /* Blocco orientamento schermo per dispositivi mobili */
  html {
    /* Impedisce la rotazione automatica del contenuto */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    
    /* Forza orientamento portrait su iOS */
    -webkit-orientation: portrait;
    -moz-orientation: portrait;
    -ms-orientation: portrait;
    orientation: portrait;
  }
  
  /* Supporto naturale per dispositivi landscape senza rotazione forzata */
  @media screen and (orientation: landscape) and (max-width: 1024px) {
    body {
      overflow-x: hidden;
    }
  }
  
  /* Override per dispositivi più grandi (tablet/desktop) */
  @media screen and (min-width: 1025px) {
    html, body {
      transform: none !important;
      width: auto !important;
      height: auto !important;
      position: static !important;
      top: auto !important;
      left: auto !important;
    }
  }
  
  /* Classe CSS per blocco orientamento visual quando API non sono supportate */
  .orientation-locked-portrait {
    /* Forza aspetto portrait anche se il dispositivo è ruotato */
    min-height: 100vh;
    max-width: 100vw;
  }
  
  .orientation-locked-portrait body {
    /* Impedisce scroll orizzontale in caso di rotazione */
    overflow-x: hidden;
    width: 100vw;
    min-height: 100vh;
  }
  
  /* Messaggio di avviso per orientamento non supportato */
  .orientation-warning {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    text-align: center;
    padding: 20px;
  }
  
  /* Mostra avviso solo su dispositivi mobili in landscape */
  @media screen and (orientation: landscape) and (max-height: 500px) and (max-width: 1024px) {
    .orientation-warning {
      display: flex;
    }
  }
}

/* QR Scanner Enhanced Styles */
.qr-corner {
  @apply absolute w-16 h-16 border-4 border-blue-500 opacity-80 z-10;
}

.qr-corner-top-left {
  @apply top-3 left-3 border-t-0 border-l-0 rounded-br-lg;
}

.qr-corner-top-right {
  @apply top-3 right-3 border-t-0 border-r-0 rounded-bl-lg;
}

.qr-corner-bottom-left {
  @apply bottom-3 left-3 border-b-0 border-l-0 rounded-tr-lg;
}

.qr-corner-bottom-right {
  @apply bottom-3 right-3 border-b-0 border-r-0 rounded-tl-lg;
}

.qr-scan-line {
  @apply absolute top-0 left-0 w-full h-1 bg-red-500 shadow-[0_0_10px_rgba(239,68,68,0.7)] z-10;
  animation: scanLine 1.5s linear infinite;
}

.scan-text {
  @apply absolute -bottom-10 left-0 right-0 text-center text-white text-sm font-medium bg-black/60 py-1 px-4 rounded-full mx-auto w-max;
}

@keyframes scanLine {
  0% {
    top: 5%;
  }
  50% {
    top: 95%;
  }
  100% {
    top: 5%;
  }
}

/* ===== DROPDOWN MENU POSITIONING FIX - NUCLEAR APPROACH ===== */
/* Forza assoluta per posizionamento dropdown - IMPOSSIBILE DA SOVRASCRIVERE */

/* NUCLEAR RULE: Massima specificità CSS possibile */
html body #root header.bg-white [data-radix-dropdown-menu-content][data-align="end"].desktop-menu-right,
html body header [data-radix-dropdown-menu-content][data-align="end"].desktop-menu-right,
[data-radix-dropdown-menu-content][data-align="end"][data-state="open"].desktop-menu-right,
.desktop-menu-right[data-radix-dropdown-menu-content] {
  transform-origin: top right !important;
  right: 0px !important;
  left: auto !important;
  margin-left: auto !important;
  margin-right: 0px !important;
  position: absolute !important;
  animation-name: slideInFromRight !important;
}

/* FORCED POSITIONING con timing aggressivo */
[data-radix-dropdown-menu-content].desktop-menu-right {
  transform-origin: top right !important;
  right: 0px !important;
  left: auto !important;
  animation-name: slideInFromRight !important;
  animation-duration: 150ms !important;
  animation-timing-function: ease-out !important;
  animation-fill-mode: forwards !important;
}

/* NUCLEAR ANIMATION che mantiene il posizionamento */
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
    transform-origin: top right !important;
    right: 0px !important;
    left: auto !important;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    transform-origin: top right !important;
    right: 0px !important;
    left: auto !important;
  }
}

/* BACKUP RULES per ogni possibile stato */
[data-radix-popper-content-wrapper] > [data-radix-dropdown-menu-content].desktop-menu-right {
  right: 0px !important;
  left: auto !important;
  transform-origin: top right !important;
}

/* OVERRIDE per qualsiasi modifica dinamica */
[data-radix-dropdown-menu-content].desktop-menu-right * {
  transform-origin: inherit !important;
}

/* CSS VARIABLES OVERRIDE per Radix positioning */
[data-radix-dropdown-menu-content].desktop-menu-right {
  --radix-dropdown-menu-content-transform-origin: top right !important;
  --radix-popper-transform-origin: top right !important;
}

/* Animazione fadeIn per i dropdown */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}