import { useState, useEffect } from "react";
import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { optimizedQueryClient } from "./lib/optimizedQueryClient";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider, useAuth } from "@/context/auth-context";
import { OfflineApiProvider } from "@/hooks/use-offline-api";
import Login from "@/pages/login";
import Home from "@/pages/home";

// Componente di caricamento semplice
const LoadingSpinner = () => (
  <div className="h-screen w-full flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
);

// Rotte semplici per diagnostica
function SimpleRoutes() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Switch>
      {isAuthenticated ? (
        <Route path="/">
          <Home />
        </Route>
      ) : (
        <Route path="*">
          <Login />
        </Route>
      )}
    </Switch>
  );
}

// App semplificata
function SimpleApp() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Simula l'inizializzazione
    console.log("App semplificata inizializzata");
    setIsReady(true);
  }, []);

  if (!isReady) {
    return <LoadingSpinner />;
  }

  return (
    <QueryClientProvider client={optimizedQueryClient}>
      <OfflineApiProvider>
        <AuthProvider>
          <div className="min-h-screen bg-background">
            <SimpleRoutes />
            <Toaster />
          </div>
        </AuthProvider>
      </OfflineApiProvider>
    </QueryClientProvider>
  );
}

export default SimpleApp;