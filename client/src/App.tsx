import { useEffect, useState, lazy, Suspense } from "react";
import { Switch, Route } from "wouter";
import { optimizedQueryClient } from "./lib/optimizedQueryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { ErrorBoundary } from "@/components/error-handling/error-boundary";
import { initializeGlobalErrorHandler, configureQueryRetry } from "@/lib/globalErrorHandler";
import { ConnectionStatus } from "@/components/error-handling/error-recovery";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { OfflineBanner } from "@/components/ui/offline-banner";
import { UpdatePrompt } from "@/components/pwa/update-prompt";
import { ImpersonationBanner } from "@/components/layout/impersonation-banner";
import { TenantAdminBanner } from "@/components/layout/tenant-admin-banner";
// Rimozione pulsante di aggiornamento manuale
import { OfflineApiProvider } from "@/hooks/use-offline-api";
import { SyncStatus } from "@/components/ui/sync-status";
import { DebugPanel } from "@/components/ui/debug-panel";
import { checkForNewVersion, saveCurrentVersion, getCurrentVersion } from "@/lib/version";
import { pwaUpdateManager } from "@/lib/pwaUpdateManager";
import { useOrientationLock } from "@/hooks/use-orientation-lock";
import { OrientationWarning } from "@/components/ui/orientation-warning";
import NativeExperience from "@/lib/native-experience";
import { serviceWorkerOptimizer } from "@/lib/serviceWorkerOptimizer";
import { runtimeOptimizer } from "@/lib/bundleOptimizer";
import { clientPerformanceMonitor } from "@/lib/performance-monitor-client";
import { slowResourceEliminator } from "@/lib/slow-resource-eliminator";
import { criticalCSSOptimizer } from "@/lib/critical-css-optimizer";
import { radicalPerformanceEliminator } from "@/lib/radical-performance-eliminator";
import { ultimatePerformanceBlocker } from "@/lib/ultimate-performance-blocker";
import NotFound from "@/pages/not-found";
import Login from "@/pages/login";
import Home from "@/pages/home";

// Componenti critici caricati immediatamente
import { AuthProvider } from "@/context/auth-context";
import { useAuth } from "@/context/auth-context";

// Sistema lazy loading intelligente con prefetching ottimizzato
import { useSmartLazy, trackRouteVisit } from '@/lib/smartLazyLoading-fixed';

// CRITICAL COMPONENTS - Caricamento immediato + prefetch automatico
const IncomingGoods = useSmartLazy(() => import('@/pages/incoming-goods'), '/incoming-goods', 
  { priority: 'CRITICAL', prefetch: true, preloadDelay: 500 });
const Containers = useSmartLazy(() => import('@/pages/containers'), '/containers', 
  { priority: 'CRITICAL', prefetch: true, preloadDelay: 1000 });
const Search = useSmartLazy(() => import('@/pages/search'), '/search', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 1500 });

// HIGH PRIORITY COMPONENTS - Prefetch con delay ragionevole  
const Settings = useSmartLazy(() => import('@/pages/settings'), '/settings', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 2000 });
const Profile = useSmartLazy(() => import('@/pages/profile'), '/profile', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 2500 });

// WORKFLOW COMPONENTS - Prefetch medio-alto per UX fluida
const DDTProcessing = useSmartLazy(() => import('@/pages/ddt-processing'), '/ddt-processing', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 3000 });
const ProductLabel = useSmartLazy(() => import('@/pages/product-label'), '/product-label', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 3500 });
const ProductLabelNew = useSmartLazy(() => import('@/pages/product-label/new'), '/product-label/new', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 4000 });
const DDTQRCode = useSmartLazy(() => import('@/pages/ddt-qrcode'), '/ddt-qrcode', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 6000 });

// WORKFLOW CRITICAL - Caricamento immediato per evitare rimounting
import LabelProcessing from '@/pages/label-processing';

// MEDIUM PRIORITY COMPONENTS - Prefetch intelligente basato su usage patterns
const DirectLabel = useSmartLazy(() => import('@/pages/direct-label'), '/direct-label', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 4500 });
const NewContainer = useSmartLazy(() => import('@/pages/new-container'), '/new-container', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 5000 });
const Suppliers = useSmartLazy(() => import('@/pages/suppliers'), '/suppliers', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 2000 });
const Users = useSmartLazy(() => import('@/pages/users'), '/users', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 6000 });

// ADMIN & SETTINGS - Prefetch condizionale per admin users
const ContainerTypes = useSmartLazy(() => import('@/pages/container-types'), '/container-types', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 7000 });
const Prompts = useSmartLazy(() => import('@/pages/prompts'), '/prompts', 
  { priority: 'LOW', prefetch: false });

// DETAILS PAGES - Lazy standard (solo quando necessario)
const DDTDetails = useSmartLazy(() => import('@/pages/ddt-details'), '/ddt-details', 
  { priority: 'LOW', prefetch: false });
const ProductDetails = useSmartLazy(() => import('@/pages/product-details'), '/product-details', 
  { priority: 'LOW', prefetch: false });
const ContainerDetails = useSmartLazy(() => import('@/pages/container/[id]'), '/container/[id]', 
  { priority: 'LOW', prefetch: false });

// UTILITY PAGES - Lazy on-demand loading
const Activities = useSmartLazy(() => import('@/pages/activities'), '/activities', 
  { priority: 'LOW', prefetch: false });
const QRScannerPage = useSmartLazy(() => import('@/pages/qr-scanner'), '/qr-scanner', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 8000 });
const RetiredProducts = useSmartLazy(() => import('@/pages/retired-products'), '/retired-products', 
  { priority: 'LOW', prefetch: false });

// SPECIALIZED WORKFLOW - Lazy loading ottimizzato
const ContainerHistoryScanPage = useSmartLazy(() => import('@/pages/container-history-scan'), '/container-history-scan', 
  { priority: 'LOW', prefetch: false });
const ContainerHistoryScannerPage = useSmartLazy(() => import('@/pages/container-history-scanner'), '/container-history-scanner', 
  { priority: 'LOW', prefetch: false });
const AssociazioneSemplice = useSmartLazy(() => import('@/pages/associazione-semplice'), '/associazione-semplice', 
  { priority: 'LOW', prefetch: false });
const AssociazioneDiretta = useSmartLazy(() => import('@/pages/associazione-diretta'), '/associazione-diretta', 
  { priority: 'LOW', prefetch: false });
// DEVELOPMENT & TESTING - Lazy loading minimal per dev tools
const TestPage = useSmartLazy(() => import('@/pages/test-page'), '/test-page', 
  { priority: 'LOW', prefetch: false });
const TestUpdatePage = useSmartLazy(() => import('@/pages/test-update'), '/test-update', 
  { priority: 'LOW', prefetch: false });
const ApiTest = useSmartLazy(() => import('@/pages/api-test'), '/api-test', 
  { priority: 'LOW', prefetch: false });
const DebugPage = useSmartLazy(() => import('@/pages/debug'), '/debug', 
  { priority: 'LOW', prefetch: false });
const PWAStatusPage = useSmartLazy(() => import('@/pages/pwa-status'), '/pwa-status', 
  { priority: 'LOW', prefetch: false });
const OfflineTestPage = useSmartLazy(() => import('./pages/offline-test'), '/offline-test', 
  { priority: 'LOW', prefetch: false });

// CRITICAL OFFLINE - Import diretto per evitare problemi offline
import OfflineManagerPage from './pages/offline-manager';

// SYSTEM & ADMIN PAGES - Prefetch intelligente per admin
const DiagnosticsPage = useSmartLazy(() => import('./pages/diagnostics'), '/diagnostics', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 10000 });
const SystemStatusPage = useSmartLazy(() => import('./pages/system-status'), '/system-status', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 12000 });
const AdminDashboard = useSmartLazy(() => import('./pages/admin-dashboard'), '/admin-dashboard', 
  { priority: 'HIGH', prefetch: true, preloadDelay: 8000 });
const SecurityAdmin = useSmartLazy(() => import('./pages/security-admin'), '/security-admin', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 15000 });
const PerformanceMonitorPage = useSmartLazy(() => import('./pages/performance-monitor'), '/performance-monitor', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 18000 });
const SystemMonitoringPage = useSmartLazy(() => import('./pages/system-monitoring'), '/system-monitoring', 
  { priority: 'MEDIUM', prefetch: true, preloadDelay: 20000 });

// Loading component riutilizzabile
const LoadingSpinner = () => (
  <div className="h-screen w-full flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
);

function AuthenticatedRoutes() {
  const { isAuthenticated, isLoading, user } = useAuth();

  // Hook per il controllo automatico della versione PWA e performance monitoring
  useEffect(() => {
    // Controlla se c'è una nuova versione disponibile
    const hasNewVersion = checkForNewVersion();
    
    if (hasNewVersion) {
      console.log('🆕 Nuova versione PWA disponibile!');
      // Salva la nuova versione come corrente
      saveCurrentVersion();
      
      // In ambiente di sviluppo, mostra sempre la versione corrente
      if (import.meta.env.DEV) {
        console.log(`📱 Versione PWA corrente: ${getCurrentVersion()}`);
      }
    }

    // Inizializza monitoring performance client
    clientPerformanceMonitor.applyImmediateOptimizations();
  }, []);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Componente di fallback ottimizzato per il lazy loading
  const SuspenseWrapper = ({ children, routePath }: { children: React.ReactNode; routePath?: string }) => {
    // Traccia utilizzo route per ottimizzazioni future
    if (routePath) {
      trackRouteUsage(routePath);
    }
    
    return (
      <Suspense fallback={<LoadingSpinner />}>
        {children}
      </Suspense>
    );
  };

  return (
    <Switch>
      {isAuthenticated ? (
        <>
          {/* Rotte principali - accesso immediato */}
          <Route path="/">
            <SuspenseWrapper><Home /></SuspenseWrapper>
          </Route>
          
          {/* Rotte con lazy loading ottimizzato */}
          <Route path="/incoming-goods">
            <SuspenseWrapper><IncomingGoods /></SuspenseWrapper>
          </Route>
          <Route path="/containers">
            <SuspenseWrapper><Containers /></SuspenseWrapper>
          </Route>
          <Route path="/search">
            <SuspenseWrapper><Search /></SuspenseWrapper>
          </Route>
          <Route path="/profile">
            <SuspenseWrapper><Profile /></SuspenseWrapper>
          </Route>
          <Route path="/settings">
            <SuspenseWrapper><Settings /></SuspenseWrapper>
          </Route>
          
          {/* Rotte amministrative */}
          {user?.isAdmin && (
            <>
              <Route path="/admin-dashboard">
                <SuspenseWrapper><AdminDashboard /></SuspenseWrapper>
              </Route>
              <Route path="/security-admin">
                <SuspenseWrapper><SecurityAdmin /></SuspenseWrapper>
              </Route>
              <Route path="/performance-monitor">
                <SuspenseWrapper><PerformanceMonitorPage /></SuspenseWrapper>
              </Route>
              <Route path="/prompts">
                <SuspenseWrapper><Prompts /></SuspenseWrapper>
              </Route>
              <Route path="/users">
                <SuspenseWrapper><Users /></SuspenseWrapper>
              </Route>
              <Route path="/pwa-status">
                <SuspenseWrapper><PWAStatusPage /></SuspenseWrapper>
              </Route>
              <Route path="/diagnostics">
                <SuspenseWrapper><DiagnosticsPage /></SuspenseWrapper>
              </Route>
              <Route path="/system-status">
                <SuspenseWrapper><SystemStatusPage /></SuspenseWrapper>
              </Route>
              <Route path="/system-monitoring">
                <SuspenseWrapper><SystemMonitoringPage /></SuspenseWrapper>
              </Route>
            </>
          )}
          
          {/* Rotte secondarie */}
          <Route path="/ddt-processing">
            <SuspenseWrapper><DDTProcessing /></SuspenseWrapper>
          </Route>
          <Route path="/ddt-qrcode">
            <SuspenseWrapper><DDTQRCode /></SuspenseWrapper>
          </Route>
          <Route path="/product-label">
            <SuspenseWrapper><ProductLabel /></SuspenseWrapper>
          </Route>
          <Route path="/product-label/new">
            <SuspenseWrapper><ProductLabelNew /></SuspenseWrapper>
          </Route>
          <Route path="/label-processing">
            <LabelProcessing />
          </Route>
          <Route path="/direct-label">
            <SuspenseWrapper><DirectLabel /></SuspenseWrapper>
          </Route>
          <Route path="/new-container">
            <SuspenseWrapper><NewContainer /></SuspenseWrapper>
          </Route>
          <Route path="/suppliers">
            <SuspenseWrapper><Suppliers /></SuspenseWrapper>
          </Route>
          <Route path="/container-types">
            <SuspenseWrapper><ContainerTypes /></SuspenseWrapper>
          </Route>
          <Route path="/activities">
            <SuspenseWrapper><Activities /></SuspenseWrapper>
          </Route>
          <Route path="/retired-products">
            <SuspenseWrapper><RetiredProducts /></SuspenseWrapper>
          </Route>
          <Route path="/ddt-details/:id">
            <SuspenseWrapper><DDTDetails /></SuspenseWrapper>
          </Route>
          <Route path="/product-details/:id">
            <SuspenseWrapper><ProductDetails /></SuspenseWrapper>
          </Route>
          <Route path="/container/:id">
            <SuspenseWrapper><ContainerDetails /></SuspenseWrapper>
          </Route>
          <Route path="/qr-scanner">
            <SuspenseWrapper><QRScannerPage /></SuspenseWrapper>
          </Route>
          <Route path="/container-history-scan">
            <SuspenseWrapper><ContainerHistoryScanPage /></SuspenseWrapper>
          </Route>
          <Route path="/container-history-scanner">
            <SuspenseWrapper><ContainerHistoryScannerPage /></SuspenseWrapper>
          </Route>
          <Route path="/associazione-semplice">
            <SuspenseWrapper><AssociazioneSemplice /></SuspenseWrapper>
          </Route>
          <Route path="/associazione-diretta">
            <SuspenseWrapper><AssociazioneDiretta /></SuspenseWrapper>
          </Route>
          <Route path="/test-page">
            <SuspenseWrapper><TestPage /></SuspenseWrapper>
          </Route>
          <Route path="/test-update">
            <SuspenseWrapper><TestUpdatePage /></SuspenseWrapper>
          </Route>
          <Route path="/api-test">
            <SuspenseWrapper><ApiTest /></SuspenseWrapper>
          </Route>
          <Route path="/debug">
            <SuspenseWrapper><DebugPage /></SuspenseWrapper>
          </Route>
          <Route path="/offline-test">
            <SuspenseWrapper><OfflineTestPage /></SuspenseWrapper>
          </Route>
          <Route path="/performance-monitor">
            <SuspenseWrapper routePath="/performance-monitor"><PerformanceMonitorPage /></SuspenseWrapper>
          </Route>
          <Route path="/offline-manager" component={OfflineManagerPage} />
        </>
      ) : (
        <Route path="*" component={Login} />
      )}
      <Route component={NotFound} />
    </Switch>
  );
}

// Configurazione ultra-ottimizzata con cache unificata
// Sistema integrato: cache unificata + lazy loading + memoization
// Obiettivo: -40% bundle size, -60% initial load, -30% re-renders

function App() {
  // Disabilito blocco orientamento per risolvere problemi con preview Replit
  // useOrientationLock({
  //   orientation: 'portrait-primary',
  //   fallbackWarning: false
  // });

  // Apply simple direct FCP optimization
  useEffect(() => {
    // Apply simple FCP fix
    import('./lib/simple-fcp-fix');
    
    // Inizializza Global Error Handler
    initializeGlobalErrorHandler();
    configureQueryRetry(optimizedQueryClient);
    
    return () => {
      // Cleanup verrà gestito automaticamente
    };
  }, []);

  // Effetto per tracciare la performance e gestire gli aggiornamenti automatici
  useEffect(() => {
    // Inizializza esperienza nativa su mobile
    if (/Mobi|Android/i.test(navigator.userAgent)) {
      NativeExperience.init();
    }
    
    // Importa dinamicamente il gestore degli aggiornamenti (solo in produzione)
    if (process.env.NODE_ENV === 'production') {
      import('@/lib/appUpdateManager').then(({ checkAndUpdateApp }) => {
        // Esegui il controllo automatico degli aggiornamenti all'avvio dell'app
        checkAndUpdateApp();
      });
    }
    
    // Registra metriche di performance solo in modalità sviluppo per evitare overhead in produzione
    if (process.env.NODE_ENV === 'development') {
      window.addEventListener('load', () => {
        // Utilizza setTimeout per consentire il rendering delle metriche dopo che l'app è caricata
        setTimeout(() => {
          if (window.performance) {
            const perfData = window.performance.timing;
            const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
            console.log(`Tempo di caricamento completo: ${pageLoadTime}ms`);
          }
        }, 0);
      });
    }
  }, []);

  return (
    <ErrorBoundary level="critical" name="App Root">
      <QueryClientProvider client={optimizedQueryClient}>
        <TooltipProvider>
          <AuthProvider>
            <OfflineApiProvider>
              {/* Sistema di gestione errori globale */}
              <ConnectionStatus />
              
              {/* Componenti UI di primo piano - mostrati immediatamente */}
              <ErrorBoundary level="page" name="Main Routes">
                <AuthenticatedRoutes />
              </ErrorBoundary>
              
              {/* Componenti UI di secondo piano - caricati dopo il rendering principale */}
              <Toaster />
              <OfflineBanner />
              <UpdatePrompt />
              <ImpersonationBanner />
              <TenantAdminBanner />
              <OrientationWarning />
            </OfflineApiProvider>
          </AuthProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
