import React, { createContext, useContext, useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface OfflineApiContextType {
  isOnline: boolean;
  pendingOperations: number;
  cacheInitialized: boolean;
}

const OfflineApiContext = createContext<OfflineApiContextType | undefined>(undefined);

export function useOfflineApi() {
  const context = useContext(OfflineApiContext);
  if (context === undefined) {
    throw new Error('useOfflineApi must be used within an OfflineApiProvider');
  }
  return context;
}

export function OfflineApiProvider({ children }: { children: React.ReactNode }) {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const { toast } = useToast();

  useEffect(() => {
    const updateOnlineStatus = () => {
      const online = navigator.onLine;
      
      if (online !== isOnline) {
        setIsOnline(online);
        
        if (online) {
          toast({
            title: "Connessione ripristinata",
            description: "Connessione internet ristabilita",
            variant: "default"
          });
        } else {
          toast({
            title: "Connessione persa",
            description: "Verificare la connessione internet",
            variant: "destructive"
          });
        }
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, [isOnline, toast]);

  const value = {
    isOnline,
    pendingOperations: 0,
    cacheInitialized: true
  };

  return (
    <OfflineApiContext.Provider value={value}>
      {children}
    </OfflineApiContext.Provider>
  );
}