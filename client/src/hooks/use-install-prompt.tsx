import { useState, useEffect } from 'react';

interface InstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed', platform: string }>;
}

// Funzione per verificare se il dispositivo è iOS
const isIOSDevice = () => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && 
          !(window as any).MSStream;
};

export function useInstallPrompt() {
  const [installPrompt, setInstallPrompt] = useState<InstallPromptEvent | null>(null);
  const [isAppInstallable, setIsAppInstallable] = useState(false);
  const [isAppInstalled, setIsAppInstalled] = useState(false);
  const [isIOS] = useState(isIOSDevice());

  useEffect(() => {
    // Su iOS, forza sempre l'opzione di installazione
    if (isIOS) {
      setIsAppInstallable(true);
    }
    
    // Funzione per verificare se l'app è già installata
    const checkIfInstalled = () => {
      // Per iOS con Safari (standalone è una proprietà non standard)
      // @ts-ignore - proprietà specifica di Safari su iOS
      const iOSStandalone = window.navigator.standalone;
      
      if (
        iOSStandalone ||
        window.matchMedia('(display-mode: standalone)').matches ||
        window.matchMedia('(display-mode: fullscreen)').matches ||
        window.matchMedia('(display-mode: minimal-ui)').matches
      ) {
        setIsAppInstalled(true);
        return true;
      }
      return false;
    };

    // Verifica iniziale
    const isInstalled = checkIfInstalled();
    if (!isInstalled) {
      // Salva l'evento beforeinstallprompt per usarlo in seguito
      const handleBeforeInstallPrompt = (e: Event) => {
        // Previene che Chrome 67+ mostri automaticamente il prompt
        e.preventDefault();
        // Salva l'evento da utilizzare in seguito
        setInstallPrompt(e as InstallPromptEvent);
        setIsAppInstallable(true);
      };

      // Ascolta il display-mode per sapere quando l'app viene installata
      const mediaQuery = window.matchMedia('(display-mode: standalone)');
      const handleDisplayChange = (e: MediaQueryListEvent) => {
        if (e.matches) {
          setIsAppInstalled(true);
          setIsAppInstallable(false);
        }
      };

      // Ascolta quando l'app viene installata attraverso la UI browser
      const handleAppInstalled = () => {
        setIsAppInstalled(true);
        setIsAppInstallable(false);
        // Pulisci il prompt salvato
        setInstallPrompt(null);
      };

      // Aggiungi event listeners
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      mediaQuery.addEventListener('change', handleDisplayChange);
      window.addEventListener('appinstalled', handleAppInstalled);

      // Cleanup
      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
        mediaQuery.removeEventListener('change', handleDisplayChange);
        window.removeEventListener('appinstalled', handleAppInstalled);
      };
    }
  }, []);

  // Funzione per mostrare il prompt di installazione
  const showInstallPrompt = async () => {
    if (isIOS) {
      // Su iOS non abbiamo un prompt nativo - richiede istruzioni manuali
      console.log('iOS: Prompt di installazione non disponibile, follow istruzioni manuali');
      return;
    }
    
    if (!installPrompt) {
      console.log('Nessun prompt di installazione disponibile');
      return;
    }

    // Mostra il prompt
    installPrompt.prompt();

    // Attendi la scelta dell'utente
    const choiceResult = await installPrompt.userChoice;
    console.log('User choice:', choiceResult.outcome);

    // Resetta il prompt - può essere usato una sola volta
    setInstallPrompt(null);
    
    // Solo per non-iOS, resetta lo stato installable se l'utente rifiuta
    if (!isIOS) {
      setIsAppInstallable(false);
    }

    if (choiceResult.outcome === 'accepted') {
      setIsAppInstalled(true);
    }
  };

  return {
    isAppInstallable,
    isAppInstalled,
    showInstallPrompt
  };
}