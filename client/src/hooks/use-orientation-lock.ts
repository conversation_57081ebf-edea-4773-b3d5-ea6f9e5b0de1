import { useEffect, useCallback } from 'react';

interface OrientationLockOptions {
  orientation?: 'portrait' | 'landscape' | 'portrait-primary' | 'landscape-primary';
  fallbackWarning?: boolean;
}

export function useOrientationLock(options: OrientationLockOptions = {}) {
  const { 
    orientation = 'portrait-primary', 
    fallbackWarning = true 
  } = options;

  const lockOrientation = useCallback(async () => {
    try {
      // Metodo 1: Screen Orientation API (standard moderno)
      if ('screen' in window && 'orientation' in window.screen && 'lock' in window.screen.orientation) {
        try {
          await (window.screen.orientation as any).lock(orientation);
          console.log(`Orientamento bloccato su ${orientation} tramite Screen Orientation API`);
          return true;
        } catch (error) {
          console.log('Screen Orientation API fallita:', error);
        }
      }

      // Metodo 2: Legacy screen.lockOrientation (Android più vecchi)
      const screen = window.screen as any;
      if (screen.lockOrientation) {
        const success = screen.lockOrientation(orientation);
        if (success) {
          console.log(`Orientamento bloccato su ${orientation} tramite lockOrientation legacy`);
          return true;
        }
      }

      // Metodo 3: Vendor-specific per dispositivi più vecchi
      const legacyMethods = [
        'mozLockOrientation',
        'msLockOrientation', 
        'webkitLockOrientation'
      ];

      for (const method of legacyMethods) {
        if (screen[method]) {
          const success = screen[method](orientation);
          if (success) {
            console.log(`Orientamento bloccato su ${orientation} tramite ${method}`);
            return true;
          }
        }
      }

      // Metodo 4: Fallback CSS-only per iOS e dispositivi non supportati
      if (fallbackWarning) {
        console.log('API di blocco orientamento non supportate, usando fallback CSS');
      }
      
      // Aggiungiamo una classe CSS per il blocco visual
      document.documentElement.classList.add('orientation-locked-portrait');
      return false;

    } catch (error) {
      console.error('Errore nel blocco orientamento:', error);
      return false;
    }
  }, [orientation, fallbackWarning]);

  const unlockOrientation = useCallback(() => {
    try {
      // Screen Orientation API unlock
      if ('screen' in window && 'orientation' in window.screen && 'unlock' in window.screen.orientation) {
        (window.screen.orientation as any).unlock();
        console.log('Orientamento sbloccato tramite Screen Orientation API');
        return true;
      }

      // Legacy methods unlock
      const screen = window.screen as any;
      const unlockMethods = [
        'unlockOrientation',
        'mozUnlockOrientation', 
        'msUnlockOrientation',
        'webkitUnlockOrientation'
      ];

      for (const method of unlockMethods) {
        if (screen[method]) {
          screen[method]();
          console.log(`Orientamento sbloccato tramite ${method}`);
          return true;
        }
      }

      // Rimuovi classe CSS fallback
      document.documentElement.classList.remove('orientation-locked-portrait');
      console.log('Orientamento sbloccato (fallback CSS)');
      return false;

    } catch (error) {
      console.error('Errore nello sblocco orientamento:', error);
      return false;
    }
  }, []);

  const detectOrientationChange = useCallback(() => {
    const handleOrientationChange = () => {
      // Riapplica il blocco se l'orientamento cambia
      setTimeout(() => {
        lockOrientation();
      }, 100);
    };

    // Listener per cambio orientamento
    if ('screen' in window && 'orientation' in window.screen) {
      window.screen.orientation.addEventListener('change', handleOrientationChange);
      return () => {
        window.screen.orientation.removeEventListener('change', handleOrientationChange);
      };
    }

    // Fallback per eventi legacy
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);
    
    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, [lockOrientation]);

  useEffect(() => {
    // Applica il blocco orientamento al mount del componente
    lockOrientation();
    
    // Configura listener per cambi orientamento
    const cleanup = detectOrientationChange();
    
    // Cleanup al unmount
    return () => {
      cleanup?.();
      // Non sblocchiamo automaticamente per mantenere il blocco app-wide
    };
  }, [lockOrientation, detectOrientationChange]);

  return {
    lockOrientation,
    unlockOrientation,
    isOrientationLockSupported: () => {
      return !!(
        ('screen' in window && 'orientation' in window.screen && 'lock' in window.screen.orientation) ||
        (window.screen as any).lockOrientation ||
        (window.screen as any).mozLockOrientation ||
        (window.screen as any).msLockOrientation ||
        (window.screen as any).webkitLockOrientation
      );
    }
  };
}