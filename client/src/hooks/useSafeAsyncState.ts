/**
 * Hook per gestire stati asincroni sicuri
 * Previene memory leak e warning di setState dopo unmount
 */

import { useRef, useCallback, useEffect, useState } from 'react';
import { clientLogger } from '@/lib/clientLogger';

/**
 * Hook per gestire setState sicuro che non viene eseguito dopo unmount
 */
export function useSafeAsyncState<T>(initialValue: T) {
  const [state, setState] = useState<T>(initialValue);
  const isMountedRef = useRef(true);

  // Cleanup quando il componente viene smontato
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const setSafeState = useCallback((newState: T | ((prevState: T) => T)) => {
    if (isMountedRef.current) {
      setState(newState);
    } else {
      clientLogger.warn('Tentativo di setState dopo unmount bloccato', {
        component: 'useSafeAsyncState',
        attemptedState: typeof newState === 'function' ? 'function' : newState
      });
    }
  }, []);

  return [state, setSafeState] as const;
}

/**
 * Hook per eseguire effetti asincroni sicuri
 */
export function useSafeAsyncEffect(
  effect: (isMounted: () => boolean) => Promise<void> | void,
  deps?: React.DependencyList
) {
  const isMountedRef = useRef(true);

  useEffect(() => {
    const isMounted = () => isMountedRef.current;
    
    const executeEffect = async () => {
      try {
        await effect(isMounted);
      } catch (error) {
        if (isMountedRef.current) {
          clientLogger.error('Errore in useSafeAsyncEffect', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
          });
        }
      }
    };

    executeEffect();
  }, deps);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
}

/**
 * Hook per gestire promise con cancellazione automatica
 */
export function useCancellablePromise() {
  const pendingPromises = useRef<Set<{ cancel: () => void }>>(new Set());

  const cancellablePromise = useCallback(<T>(promise: Promise<T>) => {
    let isCancelled = false;
    
    const wrappedPromise = new Promise<T>((resolve, reject) => {
      promise.then(
        value => isCancelled ? undefined : resolve(value),
        error => isCancelled ? undefined : reject(error)
      );
    });

    const cancellableObj = {
      promise: wrappedPromise,
      cancel: () => {
        isCancelled = true;
        pendingPromises.current.delete(cancellableObj);
      }
    };

    pendingPromises.current.add(cancellableObj);
    return cancellableObj;
  }, []);

  useEffect(() => {
    return () => {
      // Cancella tutte le promise pendenti quando il componente viene smontato
      pendingPromises.current.forEach(({ cancel }) => cancel());
      pendingPromises.current.clear();
    };
  }, []);

  return cancellablePromise;
}

/**
 * Hook per gestire fetch requests con cancellazione automatica
 */
export function useSafeFetch() {
  const abortControllersRef = useRef<Set<AbortController>>(new Set());

  const safeFetch = useCallback((url: string, options?: RequestInit) => {
    const controller = new AbortController();
    abortControllersRef.current.add(controller);

    const fetchPromise = fetch(url, {
      ...options,
      signal: controller.signal
    }).finally(() => {
      abortControllersRef.current.delete(controller);
    });

    return {
      promise: fetchPromise,
      abort: () => controller.abort()
    };
  }, []);

  useEffect(() => {
    return () => {
      // Abort tutte le fetch requests pendenti
      abortControllersRef.current.forEach(controller => {
        try {
          controller.abort();
        } catch (error) {
          clientLogger.warn('Errore durante abort della fetch request', { error });
        }
      });
      abortControllersRef.current.clear();
    };
  }, []);

  return safeFetch;
}

/**
 * Hook per gestire interval e timeout con cleanup automatico
 */
export function useSafeTimers() {
  const timersRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const intervalsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  const setSafeTimeout = useCallback((callback: () => void, delay: number) => {
    const timer = setTimeout(() => {
      timersRef.current.delete(timer);
      callback();
    }, delay);
    
    timersRef.current.add(timer);
    return timer;
  }, []);

  const setSafeInterval = useCallback((callback: () => void, delay: number) => {
    const interval = setInterval(callback, delay);
    intervalsRef.current.add(interval);
    return interval;
  }, []);

  const clearSafeTimeout = useCallback((timer: NodeJS.Timeout) => {
    clearTimeout(timer);
    timersRef.current.delete(timer);
  }, []);

  const clearSafeInterval = useCallback((interval: NodeJS.Timeout) => {
    clearInterval(interval);
    intervalsRef.current.delete(interval);
  }, []);

  const clearAllTimers = useCallback(() => {
    timersRef.current.forEach(timer => clearTimeout(timer));
    intervalsRef.current.forEach(interval => clearInterval(interval));
    timersRef.current.clear();
    intervalsRef.current.clear();
  }, []);

  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    setSafeTimeout,
    setSafeInterval,
    clearSafeTimeout,
    clearSafeInterval,
    clearAllTimers
  };
}

/**
 * Hook per gestire observer (IntersectionObserver, MutationObserver, etc.) con cleanup
 */
export function useSafeObserver<T>(
  createObserver: () => T & { disconnect: () => void }
) {
  const observerRef = useRef<T & { disconnect: () => void } | null>(null);

  useEffect(() => {
    observerRef.current = createObserver();

    return () => {
      if (observerRef.current) {
        try {
          observerRef.current.disconnect();
        } catch (error) {
          clientLogger.warn('Errore durante disconnect dell\'observer', { error });
        }
        observerRef.current = null;
      }
    };
  }, []);

  return observerRef.current;
}