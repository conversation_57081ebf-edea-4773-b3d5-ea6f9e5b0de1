import { useCallback, useRef, useEffect } from 'react';

/**
 * Hook personalizzato per la gestione sicura di timer e interval
 * Previene memory leak e race condition con pulizia automatica
 */
export function useTimerManager() {
  const timersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const intervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  /**
   * Crea un setTimeout con pulizia automatica
   */
  const setSafeTimeout = useCallback((
    callback: () => void,
    delay: number,
    key?: string
  ): string => {
    const timerId = key || `timeout_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Pulisci il timer precedente se esiste
    if (timersRef.current.has(timerId)) {
      clearTimeout(timersRef.current.get(timerId)!);
    }
    
    const timer = setTimeout(() => {
      try {
        callback();
      } catch (error) {
        console.error(`[TimerManager] Errore in timeout ${timerId}:`, error);
      } finally {
        // Rimuovi dalla mappa dopo l'esecuzione
        timersRef.current.delete(timerId);
      }
    }, delay);
    
    timersRef.current.set(timerId, timer);
    return timerId;
  }, []);

  /**
   * Crea un setInterval con pulizia automatica
   */
  const setSafeInterval = useCallback((
    callback: () => void,
    delay: number,
    key?: string
  ): string => {
    const intervalId = key || `interval_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Pulisci l'interval precedente se esiste
    if (intervalsRef.current.has(intervalId)) {
      clearInterval(intervalsRef.current.get(intervalId)!);
    }
    
    const interval = setInterval(() => {
      try {
        callback();
      } catch (error) {
        console.error(`[TimerManager] Errore in interval ${intervalId}:`, error);
      }
    }, delay);
    
    intervalsRef.current.set(intervalId, interval);
    return intervalId;
  }, []);

  /**
   * Pulisce un timeout specifico
   */
  const clearSafeTimeout = useCallback((timerId: string) => {
    const timer = timersRef.current.get(timerId);
    if (timer) {
      clearTimeout(timer);
      timersRef.current.delete(timerId);
    }
  }, []);

  /**
   * Pulisce un interval specifico
   */
  const clearSafeInterval = useCallback((intervalId: string) => {
    const interval = intervalsRef.current.get(intervalId);
    if (interval) {
      clearInterval(interval);
      intervalsRef.current.delete(intervalId);
    }
  }, []);

  /**
   * Pulisce tutti i timer attivi
   */
  const clearAllTimers = useCallback(() => {
    // Pulisce tutti i timeout
    timersRef.current.forEach((timer) => {
      clearTimeout(timer);
    });
    timersRef.current.clear();

    // Pulisce tutti gli interval
    intervalsRef.current.forEach((interval) => {
      clearInterval(interval);
    });
    intervalsRef.current.clear();
  }, []);

  /**
   * Ottiene informazioni sui timer attivi (per debugging)
   */
  const getActiveTimers = useCallback(() => {
    return {
      timeouts: Array.from(timersRef.current.keys()),
      intervals: Array.from(intervalsRef.current.keys()),
      totalCount: timersRef.current.size + intervalsRef.current.size
    };
  }, []);

  // Pulizia automatica quando il componente viene smontato
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    setSafeTimeout,
    setSafeInterval,
    clearSafeTimeout,
    clearSafeInterval,
    clearAllTimers,
    getActiveTimers
  };
}

/**
 * Hook semplificato per un singolo interval con pulizia automatica
 */
export function useSafeInterval(
  callback: () => void,
  delay: number | null,
  dependencies?: React.DependencyList
) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (delay !== null) {
      intervalRef.current = setInterval(() => {
        try {
          callback();
        } catch (error) {
          console.error('[useSafeInterval] Errore in callback:', error);
        }
      }, delay);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [delay, ...(dependencies || [])]);

  // Pulizia su unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);
}

/**
 * Hook semplificato per un singolo timeout con pulizia automatica
 */
export function useSafeTimeout(
  callback: () => void,
  delay: number | null,
  dependencies?: React.DependencyList
) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (delay !== null) {
      timeoutRef.current = setTimeout(() => {
        try {
          callback();
        } catch (error) {
          console.error('[useSafeTimeout] Errore in callback:', error);
        }
      }, delay);

      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      };
    }
  }, [delay, ...(dependencies || [])]);

  // Pulizia su unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);
}