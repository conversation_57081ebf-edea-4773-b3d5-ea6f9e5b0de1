import { useState, useCallback, useRef, useEffect } from "react";

type CameraOptions = {
  facingMode?: "user" | "environment";
  aspectRatio?: number;
};

export function useCamera() {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [isCameraHealthy, setIsCameraHealthy] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const healthCheckInterval = useRef<NodeJS.Timeout | null>(null);

  // Funzione per verificare lo stato di salute della camera
  const checkCameraHealth = useCallback(() => {
    if (!stream || !videoRef.current) {
      setIsCameraHealthy(false);
      return false;
    }

    const videoTracks = stream.getVideoTracks();
    if (videoTracks.length === 0) {
      console.warn("⚠️ Nessuna traccia video disponibile");
      setIsCameraHealthy(false);
      setError("Camera disconnessa - nessuna traccia video");
      return false;
    }

    const activeTrack = videoTracks[0];
    
    // Verifica che la traccia sia ancora attiva
    if (activeTrack.readyState !== 'live') {
      console.warn("⚠️ Traccia video non più attiva:", activeTrack.readyState);
      setIsCameraHealthy(false);
      setError("Camera non più attiva");
      return false;
    }

    // Verifica che la traccia sia abilitata
    if (!activeTrack.enabled) {
      console.warn("⚠️ Traccia video disabilitata");
      setIsCameraHealthy(false);
      setError("Camera disabilitata");
      return false;
    }

    // Verifica che il video stia effettivamente ricevendo dati
    const video = videoRef.current;
    
    // Permetti un periodo di grazia per il caricamento del video
    // Se il video è in stato di caricamento (readyState < 2), non considerarlo un errore
    if (video.readyState >= 2 && (video.videoWidth === 0 || video.videoHeight === 0)) {
      console.warn("⚠️ Video non riceve dati dalla camera dopo il caricamento");
      setIsCameraHealthy(false);
      setError("Camera non fornisce dati video");
      return false;
    }

    // Verifica che il video non sia in pausa o fermo (solo se ha dimensioni valide)
    if (video.videoWidth > 0 && video.videoHeight > 0 && (video.paused || video.ended)) {
      console.warn("⚠️ Video in pausa o terminato");
      setIsCameraHealthy(false);
      setError("Video della camera in pausa");
      return false;
    }

    // Tutti i controlli sono passati
    setIsCameraHealthy(true);
    setError(null);
    console.log("✅ Camera funziona correttamente");
    return true;
  }, [stream]);

  // Avvia il monitoraggio continuo della camera
  const startHealthMonitoring = useCallback(() => {
    // Ferma il monitoraggio precedente se esiste
    if (healthCheckInterval.current) {
      clearInterval(healthCheckInterval.current);
    }

    // Avvia un nuovo monitoraggio ogni 10 secondi per essere meno aggressivo
    healthCheckInterval.current = setInterval(() => {
      checkCameraHealth();
    }, 10000);

    console.log("🔍 Monitoraggio stato camera avviato");
  }, [checkCameraHealth]);

  // Ferma il monitoraggio della camera
  const stopHealthMonitoring = useCallback(() => {
    if (healthCheckInterval.current) {
      clearInterval(healthCheckInterval.current);
      healthCheckInterval.current = null;
      console.log("⏹️ Monitoraggio stato camera fermato");
    }
  }, []);

  const startCamera = useCallback(async (options: CameraOptions = {}) => {
    try {
      // Prima di richiedere nuovi permessi, fermiamo eventuali stream attivi
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      
      // Verifica del browser e dell'ambiente
      const isSecureContext = window.isSecureContext;
      if (!isSecureContext) {
        throw new Error("Camera requires a secure context (HTTPS or localhost)");
      }
      
      // Controlliamo se l'API mediaDevices è supportata
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Camera API not supported in this browser");
      }

      // Controlliamo se ci sono dispositivi di input video disponibili
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        
        if (videoDevices.length === 0) {
          throw new Error("No camera detected on this device");
        }
        
        console.log(`Found ${videoDevices.length} video input devices`);
      } catch (deviceErr) {
        console.warn("Could not enumerate devices:", deviceErr);
        // Continuiamo comunque, perché potrebbe funzionare ugualmente
      }

      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: options.facingMode || "environment",
          aspectRatio: options.aspectRatio || 1,
          // Aggiunta di altri parametri per migliorare la compatibilità
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
        audio: false,
      };

      console.log("Requesting camera with constraints:", constraints);
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
        .catch((permissionErr) => {
          if (permissionErr.name === 'NotAllowedError') {
            throw new Error("Camera access denied. Please grant permission in your browser settings.");
          }
          if (permissionErr.name === 'NotFoundError') {
            throw new Error("No camera found on this device or camera is already in use.");
          }
          throw permissionErr; // Rilancia altri tipi di errori
        });
        
      console.log("Camera access granted, tracks:", mediaStream.getTracks().length);
      
      // Verifica che ci sia almeno una traccia video attiva
      const videoTracks = mediaStream.getVideoTracks();
      if (videoTracks.length === 0) {
        throw new Error("Could not access video track from camera");
      }

      // Controllo aggiuntivo: verifica che la traccia video sia effettivamente attiva
      const activeTrack = videoTracks[0];
      if (activeTrack.readyState !== 'live') {
        throw new Error("Camera not ready or not accessible");
      }

      // Verifica che la traccia non sia stata terminata
      if (activeTrack.enabled === false) {
        throw new Error("Camera track is disabled");
      }

      console.log("Camera verification passed - track is live and enabled");
      
      setStream(mediaStream);
      setIsActive(true);
      setError(null);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        console.log("Video element updated with new stream");
        
        // Evento per verificare che il video stia effettivamente ricevendo dati
        videoRef.current.onloadeddata = () => {
          console.log("Video data loaded");
        };
        
        // Assicuriamoci che il video si avvii
        try {
          await videoRef.current.play();
          console.log("Video playback started");
          
          // Verifica finale: controllo che il video stia effettivamente ricevendo dati
          const checkVideoData = () => {
            if (videoRef.current && videoRef.current.videoWidth > 0 && videoRef.current.videoHeight > 0) {
              console.log("Camera verification complete - video data confirmed");
              return true;
            }
            return false;
          };

          // Attendiamo un momento per permettere al video di caricare i primi frame
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              if (!checkVideoData()) {
                reject(new Error("Camera not providing video data"));
              } else {
                resolve(true);
              }
            }, 2000);

            // Se il video si carica prima del timeout, risolviamo immediatamente
            if (videoRef.current) {
              videoRef.current.onloadeddata = () => {
                clearTimeout(timeout);
                if (checkVideoData()) {
                  resolve(true);
                } else {
                  reject(new Error("Camera not providing valid video data"));
                }
              };
            }
          });

        } catch (playErr: any) {
          console.error("Error playing video:", playErr);
          throw new Error(`Failed to play video: ${playErr.message || 'Unknown error'}`);
        }
      } else {
        console.warn("Video reference not available");
        throw new Error("Video element not initialized");
      }

      // Avvia il monitoraggio continuo dello stato della camera
      startHealthMonitoring();

      return true;
    } catch (err: any) {
      console.error("Error accessing camera:", err);
      const errorMessage = err.message || 'Please check camera permissions.';
      setError(`Camera error: ${errorMessage}`);
      setIsActive(false);
      return false;
    }
  }, [stream]);

  const stopCamera = useCallback(() => {
    console.log("Stopping camera - cleaning up stream");
    
    // Ferma il monitoraggio dello stato della camera
    stopHealthMonitoring();
    
    if (stream) {
      console.log("Found active stream, stopping all tracks:", stream.getTracks().length);
      stream.getTracks().forEach((track) => {
        console.log("Stopping track:", track.kind, track.readyState);
        track.stop();
      });
      setStream(null);
    }
    setIsActive(false);
    setIsCameraHealthy(false);
    setError(null);
    console.log("Camera stopped successfully and monitoring disabled");
  }, [stream, stopHealthMonitoring]);

  // Cleanup automatico quando il componente si smonta o lo stream cambia
  useEffect(() => {
    return () => {
      console.log("useCamera cleanup: Component unmounting");
      
      // Ferma il monitoraggio
      stopHealthMonitoring();
      
      if (stream) {
        console.log("useCamera cleanup: Stopping remaining stream");
        stream.getTracks().forEach((track) => {
          console.log("useCamera cleanup: Stopping track:", track.kind);
          track.stop();
        });
      }
    };
  }, [stream, stopHealthMonitoring]);

  const switchCamera = useCallback(async () => {
    const currentFacingMode = stream?.getVideoTracks()[0]?.getSettings()?.facingMode;
    const newFacingMode = currentFacingMode === "user" ? "environment" : "user";
    
    stopCamera();
    return startCamera({ facingMode: newFacingMode });
  }, [stream, startCamera, stopCamera]);

  const takePhoto = useCallback((): string | null => {
    if (!videoRef.current || !canvasRef.current || !isActive) {
      return null;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext("2d");

    if (!context) {
      return null;
    }

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Get image as base64 string
    return canvas.toDataURL("image/jpeg");
  }, [isActive]);

  return {
    videoRef,
    canvasRef,
    isActive,
    isCameraHealthy,
    error,
    startCamera,
    stopCamera,
    switchCamera,
    takePhoto,
    checkCameraHealth,
  };
}
