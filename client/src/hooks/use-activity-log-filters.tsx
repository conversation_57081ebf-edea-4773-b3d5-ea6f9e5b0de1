import { useState, useCallback, useEffect } from 'react';
import { ActivityLogFilters } from '@shared/schema';
import { useQuery } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';

// Costanti per ottimizzare la gestione della cache
const ACTIVITY_LOGS_CACHE_KEY = 'activity-logs-cache';
const ACTIVITY_LOGS_CACHE_TIME = 5 * 60 * 1000; // 5 minuti in ms
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // 1 secondo tra i tentativi

export function useActivityLogFilters() {
  const [filters, setFilters] = useState<ActivityLogFilters>({});
  const [localActivities, setLocalActivities] = useState<any[]>([]);
  const [localLoading, setLocalLoading] = useState(false);

  // Costruisci la URL con i parametri di query per i dati
  const buildQueryUrl = useCallback(() => {
    const url = new URL('/api/activity-logs', window.location.origin);
    
    if (filters.startDate) url.searchParams.append('startDate', filters.startDate);
    if (filters.endDate) url.searchParams.append('endDate', filters.endDate);
    if (filters.userId) url.searchParams.append('userId', filters.userId.toString());
    if (filters.containerId) url.searchParams.append('containerId', filters.containerId.toString());
    if (filters.action) url.searchParams.append('action', filters.action);
    
    // Aggiunto parametro di versione per evitare la cache del browser
    url.searchParams.append('v', Date.now().toString());
    
    return url.toString();
  }, [filters]);

  // Costruisci la URL per l'esportazione con i parametri di query correnti
  const buildExportUrl = useCallback((format: 'csv' | 'excel' | 'pdf') => {
    const url = new URL(`/api/activity-logs/export/${format}`, window.location.origin);
    
    if (filters.startDate) url.searchParams.append('startDate', filters.startDate);
    if (filters.endDate) url.searchParams.append('endDate', filters.endDate);
    if (filters.userId) url.searchParams.append('userId', filters.userId.toString());
    if (filters.containerId) url.searchParams.append('containerId', filters.containerId.toString());
    if (filters.action) url.searchParams.append('action', filters.action);
    
    return url.toString();
  }, [filters]);

  // Funzione migliorata per recuperare i dati con gestione ottimizzata della cache
  const fetchActivitiesWithCache = useCallback(async () => {
    setLocalLoading(true);
    let retryCount = 0;
    
    // Funzione per tentare il recupero dati
    const attemptFetch = async (): Promise<any[]> => {
      try {
        // Prova a caricare i dati dalla rete
        console.log('Tentativo di caricamento attività dalla rete');
        const response = await fetch(buildQueryUrl(), {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include' // Assicura che i cookie di autenticazione vengano inviati
        });
        
        // Log dettagliato dello stato della risposta
        console.log(`Risposta API: status=${response.status}, ok=${response.ok}`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Errore API ${response.status}: ${errorText}`);
          throw new Error(`Errore di rete: ${response.status} - ${errorText}`);
        }
        
        const data = await response.json();
        console.log(`Dati ricevuti: ${JSON.stringify(data).substring(0, 200)}...`);
        
        // Archivia i dati più recenti nella cache locale
        try {
          localStorage.setItem(ACTIVITY_LOGS_CACHE_KEY, JSON.stringify({
            timestamp: Date.now(),
            data: data,
            filters: filters
          }));
          console.log('Dati salvati correttamente nella cache locale');
        } catch (storageError) {
          console.warn('Impossibile salvare nella cache locale:', storageError);
        }
        
        console.log(`Recuperate ${Array.isArray(data) ? data.length : 'N/A'} attività dal server`);
        
        // Verifica che i dati siano un array
        if (!Array.isArray(data)) {
          console.error('I dati ricevuti non sono un array:', data);
          return [];
        }
        
        return data;
      } catch (error) {
        console.error('Errore nel recupero attività:', error);
        
        // Se ci sono altri tentativi possibili, riprova
        if (retryCount < RETRY_ATTEMPTS) {
          retryCount++;
          console.log(`Tentativo ${retryCount}/${RETRY_ATTEMPTS} di recupero attività...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          return attemptFetch();
        }
        
        // Altrimenti prova a recuperare dalla cache locale
        console.log('Tentativo di recupero dalla cache locale...');
        try {
          const cachedData = localStorage.getItem(ACTIVITY_LOGS_CACHE_KEY);
          if (cachedData) {
            const { safeJsonParseWithFallback } = await import('@/lib/safe-json');
            const parsed = safeJsonParseWithFallback(cachedData, null);
            if (Array.isArray(parsed.data)) {
              console.log(`Recuperate ${parsed.data.length} attività dalla cache locale (salvata ${new Date(parsed.timestamp).toLocaleString()})`);
              return parsed.data;
            } else {
              console.error('I dati nella cache non sono un array:', parsed.data);
            }
          }
        } catch (cacheError) {
          console.error('Errore nel recupero dalla cache:', cacheError);
        }
        
        // Se tutto fallisce, restituisci un array vuoto
        return [];
      }
    };
    
    const data = await attemptFetch();
    setLocalActivities(data);
    setLocalLoading(false);
    return data;
  }, [buildQueryUrl, filters]);

  // Per ottenere i dati filtrati (mantenuto per compatibilità)
  const { data: activities, isLoading } = useQuery({
    queryKey: ['/api/activity-logs', filters],
    queryFn: fetchActivitiesWithCache,
    staleTime: 30000, // 30 secondi prima di considerare i dati obsoleti
    gcTime: ACTIVITY_LOGS_CACHE_TIME, // Usa gcTime invece di cacheTime (deprecato in React Query v5)
    retry: RETRY_ATTEMPTS,
    retryDelay: RETRY_DELAY,
  });

  // Carica i dati iniziali
  useEffect(() => {
    fetchActivitiesWithCache();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Applica i filtri
  const applyFilters = useCallback((newFilters: ActivityLogFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    queryClient.invalidateQueries({ queryKey: ['/api/activity-logs'] });
    fetchActivitiesWithCache();
  }, [fetchActivitiesWithCache]);

  // Avvia esportazione - ora usa gli endpoint dedicati
  const exportData = useCallback((format: 'csv' | 'excel' | 'pdf') => {
    // Apre direttamente l'URL di esportazione in una nuova scheda
    window.open(buildExportUrl(format), '_blank');
  }, [buildExportUrl]);

  // Resetta i filtri
  const resetFilters = useCallback(() => {
    setFilters({});
    queryClient.invalidateQueries({ queryKey: ['/api/activity-logs'] });
    fetchActivitiesWithCache();
  }, [fetchActivitiesWithCache]);
  
  // Rimuovi un singolo filtro
  const removeFilter = useCallback((key: keyof ActivityLogFilters) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
    queryClient.invalidateQueries({ queryKey: ['/api/activity-logs'] });
    fetchActivitiesWithCache();
  }, [fetchActivitiesWithCache]);

  // Usa i dati dalla query se disponibili, altrimenti usa i dati locali
  const finalActivities = activities || localActivities;
  const finalLoading = isLoading || localLoading;

  return {
    filters,
    applyFilters,
    exportData,
    resetFilters,
    removeFilter,
    activities: finalActivities,
    isLoading: finalLoading
  };
}