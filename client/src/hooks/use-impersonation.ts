import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';

export function useImpersonation() {
  const { user } = useAuth();
  const [isImpersonating, setIsImpersonating] = useState<boolean>(false);

  useEffect(() => {
    const checkImpersonation = async () => {
      if (!user) {
        setIsImpersonating(false);
        return;
      }

      try {
        // Prima controlla localStorage
        const storedImpersonation = localStorage.getItem('user_impersonation');
        if (storedImpersonation) {
          const { safeJsonParse } = await import('@/lib/safe-json');
          const result = safeJsonParse(storedImpersonation);
          if (!result.success) {
            localStorage.removeItem('user_impersonation');
            return;
          }
          const data = result.data;
          if (data && data.timestamp && (Date.now() - data.timestamp < 30 * 60 * 1000)) {
            if (data.userId === user.id) {
              setIsImpersonating(data.isImpersonating);
              return;
            }
          }
          // Rimuovi dati scaduti
          localStorage.removeItem('user_impersonation');
        }

        // Poi verifica con il server
        const response = await fetch('/api/auth/me', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const userData = await response.json();
          const isImpersonatingUser = !!(userData && userData.isImpersonated);
          setIsImpersonating(isImpersonatingUser);
          
          // Salva in localStorage per sincronizzazione
          if (isImpersonatingUser) {
            localStorage.setItem('user_impersonation', JSON.stringify({
              userId: user.id,
              isImpersonating: isImpersonatingUser,
              timestamp: Date.now()
            }));
          }
        }
      } catch (error) {
        console.error('Errore nel controllo impersonazione:', error);
        setIsImpersonating(false);
      }
    };

    checkImpersonation();

    // Controlla periodicamente per cambiamenti
    const interval = setInterval(checkImpersonation, 10000); // ogni 10 secondi
    
    // Listener per cambiamenti in localStorage (sync tra tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user_impersonation') {
        checkImpersonation();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [user]);

  // Applica classe CSS al body
  useEffect(() => {
    if (isImpersonating) {
      document.body.classList.add('impersonation-active');
    } else {
      document.body.classList.remove('impersonation-active');
    }
    
    return () => {
      document.body.classList.remove('impersonation-active');
    };
  }, [isImpersonating]);

  return { isImpersonating };
}