import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ActivityLogFilters } from '@shared/schema';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';

interface ActivityFiltersSummaryProps {
  filters: ActivityLogFilters;
  onRemoveFilter: (key: keyof ActivityLogFilters) => void;
  onResetAll: () => void;
}

export function ActivityFiltersSummary({ 
  filters, 
  onRemoveFilter,
  onResetAll 
}: ActivityFiltersSummaryProps) {
  if (Object.keys(filters).length === 0) {
    return null;
  }

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      'login': 'Accesso',
      'logout': 'Disconnessione',
      'create_ddt': 'Creazione DDT',
      'create_product_label': 'Creazione Etichetta',
      'create_container': 'Creazione Contenitore',
      'process_ddt_ocr': 'Scansione DDT',
      'process_label_ocr': 'Scansione Etichetta',
      'update_profile': 'Aggiornamento Profilo',
    };
    
    return labels[action] || action;
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'd MMMM yyyy', { locale: it });
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="mb-4 p-3 bg-gray-50 rounded-md">
      <div className="flex flex-wrap items-center gap-2">
        <span className="text-sm font-medium text-gray-700">Filtri attivi:</span>
        
        {filters.startDate && (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            Da: {formatDate(filters.startDate)}
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-4 w-4 p-0 ml-1 text-blue-700 hover:text-blue-900 hover:bg-transparent" 
              onClick={() => onRemoveFilter('startDate')}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        )}
        
        {filters.endDate && (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            A: {formatDate(filters.endDate)}
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-4 w-4 p-0 ml-1 text-blue-700 hover:text-blue-900 hover:bg-transparent" 
              onClick={() => onRemoveFilter('endDate')}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        )}
        
        {filters.userId && (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            Utente: ID {filters.userId}
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-4 w-4 p-0 ml-1 text-green-700 hover:text-green-900 hover:bg-transparent" 
              onClick={() => onRemoveFilter('userId')}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        )}
        
        {filters.containerId && (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1">
            Contenitore: ID {filters.containerId}
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-4 w-4 p-0 ml-1 text-purple-700 hover:text-purple-900 hover:bg-transparent" 
              onClick={() => onRemoveFilter('containerId')}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        )}
        
        {filters.action && (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1">
            Azione: {getActionLabel(filters.action)}
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-4 w-4 p-0 ml-1 text-amber-700 hover:text-amber-900 hover:bg-transparent" 
              onClick={() => onRemoveFilter('action')}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        )}
        
        <Button 
          variant="ghost" 
          size="sm" 
          className="ml-auto text-gray-500 hover:text-gray-700 p-1 h-auto text-xs" 
          onClick={onResetAll}
        >
          Rimuovi tutti
        </Button>
      </div>
    </div>
  );
}