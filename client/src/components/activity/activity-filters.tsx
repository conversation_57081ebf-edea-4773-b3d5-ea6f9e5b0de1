import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ActivityLogFilters } from '@shared/schema';
import { CalendarIcon, FilterIcon, XCircleIcon, Download, FileSpreadsheet, FileText } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { useQuery } from '@tanstack/react-query';
import { User, Container } from '@shared/schema';

interface ActivityFiltersProps {
  onApplyFilters: (filters: ActivityLogFilters) => void;
  onExport: (format: 'csv' | 'excel' | 'pdf') => void;
  onReset: () => void;
  filters: ActivityLogFilters;
}

export function ActivityFilters({ onApplyFilters, onExport, onReset, filters }: ActivityFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.startDate ? new Date(filters.startDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.endDate ? new Date(filters.endDate) : undefined
  );
  const [userId, setUserId] = useState<string>(filters.userId?.toString() || '');
  const [containerId, setContainerId] = useState<string>(filters.containerId?.toString() || '');
  const [action, setAction] = useState<string>(filters.action || '');

  const { data: users } = useQuery<User[]>({
    queryKey: ["/api/users"],
    enabled: isOpen
  });

  const { data: containers } = useQuery<Container[]>({
    queryKey: ["/api/containers"],
    enabled: isOpen
  });

  // Lista di azioni disponibili
  const actions = [
    { value: 'login', label: 'Accesso al sistema' },
    { value: 'logout', label: 'Disconnessione dal sistema' },
    { value: 'create_ddt', label: 'Creazione documento di trasporto' },
    { value: 'create_product_label', label: 'Aggiunta etichetta prodotto' },
    { value: 'create_container', label: 'Creazione nuovo contenitore' },
    { value: 'create_supplier', label: 'Creazione nuovo fornitore' },
    { value: 'create_supplier_from_ddt', label: 'Fornitore creato automaticamente da DDT' },
    { value: 'update_supplier', label: 'Aggiornamento dati fornitore' },
    { value: 'process_ddt_ocr', label: 'Elaborazione automatica DDT' },
    { value: 'process_label_ocr', label: 'Elaborazione automatica etichetta' },
    { value: 'link_ddt_supplier', label: 'Collegamento DDT a fornitore' },
    { value: 'update_profile', label: 'Aggiornamento profilo utente' },
    { value: 'add_product_to_container', label: 'Prodotto aggiunto al contenitore' },
    { value: 'remove_product_from_container', label: 'Prodotto rimosso dal contenitore' },
    { value: 'archive_container', label: 'Archiviazione contenitore' },
    { value: 'reset_default_prompts', label: 'Ripristino modelli predefiniti' }
  ];

  const handleApply = () => {
    const newFilters: ActivityLogFilters = {};
    
    if (startDate) newFilters.startDate = startDate.toISOString();
    if (endDate) newFilters.endDate = endDate.toISOString();
    if (userId && userId !== 'all') newFilters.userId = parseInt(userId, 10);
    if (containerId && containerId !== 'all') newFilters.containerId = parseInt(containerId, 10);
    if (action && action !== 'all') newFilters.action = action;
    
    onApplyFilters(newFilters);
    setIsOpen(false);
  };

  const handleReset = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setUserId('all');
    setContainerId('all');
    setAction('all');
    onReset();
    setIsOpen(false);
  };

  const isFiltersActive = Object.keys(filters).length > 0;

  return (
    <div className="mb-4 flex flex-col md:flex-row justify-between items-start md:items-center gap-4 px-4">
      <div className="space-y-1">
        <h2 className="text-lg font-medium">Filtri e Esportazione</h2>
        {isFiltersActive ? (
          <p className="text-sm text-gray-500">Filtri attivi: {Object.keys(filters).length}</p>
        ) : (
          <p className="text-sm text-gray-500">Nessun filtro attivo</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-2 w-full">
        {/* Bottone filtri */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant={isFiltersActive ? "default" : "outline"} size="sm" className="flex items-center justify-center w-full px-3">
              <FilterIcon className="mr-2 h-4 w-4" />
              Filtri
              {isFiltersActive && (
                <span className="ml-1 rounded-full bg-white text-primary text-xs w-5 h-5 flex items-center justify-center">
                  {Object.keys(filters).length}
                </span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <Card className="border-0 shadow-none">
              <CardContent className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="action">Tipo di Attività</Label>
                  <Select value={action} onValueChange={setAction}>
                    <SelectTrigger>
                      <SelectValue placeholder="Tutte le attività" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tutte le attività</SelectItem>
                      {actions.map(a => (
                        <SelectItem key={a.value} value={a.value}>{a.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="user">Utente</Label>
                  <Select value={userId} onValueChange={setUserId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Tutti gli utenti" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tutti gli utenti</SelectItem>
                      {users?.map(user => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.username}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="container">Contenitore</Label>
                  <Select value={containerId} onValueChange={setContainerId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Tutti i contenitori" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tutti i contenitori</SelectItem>
                      {containers?.map(container => (
                        <SelectItem key={container.id} value={container.id.toString()}>
                          {container.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Data Inizio</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "d MMMM yyyy", { locale: it }) : "Seleziona data inizio"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>Data Fine</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "d MMMM yyyy", { locale: it }) : "Seleziona data fine"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="flex justify-between pt-2">
                  <Button variant="outline" size="sm" onClick={handleReset}>
                    <XCircleIcon className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button size="sm" onClick={handleApply}>
                    Applica Filtri
                  </Button>
                </div>
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>

        {/* Bottone export CSV */}
        <Button variant="outline" size="sm" onClick={() => onExport('csv')} className="flex items-center justify-center w-full px-3">
          <FileText className="mr-2 h-4 w-4" />
          CSV
        </Button>

        {/* Bottone export Excel */}
        <Button variant="outline" size="sm" onClick={() => onExport('excel')} className="flex items-center justify-center w-full px-3">
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          Excel
        </Button>

        {/* Bottone export PDF */}
        <Button variant="outline" size="sm" onClick={() => onExport('pdf')} className="flex items-center justify-center w-full px-3">
          <Download className="mr-2 h-4 w-4" />
          PDF
        </Button>
      </div>
    </div>
  );
}