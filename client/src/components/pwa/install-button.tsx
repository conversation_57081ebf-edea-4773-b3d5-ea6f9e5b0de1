import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, ArrowDown } from "lucide-react";
import { useInstallPrompt } from "@/hooks/use-install-prompt";
import { useToast } from "@/hooks/use-toast";

interface InstallButtonProps {
  variant?: "default" | "subtle" | "icon" | "banner";
  className?: string;
}

export function InstallButton({ 
  variant = "default",
  className = "" 
}: InstallButtonProps) {
  const { isAppInstallable, isAppInstalled, showInstallPrompt } = useInstallPrompt();
  const { toast } = useToast();

  // Verifica se siamo su iOS
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && 
                !(window as any).MSStream;

  // Se l'app non è installabile o è già installata, non mostrare nulla
  if (!isAppInstallable || isAppInstalled) {
    return null;
  }

  const handleInstallClick = () => {
    if (isIOS) {
      // Mostra istruzioni specifiche per iOS
      toast({
        title: "Installazione su iOS",
        description: "Tocca l'icona di condivisione (▲) in basso e seleziona 'Aggiungi a Home'",
        duration: 8000,
      });
    } else {
      // Per altri dispositivi, usa la funzione standard
      showInstallPrompt();
      toast({
        title: "Installazione",
        description: "Segui le istruzioni per installare l'app sul tuo dispositivo",
        duration: 4000,
      });
    }
  };

  // Rendering condizionale in base alla variante richiesta
  if (variant === "icon") {
    return (
      <Button
        size="icon"
        variant="ghost"
        onClick={handleInstallClick}
        className={`rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 ${className}`}
        aria-label="Installa l'app"
      >
        <div className="w-9 h-9 overflow-visible" style={{ 
          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.25))',
          transform: 'translateZ(5px)',
          perspective: '100px'
        }}>
          <img 
            src="icons/icon.png" 
            alt="HACCP Tracker icon" 
            className="w-full h-full object-contain"
            style={{ 
              filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.25))'
            }}
            onError={(e) => {
              console.error("Errore caricamento icona:", e);
            }}
          />
        </div>
      </Button>
    );
  }

  if (variant === "subtle") {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={handleInstallClick}
        className={`text-xs flex items-center gap-1 ${className}`}
      >
        <div className="w-5 h-5 overflow-visible" style={{ 
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.15))', 
          transform: 'translateZ(2px)'
        }}>
          <img 
            src="icons/icon.png" 
            alt="HACCP Tracker icon" 
            className="w-full h-full object-contain"
            style={{ 
              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))'
            }}
            onError={(e) => {
              console.error("Errore caricamento icona:", e);
            }}
          />
        </div>
        <span>Installa l'app</span>
      </Button>
    );
  }

  if (variant === "banner") {
    // Diversi contenuti in base al dispositivo
    const bannerTitle = isIOS 
      ? "Aggiungi alla schermata Home" 
      : "Installa HACCP Tracker";
    
    const bannerDesc = isIOS 
      ? "Tocca ▲ e poi 'Aggiungi a Home'" 
      : "Utilizzalo anche offline";
      
    const buttonText = isIOS
      ? "Come fare"
      : "Installa";
    
    return (
      <div className={`bg-gradient-to-r from-teal-600 to-teal-500 text-white p-3 rounded-lg shadow-md ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-14 h-14 flex items-center justify-center" style={{ 
              filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.35))',
              transform: 'translateZ(12px) scale(1.05)',
              perspective: '100px',
              position: 'relative',
              marginLeft: '-5px',
              overflow: 'visible'
            }}>
              <img 
                src="icons/icon.png" 
                alt="HACCP Tracker icon" 
                className="w-full h-full object-contain"
                style={{ 
                  filter: 'drop-shadow(0 3px 4px rgba(0,0,0,0.25))'
                }}
                onError={(e) => {
                  console.error("Errore caricamento icona:", e);
                }}
              />
            </div>
            <div>
              <p className="text-sm font-medium">{bannerTitle}</p>
              <p className="text-xs opacity-90">{bannerDesc}</p>
            </div>
          </div>
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={handleInstallClick}
            className="bg-white/20 hover:bg-white/30 text-white border-0"
          >
            {buttonText}
          </Button>
        </div>
      </div>
    );
  }

  // Default button
  return (
    <Button
      onClick={handleInstallClick}
      className={`flex items-center gap-2 ${className}`}
    >
      <div className="w-6 h-6 overflow-visible" style={{ 
        filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.2))',
        transform: 'translateZ(4px)',
        perspective: '100px'
      }}>
        <img 
          src="icons/icon.png" 
          alt="HACCP Tracker icon" 
          className="w-full h-full object-contain"
          style={{ 
            filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.2))'
          }}
          onError={(e) => {
            console.error("Errore caricamento icona:", e);
          }}
        />
      </div>
      <span>Installa App</span>
    </Button>
  );
}