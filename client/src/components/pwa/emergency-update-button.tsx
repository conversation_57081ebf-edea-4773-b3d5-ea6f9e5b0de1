import React from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

/**
 * Pulsante di emergenza per forzare l'aggiornamento del service worker
 * quando il metodo standard non funziona
 */
export function EmergencyUpdateButton() {
  const { toast } = useToast();

  const forceUpdate = async () => {
    try {
      // Mostro toast per informare l'utente
      toast({
        title: "Aggiornamento in corso",
        description: "Tentativo di aggiornamento forzato...",
        duration: 3000
      });

      if ('serviceWorker' in navigator) {
        // 1. Ottieni tutte le registrazioni dei service worker
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        if (registrations.length === 0) {
          toast({
            title: "Nessun service worker attivo",
            description: "Ricarico la pagina...",
            duration: 2000
          });
          
          // Se non ci sono service worker, ricarica semplicemente la pagina
          setTimeout(() => {
            window.location.reload();
          }, 1000);
          
          return;
        }
        
        // Flag per tracciare se almeno un'azione è stata intrapresa
        let actionTaken = false;
        
        // 2. Per ogni service worker trovato, invia il comando di aggiornamento in entrambi i formati
        for (const registration of registrations) {
          if (registration.waiting) {
            actionTaken = true;
            
            // Prova entrambi i metodi di aggiornamento per maggiore compatibilità
            try {
              // Metodo 1: Oggetto standard
              registration.waiting.postMessage({ type: 'SKIP_WAITING' });
              
              // Metodo 2: Stringa diretta (alcuni browser potrebbero gestire meglio questo)
              setTimeout(() => {
                if (registration.waiting) {
                  registration.waiting.postMessage('SKIP_WAITING');
                }
              }, 100);
              
              console.log('Richieste di aggiornamento forzato inviate al service worker');
            } catch (err) {
              console.error('Errore nell\'invio del messaggio di aggiornamento forzato:', err);
            }
          } else {
            // Se non c'è un worker in attesa, forza un aggiornamento
            await registration.update();
          }
        }

        // 3. Ascolta per cambio di controller e ricarica la pagina quando succede
        let hasReloaded = false;
        
        const handleControllerChange = () => {
          if (!hasReloaded) {
            hasReloaded = true;
            navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
            console.log('Nuovo service worker in controllo, ricaricando pagina');
            window.location.reload();
          }
        };
        
        navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
        
        // 4. Se nessuna azione è stata intrapresa, ricarica comunque la pagina dopo un breve timeout
        if (!actionTaken) {
          toast({
            title: "Aggiornamento forzato",
            description: "Ricaricamento pagina...",
            duration: 2000
          });
          
          setTimeout(() => {
            if (!hasReloaded) {
              window.location.reload();
            }
          }, 2000);
        } else {
          // Se azioni sono state intraprese ma non c'è stato aggiornamento dopo 3 secondi, ricarica comunque
          setTimeout(() => {
            if (!hasReloaded) {
              toast({
                title: "Aggiornamento completato",
                description: "Ricaricamento pagina..."
              });
              
              window.location.reload();
            }
          }, 3000);
        }
      } else {
        // Service worker non supportato, semplicemente ricarica la pagina
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (error) {
      console.error("Errore durante l'aggiornamento forzato:", error);
      toast({
        title: "Errore durante l'aggiornamento",
        description: "Ricarico comunque la pagina...",
        variant: "destructive",
        duration: 2000
      });
      
      // In caso di errore, ricarica comunque la pagina dopo un breve timeout
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }
  };

  return (
    <Button
      size="sm"
      variant="destructive"
      onClick={forceUpdate}
      className="fixed bottom-3 right-3 z-[9999] flex items-center gap-1 shadow-lg"
    >
      <RefreshCw className="h-4 w-4 animate-spin" />
      <span>Aggiorna App</span>
    </Button>
  );
}

export default EmergencyUpdateButton;