import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { updateServiceWorker } from "@/lib/pwaManager";
import { useTimerManager } from "@/hooks/useTimerManager";

export function UpdatePrompt() {
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [swVersion, setSwVersion] = useState<string | null>(null);
  const { toast } = useToast();
  const { setTimerId, clearAllTimers } = useTimerManager();

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      // Ottieni la registrazione attuale del Service Worker
      navigator.serviceWorker.getRegistration().then(reg => {
        if (reg) {
          setSwRegistration(reg);
          
          // Controlla se c'è un worker in attesa o in installazione
          if (reg.waiting) {
            setIsUpdateAvailable(true);
            getServiceWorkerVersion(reg.waiting);
            
            // Avvia l'aggiornamento automatico dopo un breve ritardo
            const updateTimerId1 = setTimerId(() => {
              updateApp(reg);
            }, 3000, 'timeout');
          }
          
          if (reg.installing) {
            // Monitora lo stato del worker in installazione
            reg.installing.addEventListener('statechange', (event) => {
              const sw = event.target as ServiceWorker;
              if (sw.state === 'installed' && navigator.serviceWorker.controller) {
                setIsUpdateAvailable(true);
                getServiceWorkerVersion(sw);
                
                // Avvia l'aggiornamento automatico dopo un breve ritardo
                const updateTimerId2 = setTimerId(() => {
                  updateApp(reg);
                }, 3000, 'timeout');
              }
            });
          }
          
          // Monitora gli aggiornamenti futuri
          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing;
            if (newWorker) {
              console.log('Nuovo service worker in fase di installazione');
              newWorker.addEventListener('statechange', (event) => {
                const sw = event.target as ServiceWorker;
                if (sw.state === 'installed' && navigator.serviceWorker.controller) {
                  console.log('Nuovo service worker installato, aggiornamento disponibile');
                  setIsUpdateAvailable(true);
                  getServiceWorkerVersion(sw);
                  
                  // Avvia l'aggiornamento automatico dopo un breve ritardo
                  const updateTimerId3 = setTimerId(() => {
                    updateApp(reg);
                  }, 3000, 'timeout');
                }
              });
            }
          });
        }
      }).catch(error => {
        console.error('Errore nel recupero registrazione service worker:', error);
        toast({
          title: 'Errore aggiornamento',
          description: 'Impossibile verificare aggiornamenti app.',
          variant: 'destructive',
        });
      });
      
      // Ascolta i messaggi dal service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
          setIsUpdateAvailable(true);
          if (event.data.version) {
            setSwVersion(event.data.version);
          }
          
          // Avvio automatico dell'aggiornamento
          navigator.serviceWorker.getRegistration().then(reg => {
            if (reg) {
              const updateTimerId4 = setTimerId(() => {
                updateApp(reg);
              }, 3000, 'timeout');
            }
          }).catch(error => {
            console.error('Errore durante aggiornamento automatico:', error);
            toast({
              title: 'Errore aggiornamento',
              description: 'Impossibile avviare aggiornamento automatico.',
              variant: 'destructive',
            });
          });
        }
      });
    }
    
    // Cleanup dei timer quando il componente viene smontato
    return () => clearAllTimers();
  }, []);
  
  // Funzione per ottenere la versione del Service Worker
  const getServiceWorkerVersion = (worker: ServiceWorker) => {
    const messageChannel = new MessageChannel();
    
    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.version) {
        setSwVersion(event.data.version);
      }
    };
    
    worker.postMessage({
      type: 'GET_VERSION'
    }, [messageChannel.port2]);
  };
  
  // Funzione per attivare l'aggiornamento con pieno supporto per feedback
  const updateApp = (registration: ServiceWorkerRegistration) => {
    try {
      // Mostra il toast informativo all'inizio del processo
      toast({
        title: 'Aggiornamento in corso',
        description: 'Nuovo aggiornamento disponibile, installazione in corso...',
        duration: 3000
      });
      
      if (registration.waiting) {
        // Invia un messaggio al worker in attesa per attivarlo (formato oggetto)
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        
        // Invia anche come stringa per maggiore compatibilità
        const compatTimerId = setTimerId(() => {
          if (registration.waiting) {
            registration.waiting.postMessage('SKIP_WAITING');
          }
        }, 100, 'timeout');
      }
      
      // Ricarica la pagina quando il nuovo service worker prende il controllo
      let hasReloaded = false;
      
      const controllerChangeHandler = () => {
        if (!hasReloaded) {
          hasReloaded = true;
          console.log('Nuovo service worker in controllo, ricaricando la pagina');
          
          // Toast finale prima del ricaricamento
          toast({
            title: 'Aggiornamento completato',
            description: 'Applicazione aggiornata con successo!',
            duration: 2000
          });
          
          // Rimuovi l'event listener per evitare chiamate multiple
          navigator.serviceWorker.removeEventListener('controllerchange', controllerChangeHandler);
          
          // Breve ritardo per consentire la visualizzazione del toast
          const reloadTimerId = setTimerId(() => {
            window.location.reload();
          }, 1000, 'timeout');
        }
      };
      
      navigator.serviceWorker.addEventListener('controllerchange', controllerChangeHandler);
      
      // Timeout di sicurezza in caso di mancata risposta del service worker
      const safetyTimerId = setTimerId(() => {
        if (!hasReloaded) {
          console.log('Timeout durante l\'aggiornamento, ricaricamento forzato');
          toast({
            title: 'Aggiornamento in corso',
            description: 'Completamento aggiornamento...',
            duration: 2000
          });
          window.location.reload();
        }
      }, 6000, 'timeout');
    } catch (error) {
      console.error('Errore durante l\'aggiornamento:', error);
      toast({
        title: 'Errore durante l\'aggiornamento',
        description: 'Riprova tra qualche istante',
        variant: 'destructive',
        duration: 4000
      });
    }
  };
  
  // Mostra il prompt di aggiornamento solo se c'è un aggiornamento disponibile
  if (!isUpdateAvailable) {
    return null;
  }
  
  return (
    <div className="fixed bottom-20 left-0 right-0 mx-auto w-[90%] max-w-md z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
          <div>
            <h4 className="text-sm font-medium">Aggiornamento in corso</h4>
            {swVersion && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Versione {swVersion} in fase di installazione
              </p>
            )}
          </div>
        </div>
        <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      </div>
    </div>
  );
}