import React from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { forceAppUpdate } from "@/lib/appUpdateManager";

/**
 * Questo componente mostra un pulsante che forza il ricaricamento della pagina
 * Utile quando il normale meccanismo di aggiornamento del service worker non funziona
 */
export function ForceUpdateButton() {
  const handleForceUpdate = () => {
    // Usa la funzione centralizzata di aggiornamento forzato
    forceAppUpdate();
  };

  // Questo pulsante sarà visibile solo in produzione
  if (process.env.NODE_ENV !== 'production') {
    return null;
  }

  return (
    <Button
      variant="destructive"
      size="sm"
      className="fixed bottom-4 right-4 z-50 shadow-lg"
      onClick={handleForceUpdate}
    >
      <RefreshCw className="mr-2 h-4 w-4" />
      Forza Aggiornamento
    </Button>
  );
}

export default ForceUpdateButton;