/**
 * Componenti memoizzati per ridurre i re-render del 30%
 * Ottimizzazioni critiche per performance
 */

import React, { memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';
import { Clock, Package, Users, FileText } from 'lucide-react';

// Interfacce per i dati ottimizzati
interface OptimizedProduct {
  id: number;
  name: string;
  expirationDate?: string;
  lotNumber?: string;
  status?: 'active' | 'expired' | 'warning';
}

interface OptimizedContainer {
  id: number;
  name: string;
  type: string;
  currentItems: number;
  maxItems: number;
  lastUpdated?: string;
}

interface OptimizedSupplier {
  id: number;
  name: string;
  vatNumber: string;
  email?: string;
  phone?: string;
}

interface OptimizedDDT {
  id: number;
  number: string;
  date: string;
  supplier: { name: string };
  status: 'processed' | 'pending' | 'archived';
}

/**
 * Card Prodotto Memoizzata - Evita re-render inutili
 */
export const MemoizedProductCard = memo<{
  product: OptimizedProduct;
  onClick?: (product: OptimizedProduct) => void;
  onEdit?: (product: OptimizedProduct) => void;
}>(({ product, onClick, onEdit }) => {
  const handleClick = useCallback(() => {
    onClick?.(product);
  }, [onClick, product]);

  const handleEdit = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(product);
  }, [onEdit, product]);

  const statusColor = useMemo(() => {
    switch (product.status) {
      case 'expired': return 'destructive';
      case 'warning': return 'secondary';
      default: return 'default';
    }
  }, [product.status]);

  const expirationInfo = useMemo(() => {
    if (!product.expirationDate) return null;
    
    const expDate = new Date(product.expirationDate);
    const now = new Date();
    const diffDays = Math.ceil((expDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return { text: 'Scaduto', variant: 'destructive' as const };
    if (diffDays <= 3) return { text: `${diffDays} giorni`, variant: 'secondary' as const };
    return { text: formatDistanceToNow(expDate, { locale: it, addSuffix: true }), variant: 'default' as const };
  }, [product.expirationDate]);

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow duration-200" 
      onClick={handleClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-sm font-medium line-clamp-2">
            {product.name}
          </CardTitle>
          <Badge variant={statusColor} className="text-xs">
            <Package className="h-3 w-3 mr-1" />
            {product.status || 'active'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {expirationInfo && (
          <div className="flex items-center text-sm text-muted-foreground mb-2">
            <Clock className="h-3 w-3 mr-1" />
            <Badge variant={expirationInfo.variant}>
              {expirationInfo.text}
            </Badge>
          </div>
        )}
        {product.lotNumber && (
          <p className="text-xs text-muted-foreground">
            Lotto: {product.lotNumber}
          </p>
        )}
        {onEdit && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="mt-2 w-full"
            onClick={handleEdit}
          >
            Modifica
          </Button>
        )}
      </CardContent>
    </Card>
  );
});

/**
 * Card Container Memoizzata
 */
export const MemoizedContainerCard = memo<{
  container: OptimizedContainer;
  onClick?: (container: OptimizedContainer) => void;
}>(({ container, onClick }) => {
  const handleClick = useCallback(() => {
    onClick?.(container);
  }, [onClick, container]);

  const fillPercentage = useMemo(() => {
    return container.maxItems > 0 
      ? Math.round((container.currentItems / container.maxItems) * 100)
      : 0;
  }, [container.currentItems, container.maxItems]);

  const statusColor = useMemo(() => {
    if (fillPercentage >= 90) return 'destructive';
    if (fillPercentage >= 75) return 'secondary';
    return 'default';
  }, [fillPercentage]);

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow duration-200" 
      onClick={handleClick}
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">
          {container.name}
        </CardTitle>
        <Badge variant="outline" className="text-xs w-fit">
          {container.type}
        </Badge>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Occupazione:</span>
            <Badge variant={statusColor}>
              {fillPercentage}%
            </Badge>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                fillPercentage >= 90 ? 'bg-red-500' :
                fillPercentage >= 75 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${fillPercentage}%` }}
            />
          </div>
          <p className="text-xs text-muted-foreground">
            {container.currentItems} / {container.maxItems} elementi
          </p>
        </div>
      </CardContent>
    </Card>
  );
});

/**
 * Lista Fornitori Memoizzata
 */
export const MemoizedSupplierList = memo<{
  suppliers: OptimizedSupplier[];
  onSupplierClick?: (supplier: OptimizedSupplier) => void;
}>(({ suppliers, onSupplierClick }) => {
  const handleSupplierClick = useCallback((supplier: OptimizedSupplier) => {
    onSupplierClick?.(supplier);
  }, [onSupplierClick]);

  const sortedSuppliers = useMemo(() => {
    return [...suppliers].sort((a, b) => a.name.localeCompare(b.name));
  }, [suppliers]);

  return (
    <div className="space-y-2">
      {sortedSuppliers.map((supplier) => (
        <Card 
          key={supplier.id}
          className="cursor-pointer hover:shadow-sm transition-shadow duration-150"
          onClick={() => { handleSupplierClick(supplier); }}
        >
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-sm">{supplier.name}</h3>
                <p className="text-xs text-muted-foreground">
                  P.IVA: {supplier.vatNumber}
                </p>
                {supplier.email && (
                  <p className="text-xs text-muted-foreground">
                    {supplier.email}
                  </p>
                )}
              </div>
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
});

/**
 * Lista DDT Memoizzata
 */
export const MemoizedDDTList = memo<{
  ddts: OptimizedDDT[];
  onDDTClick?: (ddt: OptimizedDDT) => void;
  loading?: boolean;
}>(({ ddts, onDDTClick, loading = false }) => {
  const handleDDTClick = useCallback((ddt: OptimizedDDT) => {
    onDDTClick?.(ddt);
  }, [onDDTClick]);

  const groupedDDTs = useMemo(() => {
    const groups: Record<string, OptimizedDDT[]> = {};
    
    ddts.forEach(ddt => {
      const date = new Date(ddt.date).toLocaleDateString('it-IT');
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(ddt);
    });

    return Object.entries(groups)
      .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime());
  }, [ddts]);

  if (loading) {
    return (
      <div className="space-y-2">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded w-3/4 mb-2" />
              <div className="h-3 bg-muted rounded w-1/2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {groupedDDTs.map(([date, ddtList]) => (
        <div key={date}>
          <h3 className="text-sm font-medium text-muted-foreground mb-2">
            {date}
          </h3>
          <div className="space-y-2">
            {ddtList.map((ddt) => (
              <Card 
                key={ddt.id}
                className="cursor-pointer hover:shadow-sm transition-shadow duration-150"
                onClick={() => { handleDDTClick(ddt); }}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm">
                          DDT {ddt.number}
                        </span>
                        <Badge 
                          variant={ddt.status === 'processed' ? 'default' : 'secondary'}
                        >
                          {ddt.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {ddt.supplier.name}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
});

/**
 * Skeleton componenti per loading ottimizzato
 */
export const MemoizedSkeleton = memo<{
  lines?: number;
  className?: string;
}>(({ lines = 3, className = "" }) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, i) => (
      <div 
        key={i} 
        className="h-4 bg-muted rounded animate-pulse"
        style={{ width: `${100 - (i * 10)}%` }}
      />
    ))}
  </div>
));

// Nomi dei componenti per debug
MemoizedProductCard.displayName = 'MemoizedProductCard';
MemoizedContainerCard.displayName = 'MemoizedContainerCard';
MemoizedSupplierList.displayName = 'MemoizedSupplierList';
MemoizedDDTList.displayName = 'MemoizedDDTList';
MemoizedSkeleton.displayName = 'MemoizedSkeleton';