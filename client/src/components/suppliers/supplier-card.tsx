import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Supplier } from "@shared/schema";
import { Eye, Edit } from "lucide-react";

interface SupplierCardProps {
  supplier: Supplier;
  onViewDetails: (supplier: Supplier) => void;
  onEditSupplier: (supplier: Supplier) => void;
}

export function SupplierCard({ supplier, onViewDetails, onEditSupplier }: SupplierCardProps) {
  return (
    <Card className="bg-white">
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold text-gray-800">
          {supplier.companyName}
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          P.IVA: {supplier.vatNumber}
        </p>
        <p className="text-sm text-gray-500">
          {supplier.address}
        </p>
        <div className="mt-2 flex justify-end space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-sm font-medium text-primary-700"
            onClick={() => onViewDetails(supplier)}
          >
            <Eye className="h-4 w-4 mr-1" />
            Dettagli
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-sm font-medium text-amber-600"
            onClick={() => onEditSupplier(supplier)}
          >
            <Edit className="h-4 w-4 mr-1" />
            Modifica
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
