/**
 * Test Suite per Header Component
 * 
 * @description Test completi per il componente Header includendo:
 * - Rendering condizionale base su autenticazione
 * - Interazioni utente (menu, logout)
 * - Responsive behavior
 * - Integration con routing
 * <AUTHOR> di Testing Automatizzato HACCP Tracker
 * @version 1.0.0 - Test Header component
 */

import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockUser, mockAdminUser, mockFetch } from '../../../tests/utils/test-utils';

// Mock del componente Header (lo creeremo se non esiste)
const MockHeader: React.FC<{ user?: typeof mockUser }> = ({ user }) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  const handleLogout = async () => {
    // Simulazione logout
    await fetch('/api/auth/logout', { method: 'POST' });
  };

  if (!user) {
    return (
      <header data-testid="header-unauthenticated">
        <h1>HACCP Tracker</h1>
        <div>Please log in</div>
      </header>
    );
  }

  return (
    <header data-testid="header-authenticated" className="flex justify-between items-center p-4">
      <h1 data-testid="app-title">HACCP Tracker</h1>
      
      <div className="flex items-center gap-4">
        <span data-testid="user-name">{user.username}</span>
        
        {user.isAdmin && (
          <span data-testid="admin-badge" className="bg-red-500 text-white px-2 py-1 rounded">
            Admin
          </span>
        )}
        
        <button
          data-testid="menu-toggle"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="md:hidden"
        >
          Menu
        </button>
        
        <nav className={`${isMenuOpen ? 'block' : 'hidden'} md:block`}>
          <ul className="flex gap-4">
            <li><a href="/dashboard" data-testid="nav-dashboard">Dashboard</a></li>
            <li><a href="/containers" data-testid="nav-containers">Containers</a></li>
            <li><a href="/products" data-testid="nav-products">Products</a></li>
            {user.isAdmin && (
              <li><a href="/admin-dashboard" data-testid="nav-admin">Admin</a></li>
            )}
          </ul>
        </nav>
        
        <button
          data-testid="logout-button"
          onClick={handleLogout}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Logout
        </button>
      </div>
    </header>
  );
};

describe('Header Component', () => {
  beforeEach(() => {
    // Reset mocks prima di ogni test
    jest.clearAllMocks();
  });

  describe('Unauthenticated State', () => {
    it('should render login prompt when user is not authenticated', () => {
      render(<MockHeader />);
      
      expect(screen.getByTestId('header-unauthenticated')).toBeInTheDocument();
      expect(screen.getByText('Please log in')).toBeInTheDocument();
      expect(screen.queryByTestId('logout-button')).not.toBeInTheDocument();
    });

    it('should display app title even when unauthenticated', () => {
      render(<MockHeader />);
      
      expect(screen.getByText('HACCP Tracker')).toBeInTheDocument();
    });
  });

  describe('Authenticated State - Regular User', () => {
    it('should render authenticated header for regular user', () => {
      render(<MockHeader user={mockUser} />);
      
      expect(screen.getByTestId('header-authenticated')).toBeInTheDocument();
      expect(screen.getByTestId('user-name')).toHaveTextContent(mockUser.username);
      expect(screen.getByTestId('logout-button')).toBeInTheDocument();
    });

    it('should not show admin badge for regular user', () => {
      render(<MockHeader user={mockUser} />);
      
      expect(screen.queryByTestId('admin-badge')).not.toBeInTheDocument();
    });

    it('should not show admin navigation for regular user', () => {
      render(<MockHeader user={mockUser} />);
      
      expect(screen.queryByTestId('nav-admin')).not.toBeInTheDocument();
    });

    it('should show standard navigation items', () => {
      render(<MockHeader user={mockUser} />);
      
      expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('nav-containers')).toBeInTheDocument();
      expect(screen.getByTestId('nav-products')).toBeInTheDocument();
    });
  });

  describe('Authenticated State - Admin User', () => {
    it('should render authenticated header for admin user', () => {
      render(<MockHeader user={mockAdminUser} />);
      
      expect(screen.getByTestId('header-authenticated')).toBeInTheDocument();
      expect(screen.getByTestId('user-name')).toHaveTextContent(mockAdminUser.username);
      expect(screen.getByTestId('admin-badge')).toBeInTheDocument();
    });

    it('should show admin badge for admin user', () => {
      render(<MockHeader user={mockAdminUser} />);
      
      const adminBadge = screen.getByTestId('admin-badge');
      expect(adminBadge).toBeInTheDocument();
      expect(adminBadge).toHaveTextContent('Admin');
      expect(adminBadge).toHaveClass('bg-red-500');
    });

    it('should show admin navigation for admin user', () => {
      render(<MockHeader user={mockAdminUser} />);
      
      expect(screen.getByTestId('nav-admin')).toBeInTheDocument();
      expect(screen.getByTestId('nav-admin')).toHaveAttribute('href', '/admin-dashboard');
    });
  });

  describe('User Interactions', () => {
    it('should toggle mobile menu when menu button is clicked', async () => {
      const user = userEvent.setup();
      render(<MockHeader user={mockUser} />);
      
      const menuToggle = screen.getByTestId('menu-toggle');
      const nav = menuToggle.nextElementSibling;
      
      // Menu dovrebbe essere nascosto inizialmente (solo su mobile)
      expect(nav).toHaveClass('hidden');
      
      // Click per aprire menu
      await user.click(menuToggle);
      expect(nav).toHaveClass('block');
      
      // Click per chiudere menu
      await user.click(menuToggle);
      expect(nav).toHaveClass('hidden');
    });

    it('should call logout API when logout button is clicked', async () => {
      const user = userEvent.setup();
      mockFetch({ success: true }, 200);
      
      render(<MockHeader user={mockUser} />);
      
      const logoutButton = screen.getByTestId('logout-button');
      await user.click(logoutButton);
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/logout', {
          method: 'POST'
        });
      });
    });
  });

  describe('Responsive Design', () => {
    it('should have proper responsive classes', () => {
      render(<MockHeader user={mockUser} />);
      
      const menuToggle = screen.getByTestId('menu-toggle');
      expect(menuToggle).toHaveClass('md:hidden');
      
      const nav = menuToggle.nextElementSibling;
      expect(nav).toHaveClass('md:block');
    });
  });

  describe('Accessibility', () => {
    it('should have proper semantic HTML structure', () => {
      render(<MockHeader user={mockUser} />);
      
      const header = screen.getByRole('banner');
      expect(header).toBeInTheDocument();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toBeInTheDocument();
      
      const list = screen.getByRole('list');
      expect(list).toBeInTheDocument();
    });

    it('should have accessible button text', () => {
      render(<MockHeader user={mockUser} />);
      
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      expect(logoutButton).toBeInTheDocument();
      
      const menuButton = screen.getByRole('button', { name: /menu/i });
      expect(menuButton).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle logout API errors gracefully', async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Mock API error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Logout failed'));
      
      render(<MockHeader user={mockUser} />);
      
      const logoutButton = screen.getByTestId('logout-button');
      await user.click(logoutButton);
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });
      
      consoleSpy.mockRestore();
    });
  });
});