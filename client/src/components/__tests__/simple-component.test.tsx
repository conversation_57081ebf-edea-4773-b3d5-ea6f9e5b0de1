/**
 * Test semplice per verificare setup Jest
 * <AUTHOR> di Testing Automatizzato HACCP Tracker
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Componente di test semplice
const SimpleComponent: React.FC<{ title: string }> = ({ title }) => {
  return (
    <div data-testid="simple-component">
      <h1>{title}</h1>
      <p>Test component for HACCP Tracker</p>
    </div>
  );
};

describe('Simple Component Test', () => {
  it('should render component with title', () => {
    const title = 'HACCP Tracker Test';
    render(<SimpleComponent title={title} />);
    
    expect(screen.getByTestId('simple-component')).toBeInTheDocument();
    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText('Test component for HACCP Tracker')).toBeInTheDocument();
  });

  it('should render different titles', () => {
    const { rerender } = render(<SimpleComponent title="First Title" />);
    expect(screen.getByText('First Title')).toBeInTheDocument();
    
    rerender(<SimpleComponent title="Second Title" />);
    expect(screen.getByText('Second Title')).toBeInTheDocument();
  });
});