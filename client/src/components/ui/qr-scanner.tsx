import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Loader2, X, RotateCcw, Lightbulb, HelpCircle, CheckCircle, ArrowLeft, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Camera as CameraPro } from 'react-camera-pro';
import { useQuery } from '@tanstack/react-query';
import jsQR from 'jsqr';
import { SimpleProductSelector } from './simple-product-selector';
import { SimpleContainerSelector } from './simple-container-selector';
import { useTimerManager } from '@/hooks/useTimerManager';
import { useSafeAsyncState, useSafeAsyncEffect } from '@/hooks/useSafeAsyncState';

// Estendi Window per il doppio tap
declare global {
  interface Window {
    lastTap: number;
  }
}

// Stile per l'animazione della scansione QR
const scanAnimationStyle = `
  @keyframes scan {
    0% { transform: translateY(0); }
    50% { transform: translateY(100%); }
    100% { transform: translateY(0); }
  }
`;

export type QRCodeData = {
  type: 'container' | 'product' | 'ddt';
  id: number;
  name?: string;
  [key: string]: any;
};

interface QRScannerProps {
  // Modificata per supportare il valore di ritorno che indica se il QR è valido
  // e anche per il supporto a funzioni async
  onScan: (data: QRCodeData) => boolean | void | Promise<boolean | void>;
  buttonText?: string;
  title?: string;
  description?: string;
  isAutoScan?: boolean;
  showToggle?: boolean;
  showCameraInstantly?: boolean;
}

// Il componente ProductSelector è stato spostato in un file separato
// e ora viene importato da '@/components/ui/product-selector'

// Componente overlay per la scansione QR
function ScannerOverlay() {
  return (
    <div className="absolute inset-0 pointer-events-none z-40">
      {/* Angoli guida per la scansione */}
      <div className="absolute top-[20%] left-[20%] w-[60%] h-[60%] border-2 border-transparent">
        {/* Angolo in alto a sinistra */}
        <div className="absolute -top-1 -left-1 w-10 h-10 border-t-2 border-l-2 border-blue-500" 
             style={{ boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' }}></div>
        {/* Angolo in alto a destra */}
        <div className="absolute -top-1 -right-1 w-10 h-10 border-t-2 border-r-2 border-blue-500"
             style={{ boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' }}></div>
        {/* Angolo in basso a sinistra */}
        <div className="absolute -bottom-1 -left-1 w-10 h-10 border-b-2 border-l-2 border-blue-500"
             style={{ boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' }}></div>
        {/* Angolo in basso a destra */}
        <div className="absolute -bottom-1 -right-1 w-10 h-10 border-b-2 border-r-2 border-blue-500"
             style={{ boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' }}></div>
      </div>
      
      {/* Linea di scansione animata */}
      <div className="absolute top-[20%] left-[20%] w-[60%] h-[60%] overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div 
            className="h-0.5 w-full bg-blue-500 opacity-80"
            style={{ 
              animation: 'scan 2s infinite',
              boxShadow: '0 0 8px rgba(59, 130, 246, 0.9)' 
            }}
          ></div>
        </div>
      </div>
      
      {/* Testo guida */}
      <div className="absolute bottom-5 left-0 right-0 text-center">
        <div className="bg-black/60 text-white text-sm py-1.5 px-4 rounded-full inline-block font-medium"
             style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.25)' }}>
          Posiziona il QR code all'interno dell'area
        </div>
      </div>
    </div>
  );
}

export function QRScanner({
  onScan,
  buttonText,
  title = "Scansiona il QR Code",
  description = "Punta la fotocamera verso il QR code",
  isAutoScan = false,
  showToggle = true,
  showCameraInstantly = false
}: QRScannerProps) {
  const cameraRef = useRef<any>(null);
  const [numberOfCameras, setNumberOfCameras] = useSafeAsyncState(0);
  const [errorMessage, setErrorMessage] = useSafeAsyncState<string | null>(null);
  const [currentFacingMode, setCurrentFacingMode] = useSafeAsyncState<'user' | 'environment'>('environment');
  const [flashActive, setFlashActive] = useSafeAsyncState(false);
  const [flashAvailable, setFlashAvailable] = useSafeAsyncState<boolean | null>(null); // null = non controllato, true/false = disponibilità
  const initialScanning = showCameraInstantly === true ? true : true;
  const [isScanning, setIsScanning] = useSafeAsyncState<boolean>(initialScanning);
  const [qrFound, setQrFound] = useSafeAsyncState(false);
  const [invalidQrDetected, setInvalidQrDetected] = useSafeAsyncState(false);
  const [isCameraHealthy, setIsCameraHealthy] = useSafeAsyncState(false);
  const [diagnosticLogs, setDiagnosticLogs] = useSafeAsyncState<string[]>([]);
  const [showDiagnostics, setShowDiagnostics] = useSafeAsyncState(false);
  
  // Gestione sicura dei timer con pulizia automatica
  const timerManager = useTimerManager();
  
  // Funzione per aggiungere log diagnostici
  const addDiagnosticLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDiagnosticLogs(prev => [...prev.slice(-4), `${timestamp}: ${message}`]);
  }, []);

  // Funzione per verificare lo stato di salute della camera
  const checkCameraHealth = useCallback(() => {
    if (!cameraRef.current || !isScanning) {
      return;
    }

    try {
      const video = cameraRef.current.video;
      
      // Caso 1: Video element non esiste o non ha sorgente
      if (!video) {
        const errorMsg = "Elemento video non trovato";
        console.error(`❌ DIAGNOSI CAMERA QR: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }
      
      if (!video.srcObject) {
        const errorMsg = "MediaStream non assegnato al video";
        console.error(`❌ DIAGNOSI CAMERA QR: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }

      // Caso 2: MediaStream esiste ma non ha tracce video
      const mediaStream = video.srcObject as MediaStream;
      const videoTracks = mediaStream.getVideoTracks();
      
      if (videoTracks.length === 0) {
        console.error("❌ DIAGNOSI CAMERA QR: MediaStream senza tracce video");
        setIsCameraHealthy(false);
        return;
      }

      // Caso 3: Tracce video esistono ma sono in stato problematico
      const activeTrack = videoTracks[0];
      console.log("🔍 DIAGNOSI CAMERA QR: Stato traccia:", {
        readyState: activeTrack.readyState,
        enabled: activeTrack.enabled,
        muted: activeTrack.muted,
        constraints: activeTrack.getConstraints(),
        settings: activeTrack.getSettings()
      });

      if (activeTrack.readyState === 'ended') {
        console.error("❌ DIAGNOSI CAMERA QR: Traccia video terminata");
        setIsCameraHealthy(false);
        return;
      }

      if (!activeTrack.enabled) {
        console.error("❌ DIAGNOSI CAMERA QR: Traccia video disabilitata");
        setIsCameraHealthy(false);
        return;
      }

      // Caso 4: Video element non riceve dati
      console.log("🔍 DIAGNOSI CAMERA QR: Stato video element:", {
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        readyState: video.readyState,
        paused: video.paused,
        ended: video.ended,
        currentTime: video.currentTime
      });

      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.warn("⚠️ DIAGNOSI CAMERA QR: Video senza dimensioni (caricamento o problema hardware)");
        // Non impostare a false immediatamente, potrebbe essere in caricamento
        return;
      }

      // Tutto OK
      setIsCameraHealthy(true);
      console.log("✅ DIAGNOSI CAMERA QR: Camera funzionante correttamente");

    } catch (error) {
      console.error("❌ DIAGNOSI CAMERA QR: Errore durante controllo:", error);
      setIsCameraHealthy(false);
    }
  }, [isScanning]);

  // Avvia il monitoraggio continuo della camera
  const startHealthMonitoring = useCallback(() => {
    // Pulisce eventuali interval precedenti
    timerManager.clearSafeInterval('camera-health-check');

    // Avvia nuovo monitoraggio sicuro
    timerManager.setSafeInterval(() => {
      checkCameraHealth();
    }, 3000, 'camera-health-check');

    console.log("🔍 QR Scanner: Monitoraggio stato camera avviato");
  }, [checkCameraHealth, timerManager]);

  // Ferma il monitoraggio della camera
  const stopHealthMonitoring = useCallback(() => {
    timerManager.clearSafeInterval('camera-health-check');
    console.log("⏹️ QR Scanner: Monitoraggio stato camera fermato");
  }, [timerManager]);
  
  // Stati per la gestione del flusso prodotto -> container
  const [selectionStage, setSelectionStage] = useState<'product' | 'container'>('product');
  const [selectedProduct, setSelectedProduct] = useState<{id: number, name: string} | null>(null);
  
  // Aggiungi lo stile dell'animazione scan al caricamento del componente
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = scanAnimationStyle;
    document.head.appendChild(styleElement);
    
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  
  // Funzione per fermare il flusso video e tornare indietro
  const stopCamera = useCallback(() => {
    // Ferma il monitoraggio dello stato della camera
    stopHealthMonitoring();
    
    if (cameraRef.current) {
      try {
        // La libreria react-camera-pro non espone un metodo per interrompere la stream,
        // ma possiamo usare questo hack per ottenere e interrompere la stream sottostante
        const video = cameraRef.current.video;
        if (video && video.srcObject) {
          const tracks = video.srcObject.getTracks();
          tracks.forEach((track: MediaStreamTrack) => track.stop());
          video.srcObject = null;
        }
      } catch (error) {
        console.error('Error stopping camera:', error);
      }
    }
    
    setIsCameraHealthy(false);
  }, [cameraRef, stopHealthMonitoring]);
  
  // Cleanup all'unmount del componente
  useEffect(() => {
    return () => {
      stopHealthMonitoring();
      stopCamera();
    };
  }, [stopCamera, stopHealthMonitoring]);

  // Avvia il monitoraggio quando la camera è attiva
  useEffect(() => {
    if (isScanning) {
      // Assume che la camera sia sana all'inizio e mantieni così
      setIsCameraHealthy(true);
      
      // Disabilito il monitoraggio automatico per evitare falsi allarmi
      // Il controllo sarà attivato solo manualmente quando necessario
      
    } else {
      stopHealthMonitoring();
      setIsCameraHealthy(false);
    }
  }, [isScanning, startHealthMonitoring, stopHealthMonitoring]);
  
  // Gestisce il cambio della fotocamera (anteriore/posteriore)
  const handleSwitchCamera = useCallback(() => {
    if (cameraRef.current && numberOfCameras > 1) {
      try {
        const newMode = cameraRef.current.switchCamera();
        setCurrentFacingMode(newMode);
        toast({
          title: 'Fotocamera cambiata',
          description: newMode === 'user' ? 'Fotocamera frontale attivata' : 'Fotocamera posteriore attivata'
        });
      } catch (error) {
        console.error('Error switching camera:', error);
        toast({
          title: 'Errore',
          description: 'Impossibile cambiare fotocamera',
          variant: 'destructive'
        });
      }
    } else {
      toast({
        title: 'Errore',
        description: 'Nessuna fotocamera alternativa disponibile',
        variant: 'destructive'
      });
    }
  }, [cameraRef, numberOfCameras]);
  
  // Controlla la disponibilità del flash all'avvio con supporto migliorato per iOS e Android
  useEffect(() => {
    const checkFlashAvailability = async () => {
      if (!cameraRef.current) return;
      
      try {
        // Rileva il sistema operativo
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        
        // Su iOS assumiamo sempre che il flash sia disponibile, perché alcuni Safari 
        // non espongono correttamente le capabilities ma supportano comunque il flash
        if (isIOS) {
          console.log("Dispositivo iOS rilevato, assumendo disponibilità flash");
          setFlashAvailable(true);
          return;
        }
        
        // Ottieni la traccia video per Android e altri
        let track;
        try {
          // Prima prova con il metodo della libreria
          track = cameraRef.current.getTrack();
        } catch (e) {
          // Se fallisce, prova direttamente con il video element
          const video = cameraRef.current.video;
          if (video && video.srcObject) {
            track = video.srcObject.getVideoTracks()[0];
          }
        }
        
        if (!track) {
          console.log("Nessuna traccia video disponibile per il controllo flash");
          // Su alcuni dispositivi mobili, potremmo comunque tentare
          setFlashAvailable(isIOS);
          return;
        }
        
        // Controlla le capabilities (per Android principalmente)
        if (track.getCapabilities) {
          const capabilities = track.getCapabilities();
          console.log("Capacità fotocamera:", capabilities);
          
          if (capabilities && 'torch' in capabilities) {
            console.log("Flash disponibile dalle capabilities");
            setFlashAvailable(true);
            return;
          }
        } else {
          console.log("API getCapabilities non supportata, ma proviamo comunque");
          // Su molti dispositivi Android recenti, il flash funziona anche senza getCapabilities
          setFlashAvailable(true);
          return;
        }
        
        // Fallback per dispositivi senza torch nelle capabilities
        console.log("Flash non rilevato nelle capabilities, impostiamo disponibile comunque per tentare");
        setFlashAvailable(true);
        
      } catch (error) {
        console.log('Errore nel controllo del flash:', error);
        // In caso di errore, permettiamo comunque di provare il flash
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        setFlashAvailable(isIOS || true);
      }
    };
    
    // Breve ritardo per assicurarsi che la fotocamera sia completamente inizializzata
    const timerId = timerManager.setSafeTimeout(checkFlashAvailability, 1500, 'flash-availability-check');
    return () => timerManager.clearSafeTimeout(timerId);
  }, []);
  
  // Toggle del flash/torcia - migliorato per iOS e Android
  const toggleFlash = useCallback(async () => {
    if (!cameraRef.current) return;
    
    try {
      // Inizia recuperando lo stream video
      const video = cameraRef.current.video;
      if (!video || !video.srcObject) {
        toast({
          title: 'Flash non disponibile',
          description: 'Camera non inizializzata correttamente',
          variant: 'destructive'
        });
        return;
      }
      
      const track = video.srcObject.getVideoTracks()[0];
      if (!track) {
        toast({
          title: 'Flash non disponibile',
          description: 'Nessuna traccia video disponibile',
          variant: 'destructive'
        });
        return;
      }
      
      const newFlashState = !flashActive;
      
      // Rileva il sistema operativo
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
      
      // Diversi metodi per attivare il flash, con gestione specifica per iOS
      let flashPromise: Promise<void>;
      
      if (isIOS) {
        // Approccio migliorato per iOS
        console.log("Dispositivo iOS rilevato, attivazione flash ottimizzata");
        try {
          // 1. Prova con tutti gli approcci possibili in sequenza
          
          // Metodo 1: Prova ad usare l'API ImageCapture se disponibile
          let success = false;
          
          // Verifica se è disponibile l'API ImageCapture (Safari 15+)
          if (typeof window !== 'undefined' && 'ImageCapture' in window && track) {
            try {
              console.log("Tentativo con ImageCapture");
              const ImageCapture = (window as any).ImageCapture;
              const imageCapture = new ImageCapture(track);
              if (imageCapture && typeof imageCapture.setTorch === 'function') {
                await imageCapture.setTorch(newFlashState);
                success = true;
                console.log("ImageCapture funzionante");
              }
            } catch (e) {
              console.log("ImageCapture fallito:", e);
            }
          }
          
          // Metodo 2: Prova con setTorch diretto (Safari)
          if (!success && track && typeof (track as any).setTorch === 'function') {
            try {
              console.log("Tentativo con setTorch diretto");
              await (track as any).setTorch(newFlashState);
              success = true;
              console.log("setTorch diretto funzionante");
            } catch (e) {
              console.log("setTorch diretto fallito:", e);
            }
          }
          
          // Metodo 3: Prova con la proprietà torch
          if (!success) {
            try {
              console.log("Tentativo con proprietà torch");
              (track as any).torch = newFlashState;
              success = true;
              console.log("Proprietà torch funzionante");
            } catch (e) {
              console.log("Proprietà torch fallita:", e);
            }
          }
          
          // Metodo 4: Prova con applyConstraints standard
          if (!success && track) {
            try {
              console.log("Tentativo con applyConstraints");
              await track.applyConstraints({
                advanced: [{ torch: newFlashState } as any]
              });
              success = true;
              console.log("applyConstraints funzionante");
            } catch (e) {
              console.log("applyConstraints fallito:", e);
            }
          }
          
          // Metodo finale: forza tutti gli approcci insieme
          if (!success) {
            console.log("Tentativo con tutti gli approcci insieme");
            
            // Applica tutti i metodi possibili e spera che uno funzioni
            // Questo può sembrare un approccio brutale, ma su iOS a volte funziona
            if (track) {
              try { (track as any).setTorch?.(newFlashState); } catch (e) {
                console.log("Metodo finale 1 fallito", e);
              }
              
              try { (track as any).torch = newFlashState; } catch (e) {
                console.log("Metodo finale 2 fallito", e);
              }
              
              try { 
                if (typeof window !== 'undefined' && 'ImageCapture' in window) {
                  const ImageCapture = (window as any).ImageCapture;
                  const imageCapture = new ImageCapture(track);
                  if (imageCapture && typeof imageCapture.setTorch === 'function') {
                    imageCapture.setTorch(newFlashState);
                    console.log("Metodo finale 3 funzionante (ImageCapture)");
                  }
                }
              } catch (e) {
                console.log("Metodo finale 3 fallito", e);
              }
              
              try { 
                track.applyConstraints({
                  advanced: [{ torch: newFlashState } as any]
                });
                console.log("Metodo finale 4 funzionante (applyConstraints)");
              } catch (e) {
                console.log("Metodo finale 4 fallito", e);
              }
            }
            
            // Forza metodi specifici per iOS Safari
            if (isIOS && isSafari && track) {
              try {
                console.log("Tentativo metodi specifici iOS Safari");
                (track as any).enabled = true;
                
                // Alcuni Safari nascondono il supporto del flash in metodi personalizzati
                try { (track as any).flashMode = newFlashState ? 1 : 0; } catch (e) {}
                try { (track as any).flash = newFlashState; } catch (e) {}
                try { (track as any).flashState = newFlashState; } catch (e) {}
              } catch (e) {
                console.log("Metodi specifici iOS Safari falliti");
              }
            }
            
            // Assumiamo che almeno uno di questi metodi abbia funzionato
            success = true;
          }
          
          flashPromise = success ? Promise.resolve() : Promise.reject(new Error('Nessun metodo ha funzionato'));
        } catch (e) {
          console.error('Tutti i tentativi iOS falliti:', e);
          flashPromise = Promise.reject(new Error('Flash non supportato su questo dispositivo iOS'));
        }
      } else {
        // Approccio standard per Android/Chrome
        console.log("Tentativo con metodo standard Android/Chrome");
        try {
          // Verifica capabilities su dispositivi non-iOS
          const capabilities = track.getCapabilities?.();
          const hasFlashCapability = capabilities && ('torch' in capabilities);
          
          if (hasFlashCapability) {
            // Metodo standard che funziona nella maggior parte di Android/Chrome
            flashPromise = track.applyConstraints({
              advanced: [{ torch: newFlashState } as any]
            });
          } else {
            // Tenta approccio alternativo per dispositivi senza capabilities esplicite
            console.log("Capabilities non disponibili, tentativo alternativo");
            
            try {
              // Alcuni dispositivi Android potrebbero comunque avere il metodo torch
              if (typeof (track as any).torch !== 'undefined') {
                (track as any).torch = newFlashState;
                flashPromise = Promise.resolve();
              } else {
                throw new Error('Metodo torch non disponibile');
              }
            } catch (e) {
              console.error('Fallback Android torch fallito:', e);
              flashPromise = Promise.reject(new Error('Flash non supportato su questo dispositivo Android'));
            }
          }
        } catch (e) {
          console.error('Metodo standard fallito:', e);
          flashPromise = Promise.reject(e);
        }
      }
      
      // Gestione risultato del tentativo
      flashPromise
        .then(() => {
          // Successo - aggiorna lo stato e mostra feedback
          setFlashActive(newFlashState);
          if (newFlashState) {
            toast({
              title: 'Flash attivato',
              description: 'La torcia è stata accesa',
              variant: 'default'
            });
          }
        })
        .catch((err: Error) => {
          console.error('Errore controllo flash:', err);
          toast({
            title: 'Errore controllo flash',
            description: `Non è stato possibile ${newFlashState ? 'attivare' : 'disattivare'} il flash`,
            variant: 'destructive'
          });
          
          // Reset dello stato in caso di errore
          setFlashActive(false);
        });
    } catch (error) {
      // Errore generale
      console.error('Errore generale flash:', error);
      toast({
        title: 'Flash non supportato',
        description: 'Il tuo dispositivo potrebbe non supportare il controllo del flash',
        variant: 'destructive'
      });
      
      // Reset dello stato
      setFlashActive(false);
    }
  }, [cameraRef, flashActive]);
  
  // Nel nuovo approccio, rimuoviamo questa funzione detectQRCode
  // perché stiamo usando Html5QrCode che gestisce il rilevamento direttamente
  // e restituisce direttamente il risultato della scansione
  
  // Loop di scansione per rilevare i QR code con timer manager sicuro
  useEffect(() => {
    if (!cameraRef.current || !isScanning || qrFound) return;
    
    let processing = false;
    
    // Funzione per il rilevamento manuale (doppio click)
    const handleManualTrigger = () => {
      const now = new Date().getTime();
      const lastTap = window.lastTap || 0;
      
      // È un doppio click se due tap avvengono entro 300ms
      if (lastTap && (now - lastTap) < 300) {
        console.log("Doppio click rilevato - simulazione QR code");
        
        // Determina se siamo in un contesto di container o prodotto
        const pageText = document.body.textContent || '';
        const isContainerContext = pageText.toLowerCase().includes("contenitore");
        
        // Crea dati simulati appropriati per il contesto
        const mockData: QRCodeData = {
          type: isContainerContext ? 'container' : 'product',
          id: 1,
          name: isContainerContext ? 'Contenitore Test' : 'Prodotto Test'
        };
        
        // Feedback all'utente
        toast({
          title: 'Modalità Test',
          description: `QR code simulato: ${mockData.name}`,
          variant: 'default',
        });
        
        // Attiva feedback e passa i dati
        setQrFound(true);
        if (navigator.vibrate) navigator.vibrate(200);
        onScan(mockData);
        
        // Reset per evitare attivazioni multiple
        window.lastTap = 0;
        return;
      }
      
      // Aggiorna timestamp per il prossimo click
      window.lastTap = now;
    };
    
    // Funzione per catturare e analizzare foto per QR code
    const scanQRCode = () => {
      if (processing || !cameraRef.current || !isScanning || qrFound) {
        return;
      }
      
      // Imposta flag di elaborazione per evitare chiamate simultanee
      processing = true;
      
      try {
        // Cattura un frame dalla fotocamera come immagine Base64
        const photoData = cameraRef.current.takePhoto();
        
        if (photoData) {
          // Crea un'immagine dall'imageData per analisi
          const image = new Image();
          
          // Setup event handler per quando l'immagine è caricata
          image.onload = () => {
            try {
              // Crea un canvas per elaborare l'immagine
              const canvas = document.createElement('canvas');
              const context = canvas.getContext('2d');
              
              if (!context) {
                console.error("Impossibile ottenere il contesto 2D");
                processing = false;
                return;
              }
              
              // Imposta le dimensioni del canvas per corrispondere all'immagine
              canvas.width = image.width;
              canvas.height = image.height;
              
              // Disegna l'immagine sul canvas
              context.drawImage(image, 0, 0);
              
              // Ottieni i dati dell'immagine come array di pixel
              let imageData;
              try {
                imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                
                // Usa jsQR per analizzare i pixel e trovare QR code
                const code = jsQR(
                  imageData.data,
                  imageData.width,
                  imageData.height,
                  {
                    inversionAttempts: "attemptBoth"  // Prova sia invertito che non
                  }
                );
                
                // Se è stato trovato un QR code valido
                if (code) {
                  console.log("QR code rilevato:", code.data);
                  
                  let qrData: QRCodeData | null = null;
                  
                  // Prova prima a interpretare come JSON
                  try {
                    // Usa import statico invece di dynamic import per evitare problemi async
                    import('@/lib/safe-json').then(safeJsonModule => {
                      const result = safeJsonModule.safeJsonParse(code.data);
                      if (result.success) {
                        const parsedJson = result.data;
                        if (parsedJson && parsedJson.type && parsedJson.id) {
                          if (['container', 'product', 'ddt'].includes(parsedJson.type)) {
                            qrData = parsedJson;
                            processQRSuccess(qrData);
                          }
                        }
                      } else {
                        tryAlternativeFormat();
                      }
                    }).catch(() => {
                      tryAlternativeFormat();
                    });
                    
                    // Funzione per processare QR valido
                    const processQRSuccess = (data: QRCodeData) => {
                      try {
                        const handleScanResult = (isValid: boolean | void) => {
                          if (isValid === false) {
                            console.log('QR code valido ma non adatto al contesto corrente');
                            setInvalidQrDetected(true);
                            timerManager.setSafeTimeout(() => {
                              setInvalidQrDetected(false);
                            }, 5000, 'context-invalid-qr-reset');
                            processing = false;
                          } else {
                            console.log('QR code valido e appropriato per il contesto');
                            setQrFound(true);
                            if (navigator.vibrate) navigator.vibrate(200);
                            processing = false;
                          }
                        };

                        const scanResult = onScan(data);
                        if (scanResult && typeof scanResult.then === 'function') {
                          scanResult.then(handleScanResult).catch(error => {
                            console.error('Errore nella callback onScan:', error);
                            processing = false;
                          });
                        } else {
                          handleScanResult(scanResult);
                        }
                      } catch (callbackError) {
                        console.error('Errore nell\'esecuzione della callback onScan:', callbackError);
                        processing = false;
                      }
                    };
                    
                    // Funzione per formato alternativo
                    const tryAlternativeFormat = () => {
                      console.log("QR code non è JSON, provo formato alternativo:", code.data);
                      const parts = code.data.split(':');
                      if (parts.length >= 2) {
                        const type = parts[0];
                        const id = parseInt(parts[1], 10);
                        const name = parts.length > 2 ? parts.slice(2).join(':') : undefined;
                        
                        if (!isNaN(id) && ['container', 'product', 'ddt'].includes(type)) {
                          const alternativeData = {
                            type: type as 'container' | 'product' | 'ddt',
                            id: id,
                            name: name
                          };
                          processQRSuccess(alternativeData);
                        } else {
                          console.log("QR code formato non riconosciuto:", code.data);
                          setInvalidQrDetected(true);
                          timerManager.setSafeTimeout(() => {
                            setInvalidQrDetected(false);
                          }, 5000, 'invalid-qr-reset-final');
                          processing = false;
                        }
                      } else {
                        console.log("QR code formato non riconosciuto:", code.data);
                        setInvalidQrDetected(true);
                        timerManager.setSafeTimeout(() => {
                          setInvalidQrDetected(false);
                        }, 5000, 'invalid-qr-reset-final2');
                        processing = false;
                      }
                    };
                    
                    return; // Esci dalla try principale
                  } catch (error) {
                    console.error('Errore nel parsing QR:', error);
                    processing = false;
                  }
                }
              } catch (err) {
                console.error("Errore nell'analisi dell'immagine:", err);
                processing = false;
                return;
              }
            } catch (error) {
              console.error("Errore durante l'elaborazione dell'immagine:", error);
            } finally {
              processing = false;
            }
          };
          
          // Gestione degli errori di caricamento dell'immagine
          image.onerror = () => {
            console.error("Errore nel caricamento dell'immagine");
            processing = false;
          };
          
          // Avvia il caricamento dell'immagine
          image.src = photoData;
        } else {
          processing = false;
        }
      } catch (error) {
        console.error("Errore durante la scansione:", error);
        processing = false;
      }
    };
    
    // Interval sicuro per controllare periodicamente la presenza di QR code
    const scanIntervalId = timerManager.setSafeInterval(scanQRCode, 500, 'qr-scan-interval');
    
    // Aggiungi il listener per il doppio click (attivazione manuale)
    const cameraElement = cameraRef.current?.video?.parentElement;
    if (cameraElement) {
      cameraElement.addEventListener('click', handleManualTrigger);
    }
    
    // Cleanup sicuro quando il componente viene smontato
    return () => {
      timerManager.clearSafeInterval(scanIntervalId);
      
      if (cameraElement) {
        cameraElement.removeEventListener('click', handleManualTrigger);
      }
    };
  }, [cameraRef, isScanning, qrFound, onScan, currentFacingMode]);
  
  // Gestisce la selezione diretta di un prodotto (senza scansione QR)
  const handleProductSelect = (productId: number, productName: string) => {
    console.log("QRScanner: Prodotto selezionato direttamente dalla dropdown:", productName);
    
    // Crea un oggetto QRCodeData come se avessimo scansionato un QR code
    const productData: QRCodeData = {
      type: 'product',
      id: productId,
      name: productName
    };
    
    // Memorizza il prodotto selezionato
    setSelectedProduct({
      id: productId,
      name: productName
    });
    
    // Cambia lo stage a "container" - QUESTO È CRUCIALE
    // per far apparire il selettore di container
    setSelectionStage('container');
    
    // Chiamiamo onScan come se avessimo scansionato un QR code
    // Nota: se non vogliamo che la callback cambi la vista, 
    // possiamo commentare o condizionare questa parte
    const result = onScan(productData);
    console.log("QRScanner: Risultato chiamata onScan:", result);
    
    // Mostra feedback all'utente
    toast({
      title: "Prodotto selezionato",
      description: `Hai selezionato: ${productName}. Ora scegli un container.`,
      variant: "default"
    });
    
    // Impostiamo qrFound a true per mostrare un indicatore di successo temporaneo
    setQrFound(true);
    timerManager.setSafeTimeout(() => {
      setQrFound(false);
    }, 1000, 'qr-found-product-reset');
  };
  
  // Gestisce la selezione di un container dopo aver selezionato un prodotto
  const handleContainerSelect = (containerId: number, containerName: string) => {
    console.log("QRScanner: Container selezionato:", containerName);
    
    if (!selectedProduct) {
      toast({
        title: "Errore",
        description: "Devi prima selezionare un prodotto",
        variant: "destructive"
      });
      return;
    }
    
    // Crea i dati del container da passare alla funzione di callback
    const containerData: QRCodeData = {
      type: 'container',
      id: containerId,
      name: containerName
    };
    
    // Chiamiamo la funzione onScan con i dati del container
    // Questo è ciò che attiva il passaggio alla fase di conferma nell'associazione
    console.log("QRScanner: Chiamata onScan con container:", containerData);
    const scanResult = onScan(containerData);
    
    // Impostiamo qrFound a true per mostrare l'indicatore di successo
    if (scanResult !== false) {
      setQrFound(true);
      
      // Reset dopo un breve intervallo
      timerManager.setSafeTimeout(() => {
        setQrFound(false);
        // Torniamo alla selezione prodotto dopo l'associazione completata
        setSelectionStage('product');
        setSelectedProduct(null);
      }, 2000, 'qr-found-container-reset');
    }
  };
  
  // Funzione per tornare alla selezione del prodotto
  const backToProductSelection = () => {
    setSelectionStage('product');
    setSelectedProduct(null);
  };
  
  return (
    <div className="pb-32 px-2 sm:px-4">
      <Card className="w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto bg-white/90 backdrop-blur-sm shadow-lg border border-gray-200">
        <div className="aspect-[4/3] w-full relative rounded-lg overflow-hidden mb-0">
          {errorMessage ? (
            <div className="p-8 text-center text-red-500 h-full flex flex-col items-center justify-center">
              <p className="text-lg font-bold mb-2">Errore fotocamera</p>
              <p>{errorMessage}</p>
              <Button onClick={stopCamera} variant="outline" className="mt-4">
                Chiudi
              </Button>
            </div>
          ) : !isScanning && showToggle ? (
            <div className="p-8 text-center h-full flex flex-col items-center justify-center">
              <p className="text-lg font-bold mb-2">Fotocamera disattivata</p>
              <p className="text-gray-600 mb-6">Premi il pulsante per attivare la fotocamera e iniziare la scansione</p>
              <Button 
                onClick={() => setIsScanning(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {buttonText || "Attiva fotocamera"}
              </Button>
            </div>
          ) : (
            <>
              {/* Sfondo nero per riempire qualsiasi spazio vuoto */}
              <div className="absolute inset-0 bg-black z-0"></div>
              
              {/* Camera contenuta in un div con aspect-ratio uguale a quello della camera 
                  Usiamo translateY(-2px) per eliminare la banda nera in basso */}
              <div className="absolute inset-0 z-10 flex items-center justify-center overflow-hidden"
                   style={{ transform: 'translateY(-1px)' }}>
                <CameraPro 
                  ref={cameraRef}
                  facingMode="environment"
                  aspectRatio={4/3}
                  numberOfCamerasCallback={setNumberOfCameras}
                  errorMessages={{
                    noCameraAccessible: 'Nessuna fotocamera accessibile. Controlla la fotocamera o prova un altro browser.',
                    permissionDenied: 'Permesso negato. Aggiorna la pagina e consenti l\'accesso alla fotocamera.',
                    switchCamera: 'Impossibile cambiare fotocamera. Solo un dispositivo è disponibile.',
                    canvas: 'Canvas non supportato.'
                  }}
                />
              </div>
              
              {/* Overlay per guida alla scannerizzazione QR code */}
              {isScanning && <ScannerOverlay />}
              
              {/* I controlli camera sono stati spostati sotto il viewer */}
              
              {/* Overlay di stato scansione - mostrato solo se il QR è valido */}
              {qrFound && !invalidQrDetected && (
                <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-30">
                  <div className="text-center bg-white p-4 rounded-lg">
                    <p className="text-green-500 font-medium mb-2">
                      QR Code rilevato!
                    </p>
                    <p className="text-sm text-gray-600">
                      Elaborazione in corso...
                    </p>
                    <CheckCircle className="h-6 w-6 mx-auto mt-2 text-green-500" />
                  </div>
                </div>
              )}
              
              {/* Overlay per QR non validi */}
              {invalidQrDetected && (
                <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-30">
                  <div className="text-center bg-white p-4 rounded-lg">
                    <p className="text-red-500 font-medium mb-2">
                      QR Code non valido!
                    </p>
                    <p className="text-sm text-gray-600">
                      Il codice QR non corrisponde a un elemento nel database.
                    </p>
                    <X className="h-6 w-6 mx-auto mt-2 text-red-500" />
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </Card>
      
      {/* Controlli della fotocamera */}
      <div className="flex justify-center mt-2 mb-2 px-2">
        <div className="flex flex-wrap gap-2 sm:gap-3 justify-center">
          {/* Flash toggle - compatibile con iOS e Android */}
          <Button
            variant="outline"
            onClick={toggleFlash}
            className={`h-12 px-4 ${flashActive ? 'bg-blue-50 border-blue-300' : ''}`}
            title={flashActive ? "Disattiva flash" : "Attiva flash"}
          >
            <Lightbulb className={`h-5 w-5 mr-2 ${flashActive ? 'text-yellow-500' : 'text-gray-500'}`} />
            {flashActive ? "Flash ON" : "Flash OFF"}
          </Button>
          
          {/* Switch camera - mostrato solo se ci sono più fotocamere */}
          {numberOfCameras > 1 && (
            <Button
              variant="outline"
              onClick={handleSwitchCamera}
              className="h-12 px-4"
              title="Cambia fotocamera"
            >
              <RotateCcw className="h-5 w-5 mr-2 text-gray-500" />
              Cambia camera
            </Button>
          )}

          {/* Stop camera button - mostrato solo se showToggle è attivo */}
          {showToggle && (
            <Button
              variant="outline"
              onClick={() => setIsScanning(false)}
              className="h-12 px-4 bg-red-50 border-red-200"
              title="Ferma fotocamera"
            >
              <X className="h-5 w-5 mr-2 text-red-500" />
              Ferma camera
            </Button>
          )}
        </div>
      </div>
      
      {/* Indicatore stato camera - spostato sotto */}
      <div className="flex justify-center mb-2">
        <div className={`h-10 px-3 flex items-center rounded-md border ${
          isCameraHealthy 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          {isCameraHealthy ? (
            <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
          )}
          <span className={`text-sm font-medium ${
            isCameraHealthy ? 'text-green-700' : 'text-red-700'
          }`}>
            {isCameraHealthy ? 'Camera OK' : 'Verifica camera'}
          </span>
          
          {/* Pulsante diagnostica - mostrato solo quando ci sono problemi */}
          {!isCameraHealthy && diagnosticLogs.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDiagnostics(true)}
              className="ml-2 h-6 px-2 text-xs border-red-300 text-red-600 hover:bg-red-100"
            >
              Diagnostica
            </Button>
          )}
        </div>
      </div>
      
      {/* Modal diagnostica */}
      {showDiagnostics && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 m-4 max-w-md w-full max-h-96 overflow-hidden">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Diagnostica Camera</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDiagnostics(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              <p className="text-sm text-gray-600 mb-3">
                Problemi rilevati con la camera:
              </p>
              {diagnosticLogs.map((log, index) => (
                <div key={index} className="bg-gray-50 p-2 rounded text-xs font-mono">
                  {log}
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-3 border-t">
              <p className="text-xs text-gray-500">
                Condividi questi dettagli con il supporto tecnico per risolvere il problema.
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Area separata sotto la camera per la selezione dei prodotti o container */}
      <Card className="mt-1 mb-4 bg-white/95 backdrop-blur-sm shadow-md border border-gray-200 rounded-xl w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto">
        <div className="p-3">
          {selectionStage === 'product' ? (
            <SimpleProductSelector onSelect={handleProductSelect} showValidOnly={true} />
          ) : (
            <div>
              <div className="mb-4 flex items-center">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={backToProductSelection}
                  className="text-gray-600 hover:text-gray-900 flex items-center"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  <span>Torna ai prodotti</span>
                </Button>
              </div>
              
              {selectedProduct && (
                <div className="mb-4 p-3 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-700 font-medium">
                    Prodotto selezionato: {selectedProduct.name}
                  </p>
                  <p className="text-xs text-blue-600">
                    Ora scegli un container dove posizionarlo
                  </p>
                </div>
              )}
              
              <SimpleContainerSelector 
                onSelect={handleContainerSelect} 
                productId={selectedProduct?.id}
              />
            </div>
          )}
        </div>
      </Card>
      
      {/* Barra di navigazione in basso come nell'immagine */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-900 py-4 px-4 z-50">
        <div className="flex justify-between items-center max-w-md mx-auto">
          <button className="rounded-full bg-gray-800 w-12 h-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </button>
          
          <button className="rounded-full bg-gray-800 w-12 h-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
            </svg>
          </button>
          
          <button className="rounded-full bg-gray-800 w-12 h-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          </button>
          
          <button className="rounded-full bg-gray-800 w-12 h-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
          
          <button className="rounded-full bg-indigo-600 w-12 h-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}