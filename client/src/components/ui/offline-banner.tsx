import React, { useState, useEffect } from 'react';
import { WifiOff, X } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import { useSafeAsyncState } from '@/hooks/useSafeAsyncState';

/**
 * Componente che mostra un banner quando l'applicazione è offline
 */
export function OfflineBanner() {
  const [isOnline, setIsOnline] = useSafeAsyncState<boolean>(navigator.onLine);
  const [showBanner, setShowBanner] = useSafeAsyncState<boolean>(!navigator.onLine);
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Funzione che aggiorna lo stato online/offline
    const updateOnlineStatus = () => {
      const online = navigator.onLine;
      setIsOnline(online);
      
      // Se il banner è stato chiuso manualmente, non mostrarlo nuovamente
      // a meno che non passiamo da online a offline
      if (!online && isOnline) {
        setShowBanner(true);
      } else if (online) {
        setShowBanner(false);
      }
    };

    // Aggiungi event listener
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Rimuovi event listener quando il componente viene smontato
    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, [isOnline]);

  // Funzione per chiudere manualmente il banner
  const closeBanner = () => {
    setShowBanner(false);
  };
  
  // Funzione per aprire il gestore offline
  const openOfflineManager = () => {
    setLocation("/offline-manager");
    closeBanner();
  };

  // Se siamo online o il banner è stato chiuso, non mostrare nulla
  if (isOnline || !showBanner) {
    return null;
  }

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 px-4 py-2">
      <Alert variant="destructive" className="border-amber-300 bg-amber-50 pr-2 relative">
        <WifiOff className="h-4 w-4 text-amber-600" />
        <AlertTitle className="text-amber-800 text-sm font-medium">Modalità Offline</AlertTitle>
        <AlertDescription className="text-amber-700 text-xs">
          Sei attualmente offline. Le operazioni principali come archiviazione contenitore e altre funzioni
          vengono salvate in locale e sincronizzate automaticamente quando tornerai online.
          <div className="mt-2 flex justify-between items-center">
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs py-1 h-7 bg-amber-100 border-amber-200 text-amber-800 hover:bg-amber-200"
              onClick={openOfflineManager}
            >
              Gestisci dati offline
            </Button>
          </div>
        </AlertDescription>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 rounded-full absolute top-2 right-2 text-amber-800 hover:bg-amber-200 hover:text-amber-900"
          onClick={closeBanner}
        >
          <X className="h-3 w-3" />
          <span className="sr-only">Chiudi</span>
        </Button>
      </Alert>
    </div>
  );
}