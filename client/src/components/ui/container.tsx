import React from 'react';
import { cn } from '@/lib/utils';

export interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'card' | 'header';
  children: React.ReactNode;
}

/**
 * Container Component
 * 
 * Un componente riutilizzabile per contenitori con stili coerenti nell'applicazione.
 * Può essere utilizzato per header, card, e altri elementi UI.
 * 
 * Varianti disponibili:
 * - default: Un container base con sfondo bianco per card generiche
 * - header: Versione scura usata per l'header dell'applicazione
 * - card: Container a carta con gradienti e effetti per contenuti importanti
 */
export function Container({
  variant = 'default',
  className,
  children,
  ...props
}: ContainerProps) {
  const variantStyles = {
    default: 'bg-white/90 backdrop-blur-md border border-gray-200 rounded-2xl shadow-2xl',
    header: 'text-white',
    card: 'bg-gradient-to-br from-gray-50 to-white backdrop-blur-md border border-gray-200 rounded-2xl shadow-xl'
  };

  return (
    <div
      className={cn(variantStyles[variant], className)}
      {...props}
    >
      {children}
    </div>
  );
}
