import React from 'react';
import { QRCode as ReactQRCode } from 'react-qrcode-logo';
import { Download, Printer } from 'lucide-react';

// Definizione dei tipi di codice QR
export enum QRCodeType {
  DDT = 'DDT',
  PRODUCT_LABEL = 'PRODUCT_LABEL',
  PRODUCT = 'PRODUCT',
  CONTAINER = 'CONTAINER'
}

/**
 * Genera il nome del file per un QR code secondo le convenzioni specificate:
 * - DDT: RagioneSociale-ddmmyy.png
 * - Etichetta: nomeprodotto-ddmmyy-hhmm.png
 * - Contenitore: nomecontenitore-ddmmyy.png
 * 
 * Gli spazi vengono sostituiti con underscore (_).
 */
export function generateQRCodeFilename(
  name: string,
  type: QRCodeType,
  creationDate: Date = new Date()
): string {
  // Sostituisci spazi con underscore
  const formattedName = name.replace(/\s+/g, '_');
  
  // Formatta la data nel formato ddmmyy
  const day = creationDate.getDate().toString().padStart(2, '0');
  const month = (creationDate.getMonth() + 1).toString().padStart(2, '0');
  const year = creationDate.getFullYear().toString().slice(-2);
  const date = `${day}${month}${year}`;
  
  // Per le etichette aggiungi anche l'ora nel formato hhmm
  if (type === QRCodeType.PRODUCT_LABEL) {
    const hour = creationDate.getHours().toString().padStart(2, '0');
    const minute = creationDate.getMinutes().toString().padStart(2, '0');
    const time = `${hour}${minute}`;
    return `${formattedName}-${date}-${time}.png`;
  }
  
  // Per DDT e Contenitori
  return `${formattedName}-${date}.png`;
}

// Interfaccia per l'utilizzo del QR Code normale
export interface QRCodeProps {
  value: string;
  size?: number;
  bgColor?: string;
  fgColor?: string;
  ecLevel?: "L" | "M" | "Q" | "H";
  logoImage?: string;
  logoWidth?: number;
  logoHeight?: number;
  // Proprietà per la nomenclatura file
  qrCodeType?: QRCodeType;  // Tipo di QR code (DDT, etichetta, contenitore)
  itemName?: string;       // Nome da usare nel file (ragione sociale, nome prodotto, nome contenitore)
  creationDate?: Date;     // Data di creazione per il nome file
  id?: string;             // ID opzionale da assegnare all'SVG
}

// Interfaccia estesa per la visualizzazione con opzioni di download/stampa
export interface QRCodeViewProps {
  code: string; // Cambiato da 'data' a 'code' per coerenza
  label?: string;
  showDownload?: boolean;
  showPrint?: boolean;
  size?: number;
  bgColor?: string;
  fgColor?: string;
  ecLevel?: "L" | "M" | "Q" | "H";
  logoImage?: string;
  logoWidth?: number;
  logoHeight?: number;
  // Proprietà per la nomenclatura file
  qrCodeType?: QRCodeType;  // Tipo di QR code (DDT, etichetta, contenitore)
  itemName?: string;       // Nome da usare nel file (ragione sociale, nome prodotto, nome contenitore)
  creationDate?: Date;     // Data di creazione per il nome file
  id?: string;             // ID opzionale da assegnare all'SVG
}

// Componente principale QR Code
export function QRCode({
  value,
  size = 200,
  bgColor = "#FFFFFF",
  fgColor = "#000000",
  ecLevel = "L",
  logoImage,
  logoWidth,
  logoHeight,
  qrCodeType,
  itemName,
  creationDate,
  id
}: QRCodeProps) {
  const [svgReady, setSvgReady] = React.useState(false);
  
  const handleClick = () => {
    if (!svgReady) {
      console.warn("QR code SVG non è ancora pronto");
      return;
    }
    
    // Crea un canvas e disegna il QR code
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // Crea un'immagine temporanea
      const img = new Image();
      img.onload = () => {
        // Invece di aprire una nuova finestra, creiamo un iframe nascosto e avviamo la stampa direttamente
        const printIframe = document.createElement('iframe');
        printIframe.style.position = 'fixed';
        printIframe.style.top = '-9999px';
        printIframe.style.left = '-9999px';
        printIframe.style.width = '0';
        printIframe.style.height = '0';
        printIframe.style.border = '0';
        document.body.appendChild(printIframe);
        
        // Generare un nome file significativo
        let fileName = qrCodeType && itemName 
          ? generateQRCodeFilename(itemName, qrCodeType, creationDate || new Date())
          : 'qrcode.png';
        
        // Scrivi il contenuto dell'iframe
        if (printIframe.contentDocument) {
          printIframe.contentDocument.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Stampa QR Code - ${fileName}</title>
                <style>
                  body {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    margin: 0;
                    padding: 20px;
                    box-sizing: border-box;
                    font-family: Arial, sans-serif;
                  }
                  .qr-container {
                    background: white;
                    padding: 20px;
                    text-align: center;
                  }
                  img {
                    max-width: 280px;
                    height: auto;
                    display: block;
                    margin: 0 auto;
                  }
                  .title {
                    font-size: 18px;
                    font-weight: bold;
                    margin-top: 10px;
                  }
                </style>
              </head>
              <body>
                <div class="qr-container">
                  <img src="${img.src}" alt="QR Code" />
                  ${itemName ? `<p class="title">${itemName}</p>` : ''}
                </div>
                <script>
                  // Stampa direttamente e poi rimuovi l'iframe
                  window.onload = function() {
                    window.print();
                    setTimeout(function() {
                      if (window.parent && window.frameElement) {
                        window.parent.document.body.removeChild(window.frameElement);
                      }
                    }, 1000);
                  };
                </script>
              </body>
            </html>
          `);
          
          printIframe.contentDocument?.close();
        }
      };
      
      // Trova l'elemento SVG del QR code utilizzando l'ID se disponibile
      const svgElement = id ? document.getElementById(id) : document.querySelector('.qr-code-svg');
      if (svgElement && svgElement instanceof SVGElement) {
        // Converti SVG in stringa
        const svgString = new XMLSerializer().serializeToString(svgElement);
        // Crea un blob URL
        const svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
        
        // Crea un nome file significativo se possibile
        if (svgElement.parentElement) {
          svgElement.parentElement.setAttribute('data-qr-filename', 
            qrCodeType && itemName 
              ? generateQRCodeFilename(itemName, qrCodeType, creationDate || new Date())
              : 'qrcode.png'
          );
        }
        
        img.src = URL.createObjectURL(svgBlob);
      } else {
        console.error("QR code SVG element non trovato");
      }
    }
  };
  
  // Utilizziamo un ref per il div contenitore
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  
  // Riferimento all'SVG
  const svgRef = React.useRef<SVGSVGElement | null>(null);
  
  // Utilizziamo useEffect per impostare l'ID sull'elemento SVG dopo il rendering
  React.useEffect(() => {
    if (containerRef.current) {
      setSvgReady(false); // Reset dello stato ad ogni cambio di dipendenze
      
      // Utilizziamo un sistema di polling più robusto per garantire che l'elemento SVG sia rilevato
      let attempts = 0;
      const maxAttempts = 20; // Aumentato a 20 tentativi
      const checkInterval = 100; // Aumentato a 100ms tra tentativi
      
      const intervalId = setInterval(() => {
        attempts++;
        
        // Cerca l'SVG all'interno del container con una query più specifica
        const svg = containerRef.current?.querySelector('svg');
        
        if (svg) {
          clearInterval(intervalId);
          
          // Salviamo il riferimento all'SVG
          svgRef.current = svg as SVGSVGElement;
          
          // Aggiungiamo sempre la classe per facilitare la ricerca
          svg.classList.add('qr-code-svg');
          
          // Aggiungi l'ID se specificato
          if (id) {
            svg.id = id;
            // Salva il valore del QR code come attributo dati per recuperarlo più facilmente
            svg.setAttribute('data-qr-value', value);
          }

          // Assicuriamoci che width e height siano esplicitati
          if (!svg.hasAttribute('width')) {
            svg.setAttribute('width', size.toString());
          }
          if (!svg.hasAttribute('height')) {
            svg.setAttribute('height', size.toString());
          }
          
          // Marchia l'SVG come pronto immediatamente per evitare problemi di timing
          setSvgReady(true);
        } else if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          
          // Se abbiamo superato i tentativi ma il componente è ancora montato,
          // proviamo a forzare la marcatura come pronto senza mostrare avvisi invasivi
          if (containerRef.current) {
            // Anche se non troviamo l'SVG, marchiamo come pronto per evitare blocchi dell'interfaccia
            // Usiamo console.debug per non riempire la console con avvisi
            if (process.env.NODE_ENV === 'development') {
              console.debug("SVG non trovato ma QR code marcato come pronto per evitare blocchi");
            }
            setSvgReady(true);
          }
        }
      }, checkInterval);

      // Cleanup function
      return () => clearInterval(intervalId);
    }
  }, [id, value, size]); // Dipende dall'ID, valore e dimensione

  return (
    <div 
      className="qrcode-container cursor-pointer hover:scale-105 transition-transform duration-200 rounded-lg p-1"
      onClick={handleClick}
      title="Clicca per aprire il QR code"
      ref={containerRef}
      id={id ? `${id}-container` : undefined}
      data-ready={svgReady ? "true" : "false"}
    >
      <ReactQRCode
        value={value}
        size={size}
        bgColor={bgColor}
        fgColor={fgColor}
        ecLevel={ecLevel}
        logoImage={logoImage}
        logoWidth={logoWidth}
        logoHeight={logoHeight}
      />
    </div>
  );
}

// Componente QRCodeView per la visualizzazione di QR Code con funzionalità aggiuntive
export function QRCodeView({
  code,
  label,
  showDownload = false,
  showPrint = false,
  size = 200,
  bgColor = "#FFFFFF",
  fgColor = "#000000",
  ecLevel = "L",
  logoImage,
  logoWidth,
  logoHeight,
  qrCodeType = QRCodeType.CONTAINER,
  itemName,
  creationDate = new Date(),
  id
}: QRCodeViewProps) {

  // Funzione per gestire il download
  const handleDownload = async () => {
    // Genera il nome del file secondo la nomenclatura specificata
    const nameToUse = itemName || label || "QRCode";
    const fileName = generateQRCodeFilename(nameToUse, qrCodeType, creationDate);
    
    try {
      console.log("Avvio download QR code, valore:", code);
      
      // Importa helper sicuri
      const { generateQRCodeImage, downloadImage } = await import('@/lib/qr-utils');
      const { safePromise } = await import('@/lib/promiseErrorHandler');
      
      // Genera l'immagine QR code in modo sicuro
      const imageData = await safePromise(
        generateQRCodeImage(code, label || nameToUse),
        'Generazione QR code per download'
      );
      
      if (imageData) {
        // Scarica l'immagine come file
        downloadImage(imageData, fileName.replace(/\.png$/, ''));
      }
    } catch (error) {
      console.error("Errore durante il download del QR code:", error);
    }
  };

  // Funzione per gestire la stampa
  const handlePrint = async () => {
    // Genera il nome per la stampa
    const nameToUse = itemName || label || "QRCode";
    
    try {
      console.log("Avvio stampa QR code, valore:", code);
      
      // Importa helper sicuri
      const { generateQRCodeImage } = await import('@/lib/qr-utils');
      const { safePromise } = await import('@/lib/promiseErrorHandler');
      
      // Generiamo l'immagine del QR code in modo sicuro
      const imageDataUrl = await safePromise(
        generateQRCodeImage(code, label || nameToUse),
        'Generazione QR code per stampa'
      );
      
      if (!imageDataUrl) {
        return; // Esci se la generazione è fallita
      }
      
      // Crea un iframe nascosto per la stampa diretta
      const printIframe = document.createElement('iframe');
      printIframe.style.position = 'fixed';
      printIframe.style.top = '-9999px';
      printIframe.style.left = '-9999px';
      printIframe.style.width = '0';
      printIframe.style.height = '0';
      printIframe.style.border = '0';
      document.body.appendChild(printIframe);
      
      // Crea contenuto nell'iframe e stampa direttamente
      if (printIframe.contentDocument) {
        // Determina il tipo di QR code in base al prefisso
        const objectType = code.startsWith('ddt:') ? 'DDT' : 
                          code.startsWith('product:') ? 'ETICHETTA' : 
                          code.startsWith('container:') ? 'CONTENITORE' : 'CODICE';
        
        // Crea il contenuto HTML per la stampa
        printIframe.contentDocument.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Stampa QR Code</title>
            <style>
              body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                padding: 20px;
                box-sizing: border-box;
                font-family: Arial, sans-serif;
              }
              .qr-container {
                background: white;
                padding: 20px;
                text-align: center;
              }
              img {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
              }
              .object-type {
                font-family: "Arial Narrow", Arial, sans-serif;
                font-size: 12px;
                margin-top: 8px;
                margin-bottom: 0;
                color: #000;
              }
              .title {
                font-family: "Arial Narrow", Arial, sans-serif;
                font-size: 30px;
                font-weight: bold;
                margin-top: 5px;
                margin-bottom: 5px;
                color: #000;
                text-transform: uppercase;
              }
              @media print {
                body {
                  margin: 0;
                  padding: 0;
                  height: auto;
                }
                .qr-container {
                  padding: 0;
                  width: 100%;
                }
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <img src="${imageDataUrl}" alt="QR Code" />
              <p class="object-type">${objectType}</p>
              <p class="title">${(label || nameToUse).toUpperCase()}</p>
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.parent.document.body.removeChild(window.frameElement);
                  }, 1000);
                }, 500);
              };
            </script>
          </body>
        </html>
      `);
        
        printIframe.contentDocument.close();
      }
    } catch (error) {
      console.error("Errore durante la stampa del QR code:", error);
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="bg-white p-4 rounded-md mb-2 flex flex-col items-center">
        <QRCode
          value={code}
          size={size}
          bgColor={bgColor}
          fgColor={fgColor}
          ecLevel={ecLevel}
          logoImage={logoImage}
          logoWidth={logoWidth}
          logoHeight={logoHeight}
          qrCodeType={qrCodeType}
          itemName={itemName}
          creationDate={creationDate}
          id={id}
        />
        
        {/* Mostra il tipo di QR code (CONTENITORE, DDT, PRODOTTO) sotto il QR code ma sopra il nome */}
        <div className="text-center mt-0.5 -mb-2">
          <p className="font-bold text-sm uppercase tracking-wide leading-none mb-0 pb-0">
            {code.startsWith('ddt:') ? 'DDT' : 
             code.startsWith('product:') ? 'ETICHETTA' : 
             code.startsWith('container:') ? 'CONTENITORE' : 
             'CODICE'}
          </p>
        </div>
      </div>
      {label && (
        <div className="text-center -mt-2">
          <p className="font-black text-xl uppercase mb-0 tracking-wide leading-none pt-0">{label}</p>
        </div>
      )}
      {(showDownload || showPrint) && (
        <div className="flex gap-4 mt-6 justify-center">
          {showDownload && (
            <button
              onClick={handleDownload}
              className="px-6 py-3 min-w-[120px] text-base font-medium bg-blue-500 text-white rounded-md shadow-md hover:bg-blue-600 transition-all hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center gap-2"
            >
              <Download size={20} />
              Scarica
            </button>
          )}
          {showPrint && (
            <button
              onClick={handlePrint}
              className="px-6 py-3 min-w-[120px] text-base font-medium bg-blue-500 text-white rounded-md shadow-md hover:bg-blue-600 transition-all hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center gap-2"
            >
              <Printer size={20} />
              Stampa
            </button>
          )}
        </div>
      )}
    </div>
  );
}

// Esportiamo il componente come default per compatibilità
export default QRCodeView;