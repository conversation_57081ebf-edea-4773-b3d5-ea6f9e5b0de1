import { RotateCcw } from "lucide-react";

export function OrientationWarning() {
  return (
    <div className="orientation-warning">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-bounce">
          <RotateCcw size={64} />
        </div>
        <h2 className="text-2xl font-bold">Ruota il dispositivo</h2>
        <p className="text-lg text-center max-w-md">
          Questa app funziona meglio in modalità verticale. 
          Ruota il tuo dispositivo per continuare.
        </p>
        <div className="text-sm text-gray-300 text-center">
          <p>Per una migliore esperienza:</p>
          <p>• Attiva il blocco rotazione automatica</p>
          <p>• Mantieni il dispositivo in verticale</p>
        </div>
      </div>
    </div>
  );
}