import React, { useEffect, useRef } from 'react';
import QRCode from 'qrcode';
import * as QRUtils from '@/lib/qr-utils';

interface SimpleQRPrintProps {
  value: string;
  title?: string;
  size?: number;
}

/**
 * Componente semplificato per visualizzare e stampare un QR code
 * Utilizza le funzioni dal modulo qr-utils
 */
export function SimpleQRPrint({ value, title = 'QR Code', size = 200 }: SimpleQRPrintProps) {
  const qrCanvasRef = useRef<HTMLCanvasElement | null>(null);
  
  // Genera il QR code quando il componente viene montato o quando cambia il value
  useEffect(() => {
    if (qrCanvasRef.current) {
      // Pulisci il canvas precedente
      const ctx = qrCanvasRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, size, size);
      }
      
      // Genera un nuovo QR code
      QRCode.toCanvas(
        qrCanvasRef.current,
        value,
        {
          width: size,
          margin: 1,
          errorCorrectionLevel: 'H',
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        },
        (error) => {
          if (error) {
            console.error('Errore nella generazione del QR code:', error);
          }
        }
      );
    }
  }, [value, size]);

  // Utilizza la funzione di stampa ufficiale dalla libreria qr-utils
  const handlePrint = async () => {
    try {
      // Usa QRUtils per evitare il conflitto con la funzione deprecata
      const success = await QRUtils.printQRCode(value, title);
      if (!success) {
        console.error('Errore durante la stampa del QR code');
      }
    } catch (error) {
      console.error('Errore nella stampa:', error);
    }
  };
  
  return (
    <div className="flex flex-col items-center gap-2">
      <canvas 
        ref={qrCanvasRef} 
        width={size} 
        height={size}
        className="border border-gray-200 rounded-md"
      />
      <button 
        onClick={handlePrint}
        className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-md"
      >
        Stampa QR code
      </button>
    </div>
  );
}