import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, X, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useToast } from "@/hooks/use-toast";
import { useSafeAsyncState } from "@/hooks/useSafeAsyncState";

interface ImageViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string | null;
}

export function ImageViewerModal({ isOpen, onClose, imageSrc }: ImageViewerModalProps) {
  const { toast } = useToast();
  const [scale, setScale] = useSafeAsyncState(1);
  const [panning, setPanning] = useSafeAsyncState(false);
  const [position, setPosition] = useSafeAsyncState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useSafeAsyncState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset zoom and position when opening a new image
  useEffect(() => {
    if (isOpen) {
      setScale(1);
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen, imageSrc]);

  const handleDownload = () => {
    if (imageSrc) {
      // Crea un elemento a temporaneo per il download
      const a = document.createElement('a');
      a.href = imageSrc;
      a.download = `immagine-${new Date().getTime()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      toast({
        title: "Download avviato",
        description: "Il download dell'immagine è stato avviato",
        duration: 3000,
      });
    }
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3)); // Limita lo zoom massimo a 3x
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5)); // Limita lo zoom minimo a 0.5x
  };

  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // Gestione del touch per il pinch-to-zoom
  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 2) {
      // Salva la distanza iniziale tra le due dita per il pinch-to-zoom
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY
      );
      
      // Salviamo la distanza iniziale in un attributo data sul container
      if (containerRef.current) {
        containerRef.current.setAttribute('data-initial-pinch-distance', distance.toString());
      }
    } else if (e.touches.length === 1) {
      // Panning
      setPanning(true);
      setStartPosition({
        x: e.touches[0].clientX - position.x,
        y: e.touches[0].clientY - position.y
      });
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault(); // Previeni lo scroll del browser
    
    if (e.touches.length === 2 && containerRef.current) {
      // Pinch-to-zoom
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY
      );
      
      const initialDistance = parseFloat(containerRef.current.getAttribute('data-initial-pinch-distance') || '0');
      if (initialDistance > 0) {
        const zoomDelta = currentDistance / initialDistance;
        const newScale = Math.max(0.5, Math.min(3, zoomDelta)); // Limita lo zoom tra 0.5x e 3x
        setScale(newScale);
      }
    } else if (e.touches.length === 1 && panning) {
      // Panning con il dito
      setPosition({
        x: e.touches[0].clientX - startPosition.x,
        y: e.touches[0].clientY - startPosition.y
      });
    }
  };

  const handleTouchEnd = () => {
    setPanning(false);
    // Resetta l'attributo data usato per il pinch
    if (containerRef.current) {
      containerRef.current.removeAttribute('data-initial-pinch-distance');
    }
  };

  // Stile per l'immagine zoomabile
  const imageStyle = {
    transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
    transition: panning ? 'none' : 'transform 0.2s ease-out',
    transformOrigin: 'center',
    cursor: scale > 1 ? 'grab' : 'default',
    width: '100%',
    height: '100%',
    objectFit: 'contain' as const
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-0 overflow-hidden bg-black" aria-describedby="dialog-description">
        <div id="dialog-description" className="sr-only">Visualizzazione dell'immagine con possibilità di zoom e download</div>
        <DialogTitle>
          <VisuallyHidden>Visualizzazione dell'immagine a dimensione piena</VisuallyHidden>
        </DialogTitle>
        <div className="relative bg-black">
          {/* Controlli sovrapposti all'immagine */}
          <div className="absolute left-0 right-0 top-4 flex items-center justify-between z-10 px-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="relative text-white rounded-full bg-black/60 border border-gray-700/50 shadow-[0_4px_12px_rgba(0,0,0,0.3)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.4)] hover:translate-y-[-2px] active:translate-y-[1px] transition-all duration-300"
            >
              <X size={24} className="text-white" />
              <span className="sr-only">Chiudi</span>
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDownload}
              className="relative text-white rounded-full bg-black/60 border border-gray-700/50 shadow-[0_4px_12px_rgba(0,0,0,0.3)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.4)] hover:translate-y-[-2px] active:translate-y-[1px] transition-all duration-300"
            >
              <Download size={24} className="text-white" />
              <span className="sr-only">Scarica immagine</span>
            </Button>
          </div>
          
          {/* Controlli zoom in basso */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleZoomOut}
              disabled={scale <= 0.5}
              className="relative text-white rounded-full bg-black/60 border border-gray-700/50 shadow-lg hover:bg-black/70"
            >
              <ZoomOut size={20} />
              <span className="sr-only">Riduci zoom</span>
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleReset}
              className="relative text-white rounded-full bg-black/60 border border-gray-700/50 shadow-lg hover:bg-black/70"
            >
              <RotateCcw size={20} />
              <span className="sr-only">Reimposta zoom</span>
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleZoomIn}
              disabled={scale >= 3}
              className="relative text-white rounded-full bg-black/60 border border-gray-700/50 shadow-lg hover:bg-black/70"
            >
              <ZoomIn size={20} />
              <span className="sr-only">Aumenta zoom</span>
            </Button>
          </div>
          
          {/* Container immagine con supporto touch */}
          {imageSrc && (
            <div 
              ref={containerRef}
              className="w-full h-[80vh] flex items-center justify-center overflow-hidden touch-none"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <img 
                src={imageSrc} 
                alt="Immagine a dimensione piena" 
                style={imageStyle}
                draggable={false}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}