import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { isDateValid } from '@/lib/memoizedDateUtils';
import { useSafeAsyncState, useSafeAsyncEffect } from '@/hooks/useSafeAsyncState';

interface SimpleProductSelectorProps {
  onSelect: (productId: number, productName: string) => void;
  currentValue?: number;
  showValidOnly?: boolean;
}

// Interfaccia per i prodotti
interface Product {
  id: number;
  productName: string;
  batchNumber?: string;
  expiryDate?: string;
  isValid?: boolean;
  [key: string]: any; // Permettiamo proprietà aggiuntive dal server
}

// Componente ultra-semplificato per la selezione di prodotti
export function SimpleProductSelector({ 
  onSelect, 
  currentValue, 
  showValidOnly = false 
}: SimpleProductSelectorProps) {
  const [products, setProducts] = useSafeAsyncState<Product[]>([]);
  const [isLoading, setIsLoading] = useSafeAsyncState(true);
  const [error, setError] = useSafeAsyncState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useSafeAsyncState<string>("");



  // Carica i prodotti una sola volta all'avvio
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch('/api/product-labels', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        setProducts(data || []);
        setError(null);
      } catch (err) {
        console.error('Errore nel caricamento dei prodotti:', err);
        setError('Errore nel caricamento dei prodotti');
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchProducts();
  }, []); // Solo al mount iniziale

  // Gestisce la selezione di un prodotto
  const handleProductSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    
    if (value) {
      const productId = parseInt(value, 10);
      const selectedProduct = products.find(p => p.id === productId);
      if (selectedProduct) {
        setSelectedProductId(value);
        onSelect(productId, selectedProduct.productName);
      }
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Caricamento prodotti...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-red-600 text-sm">
        {error}
      </div>
    );
  }

  // Filtra i prodotti in base alla validità della data di scadenza se richiesto
  const filteredProducts = showValidOnly 
    ? products.filter(p => !p.expiryDate || isDateValid(p.expiryDate))
    : products;

  // Ordina i prodotti per nome
  const sortedProducts = filteredProducts.sort((a, b) => 
    a.productName.localeCompare(b.productName)
  );

  return (
    <div className="w-full">
      <select
        value={selectedProductId}
        onChange={handleProductSelect}
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option value="">
          {showValidOnly ? "Seleziona un prodotto valido..." : "Seleziona un prodotto..."}
        </option>
        {sortedProducts.map((product) => {
          const productIsExpired = product.expiryDate ? !isDateValid(product.expiryDate) : false;
          return (
            <option 
              key={product.id} 
              value={product.id}
              disabled={showValidOnly && productIsExpired}
            >
              {product.productName}
              {product.expiryDate && ` - ${product.expiryDate}`}
              {productIsExpired && " [SCADUTO]"}
            </option>
          );
        })}
      </select>
      
      {sortedProducts.length === 0 && (
        <p className="text-gray-500 text-sm mt-2">
          {showValidOnly ? "Nessun prodotto valido disponibile" : "Nessun prodotto disponibile"}
        </p>
      )}
    </div>
  );
}

// Default export for backward compatibility
export default SimpleProductSelector;