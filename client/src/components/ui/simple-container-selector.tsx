import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useSafeAsyncState, useSafeAsyncEffect } from '@/hooks/useSafeAsyncState';

interface SimpleContainerSelectorProps {
  onSelect: (containerId: number, containerName: string) => void;
  productId?: number; // ID del prodotto selezionato (opzionale)
}

// Interfaccia per i contenitori
interface Container {
  id: number;
  name: string;
  type: string;
  currentItems: number;
  maxItems: number;
  isArchived: boolean;
  [key: string]: any; // Permettiamo proprietà aggiuntive dal server
}

// Componente ultra-semplificato per la selezione di contenitori
export function SimpleContainerSelector({ onSelect, productId }: SimpleContainerSelectorProps) {
  const [selectedContainerId, setSelectedContainerId] = useSafeAsyncState<string>('');
  const [contenitori, setContenitori] = useSafeAsyncState<Container[]>([]);
  const [isLoading, setIsLoading] = useSafeAsyncState<boolean>(true);
  const [error, setError] = useSafeAsyncState<string | null>(null);
  // Impostiamo il valore predefinito su false - mostra solo contenitori attivi
  const [showArchived, setShowArchived] = useSafeAsyncState<boolean>(false);
  const queryClient = useQueryClient();

  // Carica i contenitori dall'API usando apiRequest
  useEffect(() => {
    async function fetchContainers() {
      try {
        setIsLoading(true);
        console.log("🚀 Caricamento contenitori da API");
        
        // Importa helper sicuri per API
        const { apiRequest } = await import('@/lib/queryClient');
        const { safeApiCall } = await import('@/lib/promiseErrorHandler');
        
        const apiContainers = await safeApiCall(
          () => apiRequest('/api/containers', 'GET'),
          'Caricamento contenitori'
        );
        
        if (apiContainers) {
          console.log(`📦 Ricevuti ${apiContainers.length} contenitori dall'API`);
          
          if (apiContainers.length === 0) {
            console.log("⚠️ Nessun contenitore ricevuto dall'API");
          } else {
            console.log("📄 Primo contenitore ricevuto:", apiContainers[0]);
          }
          
          setContenitori(apiContainers);
        } else {
          throw new Error('Impossibile caricare contenitori dall\'API');
        }
      } catch (err) {
        console.error("❌ Errore:", err);
        setError("Errore nel caricamento dei contenitori: " + (err instanceof Error ? err.message : String(err)));
        
        // In caso di errori, fornisci contenitori di esempio solo in ambiente di sviluppo
        if (import.meta.env.DEV) {
          console.log("⚠️ Caricamento dati di esempio in ambiente di sviluppo");
          const demoContainers = [
            { id: 1, name: "Contenitore A", type: "Frigo", currentItems: 3, maxItems: 5, isArchived: false },
            { id: 2, name: "Contenitore B", type: "Dispensa", currentItems: 1, maxItems: 10, isArchived: false },
            { id: 3, name: "Contenitore C", type: "Freezer", currentItems: 0, maxItems: 8, isArchived: false },
            { id: 4, name: "Contenitore D", type: "Dispensa", currentItems: 10, maxItems: 10, isArchived: false },
            { id: 5, name: "Contenitore Vecchio", type: "Frigo", currentItems: 0, maxItems: 5, isArchived: true }
          ];
          
          setContenitori(demoContainers);
          setError("Usando dati di esempio (sviluppo)");
        }
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchContainers();
  }, []);

  // Gestisce la selezione di un contenitore
  const handleContainerSelect = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (!value) return;
    
    const containerId = parseInt(value, 10);
    const container = contenitori.find(c => c.id === containerId);
    
    if (container) {
      console.log(`SimpleContainerSelector: Contenitore selezionato: ${container.name} (ID: ${container.id})`);
      setSelectedContainerId(value);
      
      // Esegui la callback che notifica il componente padre della selezione
      onSelect(containerId, container.name);
      console.log('Richiamata callback onSelect per il container');
      
      // Se abbiamo un productId, facciamo direttamente l'associazione da qui
      if (productId) {
        try {
          console.log(`Avvio associazione diretta: prodotto ${productId} a container ${containerId}`);
          
          // Usiamo helper sicuri per l'associazione
          const { apiRequest } = await import('@/lib/queryClient');
          const { safeApiCall } = await import('@/lib/promiseErrorHandler');
          
          // Esegui la richiesta POST per associare il prodotto al container
          const success = await safeApiCall(
            () => apiRequest('POST', '/api/container-products', {
              containerId,
              productLabelId: productId
            }),
            'Associazione prodotto-container'
          );
          
          if (!success) {
            throw new Error('Associazione fallita');
          }
          
          console.log("✅ Associazione completata con successo!");
          
          // Invalida le query relative ai container e ai prodotti
          if (productId) {
            queryClient.invalidateQueries({ queryKey: [`/api/container-products/product/${productId}`] });
          }
          // Invalida la lista generale dei container
          queryClient.invalidateQueries({ queryKey: ['/api/containers'] });
          // Invalida il dettaglio specifico del container selezionato
          queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
          // Invalida anche i prodotti nel container
          queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}/products`] });
          
          // Mostra un messaggio di conferma con il toast
          toast({
            title: "Associazione completata",
            description: `Prodotto associato a ${container.name} con successo!`
          });
          
          // Reimpostiamo il select dopo un breve ritardo
          setTimeout(() => {
            setSelectedContainerId('');
          }, 1000);
        } catch (error: any) {
          console.error("❌ Errore durante l'associazione:", error);
          
          // Mostra un messaggio di errore dettagliato
          toast({
            title: "Errore nell'associazione",
            description: error.message || "Impossibile associare il prodotto al container",
            variant: "destructive"
          });
        }
      }
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50 rounded-md text-center">
        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
        <p>Caricamento container...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md text-center text-red-600">
        <p className="font-medium">Errore:</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  // Filtra i contenitori in base allo stato del toggle
  const filteredContainers = showArchived
    ? contenitori
    : contenitori.filter(container => !container.isArchived);
    
  // Filtra per disponibilità (non pieni)
  const availableContainers = filteredContainers.filter(container => 
    container.currentItems < container.maxItems
  );

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="mb-4 flex flex-col">
        <div className="flex justify-between items-center mb-2">
          <label className="text-sm font-medium text-gray-600">Filtra container</label>
          <span className="text-sm text-gray-600 font-medium">
            {showArchived ? "Tutti" : "Solo attivi"}
          </span>
        </div>
        <div 
          className={`relative w-full h-6 flex items-center rounded-full border-2 cursor-pointer ${showArchived ? 'bg-blue-100 border-blue-300' : 'bg-gray-200 border-gray-300'}`}
          onClick={() => setShowArchived(!showArchived)}
          role="switch"
          aria-checked={showArchived}
        >
          <span 
            aria-hidden="true" 
            className={`${showArchived ? 'translate-x-full -ml-5' : 'translate-x-0 ml-0.5'}
              pointer-events-none inline-block h-5 w-5 transform rounded-full 
              ${showArchived ? 'bg-blue-600' : 'bg-white'} shadow transition duration-200 ease-in-out`}
          ></span>
        </div>
      </div>
      
      {availableContainers.length === 0 ? (
        <div className="p-3 bg-gray-50 rounded-md text-center text-gray-500">
          <div className="text-red-500">
            Nessun contenitore {!showArchived ? "attivo" : ""} disponibile
            {!showArchived && contenitori.some(c => c.isArchived) && " o tutti i contenitori sono pieni"}
          </div>
          {!showArchived && contenitori.some(c => c.isArchived) && (
            <button 
              onClick={() => setShowArchived(true)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Mostra anche contenitori archiviati
            </button>
          )}
        </div>
      ) : (
        <div className="relative">
          <select
            className="w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-base shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 appearance-none pr-10"
            value={selectedContainerId}
            onChange={handleContainerSelect}
          >
            <option value="">Seleziona un contenitore</option>
            {availableContainers.map((container) => (
              <option 
                key={container.id} 
                value={container.id.toString()}
                disabled={container.currentItems >= container.maxItems}
              >
                {container.name} ({container.type}) - {container.currentItems}/{container.maxItems}
                {container.isArchived ? " (Archiviato)" : ""}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
      )}
      
      {productId && (
        <div className="mt-4 text-center text-gray-600 text-sm">
          <p>Stai aggiungendo il prodotto #{productId} al container selezionato</p>
        </div>
      )}
    </div>
  );
}