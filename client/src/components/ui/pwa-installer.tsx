import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Bell, Download, WifiOff, RefreshCw } from "lucide-react";
import { arePushNotificationsSupported, requestNotificationPermission, subscribeToPushNotifications } from '@/lib/push-notifications';
import { useToast } from '@/hooks/use-toast';
import { pwaState, updateServiceWorker } from '@/lib/pwaManager';

/**
 * PWA Installation Status
 */
type PWAStatus = 'not-installed' | 'installed' | 'can-install' | 'not-supported';

/**
 * PWA Installer and Manager Component
 * Provides installation prompt and notification management
 */
export function PWAInstaller() {
  const [pwaStatus, setPwaStatus] = useState<PWAStatus>('not-supported');
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [needRefresh, setNeedRefresh] = useState<boolean>(false);
  const { toast } = useToast();

  // Check if the app is already installed
  const checkInstallation = useCallback(() => {
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setPwaStatus('installed');
    } else if (deferredPrompt) {
      setPwaStatus('can-install');
    } else if ('serviceWorker' in navigator) {
      setPwaStatus('not-installed');
    } else {
      setPwaStatus('not-supported');
    }
  }, [deferredPrompt]);

  // Check network status
  const updateOnlineStatus = useCallback(() => {
    setIsOnline(navigator.onLine);
  }, []);

  // Check notification permission status
  const checkNotificationPermission = useCallback(() => {
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
    }
  }, []);

  // Install PWA handler
  const handleInstall = async () => {
    if (!deferredPrompt) {
      toast({
        title: "Installazione non disponibile",
        description: "L'app non può essere installata in questo momento.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Importa helper sicuro per Promise
      const { safePromise } = await import('@/lib/promiseErrorHandler');
      
      // Wait for the user to respond to the prompt
      const choiceResult = await safePromise(
        deferredPrompt.userChoice,
        'Installazione PWA',
        false // Non mostrare errore toast, gestiamo manualmente
      );
    
      if (choiceResult?.outcome === 'accepted') {
        toast({
          title: "Installazione completata",
          description: "HACCP Tracker è stata installata correttamente.",
        });
      } else if (choiceResult?.outcome === 'dismissed') {
        toast({
          title: "Installazione annullata",
          description: "Puoi sempre installare l'app in un secondo momento.",
        });
      }
      
      // Clear the prompt reference
      setDeferredPrompt(null);
      checkInstallation();
    } catch (error) {
      console.error('Errore durante installazione PWA:', error);
      toast({
        title: "Errore installazione",
        description: "Impossibile completare l'installazione dell'app.",
        variant: "destructive"
      });
    }
  };

  // Enable notifications handler
  const handleEnableNotifications = async () => {
    try {
      // Importa helper sicuro per API calls
      const { safePromise } = await import('@/lib/promiseErrorHandler');
      
      const permission = await safePromise(
        requestNotificationPermission(),
        'Richiesta permessi notifiche'
      );
      
      if (permission) {
        setNotificationPermission(permission);
        
        if (permission === 'granted') {
          const subscription = await safePromise(
            subscribeToPushNotifications(),
            'Sottoscrizione notifiche push'
          );
        
          if (subscription) {
            toast({
              title: "Notifiche abilitate",
              description: "Riceverai notifiche sugli aggiornamenti importanti.",
            });
          }
        } else {
          toast({
            title: "Notifiche non abilitate",
            description: "Per ricevere notifiche, concedi il permesso nelle impostazioni del browser.",
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error('Error enabling notifications:', error);
      toast({
        title: "Errore",
        description: "Non è stato possibile abilitare le notifiche.",
        variant: "destructive"
      });
    }
  };

  // Handle app update
  const handleUpdate = async () => {
    if (pwaState.updateSW) {
      try {
        // Importa helper sicuro per Service Worker
        const { safeServiceWorkerCall } = await import('@/lib/promiseErrorHandler');
        
        const success = await safeServiceWorkerCall(
          () => updateServiceWorker(),
          'Aggiornamento Service Worker'
        );
        
        if (success) {
          setNeedRefresh(false);
          toast({
            title: "Aggiornamento in corso",
            description: "L'app verrà ricaricata con la nuova versione.",
          });
        } else {
          throw new Error('Aggiornamento Service Worker fallito');
        }
      } catch (error) {
        console.error('Error updating service worker:', error);
        toast({
          title: "Errore di aggiornamento",
          description: "Non è stato possibile aggiornare l'app. Riprova più tardi.",
          variant: "destructive"
        });
      }
    }
  };

  // Effect to listen for beforeinstallprompt event and app updates
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: any) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      
      // Store the event for later use
      setDeferredPrompt(e);
      setPwaStatus('can-install');
    };

    // Controlla periodicamente se ci sono aggiornamenti disponibili
    const checkForUpdates = () => {
      if (pwaState.needRefresh) {
        setNeedRefresh(true);
      }
    };

    // Inizia a controllare gli aggiornamenti
    const updateCheckInterval = setInterval(checkForUpdates, 10000);
    checkForUpdates(); // Controlla subito
    
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', checkInstallation);
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Initial checks
    checkInstallation();
    checkNotificationPermission();
    updateOnlineStatus();

    return () => {
      clearInterval(updateCheckInterval);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', checkInstallation);
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, [checkInstallation, checkNotificationPermission, updateOnlineStatus]);

  if (pwaStatus === 'not-supported') {
    return null; // Don't render anything if PWA is not supported
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="pt-6">
        {!isOnline && (
          <div className="mb-4 p-2 bg-amber-50 border border-amber-200 rounded-md flex items-center gap-2 text-amber-700">
            <WifiOff size={18} />
            <span>Modalità offline. Alcune funzionalità potrebbero essere limitate.</span>
          </div>
        )}

        <div className="space-y-4">
          {needRefresh && (
            <div className="mb-4 p-2 bg-blue-50 border border-blue-200 rounded-md text-blue-700">
              <h3 className="font-medium flex items-center gap-2">
                <RefreshCw size={18} className="animate-spin" />
                Aggiornamento disponibile
              </h3>
              <p className="text-sm mb-2">
                È disponibile una nuova versione dell'applicazione.
              </p>
              <Button onClick={handleUpdate} className="w-full" variant="outline" size="sm">
                Aggiorna ora
              </Button>
            </div>
          )}
        
          {pwaStatus === 'can-install' && (
            <div className="border rounded-lg p-3">
              <h3 className="font-medium mb-1">Installa l'app</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Installa HACCP Tracker sul tuo dispositivo per un accesso più veloce e funzionalità offline.
              </p>
              <Button onClick={handleInstall} className="w-full" variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Installa
              </Button>
            </div>
          )}

          {pwaStatus === 'installed' && (
            <div className="border rounded-lg p-3 bg-muted/30">
              <h3 className="font-medium mb-1">App installata</h3>
              <p className="text-sm text-muted-foreground">
                HACCP Tracker è installata sul tuo dispositivo.
              </p>
            </div>
          )}

          {arePushNotificationsSupported() && notificationPermission !== 'granted' && (
            <>
              <h3 className="font-medium mb-3">Notifiche Push</h3>
              <div className="space-y-3 mb-4">
                <p className="text-sm font-medium">Scegli quali notifiche ricevere:</p>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Prodotti in scadenza (24h prima)</span>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Stock in esaurimento</span>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Nuovi DDT da processare</span>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Aggiornamenti di sistema</span>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
              <Button onClick={handleEnableNotifications} className="w-full" variant="outline">
                <Bell className="mr-2 h-4 w-4" />
                Abilita notifiche selezionate
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}