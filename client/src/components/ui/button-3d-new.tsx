import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type LucideIcon } from "lucide-react";

interface Button3DProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: LucideIcon;
  iconSize?: number;
  className?: string;
}

const Button3D = forwardRef<HTMLButtonElement, Button3DProps>(
  ({ icon: Icon, iconSize = 24, className, ...props }, ref) => {
    // Creiamo un componente personalizzato che non eredita stili hover dal Button base
    return (
      <button
        ref={ref}
        type="button"
        className={cn(
          "flex h-10 w-10 items-center justify-center rounded-full bg-black/65",
          "focus:outline-none", // Solo focus outline per accessibilità
          className
        )}
        {...props}
      >
        <Icon 
          style={{ 
            width: `26px`, 
            height: `26px`
          }} 
          className="text-white" 
        />
      </button>
    );
  }
);

Button3D.displayName = "Button3D";

export { Button3D };