import React from 'react';
import { useOfflineApi } from '@/hooks/use-offline-api';
import { WifiOff, Cloud, Signal } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface SyncStatusProps {
  showBadge?: boolean;
  showText?: boolean;
  className?: string;
  alwaysVisible?: boolean;
}

/**
 * Componente che mostra lo stato di sincronizzazione dell'app
 */
export function SyncStatus({ showBadge = true, showText = false, className = '', alwaysVisible = false }: SyncStatusProps) {
  const { isOnline, pendingOperations } = useOfflineApi();
  
  // Determina il colore e il testo in base allo stato
  let statusColor = '';
  let statusBgColor = '';
  let statusText = '';
  let Icon = Signal;
  let isAnimated = false;
  
  if (!isOnline) {
    statusColor = 'text-amber-500';
    statusBgColor = 'bg-amber-500';
    statusText = 'Offline';
    Icon = WifiOff;
  } else if (pendingOperations > 0) {
    statusColor = 'text-blue-500';
    statusBgColor = 'bg-blue-500';
    statusText = `Sincronizzazione (${pendingOperations})`;
    Icon = Cloud;
    isAnimated = true; // Aggiungiamo animazione per indicare sincronizzazione
  } else {
    statusColor = 'text-green-600';
    statusBgColor = 'bg-green-600';
    statusText = 'Sincronizzato';
    Icon = Signal;
  }
  
  // Se non ci sono operazioni pendenti, siamo online, non è richiesto testo e non è richiesto di rimanere sempre visibile, nascondi
  if (isOnline && pendingOperations === 0 && !showText && !alwaysVisible) {
    return null;
  }
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-1 ${className}`}>
            {showBadge && (
              <Badge variant="outline" className={`${statusBgColor} text-white border-none px-2 py-0.5`}>
                <Icon size={14} className={`mr-1 ${isAnimated ? 'animate-pulse' : ''}`} />
                {pendingOperations > 0 && isOnline && (
                  <span className="text-xs">{pendingOperations}</span>
                )}
              </Badge>
            )}
            
            {!showBadge && (
              <div className={`relative flex items-center justify-center ${isAnimated ? 'animate-pulse' : ''}`}>
                <Icon size={24} className={`${statusColor}`} />
                {!isOnline && (
                  <div className="absolute w-3 h-3 bg-amber-500 rounded-full border-2 border-white top-0 right-0 -mt-1 -mr-1"></div>
                )}
                {pendingOperations > 0 && isOnline && (
                  <div className="absolute w-3 h-3 bg-blue-500 rounded-full border-2 border-white top-0 right-0 -mt-1 -mr-1 animate-ping"></div>
                )}
              </div>
            )}
            
            {showText && (
              <span className="text-xs ml-1">{statusText}</span>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          {!isOnline ? (
            <p>Modalità offline attiva. Le modifiche verranno sincronizzate quando tornerai online.</p>
          ) : pendingOperations > 0 ? (
            <p>Sincronizzazione in corso: {pendingOperations} operazioni in attesa</p>
          ) : (
            <p>Tutti i dati sono sincronizzati</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}