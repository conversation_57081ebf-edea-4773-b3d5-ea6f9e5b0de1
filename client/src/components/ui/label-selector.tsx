import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';

interface LabelSelectorProps {
  onSelect: (labelId: number) => void;
  currentValue?: number;
}

interface ProductLabel {
  id: number;
  productName: string;
  expiryDate?: string;
  createdAt: string;
  batchNumber?: string;
}

export function LabelSelector({ onSelect, currentValue }: LabelSelectorProps) {
  const [labels, setLabels] = useState<ProductLabel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLabels = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch('/api/product-labels', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Sort by creation date (most recent first)
        const sortedLabels = data.sort((a: ProductLabel, b: ProductLabel) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
        
        setLabels(sortedLabels);
      } catch (err) {
        console.error('Error fetching labels:', err);
        setError('Errore nel caricamento delle etichette');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLabels();
  }, []);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      // Check if the date is already in DD/MM/YYYY format
      if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        return dateString; // Already in correct format
      }
      
      // Try to parse other formats
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleDateString('it-IT');
    } catch {
      return '';
    }
  };

  const isExpired = (dateString?: string) => {
    if (!dateString) return false;
    
    try {
      let date: Date;
      
      // Check if the date is in DD/MM/YYYY format
      if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        const { safeParseDateToDate } = require('../../../shared/safe-date-utils');
        date = safeParseDateToDate(dateString);
        if (!date) {
          return false; // Se il parsing fallisce, non è scaduto
        }
      } else {
        date = new Date(dateString);
      }
      
      if (isNaN(date.getTime())) return false;
      
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day for comparison
      date.setHours(0, 0, 0, 0);
      
      return date < today;
    } catch {
      return false;
    }
  };

  const getDisplayText = (label: ProductLabel) => {
    const formattedDate = formatDate(label.expiryDate);
    // Mostra solo nome prodotto e data di scadenza
    if (formattedDate) {
      return `${label.productName} - ${formattedDate}`;
    }
    return label.productName;
  };

  // Filter out expired products
  const validLabels = labels.filter(label => !isExpired(label.expiryDate));

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span>Caricamento etichette...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-sm p-2">
        {error}
      </div>
    );
  }

  return (
    <Select
      value={currentValue?.toString() || ""}
      onValueChange={(value) => {
        if (value) {
          onSelect(parseInt(value));
        }
      }}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Seleziona un'etichetta..." />
      </SelectTrigger>
      <SelectContent>
        {validLabels.length === 0 ? (
          <SelectItem value="no-labels" disabled>
            Nessuna etichetta disponibile
          </SelectItem>
        ) : (
          validLabels.map((label) => (
            <SelectItem key={label.id} value={label.id.toString()}>
              {getDisplayText(label)}
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  );
}