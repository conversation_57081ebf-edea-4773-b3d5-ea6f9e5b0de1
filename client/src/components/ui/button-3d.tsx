import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";

interface Button3DProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: LucideIcon;
  iconSize?: number;
  className?: string;
  onClick?: () => void;
}

/**
 * Componente Button3D
 * 
 * Un pulsante con effetto 3D che utilizza l'icona fornita.
 * Non ha stati di hover, solo uno stile di base con effetto tridimensionale.
 */
export const Button3D = forwardRef<HTMLButtonElement, Button3DProps>(
  ({ icon: Icon, iconSize = 24, className, onClick, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        variant="ghost"
        size="icon"
        className={cn(
          "relative rounded-full bg-black/65 border border-gray-700/50 shadow-[0_4px_10px_rgba(0,0,0,0.35)] transform-gpu",
          className
        )}
        onClick={onClick}
        {...props}
      >
        <Icon 
          style={{ 
            width: `${iconSize}px`, 
            height: `${iconSize}px`, 
            filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.4))', 
            transform: 'translateZ(5px) scale(1.2)' 
          }} 
          className="text-white" 
        />
      </Button>
    );
  }
);

Button3D.displayName = "Button3D";