import React, { useState, useEffect } from 'react';
import { Loader2, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProductSelectorProps {
  onSelect: (productId: number, productName: string) => void;
}

// Dati di prodotto fittizi per simulazione
const mockProducts = [
  { id: 1, productName: "Pomodori", batchNumber: "B12345", expiryDate: "15/05/25", isValid: true },
  { id: 2, productName: "Insalata", batchNumber: "B12346", expiryDate: "10/05/25", isValid: true },
  { id: 3, productName: "Patate", batchNumber: "B12347", expiryDate: "30/06/25", isValid: true },
  { id: 4, productName: "Cipolle", batchNumber: "B12348", expiryDate: "25/05/25", isValid: true },
  { id: 5, productName: "Carote", batchNumber: "B12349", expiryDate: "01/05/25", isValid: false }
];

// Componente semplificato per la selezione di prodotti
export function ProductSelector({ onSelect }: ProductSelectorProps) {
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [products, setProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(true);
  const [showOnlyValid, setShowOnlyValid] = useState<boolean>(true);
  const [useTestData, setUseTestData] = useState<boolean>(false);

  // Carica i prodotti dall'API
  useEffect(() => {
    async function fetchProducts() {
      try {
        setIsLoading(true);
        console.log("🚀 Caricamento prodotti dall'API");
        
        // Per debug: carica i dati di test se non è possibile recuperare dati dal server
        let shouldUseTestData = false;
        
        // Controlliamo prima lo stato di autenticazione con helper sicuro
        try {
          const { safePromise } = await import('@/lib/promiseErrorHandler');
          
          const authResponse = await safePromise(
            fetch('/api/auth/me', {
              credentials: 'include',
              headers: {
                'Content-Type': 'application/json'
              }
            }),
            'Verifica autenticazione',
            false // Non mostrare toast di errore
          );
          
          if (!authResponse || authResponse.status === 401) {
            console.warn("⚠️ Utente non autenticato");
            shouldUseTestData = true;
            setIsAuthenticated(false);
          }
        } catch (err) {
          console.error("❌ Errore durante la verifica dell'autenticazione:", err);
          shouldUseTestData = true;
        }
        
        // Se non siamo autenticati oppure c'è stato un errore, usiamo dati di test
        if (shouldUseTestData) {
          console.log("🔄 Usando dati di test per dimostrare la funzionalità");
          setUseTestData(true);
          setProducts(mockProducts);
          setIsLoading(false);
          return;
        }
        
        // Se siamo autenticati, procediamo con il caricamento dei prodotti con helper sicuro
        const { safeApiCall } = await import('@/lib/promiseErrorHandler');
        
        const data = await safeApiCall(
          async () => {
            const response = await fetch('/api/product-labels/valid', {
              credentials: 'include',
              headers: {
                'Content-Type': 'application/json'
              }
            });
            
            if (!response.ok) {
              throw new Error(`Errore API: ${response.status}`);
            }
            
            return await response.json();
          },
          'Caricamento prodotti validi'
        );
        
        if (!data) {
          throw new Error('Impossibile caricare prodotti dall\'API');
        }
        console.log("✅ Prodotti ricevuti:", data);
        
        if (Array.isArray(data) && data.length > 0) {
          // Converti e formatta bene le date
          const productsWithParsedDates = data.map(product => {
            const isDateValid = (dateStr: string | null | undefined): boolean => {
              if (!dateStr) return true;
              
              try {
                // Format: DD/MM/YYYY or DD/MM/YY
                const parts = dateStr.split('/');
                if (parts.length !== 3) return true;
                
                const day = parseInt(parts[0], 10);
                const month = parseInt(parts[1], 10) - 1;
                let year = parseInt(parts[2], 10);
                
                // Handle 2-digit years (assuming 20XX for YY < 50)
                if (year < 100) {
                  year = year < 50 ? 2000 + year : 1900 + year;
                }
                
                const expiryDate = new Date(year, month, day);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                return expiryDate >= today;
              } catch (e) {
                return true;
              }
            };
            
            // Aggiungi una proprietà per tracciare se il prodotto è valido
            return {
              ...product,
              isValid: isDateValid(product.expiryDate)
            };
          });
          
          setProducts(productsWithParsedDates);
        } else {
          console.log("⚠️ Nessun prodotto trovato o formato non valido");
          setProducts([]);
        }
      } catch (err) {
        console.error("❌ Errore:", err);
        setError("Errore nel caricamento dei prodotti");
        
        // Se c'è stato un errore nel caricamento, usa dati di test
        console.log("🔄 Usando dati di test per dimostrare la funzionalità dopo errore");
        setUseTestData(true);
        setProducts(mockProducts);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchProducts();
  }, []);

  // Reindirizza al login
  const handleLogin = () => {
    window.location.href = '/login';
  };

  // Gestisce la selezione di un prodotto
  const handleProductSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (!value) return;
    
    const productId = parseInt(value, 10);
    const product = products.find(p => p.id === productId);
    
    if (product) {
      setSelectedProductId(value);
      onSelect(productId, product.productName);
    }
  };

  // Mostra avviso se stiamo usando dati di test
  const TestDataBanner = () => {
    if (!useTestData) return null;
    
    return (
      <div className="mb-3 p-2 bg-blue-50 text-blue-800 text-xs text-center rounded border border-blue-200 flex items-center justify-center">
        <Lock className="w-3 h-3 mr-1" />
        <span>Modalità dimostrativa (accesso non autorizzato)</span>
      </div>
    );
  };

  // Render del componente durante il caricamento
  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50 rounded-md text-center">
        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
        <p>Caricamento prodotti...</p>
      </div>
    );
  }

  // Render del componente in caso di errore (quando non è fallback data)
  if (error && !useTestData) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md text-center text-red-600">
        <p className="font-medium">Errore:</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  // Se non siamo autenticati e non stiamo usando dati di test, mostra messaggio di login
  if (!isAuthenticated && !useTestData) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md text-center">
        <p className="font-medium text-yellow-800 mb-2">Sessione scaduta</p>
        <p className="text-sm text-yellow-700 mb-3">Effettua il login per accedere ai prodotti</p>
        <Button onClick={handleLogin} variant="outline" className="bg-white">
          Vai al login
        </Button>
      </div>
    );
  }

  // Filtra i prodotti in base al toggle "Solo validi"
  const filteredProducts = showOnlyValid 
    ? products.filter(product => product.isValid) 
    : products;

  // Render del componente quando non ci sono prodotti
  if (filteredProducts.length === 0) {
    return (
      <div className="w-full max-w-md mx-auto">
        <TestDataBanner />
        
        <div className="mb-4 flex items-center justify-between">
          <span className="text-gray-600 font-medium">
            Filtra scaduti:
          </span>
          <div className="inline-flex items-center">
            <div 
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${showOnlyValid ? 'bg-blue-600' : 'bg-gray-300'}`}
              onClick={() => setShowOnlyValid(!showOnlyValid)}
              role="switch"
              aria-checked={showOnlyValid}
            >
              <span 
                aria-hidden="true" 
                className={`${showOnlyValid ? 'translate-x-5' : 'translate-x-0'}
                  pointer-events-none inline-block h-5 w-5 transform rounded-full 
                  bg-white shadow ring-0 transition duration-200 ease-in-out`}
              ></span>
            </div>
            <span className="ml-2 text-sm text-gray-600 font-medium">
              Solo validi
            </span>
          </div>
        </div>
        
        <div className="p-3 bg-gray-50 rounded-md text-center text-gray-500">
          <div className="text-red-500">
            Nessun prodotto {showOnlyValid ? "valido" : ""} disponibile
          </div>
          {showOnlyValid && products.length > 0 && (
            <button 
              onClick={() => setShowOnlyValid(false)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Mostra anche prodotti scaduti
            </button>
          )}
        </div>
      </div>
    );
  }

  // Render del componente con i prodotti disponibili
  return (
    <div className="w-full max-w-md mx-auto">
      <TestDataBanner />
      
      <div className="mb-4 flex items-center justify-between">
        <span className="text-gray-600 font-medium">
          Filtra scaduti:
        </span>
        <div className="inline-flex items-center">
          <div 
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${showOnlyValid ? 'bg-blue-600' : 'bg-gray-300'}`}
            onClick={() => setShowOnlyValid(!showOnlyValid)}
            role="switch"
            aria-checked={showOnlyValid}
          >
            <span 
              aria-hidden="true" 
              className={`${showOnlyValid ? 'translate-x-5' : 'translate-x-0'}
                pointer-events-none inline-block h-5 w-5 transform rounded-full 
                bg-white shadow ring-0 transition duration-200 ease-in-out`}
            ></span>
          </div>
          <span className="ml-2 text-sm text-gray-600 font-medium">
            Solo validi
          </span>
        </div>
      </div>
      
      <div className="relative">
        <select
          className="w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-base shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 appearance-none pr-10"
          value={selectedProductId}
          onChange={handleProductSelect}
        >
          <option value="">Seleziona un prodotto</option>
          {filteredProducts.map((product) => (
            <option key={product.id} value={product.id.toString()}>
              {product.productName} - Lotto: {product.batchNumber || 'N/D'} {product.expiryDate ? `(Scad: ${product.expiryDate})` : ''}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
          </svg>
        </div>
      </div>
    </div>
  );
}