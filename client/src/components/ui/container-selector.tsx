import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { useSafeAsyncState } from '@/hooks/useSafeAsyncState';

interface ContainerSelectorProps {
  onSelect: (containerId: number) => void;
  currentValue?: number;
}

interface Container {
  id: number;
  name: string;
  maxItems: number;
  currentItems: number;
  isArchived: boolean;
  type: string;
}

export function ContainerSelector({ onSelect, currentValue }: ContainerSelectorProps) {
  const [containers, setContainers] = useSafeAsyncState<Container[]>([]);
  const [isLoading, setIsLoading] = useSafeAsyncState(true);
  const [error, setError] = useSafeAsyncState<string | null>(null);

  useEffect(() => {
    const fetchContainers = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch('/api/containers', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Filter active containers with available space and sort by name
        const availableContainers = data
          .filter((container: Container) => 
            !container.isArchived && container.currentItems < container.maxItems
          )
          .sort((a: Container, b: Container) => 
            a.name.localeCompare(b.name)
          );
        
        setContainers(availableContainers);
      } catch (err) {
        console.error('Error fetching containers:', err);
        setError('Errore nel caricamento dei contenitori');
      } finally {
        setIsLoading(false);
      }
    };

    fetchContainers();
  }, []);

  const getDisplayText = (container: Container) => {
    const availableSpace = container.maxItems - container.currentItems;
    return `${container.name} (${availableSpace}/${container.maxItems} disponibili)`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span>Caricamento contenitori...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-sm p-2">
        {error}
      </div>
    );
  }

  return (
    <Select
      value={currentValue?.toString() || ""}
      onValueChange={(value) => {
        if (value) {
          onSelect(parseInt(value));
        }
      }}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Seleziona un contenitore..." />
      </SelectTrigger>
      <SelectContent>
        {containers.length === 0 ? (
          <SelectItem value="no-containers" disabled>
            Nessun contenitore con spazio disponibile
          </SelectItem>
        ) : (
          containers.map((container) => (
            <SelectItem key={container.id} value={container.id.toString()}>
              {getDisplayText(container)}
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  );
}