import { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { XIcon, CameraIcon, ZapIcon, RefreshCw as SwitchCameraIcon, CheckCircle, AlertCircle } from "lucide-react";
import { useCamera } from "@/hooks/use-camera";
import { useTimerManager } from "@/hooks/useTimerManager";
import { useSafeAsyncState, useSafeAsyncEffect, useSafeTimers } from "@/hooks/useSafeAsyncState";

// Definizione interfaccia estesa per supportare la proprietà torch non standard
interface ExtendedMediaTrackConstraintSet extends MediaTrackConstraintSet {
  torch?: boolean;
}

interface CameraProps {
  aspectRatio?: "square" | "vertical" | "a4";
  onCapture: (imageData: string) => void;
  onClose: () => void;
  enableStabilization?: boolean;
  cameraMode?: "ddt" | "label";
}

export function Camera({
  aspectRatio = "a4", // Impostato A4 come default per le etichette
  onCapture,
  onClose,
  enableStabilization = false,
  cameraMode = "ddt",
}: CameraProps) {
  // Stati sicuri per prevenire memory leak
  const [capturedImage, setCapturedImage] = useSafeAsyncState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [error, setError] = useSafeAsyncState<string | null>(null);
  const [stream, setStream] = useSafeAsyncState<MediaStream | null>(null);
  const [isActive, setIsActive] = useSafeAsyncState(false);
  const [isCameraHealthy, setIsCameraHealthy] = useSafeAsyncState(false);
  const [countdown, setCountdown] = useSafeAsyncState<number | null>(null);
  const healthCheckInterval = useRef<NodeJS.Timeout | null>(null);
  const [flashEnabled, setFlashEnabled] = useSafeAsyncState(false);
  
  // Timer manager per gestire timer sicuri
  const { setSafeTimeout, setSafeInterval, clearSafeTimeout, clearSafeInterval, clearAllTimers } = useTimerManager();

  // Disabilita temporaneamente il monitoraggio problematico della camera
  const checkCameraHealth = () => {
    // Imposta sempre come sano per evitare interruzioni premature
    setIsCameraHealthy(true);
    setError(null);
  };

  const startHealthMonitoring = () => {
    // Monitoraggio disabilitato per evitare conflitti
    console.log("🔍 Monitoraggio camera disabilitato temporaneamente");
  };

  const stopHealthMonitoring = () => {
    if (healthCheckInterval.current) {
      clearInterval(healthCheckInterval.current);
      healthCheckInterval.current = null;
    }
  };
  const [lightLevel, setLightLevel] = useSafeAsyncState<"buona" | "bassa" | "eccessiva">("buona");
  // Stabilizzazione disattivata su richiesta
  const [stabilizationEnabled, setStabilizationEnabled] = useSafeAsyncState(false);
  const [facingMode, setFacingMode] = useSafeAsyncState<"user" | "environment">("environment");
  
  // Manteniamo la risoluzione originale
  const [originalWidth, setOriginalWidth] = useSafeAsyncState<number>(1920);
  const [originalHeight, setOriginalHeight] = useSafeAsyncState<number>(1080);
  const [cameraMetadata, setCameraMetadata] = useSafeAsyncState<{
    model: string;
    megapixels: number;
    lens: string;
    aperture: string;
    resolution: string;
  }>({
    model: 'Dispositivo mobile',
    megapixels: 0,
    lens: 'Standard',
    aperture: 'f/2.0',
    resolution: '0x0',
  });
  const [showMetadata, setShowMetadata] = useSafeAsyncState<boolean>(false);
  
  // Funzione per inizializzare la fotocamera
  const setupCamera = async (options: { facingMode?: "user" | "environment" } = {}) => {
    try {
      // Ferma qualsiasi stream esistente
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Fotocamera non supportata in questo browser");
      }
      
      // Verifica se l'utente ha già dato il consenso per la fotocamera
      const cameraConsent = localStorage.getItem('camera_consent');
      
      // Se non c'è un consenso memorizzato, informa l'utente
      if (!cameraConsent) {
        console.log("Prima richiesta di accesso alla fotocamera, verrà memorizzata la preferenza");
      } else {
        console.log("Utilizzo consenso fotocamera memorizzato:", cameraConsent);
      }

      const videoConstraints: MediaTrackConstraints = {
        facingMode: options.facingMode || "environment",
        // Per il formato A4, usiamo un rapporto 1:1.414 (formato A4)
        aspectRatio: aspectRatio === "square" 
          ? 1 
          : aspectRatio === "a4" 
            ? 1/1.414  // Rapporto larghezza/altezza di un A4 verticale
            : 4/3,
        // Adattamento dimensioni - DDT sempre 720x1280, A4 per etichette
        width: { 
          ideal: cameraMode === "ddt"
            ? 720  // Larghezza fissa per DDT
            : aspectRatio === "a4" 
              ? 720  // Larghezza per A4
              : 1080 
        },
        height: { 
          ideal: cameraMode === "ddt"
            ? 1280  // Altezza fissa per DDT
            : aspectRatio === "a4" 
              ? 1020  // Altezza per A4 (circa 720 * 1.414)
              : 1080 
        }
      };
      
      // Stabilizzazione disattivata
      // Aggiungiamo solo le opzioni di base per la fotocamera
      try {
        (videoConstraints as any).advanced = [
          { exposureMode: "continuous" },
          { focusMode: "continuous" }
        ];
      } catch (e) {
        console.log("Advanced camera constraints non supportati", e);
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: videoConstraints,
        audio: false,
      }).catch((permissionErr) => {
        // Se l'utente ha negato l'accesso, memorizza questa preferenza
        if (permissionErr.name === 'NotAllowedError') {
          localStorage.setItem('camera_consent', 'denied');
          console.log("Accesso alla fotocamera negato dall'utente, preferenza salvata");
        } else if (permissionErr.name === 'NotReadableError') {
          // Camera already in use or hardware issue - common in production
          console.warn("Camera già in uso o problema hardware:", permissionErr.message);
        } else {
          console.error("Errore nell'accesso alla camera:", permissionErr.name, permissionErr.message);
        }
        throw permissionErr; // Rilancia l'errore per gestirlo normalmente
      });
      
      // Utente ha consentito l'accesso, salva questa preferenza
      localStorage.setItem('camera_consent', 'granted');
      console.log("Accesso alla fotocamera consentito, preferenza salvata");
      
      setStream(mediaStream);
      setError(null);
      setIsActive(true);
      setFacingMode(options.facingMode || "environment");

      // Controllo stato camera - verifica che la traccia video sia effettivamente attiva
      const videoTracks = mediaStream.getVideoTracks();
      if (videoTracks.length > 0) {
        const activeTrack = videoTracks[0];
        if (activeTrack.readyState === 'live' && activeTrack.enabled) {
          setIsCameraHealthy(true);
          console.log("✅ Camera funziona correttamente - traccia attiva");
        } else {
          setIsCameraHealthy(false);
          console.warn("⚠️ Camera non completamente funzionante:", activeTrack.readyState);
        }

        // Avvia il monitoraggio continuo dopo 2 secondi per permettere alla camera di stabilizzarsi
        const monitoringTimerId = setTimerId(() => {
          startHealthMonitoring();
        }, 2000, 'timeout');
        
      } else {
        setIsCameraHealthy(false);
        console.warn("⚠️ Nessuna traccia video disponibile");
      }
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        
        // Attendiamo che il video sia pronto e salviamo la risoluzione originale
        videoRef.current.onloadedmetadata = () => {
          if (videoRef.current) {
            const width = videoRef.current.videoWidth;
            const height = videoRef.current.videoHeight;
            console.log(`Risoluzione fotocamera iniziale: ${width}x${height}`);
            setOriginalWidth(width); 
            setOriginalHeight(height);
            
            // Calcola i megapixel
            const megapixels = ((width * height) / 1000000).toFixed(1);
            
            // Otteniamo informazioni sulla videocamera quando disponibili
            try {
              const videoTrack = mediaStream.getVideoTracks()[0];
              const capabilities = videoTrack.getCapabilities();
              const settings = videoTrack.getSettings();
              
              // Prepariamo i metadati della fotocamera
              setCameraMetadata({
                model: videoTrack.label || 'Fotocamera predefinita',
                megapixels: parseFloat(megapixels),
                // Determiniamo il tipo di lente in base al facing mode
                lens: settings.facingMode === 'user' ? 'Frontale' : 'Posteriore',
                aperture: 'f/2.0', // Valore predefinito per tutti i dispositivi
                resolution: `${width}x${height}`,
              });
              
              console.log('Metadati fotocamera:', {
                model: videoTrack.label,
                capabilities: capabilities,
                settings: settings
              });
            } catch (err) {
              console.warn('Impossibile ottenere informazioni dettagliate sulla fotocamera:', err);
            }
          }
        };
      }
    } catch (err: any) {
      console.error("Errore accesso fotocamera:", err);
      
      // More specific error handling for production
      let errorMessage = "Errore nell'accesso alla fotocamera";
      if (err.name === 'NotAllowedError') {
        errorMessage = "Accesso alla fotocamera negato. Abilita i permessi nelle impostazioni del browser.";
      } else if (err.name === 'NotFoundError') {
        errorMessage = "Nessuna fotocamera trovata sul dispositivo.";
      } else if (err.name === 'NotReadableError') {
        errorMessage = "Fotocamera già in uso da un'altra app. Chiudi altre app che usano la camera.";
      } else if (err.name === 'OverconstrainedError') {
        errorMessage = "Impostazioni fotocamera non supportate. Prova con un dispositivo diverso.";
      } else if (err.name === 'SecurityError') {
        errorMessage = "Errore di sicurezza. Assicurati che l'app sia servita tramite HTTPS.";
      }
      
      setError(errorMessage);
      setIsActive(false);
      setIsCameraHealthy(false);
    }
  };
  
  // Funzione per analizzare il livello di luce dell'immagine della fotocamera
  const analyzeLightLevel = () => {
    if (!videoRef.current || !canvasRef.current) return;
    
    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d', { willReadFrequently: true });
      
      if (!context) return;
      
      // Usiamo una piccola porzione dell'immagine per l'analisi per maggiore efficienza
      const sampleWidth = 50;
      const sampleHeight = 50;
      
      // Impostiamo le dimensioni del canvas per il campione
      canvas.width = sampleWidth;
      canvas.height = sampleHeight;
      
      // Disegniamo una porzione centrale dell'immagine video sul canvas
      context.drawImage(
        video,
        (video.videoWidth - sampleWidth) / 2,
        (video.videoHeight - sampleHeight) / 2,
        sampleWidth,
        sampleHeight,
        0, 0, sampleWidth, sampleHeight
      );
      
      // Otteniamo i dati dei pixel
      const imageData = context.getImageData(0, 0, sampleWidth, sampleHeight);
      const data = imageData.data;
      
      // Calcoliamo la luminosità media
      let totalBrightness = 0;
      let pixelCount = 0;
      
      for (let i = 0; i < data.length; i += 4) {
        // Formula per la luminanza percepita: 0.299*R + 0.587*G + 0.114*B
        const brightness = (data[i] * 0.299 + data[i+1] * 0.587 + data[i+2] * 0.114);
        totalBrightness += brightness;
        pixelCount++;
      }
      
      const averageBrightness = totalBrightness / pixelCount;
      console.log(`Livello di luce medio: ${averageBrightness}`);
      
      // Determiniamo il livello di luce in base alla luminosità media
      if (averageBrightness < 50) {
        setLightLevel("bassa");
      } else if (averageBrightness > 200) {
        setLightLevel("eccessiva");
      } else {
        setLightLevel("buona");
      }
      
    } catch (err) {
      console.error('Errore nell\'analisi del livello di luce:', err);
    }
  };
  
  // Funzione di pulizia completa della camera
  const cleanupCamera = () => {
    console.log("🔴 Pulizia completa camera - interrompo lo stream");
    
    // Stop health monitoring first
    stopHealthMonitoring();
    
    // Interrompi tutte le tracce video e audio
    if (stream) {
      stream.getTracks().forEach(track => {
        console.log(`Interruzione traccia: ${track.kind}`, track.readyState);
        try {
          track.stop();
        } catch (error) {
          console.warn("Errore durante stop della traccia:", error);
        }
      });
    }
    
    // Rimuovi il riferimento allo stream dal video element
    if (videoRef.current) {
      try {
        if (videoRef.current.srcObject) {
          videoRef.current.srcObject = null;
        }
        videoRef.current.pause();
        videoRef.current.load(); // Forza il reset del video element
      } catch (error) {
        console.warn("Errore durante cleanup video element:", error);
      }
    }
    
    // Resetta tutti gli stati
    setStream(null);
    setCapturedImage(null);
    setError(null);
    setCountdown(null);
    setIsActive(false);
    setIsCameraHealthy(false);
  };

  // Inizializza la fotocamera all'avvio del componente
  useEffect(() => {
    // Stabilizzazione sempre disattivata su richiesta
    setStabilizationEnabled(false);
    
    // Add a small delay to ensure component is fully mounted in production
    const initTimerId = setTimerId(() => {
      setupCamera();
    }, 100, 'timeout');
    
    // Pulizia quando il componente viene smontato
    return () => {
      clearAllTimers();
      cleanupCamera();
    };
  }, [aspectRatio, enableStabilization]);

  // Aggiungi listener per la navigazione del browser
  useEffect(() => {
    const handleBeforeUnload = () => {
      cleanupCamera();
    };

    // Improved visibility change handling for production
    let visibilityTimer: NodeJS.Timeout | null = null;
    
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Don't immediately cleanup - set a delayed cleanup for production mobile browsers
        // that might temporarily trigger hidden state during normal operation
        const visibilityTimerId = setTimerId(() => {
          if (document.hidden) {
            console.log("🔄 Pagina nascosta per più di 2 secondi, cleanup camera");
            cleanupCamera();
          }
        }, 2000, 'timeout'); // 2 second delay before cleanup
      } else {
        // Page is visible again, cancel any pending cleanup
        // Timer ora gestiti automaticamente dal timer manager
        console.log("🟢 Pagina visibile, cleanup camera annullato automaticamente");
      }
    };

    // Ascolta eventi di navigazione e cambio visibilità
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityTimer) {
        clearTimeout(visibilityTimer);
      }
    };
  }, [stream]);
  
  // Aggiungiamo un effetto per analizzare il livello di luce periodicamente
  useEffect(() => {
    if (!videoRef.current || !stream || capturedImage) return;
    
    // Analizziamo il livello di luce ogni secondo
    const lightAnalysisTimerId = setTimerId(() => {
      analyzeLightLevel();
    }, 1000, 'interval');
    
    return () => {
      clearTimerId(lightAnalysisTimerId);
    };
  }, [stream, capturedImage]);
  
  // Gestisce il countdown e la cattura dell'immagine
  useEffect(() => {
    if (countdown === null) return;
    
    if (countdown > 0) {
      const countdownTimerId = setTimerId(() => {
        setCountdown(countdown - 1);
      }, 1000, 'timeout');
      return () => clearTimerId(countdownTimerId);
    } else {
      // Cattura l'immagine
      if (videoRef.current && canvasRef.current) {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const context = canvas.getContext("2d");
        
        if (context) {
          // Imposta le dimensioni del canvas in base all'aspect ratio scelto
          if (aspectRatio === "square") {
            // Per le fotografie quadrate (1:1)
            const size = Math.min(video.videoWidth, video.videoHeight);
            canvas.width = size;
            canvas.height = size;
          } else if (aspectRatio === "a4") {
            // Per A4 in verticale (210:297 o circa 1:1.414)
            const canvasWidth = Math.min(video.videoWidth, video.videoHeight * 0.707); // Inverso di 1.414
            canvas.width = canvasWidth;
            canvas.height = Math.round(canvasWidth * 1.414); // Proporzione A4 (297/210 = 1.414)
          } else {
            // Per le etichette verticali o default
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
          }
          
          // Effetto per indicare la cattura dell'immagine
          const flash = document.createElement('div');
          flash.className = 'fixed inset-0 bg-white z-50 pointer-events-none';
          flash.style.opacity = '0.7';
          document.body.appendChild(flash);
          
          // Rimuovi l'effetto flash gradualmente con timer manager
          let opacity = 0.7;
          const fadeTimerId = setTimerId(() => {
            opacity -= 0.1;
            if (opacity <= 0) {
              clearTimerId(fadeTimerId);
              if (flash.parentNode) {
                flash.parentNode.removeChild(flash);
              }
            } else {
              flash.style.opacity = opacity.toString();
            }
          }, 50, 'interval');
          
          // Effetto audio di scatto (opzionale)
          try {
            const audio = new Audio('/camera-shutter.mp3');
            audio.play().catch(e => console.log('Audio non supportato:', e));
          } catch (e) {
            console.log('Audio non supportato');
          }
          
          context.drawImage(video, 0, 0, canvas.width, canvas.height);
          
          try {
            const imageData = canvas.toDataURL("image/jpeg");
            // Applica la correzione dell'orientamento prima di salvare l'immagine
            fixOrientation(imageData)
              .then(fixedImage => {
                // Salviamo l'immagine corretta nello stato per la revisione
                setCapturedImage(fixedImage);
              })
              .catch(err => {
                console.error("Errore nella correzione dell'orientamento:", err);
                // In caso di errore, usiamo comunque l'immagine originale
                setCapturedImage(imageData);
              });
          } catch (err) {
            console.error("Errore nella cattura dell'immagine:", err);
            setError("Impossibile acquisire l'immagine");
          }
        }
      }
      setCountdown(null);
    }
  }, [countdown]);
  
  // Funzione per garantire che l'immagine abbia le dimensioni corrette senza distorsioni
  const fixOrientation = (imageDataUrl: string): Promise<string> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        // Per le etichette, adattiamo in base al formato scelto
        if (cameraMode === "label") {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (aspectRatio === "a4") {
            // Per A4 verticale (rapporto 1:1.414)
            const targetRatio = 1 / 1.414; // Rapporto larghezza/altezza per A4
            const imgRatio = img.width / img.height;
            
            console.log(`Modalità etichetta A4: adatto l'immagine ${img.width}x${img.height} al rapporto A4 (1:1.414)`);
            
            let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
            
            if (imgRatio > targetRatio) {
              // L'immagine è troppo larga, ritagliamo i lati
              drawHeight = img.height;
              drawWidth = Math.round(img.height * targetRatio);
              offsetX = Math.round((img.width - drawWidth) / 2);
            } else {
              // L'immagine è troppo alta, ritagliamo sopra e sotto
              drawWidth = img.width;
              drawHeight = Math.round(img.width / targetRatio);
              offsetY = Math.round((img.height - drawHeight) / 2);
            }
            
            // Impostiamo le dimensioni del canvas per mantenere il rapporto A4
            canvas.width = drawWidth;
            canvas.height = drawHeight;
            
            // Disegniamo l'immagine ritagliata
            ctx?.drawImage(img, offsetX, offsetY, drawWidth, drawHeight, 0, 0, drawWidth, drawHeight);
            
            // Restituiamo l'immagine trasformata
            resolve(canvas.toDataURL('image/jpeg', 0.95));
            return;
          } else {
            // Per altri formati di etichette, manteniamo l'immagine originale
            console.log(`Modalità etichetta standard: mantengo l'immagine originale ${img.width}x${img.height}`);
            resolve(imageDataUrl);
            return;
          }
        }
        
        // Per altre modalità, procediamo con la trasformazione standard
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Impostiamo le dimensioni in formato verticale per modalità DDT
        const targetWidth = 1080;
        const targetHeight = 1920;
        
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        
        if (ctx) {
          // Calcoliamo le dimensioni proporzionali per l'immagine mantenendo il rapporto
          let drawWidth, drawHeight, offsetX, offsetY;
          
          // Se l'immagine è già verticale
          if (img.height > img.width) {
            // Adattiamo l'immagine alla larghezza mantenendo proporzioni
            drawWidth = targetWidth;
            drawHeight = (img.height / img.width) * targetWidth;
            offsetX = 0;
            offsetY = (targetHeight - drawHeight) / 2;
          } else {
            // L'immagine è orizzontale, la adattiamo all'altezza
            drawHeight = targetHeight;
            drawWidth = (img.width / img.height) * targetHeight;
            offsetX = (targetWidth - drawWidth) / 2;
            offsetY = 0;
          }
          
          // Riempiamo il canvas con sfondo nero per evitare trasparenze
          ctx.fillStyle = '#000000';
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          
          // Disegniamo l'immagine centrata nel canvas
          ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
          
          // Scriviamo un messaggio di debug con le dimensioni
          console.log(`Dimensioni originali: ${img.width}x${img.height}, Formato: ${img.width > img.height ? 'orizzontale' : 'verticale'}`);
          console.log(`Dimensioni finali: ${targetWidth}x${targetHeight}, Rendering: ${drawWidth}x${drawHeight}`);
          
          // Per le altre modalità restituiamo l'immagine trasformata
          resolve(canvas.toDataURL('image/jpeg', 0.9));
        } else {
          // In caso di errore, restituisci l'immagine originale
          resolve(imageDataUrl);
        }
      };
      img.src = imageDataUrl;
    });
  };

  // Funzione per accettare l'immagine - naviga direttamente alla pagina corretta
  const handleAcceptImage = () => {
    if (capturedImage) {
      console.log("✅ Immagine accettata, arresto camera prima di navigare");
      
      // Interrompiamo immediatamente tutte le tracce video prima di procedere
      if (stream) {
        console.log(`⚠️ Interrompo stream con ${stream.getTracks().length} tracce`);
        stream.getTracks().forEach(track => {
          console.log(`↪️ Chiusura traccia ${track.kind} (${track.label || 'senza nome'})`);
          track.stop();
        });
      }
      
      // Rimuovi riferimento allo stream dal video element
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject = null;
        console.log("🧹 Rimosso srcObject dal video element");
      }
      
      // Determina il tipo di documento
      const docType = sessionStorage.getItem("documentType") || "ddt";
      
      // Memorizza l'immagine e naviga direttamente alla pagina di elaborazione
      if (docType === "ddt") {
        const ddtData = { image: capturedImage };
        sessionStorage.setItem("ddtData", JSON.stringify(ddtData));
        // Resetta lo stato
        setStream(null);
        setCapturedImage(null);
        // Passa al componente parent
        onCapture(capturedImage);
      } else if (docType === "label") {
        const labelData = { image: capturedImage };
        sessionStorage.setItem("labelData", JSON.stringify(labelData));
        // Resetta lo stato
        setStream(null);
        setCapturedImage(null);
        // Passa al componente parent
        onCapture(capturedImage);
      } else {
        // Resetta lo stato
        setStream(null);
        setCapturedImage(null);
        // Passa l'immagine al componente genitore per altri tipi di documenti
        onCapture(capturedImage);
      }
    }
  };
  
  // Funzione per rifiutare l'immagine e tornare alla fotocamera
  const handleRejectImage = () => {
    console.log("🔄 Rifiuto immagine e torno alla fotocamera");
    
    // Ferma lo stream esistente se esiste
    if (stream) {
      console.log("🛑 Fermo lo stream esistente prima di riattivare la fotocamera");
      stream.getTracks().forEach(track => {
        track.stop();
      });
      
      // Rimuovi il riferimento allo stream dal video element
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject = null;
      }
      
      // Pulisci lo stato dello stream
      setStream(null);
    }
    
    // Rimuovi l'immagine catturata
    setCapturedImage(null);
    
    // Riattiva sempre la fotocamera con un piccolo ritardo
    // per assicurarsi che il vecchio stream sia completamente chiuso
    const restartTimerId = setTimerId(() => {
      console.log("🔄 Riattivo la fotocamera con nuovo stream");
      setupCamera({ facingMode });
    }, 100, 'timeout');
  };
  
  // Inizia il countdown per la cattura
  const handleCapture = () => {
    setCountdown(3);
  };

  // Determina la risoluzione della fotocamera
  const getResolution = () => {
    if (!videoRef.current) return "";
    const width = videoRef.current.videoWidth || originalWidth;
    const height = videoRef.current.videoHeight || originalHeight;
    console.log(`Lettura risoluzione attuale: ${width}x${height}`);
    return `${width} x ${height}`;
  };

  // Toggle flash con supporto iOS migliorato
  const toggleFlash = () => {
    try {
      const newFlashState = !flashEnabled;
      setFlashEnabled(newFlashState);

      // Rileva se stiamo usando iOS
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      
      // Otteniamo il video track corrente
      const videoElement = videoRef.current;
      if (!videoElement || !videoElement.srcObject) return;
      
      const videoTrack = (videoElement.srcObject as MediaStream).getVideoTracks()[0];
      if (!videoTrack) return;
      
      // Diverse tecniche per iOS vs altri dispositivi
      if (isIOS) {
        // Metodi specifici per iOS
        try {
          console.log("Attivazione flash per iOS...");
          
          // Metodo 1: Try camera capabilities con ImageCapture API
          if ('ImageCapture' in window) {
            try {
              const imageCapture = new (window as any).ImageCapture(videoTrack);
              imageCapture.setTorch?.(newFlashState);
              console.log("Flash iOS attivato tramite ImageCapture");
            } catch (e) {
              console.log("ImageCapture fallito:", e);
            }
          }
          
          // Metodo 2: Try camera capabilities con applyConstraints
          try {
            videoTrack.applyConstraints({
              advanced: [{ torch: newFlashState } as any]
            });
            console.log("Flash iOS attivato tramite applyConstraints");
          } catch (e) {
            console.log("applyConstraints fallito:", e);
          }
          
          // Metodo 3: Assegnazione diretta
          try {
            (videoTrack as any).torch = newFlashState;
            console.log("Flash iOS attivato tramite proprietà diretta");
          } catch (e) {
            console.log("Proprietà diretta fallita:", e);
          }
        } catch (e) {
          console.log("Errore attivazione flash iOS:", e);
        }
      } else {
        // Per Android e altri
        try {
          if (videoTrack.getCapabilities && 'torch' in videoTrack.getCapabilities()) {
            videoTrack.applyConstraints({
              advanced: [{ torch: newFlashState } as any]
            });
          }
        } catch (e) {
          console.log("Errore flash Android:", e);
        }
      }
      
      // Stampiamo il valore attuale della risoluzione per verificare se cambia
      if (videoRef.current) {
        console.log(`Risoluzione dopo cambio flash: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
        // Forziamo a mantenere la risoluzione originale se necessario
        if (videoRef.current.videoWidth < originalWidth || videoRef.current.videoHeight < originalHeight) {
          console.log(`Rilevata risoluzione ridotta! Tentativo di rispristino a ${originalWidth}x${originalHeight}`);
          
          // Se notiamo che la risoluzione è diminuita, proviamo a reinizializzare la fotocamera
          // ma senza fermare lo stream corrente per evitare flickering
          if (stream) {
            const videoTracks = stream.getVideoTracks();
            if (videoTracks.length > 0) {
              const videoTrack = videoTracks[0];
              videoTrack.applyConstraints({
                width: { ideal: originalWidth, min: originalWidth },
                height: { ideal: originalHeight, min: originalHeight }
              }).then(() => {
                console.log(`Riapplicati constraints con risoluzione ${originalWidth}x${originalHeight}`);
              }).catch(err => {
                console.error(`Impossibile ripristinare risoluzione: ${err.message}`);
                setError(`Errore ripristino risoluzione: ${err.message}`);
              });
            }
          }
        }
      }
      
      // Simuliamo il risultato del flash con un messaggio in console
      // ma non modifichiamo lo stream per evitare problemi di risoluzione
      console.log(`Flash ${newFlashState ? 'attivato (simulato)' : 'disattivato (simulato)'}`);      
      
      // Mostriamo un messaggio all'utente
      const flashMessage = document.createElement('div');
      flashMessage.className = 'fixed top-28 inset-x-0 flex justify-center z-50 pointer-events-none';
      flashMessage.innerHTML = `
        <div class="bg-black/70 text-white px-4 py-2 rounded-full text-sm">
          ${newFlashState ? 'Flash attivato' : 'Flash disattivato'}
        </div>
      `;
      document.body.appendChild(flashMessage);
      
      // Aggiungiamo un controllo post-modifica dello stato flash
      const checkTimerId = setTimerId(() => {
        if (videoRef.current) {
          console.log(`Risoluzione DOPO il cambio flash: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
        }
      }, 500, 'timeout');
      
      // Rimuoviamo il messaggio dopo 1.5 secondi
      const flashMessageTimerId = setTimerId(() => {
        if (flashMessage.parentNode) {
          flashMessage.parentNode.removeChild(flashMessage);
        }
      }, 1500, 'timeout');
    } catch (err) {
      console.error('Errore nella gestione del flash:', err);
    }
  };

  // Funzione per caricare immagine dalla galleria
  const uploadImage = () => {
    console.log("Caricamento immagine dalla galleria");
    
    // Creare un input di file invisibile
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    
    // Gestire la selezione del file
    fileInput.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files[0]) {
        const reader = new FileReader();
        
        reader.onload = (event) => {
          if (event.target && typeof event.target.result === 'string') {
            // Applica la correzione dell'orientamento anche alle immagini caricate
            fixOrientation(event.target.result)
              .then(fixedImage => {
                // Salviamo l'immagine corretta nello stato per la revisione
                setCapturedImage(fixedImage);
              })
              .catch(err => {
                console.error("Errore nella correzione dell'orientamento:", err);
                // In caso di errore, usiamo comunque l'immagine originale
                if (event.target && typeof event.target.result === 'string') {
                  setCapturedImage(event.target.result);
                }
              });
          }
        };
        
        reader.readAsDataURL(target.files[0]);
      }
      
      // Rimuovi l'input dopo l'uso
      document.body.removeChild(fileInput);
    };
    
    // Aggiungi al DOM e attiva il click
    document.body.appendChild(fileInput);
    fileInput.click();
  };
  
  // Toggle stabilizzazione e applica il cambiamento - non utilizzato
  const toggleStabilization = () => {
    const newValue = !stabilizationEnabled;
    setStabilizationEnabled(newValue);
    
    // Messaggio visibile che indica il cambio di stabilizzazione
    const stabilizationMessage = document.createElement('div');
    stabilizationMessage.className = 'fixed inset-0 flex items-center justify-center z-50 pointer-events-none';
    stabilizationMessage.innerHTML = `
      <div class="${newValue ? 'bg-green-500' : 'bg-gray-500'} bg-opacity-90 text-white rounded-lg px-5 py-3 shadow-xl">
        <div class="text-xl font-medium text-center">
          Stabilizzazione ${newValue ? 'attivata' : 'disattivata'}
        </div>
      </div>
    `;
    document.body.appendChild(stabilizationMessage);
    
    // Rimuovi il messaggio dopo un breve intervallo
    const messageTimerId = setTimerId(() => {
      if (stabilizationMessage && stabilizationMessage.parentNode) {
        stabilizationMessage.parentNode.removeChild(stabilizationMessage);
      }
    }, 1500, 'timeout');
    
    // Implementiamo stabilizzazione software simulata
    if (newValue && videoRef.current) {
      // Applica stili CSS per simulare stabilizzazione
      videoRef.current.style.transition = 'transform 0.3s ease-out';
      
      // Prepara l'elemento video per la simulazione di stabilizzazione
      if (typeof window !== 'undefined') {
        // Rileva movimento e compensa con leggeri aggiustamenti CSS
        let lastX = 0, lastY = 0;
        const stabilizationTimerId = setTimerId(() => {
          if (!stabilizationEnabled || !videoRef.current) {
            clearTimerId(stabilizationTimerId);
            if (videoRef.current) {
              videoRef.current.style.transform = '';
              videoRef.current.style.transition = '';
            }
            return;
          }

          // Effetto più evidente e dimostrativo
          // Invece di simulare tremolio, impostiamo un livello di zoom leggermente maggiore per mostrare che la stabilizzazione è attiva
          if (videoRef.current) {
            videoRef.current.style.transform = 'scale(1.15)'; // Zoom più evidente
            videoRef.current.style.transition = 'transform 0.3s ease-in-out';
            
            // Aggiungiamo anche un bordo colorato per mostrare che la stabilizzazione è attiva
            videoRef.current.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.5)';
            videoRef.current.style.borderRadius = '8px';
          }
          
          // Nessun tracciamento di offset necessario in questo caso
        }, 50); // Aggiornamento più frequente per un effetto più fluido
      }
    } else if (videoRef.current) {
      // Rimuovi gli stili di stabilizzazione
      videoRef.current.style.transform = '';
      videoRef.current.style.transition = '';
      videoRef.current.style.boxShadow = '';
      videoRef.current.style.borderRadius = '';
    }
    
    // Non riapplica le impostazioni della fotocamera per evitare reset della risoluzione
    // setupCamera({ facingMode });
    
    // Log nella console per debug
    console.log(`Stabilizzazione ${newValue ? 'attivata' : 'disattivata'}`);
  };

  // Toggle per mostrare/nascondere i metadati della fotocamera
  const toggleMetadata = () => {
    setShowMetadata(!showMetadata);
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col" style={{top: "-14px"}}>
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 z-30 px-0 pt-6 pb-0">
        <div className="relative flex items-center justify-between px-3 py-0 mx-auto max-w-md h-16">
          <div className="flex items-center">
            <button
              type="button"
              className="flex h-14 w-14 items-center justify-center rounded-full bg-black/65 p-0"
              onClick={() => {
                console.log("🚪 Chiusura camera - utente ha premuto X");
                
                // Usa la funzione di pulizia completa
                cleanupCamera();
                
                // Chiama il callback di chiusura
                
                // Chiama la funzione onClose del genitore
                onClose();
              }}
            >
              <span className="sr-only">Indietro</span>
              <svg xmlns="http://www.w3.org/2000/svg" style={{ width: '26px', height: '26px' }} className="text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"><path d="M15 18l-6-6 6-6"/></svg>
            </button>
          </div>
          
          {/* Indicatore stato camera */}
          <div className="flex-1 flex justify-center">
            {isActive && (
              <div className="flex items-center gap-2 bg-black/50 rounded-full px-3 py-1 backdrop-blur-sm">
                {isCameraHealthy ? (
                  <CheckCircle className="h-4 w-4 text-green-400" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-400" />
                )}
                <span className="text-white text-sm font-medium">
                  {isCameraHealthy ? "Camera OK" : "Verifica camera"}
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Spazio vuoto per mantenere il centraggio del titolo */}
            <div style={{ width: '26px', height: '26px' }}></div>
          </div>
        </div>
      </div>
      {/* Camera Preview o immagine catturata */}
      <div className="flex-1 relative bg-gray-900">
        {error ? (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-200 text-gray-700 p-5 text-center">
            <CameraIcon size={48} className="mb-4 text-gray-500" />
            <h3 className="text-lg font-bold mb-2">Errore Fotocamera</h3>
            <p>{error}</p>
          </div>
        ) : capturedImage ? (
          <div className="absolute inset-0 flex flex-col bg-gray-900">
            {/* Immagine catturata - senza titolo */}
            <div className="flex-1 flex justify-center items-center px-4 py-2 pt-4 mb-20">
              <img 
                src={capturedImage} 
                alt="Anteprima" 
                className="object-contain w-full h-full max-h-[70vh] rounded-md border border-gray-800/30"
              />
            </div>
            
            {/* Pulsanti di conferma */}
            <div className="fixed bottom-0 left-0 right-0 px-4 bg-[#141621] flex items-center justify-between gap-3 text-[13px] pl-[20px] pr-[20px] ml-[0px] mr-[0px] pt-[2px] pb-[2px] mt-[119px] mb-[119px]">
              <Button
                onClick={handleRejectImage}
                className="flex-1 h-14 rounded-full text-white bg-[#1E1E28] border border-[#FF5722]/50 hover:bg-[#1E1E28]/80"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 mr-1">
                  <path d="M9 14l-4-4 4-4"></path>
                  <path d="M5 10h11a4 4 0 1 1 0 8h-1"></path>
                </svg>
                <span className="font-medium text-[13px]">Rifai</span>
              </Button>
              
              <Button
                onClick={() => {
                  console.log("🚫 Operazione annullata dall'utente");
                  cleanupCamera();
                  onClose();
                }}
                className="flex-1 h-14 rounded-full text-white bg-[#374151] border border-[#6B7280]/50 hover:bg-[#374151]/80"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 mr-1">
                  <path d="M18 6L6 18"></path>
                  <path d="M6 6l12 12"></path>
                </svg>
                <span className="font-medium text-[13px]">Annulla</span>
              </Button>
              
              <Button
                onClick={handleAcceptImage}
                className="flex-1 h-14 rounded-full text-white bg-[#15803d] hover:bg-[#15803d]/90"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 mr-1">
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
                <span className="font-medium text-[13px]">Prosegui</span>
              </Button>
            </div>
          </div>
        ) : (
          <>
              {/* Pannello informazioni fotocamera - posizionato in basso */}
              {showMetadata && (
                <div className="absolute bottom-[210px] left-0 right-0 z-30 px-4">
                  <div className="bg-black/85 backdrop-blur-md text-white rounded-xl p-3 shadow-lg border border-gray-700/50 mx-auto max-w-md">
                    <div className="text-xs font-semibold text-blue-400 mb-1 uppercase tracking-wider">Informazioni Fotocamera</div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-400">Modello</span>
                        <span className="text-sm font-medium truncate">{cameraMetadata.model}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-400">Megapixel</span>
                        <span className="text-sm font-medium">{cameraMetadata.megapixels} MP</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-400">Obiettivo</span>
                        <span className="text-sm font-medium">{cameraMetadata.lens} {cameraMetadata.aperture}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-400">Risoluzione</span>
                        <span className="text-sm font-medium">{cameraMetadata.resolution}</span>
                      </div>
                      <div className="flex flex-col col-span-2">
                        <span className="text-xs text-gray-400">Livello luce</span>
                        <div className="flex items-center mt-1">
                          <div className={`h-2 flex-1 rounded-full ${
                            lightLevel === "bassa" ? "bg-red-500" :
                            lightLevel === "eccessiva" ? "bg-yellow-500" :
                            "bg-green-500"
                          }`}></div>
                          <span className="text-xs ml-2 font-medium">{
                            lightLevel === "bassa" ? "Bassa" :
                            lightLevel === "eccessiva" ? "Eccessiva" :
                            "Ottimale"
                          }</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
          
              {aspectRatio === "a4" ? (
                <div className="absolute top-[-16px] left-0 right-0 bottom-0 flex items-center justify-center overflow-hidden">
                  <div className="relative flex items-center justify-center w-full h-full" style={{
                    maxHeight: "80vh", // Limite di altezza per evitare overflow
                    overflow: "hidden",
                    background: "#000", // Sfondo nero per il viewport
                    borderRadius: "8px", // Bordi arrotondati
                    border: "2px solid rgba(255, 255, 255, 0.3)", // Bordo visibile per il viewport
                  }}>
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full h-full object-cover"
                    />
                    {/* Linee guida per visualizzare il formato A4 */}
                    <div className="absolute inset-0 border-2 border-white border-opacity-10 pointer-events-none"></div>

                  </div>
                </div>
              ) : (
                <div className={`absolute top-[8px] left-0 right-0 bottom-0 flex items-center justify-center ${cameraMode === "ddt" ? "rounded-lg overflow-hidden" : ""}`} style={{
                  width: "100%", // Sempre larghezza al 100%
                  height: cameraMode === "ddt" ? "90vh" : "auto",
                  maxHeight: cameraMode === "ddt" ? "80vh" : "auto",
                  border: cameraMode === "ddt" ? "2px solid rgba(255, 255, 255, 0.3)" : "none", // Bordo solo per DDT
                }}>
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className={`${
                      cameraMode === "label" && (aspectRatio === "square" || aspectRatio === "vertical")
                        ? "w-full h-full max-w-[100vmin] max-h-[100vmin] m-auto object-scale-down" 
                        : cameraMode === "ddt"
                          ? "w-full h-full object-cover" 
                          : "w-full h-full object-contain"
                    }`}
                  />
                  

                </div>
              )}
            
            {/* Indicatore livello di luce rimosso */}
            
            {countdown !== null && (
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white text-6xl font-bold bg-black bg-opacity-50 rounded-full w-20 h-20 flex items-center justify-center">
                  {countdown}
                </span>
              </div>
            )}
          </>
        )}

        {/* Capture Button - visibile solo se non stiamo visualizzando un'immagine catturata */}
        {!error && !capturedImage && (
          <div className="absolute bottom-[66px] inset-x-0 flex justify-center">
            <Button 
              onClick={handleCapture}
              disabled={countdown !== null}
              style={{
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                backgroundColor: 'rgba(28, 31, 45, 0.9)',
                border: '4px solid rgba(59, 130, 246, 0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                boxShadow: '0 20px 25px -5px rgba(0,0,0,0.5), 0 8px 10px -6px rgba(0,0,0,0.4)',
                transform: 'perspective(500px) translateZ(0)',
                transition: 'all 0.3s ease',
                filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.4))',
              }}
              className="active:bg-[#1c2747] active:translate-y-[1px] active:scale-[0.98] transition-all duration-150 transform-gpu touch-manipulation"
            >
              <CameraIcon style={{ width: '50px', height: '50px', color: 'white', filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.5))', transform: 'translateZ(5px)' }} />
            </Button>
          </div>
        )}

        {/* Camera Control Buttons - visibili solo se non stiamo visualizzando un'immagine catturata */}
        {!error && !capturedImage && (
          <>
            {/* Flash Button and Info Button */}
            <div className="absolute right-6 bottom-[66px] flex gap-3">
              <Button 
                variant="ghost"
                style={{
                  width: '52px',
                  height: '52px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(28, 31, 45, 0.9)',
                  border: '1px solid rgba(59, 130, 246, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '8px',
                  color: showMetadata ? 'rgb(96, 165, 250)' : 'white',
                  boxShadow: '0 8px 15px -3px rgba(0,0,0,0.4), 0 2px 4px -2px rgba(0,0,0,0.2)',
                  transform: 'perspective(500px) translateZ(0)',
                  transition: 'all 0.2s ease',
                }}
                className="active:bg-[#1c2747] active:translate-y-[1px] active:scale-95 transition-all duration-150 transform-gpu touch-manipulation"
                onClick={toggleMetadata}
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor"
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  style={{ 
                    width: '24px', 
                    height: '24px',
                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))',
                    transform: 'translateZ(3px)'
                  }}
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </Button>
              <Button 
                variant="ghost"
                style={{
                  width: '52px',
                  height: '52px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(28, 31, 45, 0.9)',
                  border: '1px solid rgba(59, 130, 246, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '8px',
                  color: flashEnabled ? 'yellow' : 'white',
                  boxShadow: '0 8px 15px -3px rgba(0,0,0,0.4), 0 2px 4px -2px rgba(0,0,0,0.2)',
                  transform: 'perspective(500px) translateZ(0)',
                  transition: 'all 0.2s ease',
                }}
                className="active:bg-[#1c2747] active:translate-y-[1px] active:scale-95 transition-all duration-150 transform-gpu touch-manipulation"
                onClick={toggleFlash}
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24" 
                  style={{ 
                    width: '24px', 
                    height: '24px',
                    fill: flashEnabled ? 'yellow' : 'white',
                    stroke: 'none',
                    strokeWidth: 0,
                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))',
                    transform: 'translateZ(3px)'
                  }}
                >
                  <path d="M9.5,3h5C15.33,3,16,3.67,16,4.5v0C16,5.33,15.33,6,14.5,6h-5C8.67,6,8,5.33,8,4.5v0C8,3.67,8.67,3,9.5,3z" />
                  <path d="M11,6v3c0,0.55,0.45,1,1,1s1-0.45,1-1V6H11z" />
                  <path d="M14,7.5L14,7.5c0.82,0.82,0.82,2.14,0,2.97l0,0l-2,2c-0.28,0.28-0.28,0.72,0,1l0,0c0.28,0.28,0.72,0.28,1,0l2-2c1.39-1.39,1.39-3.64,0-5.03l0,0C14.72,6.16,14.28,7.22,14,7.5z" />
                  <path d="M10,7.5L10,7.5c-0.82,0.82-0.82,2.14,0,2.97l0,0l2,2c0.28,0.28,0.28,0.72,0,1l0,0c-0.28,0.28-0.72,0.28-1,0l-2-2c-1.39-1.39-1.39-3.64,0-5.03l0,0C9.28,6.16,9.72,7.22,10,7.5z" />
                  <path d="M12,12c-0.55,0-1,0.45-1,1v8c0,0.55,0.45,1,1,1s1-0.45,1-1v-8C13,12.45,12.55,12,12,12z" />
                </svg>
              </Button>
            </div>
            

            
            {/* Switch Camera Button & Upload Button */}
            <div className="absolute left-6 bottom-[66px] flex gap-3">
              <Button 
                variant="ghost"
                style={{
                  width: '52px',
                  height: '52px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(28, 31, 45, 0.9)',
                  border: '1px solid rgba(59, 130, 246, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '8px',
                  color: 'white',
                  boxShadow: '0 8px 15px -3px rgba(0,0,0,0.4), 0 2px 4px -2px rgba(0,0,0,0.2)',
                  transform: 'perspective(500px) translateZ(0)',
                  transition: 'all 0.2s ease',
                }}
                className="active:bg-[#1c2747] active:translate-y-[1px] active:scale-95 transition-all duration-150 transform-gpu touch-manipulation"
                onClick={() => {
                  setupCamera({ facingMode: facingMode === "user" ? "environment" : "user" });
                }}
              >
                <SwitchCameraIcon style={{ width: '24px', height: '24px', filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))', transform: 'translateZ(3px)' }} />
              </Button>
              
              {/* Upload Button */}
              <Button 
                variant="ghost"
                style={{
                  width: '52px',
                  height: '52px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(28, 31, 45, 0.9)',
                  border: '1px solid rgba(59, 130, 246, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '8px',
                  color: 'white',
                  boxShadow: '0 8px 15px -3px rgba(0,0,0,0.4), 0 2px 4px -2px rgba(0,0,0,0.2)',
                  transform: 'perspective(500px) translateZ(0)',
                  transition: 'all 0.2s ease',
                }}
                className="active:bg-[#1c2747] active:translate-y-[1px] active:scale-95 transition-all duration-150 transform-gpu touch-manipulation"
                onClick={uploadImage}
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  viewBox="0 0 24 24" 
                  style={{ 
                    width: '24px', 
                    height: '24px',
                    fill: 'none',
                    stroke: 'currentColor',
                    strokeWidth: 2,
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round',
                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))',
                    transform: 'translateZ(3px)'
                  }}
                >
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </Button>
            </div>
          </>
        )}
      </div>
      {/* Canvas nascosto per la cattura dell'immagine */}
      <canvas ref={canvasRef} className="hidden" />
      {/* Toolbar rimossa */}
    </div>
  );
}