import React, { useState, useEffect } from 'react';
import logger, { LogLevel } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from '@/hooks/use-toast';
import { DownloadIcon, AlertCircle, Bug, InfoIcon, Trash2 } from 'lucide-react';
import { useSafeAsyncState } from '@/hooks/useSafeAsyncState';

/**
 * Pannello di debug per analizzare i problemi in produzione
 * Accessibile solo in modalità di sviluppo o con un flag abilitato
 */
export function DebugPanel() {
  const [isOpen, setIsOpen] = useSafeAsyncState(false);
  const [logs, setLogs] = useSafeAsyncState<any[]>([]);
  const [activeTab, setActiveTab] = useSafeAsyncState('all');
  const [filter, setFilter] = useSafeAsyncState('');
  const [isVisible, setIsVisible] = useSafeAsyncState(
    process.env.NODE_ENV === 'development' || localStorage.getItem('enable_debug_panel') === 'true'
  );

  // Aggiorna i log ogni secondo se il pannello è aperto
  useEffect(() => {
    if (!isOpen) return;

    const getLogs = () => {
      const allLogs = logger.getLogs();
      setLogs(allLogs);
    };

    getLogs();
    const interval = setInterval(getLogs, 1000);
    
    return () => clearInterval(interval);
  }, [isOpen]);

  // Filtra i log in base alla tab attiva e al filtro di testo
  const filteredLogs = logs.filter(log => {
    // Filtra per tab
    if (activeTab === 'errors' && log.level !== LogLevel.ERROR) return false;
    if (activeTab === 'warnings' && log.level !== LogLevel.WARNING) return false;
    if (activeTab === 'info' && log.level !== LogLevel.INFO) return false;
    if (activeTab === 'debug' && log.level !== LogLevel.DEBUG) return false;
    
    // Filtra per testo
    if (filter) {
      const searchText = filter.toLowerCase();
      return (
        log.message.toLowerCase().includes(searchText) || 
        log.module.toLowerCase().includes(searchText) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(searchText))
      );
    }
    
    return true;
  });

  // Download dei log come file JSON
  const downloadLogs = () => {
    try {
      const logsJson = logger.exportLogs();
      const blob = new Blob([logsJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `app-logs-${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
      
      toast({
        title: 'Log esportati con successo',
        description: 'I log sono stati scaricati come file JSON'
      });
    } catch (error) {
      console.error('Errore durante il download dei log:', error);
      toast({
        title: 'Errore',
        description: 'Impossibile scaricare i log',
        variant: 'destructive'
      });
    }
  };

  // Pulisci tutti i log
  const clearLogs = () => {
    if (confirm('Sei sicuro di voler cancellare tutti i log?')) {
      logger.clearLogs();
      setLogs([]);
      toast({
        title: 'Log cancellati',
        description: 'Tutti i log sono stati eliminati'
      });
    }
  };

  // Non renderizzare nulla se il pannello non è visibile
  if (!isVisible) return null;

  // Pulsante per mostrare il pannello
  const triggerButton = (
    <div 
      className="fixed bottom-20 right-4 z-50 bg-primary text-white p-2 rounded-full shadow-lg"
      onClick={() => setIsOpen(true)}
    >
      <Bug size={24} />
    </div>
  );

  // Formatta la data per la visualizzazione
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  // Icona per il livello di log
  const getLevelIcon = (level: LogLevel) => {
    switch (level) {
      case LogLevel.ERROR:
        return <AlertCircle className="text-destructive mr-1" size={16} />;
      case LogLevel.WARNING:
        return <AlertCircle className="text-amber-500 mr-1" size={16} />;
      case LogLevel.INFO:
        return <InfoIcon className="text-primary mr-1" size={16} />;
      default:
        return <InfoIcon className="text-muted-foreground mr-1" size={16} />;
    }
  };

  return (
    <>
      {triggerButton}
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Pannello di Debug</DialogTitle>
          </DialogHeader>
          
          <div className="flex items-center space-x-2 my-2">
            <Input
              placeholder="Filtra log..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="flex-1"
            />
            <Button variant="outline" size="icon" onClick={downloadLogs} title="Scarica log">
              <DownloadIcon size={18} />
            </Button>
            <Button variant="outline" size="icon" onClick={clearLogs} title="Cancella log">
              <Trash2 size={18} />
            </Button>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList>
              <TabsTrigger value="all">
                Tutti ({logs.length})
              </TabsTrigger>
              <TabsTrigger value="errors">
                Errori ({logs.filter(l => l.level === LogLevel.ERROR).length})
              </TabsTrigger>
              <TabsTrigger value="warnings">
                Avvisi ({logs.filter(l => l.level === LogLevel.WARNING).length})
              </TabsTrigger>
              <TabsTrigger value="info">
                Info ({logs.filter(l => l.level === LogLevel.INFO).length})
              </TabsTrigger>
              <TabsTrigger value="debug">
                Debug ({logs.filter(l => l.level === LogLevel.DEBUG).length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value={activeTab} className="flex-1 border rounded-md mt-2">
              <ScrollArea className="h-[50vh]">
                <div className="p-4 space-y-2">
                  {filteredLogs.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      Nessun log da visualizzare
                    </div>
                  ) : (
                    filteredLogs.map((log, index) => (
                      <div key={index} className="border rounded-md p-2 text-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getLevelIcon(log.level)}
                            <span className="font-medium">{log.module}</span>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(log.timestamp)}
                          </span>
                        </div>
                        <div className="mt-1">{log.message}</div>
                        {log.data && (
                          <details className="mt-1">
                            <summary className="cursor-pointer text-xs text-muted-foreground">
                              Dettagli
                            </summary>
                            <pre className="mt-1 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                              {JSON.stringify(log.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
          
          <DialogFooter className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">
              {logs.length} log totali, {filteredLogs.length} visualizzati
            </div>
            <Button onClick={() => setIsOpen(false)}>Chiudi</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}