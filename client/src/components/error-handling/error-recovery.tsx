/**
 * Sistema di Recovery Errori per HACCP Tracker
 * 
 * @description Componenti e utilities per recovery automatico:
 * - Recovery intelligente per errori comuni
 * - Retry components con UI feedback
 * - Fallback graceful per operazioni critiche
 * - Stato di recovery con indicatori visuali
 * 
 * <AUTHOR> di Error Recovery HACCP Tracker
 * @version 1.0.0 - Error recovery components
 * @date 2025-07-26
 */

import React, { useState, useEffect, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Database, 
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';

// Interfacce per recovery system
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  backoffMultiplier: number;
  onRetry?: (attempt: number) => void;
  onSuccess?: () => void;
  onFailure?: (error: Error) => void;
}

interface RecoveryState {
  isRecovering: boolean;
  attempt: number;
  lastError?: Error;
  recoveryType: 'network' | 'api' | 'storage' | 'component' | 'unknown';
  progress: number;
}

// Utility per detecting tipi di errore
const detectErrorType = (error: Error): RecoveryState['recoveryType'] => {
  const message = error.message.toLowerCase();
  
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return 'network';
  }
  if (message.includes('api') || message.includes('server') || message.includes('http')) {
    return 'api';
  }
  if (message.includes('storage') || message.includes('quota') || message.includes('localStorage')) {
    return 'storage';
  }
  if (message.includes('component') || message.includes('render') || message.includes('react')) {
    return 'component';
  }
  
  return 'unknown';
};

// Hook per retry logic avanzato
export const useRetryLogic = (
  operation: () => Promise<any>,
  config: Partial<RetryConfig> = {}
) => {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    backoffMultiplier = 2,
    onRetry,
    onSuccess,
    onFailure
  } = config;

  const [state, setState] = useState<RecoveryState>({
    isRecovering: false,
    attempt: 0,
    recoveryType: 'unknown',
    progress: 0
  });

  const retry = async () => {
    setState(prev => ({
      ...prev,
      isRecovering: true,
      attempt: 0,
      progress: 0
    }));

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        setState(prev => ({
          ...prev,
          attempt,
          progress: (attempt / maxAttempts) * 100
        }));

        onRetry?.(attempt);
        
        const result = await operation();
        
        setState(prev => ({
          ...prev,
          isRecovering: false,
          progress: 100
        }));

        onSuccess?.(result);
        return result;

      } catch (error) {
        const errorInstance = error instanceof Error ? error : new Error(String(error));
        
        setState(prev => ({
          ...prev,
          lastError: errorInstance,
          recoveryType: detectErrorType(errorInstance)
        }));

        if (attempt === maxAttempts) {
          setState(prev => ({
            ...prev,
            isRecovering: false
          }));
          
          onFailure?.(errorInstance);
          throw errorInstance;
        }

        // Exponential backoff delay
        const delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  };

  return { ...state, retry };
};

// Componente per retry con UI
interface SmartRetryProps {
  operation: () => Promise<any>;
  config?: Partial<RetryConfig>;
  fallback?: ReactNode;
  className?: string;
}

export const SmartRetry: React.FC<SmartRetryProps> = ({
  operation,
  config,
  fallback,
  className = ''
}) => {
  const { isRecovering, attempt, lastError, recoveryType, progress, retry } = useRetryLogic(
    operation,
    config
  );

  const getRecoveryIcon = () => {
    switch (recoveryType) {
      case 'network': return <WifiOff className="w-4 h-4" />;
      case 'api': return <Database className="w-4 h-4" />;
      case 'storage': return <AlertCircle className="w-4 h-4" />;
      default: return <RefreshCw className="w-4 h-4" />;
    }
  };

  const getRecoveryMessage = () => {
    switch (recoveryType) {
      case 'network': return 'Problema di connessione rilevato';
      case 'api': return 'Errore del server rilevato';
      case 'storage': return 'Problema di storage rilevato';
      default: return 'Errore generico rilevato';
    }
  };

  if (!lastError && !isRecovering) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {getRecoveryMessage()}. Tentativo di ripristino automatico in corso...
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            {getRecoveryIcon()}
            Ripristino in corso
          </CardTitle>
          <CardDescription>
            Tentativo {attempt} di {config?.maxAttempts || 3}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Progress value={progress} className="w-full" />
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant={isRecovering ? 'default' : 'secondary'}>
                {isRecovering ? 'In corso' : 'Completato'}
              </Badge>
              {isRecovering && <RefreshCw className="w-4 h-4 animate-spin" />}
            </div>
            
            <Button 
              onClick={retry} 
              variant="outline" 
              size="sm"
              disabled={isRecovering}
            >
              Riprova Manualmente
            </Button>
          </div>

          {lastError && (
            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
              <strong>Ultimo errore:</strong> {lastError.message}
            </div>
          )}
        </CardContent>
      </Card>

      {fallback && !isRecovering && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-2">Modalità di Emergenza</h4>
          {fallback}
        </div>
      )}
    </div>
  );
};

// Hook per monitoraggio connessione
export const useConnectionStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        // Connection restored
        console.log('🌐 Connection restored, triggering recovery...');
        setWasOffline(false);
        
        // Trigger global recovery
        window.dispatchEvent(new CustomEvent('connection-restored'));
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      console.log('🚫 Connection lost, entering offline mode...');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return { isOnline, wasOffline };
};

// Componente per indicatore stato connessione
export const ConnectionStatus: React.FC = () => {
  const { isOnline, wasOffline } = useConnectionStatus();

  if (isOnline && !wasOffline) {
    return null; // Tutto normale, non mostrare nulla
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <Alert className={`w-80 ${isOnline ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
        <div className="flex items-center gap-2">
          {isOnline ? (
            <>
              <Wifi className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Connessione ripristinata
              </AlertDescription>
            </>
          ) : (
            <>
              <WifiOff className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Modalità offline attiva
              </AlertDescription>
            </>
          )}
        </div>
      </Alert>
    </div>
  );
};

// Hook per recovery automatico query React Query
export const useQueryRecovery = (queryKey: string[]) => {
  const queryClient = useQueryClient();
  const { isOnline } = useConnectionStatus();

  useEffect(() => {
    const handleConnectionRestored = () => {
      console.log(`🔄 Invalidating queries after connection restore...`);
      queryClient.invalidateQueries({ queryKey });
      queryClient.refetchQueries({ queryKey });
    };

    window.addEventListener('connection-restored', handleConnectionRestored);

    return () => {
      window.removeEventListener('connection-restored', handleConnectionRestored);
    };
  }, [queryClient, queryKey]);

  // Auto-retry query quando torna online
  useEffect(() => {
    if (isOnline) {
      queryClient.refetchQueries({ queryKey });
    }
  }, [isOnline, queryClient, queryKey]);
};

// Componente wrapper per operazioni critiche con fallback
interface CriticalOperationProps {
  children: ReactNode;
  fallback: ReactNode;
  errorMessage?: string;
  retryable?: boolean;
}

export const CriticalOperation: React.FC<CriticalOperationProps> = ({
  children,
  fallback,
  errorMessage = "Operazione non disponibile",
  retryable = true
}) => {
  const [hasError, setHasError] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    setHasError(false);
    
    // Simula retry delay
    setTimeout(() => {
      setIsRetrying(false);
    }, 1000);
  };

  if (hasError && !isRetrying) {
    return (
      <div className="text-center p-4 border border-gray-200 rounded-lg">
        <AlertCircle className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
        <p className="text-sm text-gray-600 mb-3">{errorMessage}</p>
        
        <div className="space-y-2">
          {retryable && (
            <Button onClick={handleRetry} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Riprova
            </Button>
          )}
          
          <div className="border-t pt-2">
            <p className="text-xs text-gray-500 mb-2">Modalità di emergenza:</p>
            {fallback}
          </div>
        </div>
      </div>
    );
  }

  if (isRetrying) {
    return (
      <div className="text-center p-4">
        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
        <p className="text-sm text-gray-600">Riprovo...</p>
      </div>
    );
  }

  return (
    <div onError={() => setHasError(true)}>
      {children}
    </div>
  );
};