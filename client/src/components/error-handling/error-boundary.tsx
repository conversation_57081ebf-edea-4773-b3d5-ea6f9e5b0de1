/**
 * Advanced Error Boundary per HACCP Tracker
 * 
 * @description Sistema completo di gestione errori React:
 * - Error boundary con fallback UI elegante
 * - Logging strutturato degli errori
 * - Recovery automatico e meccanismi di retry
 * - Integrazione con sistema di monitoring
 * - Reporting errori per debugging
 * 
 * <AUTHOR> di Error Handling Avanzato HACCP Tracker
 * @version 1.0.0 - Error boundary avanzato
 * @date 2025-07-26
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  RotateCcw,
  Copy,
  Send
} from 'lucide-react';

// Interfacce per error handling
interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ReactErrorInfo | null;
  errorId: string;
  retryCount: number;
  userReported: boolean;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  level?: 'page' | 'component' | 'critical';
  name?: string;
  onError?: (error: Error, errorInfo: ReactErrorInfo) => void;
}

// Utility per generare ID errore unico
const generateErrorId = (): string => {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Utility per logging strutturato errori
const logError = async (error: Error, errorInfo: ReactErrorInfo, context: {
  errorId: string;
  level: string;
  name: string;
  userAgent: string;
  url: string;
  timestamp: string;
  userId?: number;
}) => {
  const errorData = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    componentStack: errorInfo.componentStack,
    ...context
  };

  console.error('🚨 Error Boundary triggered:', errorData);

  // In produzione, invia al sistema di monitoring/alerting
  if (process.env.NODE_ENV === 'production') {
    try {
      await fetch('/api/monitoring/metrics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'ERROR_BOUNDARY_TRIGGERED',
          properties: errorData
        })
      });

      // Invia alert critico se necessario
      if (context.level === 'critical') {
        await fetch('/api/monitoring/alerts/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            severity: 'critical',
            message: `Error Boundary ${context.name}: ${error.message}`,
            context: errorData
          })
        });
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  // Log in localStorage per debugging
  try {
    const { safeStorageParse } = await import('@/lib/safe-json');
    const errorLogs = safeStorageParse('haccp_error_logs', []);
    errorLogs.push(errorData);
    // Mantieni solo gli ultimi 50 errori
    if (errorLogs.length > 50) {
      errorLogs.splice(0, errorLogs.length - 50);
    }
    localStorage.setItem('haccp_error_logs', JSON.stringify(errorLogs));
  } catch (storageError) {
    console.warn('Failed to store error log:', storageError);
  }
};

// Componente fallback UI
const ErrorFallbackUI: React.FC<{
  error: Error | null;
  errorInfo: ReactErrorInfo | null;
  errorId: string;
  level: string;
  name: string;
  retryCount: number;
  onRetry: () => void;
  onGoHome: () => void;
  onReportError: () => void;
  userReported: boolean;
}> = ({ 
  error, 
  errorInfo, 
  errorId, 
  level, 
  name, 
  retryCount, 
  onRetry, 
  onGoHome, 
  onReportError,
  userReported 
}) => {
  const copyErrorDetails = () => {
    const errorDetails = {
      id: errorId,
      message: error?.message,
      stack: error?.stack,
      component: name,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    import('@/lib/promiseErrorHandler').then(({ safeClipboardWrite }) => {
      safeClipboardWrite(
        JSON.stringify(errorDetails, null, 2),
        'Dettagli errore copiati negli appunti'
      );
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-red-100 rounded-full">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">Oops! Qualcosa è andato storto</CardTitle>
          <CardDescription>
            Si è verificato un errore imprevisto nel componente {name}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Error Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">ID Errore</span>
              <Badge variant="outline" className="font-mono text-xs">
                {errorId}
              </Badge>
            </div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Livello</span>
              <Badge variant={level === 'critical' ? 'destructive' : 'secondary'}>
                {level}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tentativo</span>
              <span className="text-sm">{retryCount}/3</span>
            </div>
          </div>

          {/* Error Details */}
          {error && (
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Dettagli Tecnici
              </h4>
              <p className="text-sm text-red-700 font-mono break-all">
                {error.message}
              </p>
            </div>
          )}

          <Separator />

          {/* Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {/* Retry */}
            {retryCount < 3 && (
              <Button onClick={onRetry} className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                Riprova
              </Button>
            )}

            {/* Go Home */}
            <Button variant="outline" onClick={onGoHome} className="w-full">
              <Home className="w-4 h-4 mr-2" />
              Home
            </Button>

            {/* Copy Details */}
            <Button variant="outline" onClick={copyErrorDetails} className="w-full">
              <Copy className="w-4 h-4 mr-2" />
              Copia Dettagli
            </Button>

            {/* Report Error */}
            <Button 
              variant={userReported ? "secondary" : "outline"} 
              onClick={onReportError} 
              className="w-full"
              disabled={userReported}
            >
              <Send className="w-4 h-4 mr-2" />
              {userReported ? 'Segnalato' : 'Segnala'}
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center text-sm text-gray-600">
            <p>
              Se il problema persiste, prova a ricaricare la pagina o contatta l'assistenza.
            </p>
            <p className="mt-1">
              Il nostro team è stato automaticamente notificato dell'errore.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Classe Error Boundary principale
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      userReported: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: generateErrorId()
    };
  }

  componentDidCatch(error: Error, errorInfo: ReactErrorInfo) {
    const { level = 'component', name = 'Unknown', onError } = this.props;
    
    this.setState({ errorInfo });

    // Log strutturato dell'errore
    logError(error, errorInfo, {
      errorId: this.state.errorId,
      level,
      name,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    });

    // Callback personalizzato
    if (onError) {
      onError(error, errorInfo);
    }

    // Auto-retry per errori non critici dopo 5 secondi
    if (level !== 'critical' && this.state.retryCount < 3) {
      this.retryTimeoutId = setTimeout(() => {
        this.handleRetry();
      }, 5000);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < 3) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
        userReported: false
      }));
    }
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportError = async () => {
    try {
      // Segnalazione errore dall'utente
      await fetch('/api/monitoring/metrics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'USER_REPORTED_ERROR',
          properties: {
            errorId: this.state.errorId,
            userAction: 'manual_report',
            timestamp: new Date().toISOString()
          }
        })
      });

      this.setState({ userReported: true });
    } catch (error) {
      console.error('Failed to report error:', error);
    }
  };

  render() {
    if (this.state.hasError) {
      // Se c'è un fallback personalizzato, usalo
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Altrimenti usa il fallback UI standard
      return (
        <ErrorFallbackUI
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          errorId={this.state.errorId}
          level={this.props.level || 'component'}
          name={this.props.name || 'Unknown'}
          retryCount={this.state.retryCount}
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
          onReportError={this.handleReportError}
          userReported={this.state.userReported}
        />
      );
    }

    return this.props.children;
  }
}

// Higher-Order Component per wrappare facilmente i componenti
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const ComponentWithErrorBoundary = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  ComponentWithErrorBoundary.displayName = 
    `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ComponentWithErrorBoundary;
};

// Hook per error reporting programmatico
export const useErrorReporting = () => {
  const reportError = async (error: Error, context?: Record<string, any>) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      context
    };

    console.error('🚨 Manual error report:', errorData);

    try {
      await fetch('/api/monitoring/metrics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'MANUAL_ERROR_REPORT',
          properties: errorData
        })
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  return { reportError };
};