/**
 * Security Dashboard Component
 * Shows API analytics, security metrics, and system monitoring data
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Shield, 
  Activity, 
  TrendingUp, 
  Clock, 
  AlertTriangle, 
  Users, 
  BarChart3,
  Eye,
  Lock,
  Zap
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface ApiAnalytics {
  overview: {
    calls24h: number;
    calls7d: number;
    calls30d: number;
    avgResponseTime: number;
    errorRate: number;
    errorCount: number;
  };
  topEndpoints: Array<{
    endpoint: string;
    method: string;
    calls: number;
    avgResponseTime: number;
  }>;
}

interface SecurityMetrics {
  timeline: Array<{
    hour: string;
    calls: number;
    avgResponseTime: number;
    errors: number;
  }>;
}

interface ErrorAnalytics {
  errors: Array<{
    id: number;
    endpoint: string;
    method: string;
    statusCode: number;
    timestamp: string;
    errorMessage?: string;
    ipAddress?: string;
    userAgent?: string;
  }>;
}

export function SecurityDashboard() {
  const [timeRange, setTimeRange] = useState<24 | 168 | 720>(24); // 24h, 7d, 30d

  // Fetch API analytics overview
  const { data: analytics, isLoading: analyticsLoading } = useQuery<ApiAnalytics>({
    queryKey: ['/api/analytics/overview'],
    queryFn: async () => {
      const response = await fetch('/api/analytics/overview');
      if (!response.ok) throw new Error('Failed to fetch analytics overview');
      return response.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch timeline data
  const { data: timeline, isLoading: timelineLoading } = useQuery<SecurityMetrics>({
    queryKey: ['/api/analytics/timeline', timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/timeline?hours=${timeRange}`);
      if (!response.ok) throw new Error('Failed to fetch timeline data');
      return response.json();
    },
    refetchInterval: 60000, // Refresh every minute
  });

  // Fetch error analytics
  const { data: errors, isLoading: errorsLoading } = useQuery<ErrorAnalytics>({
    queryKey: ['/api/analytics/errors'],
    queryFn: async () => {
      const response = await fetch('/api/analytics/errors?limit=50');
      if (!response.ok) throw new Error('Failed to fetch error analytics');
      return response.json();
    },
    refetchInterval: 30000,
  });

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getStatusColor = (errorRate: number) => {
    if (errorRate < 1) return "text-green-600";
    if (errorRate < 5) return "text-yellow-600";
    return "text-red-600";
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 100) return "text-green-600";
    if (time < 500) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Sicurezza</h1>
          <p className="text-muted-foreground">
            Monitora le prestazioni API, la sicurezza e le metriche del sistema
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Shield className="h-8 w-8 text-blue-600" />
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiamate API (24h)</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsLoading ? "..." : formatNumber(analytics?.overview.calls24h || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {analyticsLoading ? "..." : `${formatNumber(analytics?.overview.calls7d || 0)} questa settimana`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo Risposta Medio</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getResponseTimeColor(analytics?.overview.avgResponseTime || 0)}`}>
              {analyticsLoading ? "..." : `${analytics?.overview.avgResponseTime || 0}ms`}
            </div>
            <p className="text-xs text-muted-foreground">
              Prestazioni sistema
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasso Errori</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(analytics?.overview.errorRate || 0)}`}>
              {analyticsLoading ? "..." : `${analytics?.overview.errorRate?.toFixed(2) || 0}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {analyticsLoading ? "..." : `${analytics?.overview.errorCount || 0} errori (24h)`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sicurezza</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              Attiva
            </div>
            <div className="space-y-1">
              <Badge variant="secondary" className="text-xs">Rate Limiting: 100/15min</Badge>
              <Badge variant="secondary" className="text-xs">CSP: Sicuro</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="endpoints" className="space-y-4">
        <TabsList>
          <TabsTrigger value="endpoints">Top Endpoints</TabsTrigger>
          <TabsTrigger value="errors">Errori Recenti</TabsTrigger>
          <TabsTrigger value="security">Sicurezza Avanzata</TabsTrigger>
        </TabsList>

        <TabsContent value="endpoints" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Endpoint più Utilizzati (24h)</CardTitle>
              <CardDescription>
                Panoramica delle API chiamate più frequentemente
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <div className="text-center py-8">Caricamento dati...</div>
              ) : (
                <div className="space-y-4">
                  {analytics?.topEndpoints?.map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between border-b pb-3">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={endpoint.method === 'GET' ? 'secondary' : 'default'}>
                            {endpoint.method}
                          </Badge>
                          <code className="text-sm">{endpoint.endpoint}</code>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="font-semibold">{formatNumber(endpoint.calls)} chiamate</div>
                        <div className={`text-sm ${getResponseTimeColor(endpoint.avgResponseTime)}`}>
                          {Math.round(endpoint.avgResponseTime)}ms medio
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-8 text-muted-foreground">
                      Nessun dato disponibile
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Errori API Recenti</CardTitle>
              <CardDescription>
                Ultimi errori rilevati per il debugging
              </CardDescription>
            </CardHeader>
            <CardContent>
              {errorsLoading ? (
                <div className="text-center py-8">Caricamento errori...</div>
              ) : (
                <div className="space-y-4">
                  {errors?.errors?.slice(0, 10).map((error) => (
                    <Alert key={error.id} className="border-red-200">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Badge variant="destructive">{error.statusCode}</Badge>
                              <code className="text-sm">{error.method} {error.endpoint}</code>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {new Date(error.timestamp).toLocaleString('it-IT')}
                            </span>
                          </div>
                          {error.errorMessage && (
                            <div className="text-sm text-red-600 font-mono">
                              {error.errorMessage}
                            </div>
                          )}
                          {error.ipAddress && (
                            <div className="text-xs text-muted-foreground">
                              IP: {error.ipAddress}
                            </div>
                          )}
                        </div>
                      </AlertDescription>
                    </Alert>
                  )) || (
                    <div className="text-center py-8 text-muted-foreground">
                      Nessun errore recente
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Impostazioni di Sicurezza</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Rate Limiting Generale</span>
                  <Badge variant="secondary">100 req/15min</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Rate Limiting Auth</span>
                  <Badge variant="secondary">5 tentativi/15min</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">API Rate Limiting</span>
                  <Badge variant="secondary">30 req/min</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">CSP Headers</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">HSTS</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Monitoraggio</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Analytics API</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Error Tracking</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Performance Monitoring</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Gestione Errori Standard</span>
                  <Badge variant="default">Attivo</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}