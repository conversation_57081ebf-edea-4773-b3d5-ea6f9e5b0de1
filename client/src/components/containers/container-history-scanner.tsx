import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { QrCode, Archive } from 'lucide-react';
import { ContainerQRScannerModal } from './container-qr-scanner-modal';
import { ContainerHistory } from './container-history';

export function ContainerHistoryScanner() {
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [selectedContainerId, setSelectedContainerId] = useState<number | null>(null);

  // Open QR Scanner Modal
  const openQRScanner = () => {
    setIsQRScannerOpen(true);
  };

  // Close QR Scanner Modal
  const closeQRScanner = () => {
    setIsQRScannerOpen(false);
  };

  // Close History Modal
  const closeHistory = () => {
    setIsHistoryOpen(false);
  };

  // Handle when a container is found from the QR scanner
  const handleContainerFound = (containerId: number) => {
    setSelectedContainerId(containerId);
    // Close the scanner modal and open the history modal
    setIsQRScannerOpen(false);
    setIsHistoryOpen(true);
  };

  return (
    <>
      <Button 
        onClick={openQRScanner} 
        className="w-full bg-blue-600 hover:bg-blue-700 py-10 text-xl font-bold shadow-xl rounded-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] border-b-4 border-blue-800"
      >
        <QrCode className="mr-4 h-10 w-10" strokeWidth={1.5} />
        Scansiona QR Contenitore
      </Button>

      {/* QR Scanner Modal */}
      <ContainerQRScannerModal 
        isOpen={isQRScannerOpen}
        onClose={closeQRScanner}
        onContainerFound={handleContainerFound}
      />

      {/* Container History Modal */}
      <ContainerHistory
        isOpen={isHistoryOpen}
        onClose={closeHistory}
        containerId={selectedContainerId}
      />
    </>
  );
}