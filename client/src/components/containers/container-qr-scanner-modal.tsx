import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, Camera, SwitchCamera, Lightbulb, CheckCircle, XCircle, X, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Camera as CameraPro } from 'react-camera-pro';
import { apiRequest } from '@/lib/queryClient';
import jsQR from 'jsqr';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useTimerManager } from '@/hooks/useTimerManager';

// Style for the scan animation
const scanAnimationStyle = `
  @keyframes scan {
    0% { top: 5%; }
    50% { top: 95%; }
    100% { top: 5%; }
  }
  
  /* Styles for the QR frame corners */
  .qr-corner {
    position: absolute;
    width: 40px;
    height: 40px;
    border-color: #0066ff;
    border-style: solid;
    border-width: 0;
    filter: drop-shadow(0 0 5px rgba(0, 102, 255, 0.7));
  }
  
  .qr-corner-top-left {
    top: 5%;
    left: 5%;
    border-top-width: 6px;
    border-left-width: 6px;
    border-radius: 12px 0 0 0;
  }
  
  .qr-corner-top-right {
    top: 5%;
    right: 5%;
    border-top-width: 6px;
    border-right-width: 6px;
    border-radius: 0 12px 0 0;
  }
  
  .qr-corner-bottom-left {
    bottom: 5%;
    left: 5%;
    border-bottom-width: 6px;
    border-left-width: 6px;
    border-radius: 0 0 0 12px;
  }
  
  .qr-corner-bottom-right {
    bottom: 5%;
    right: 5%;
    border-bottom-width: 6px;
    border-right-width: 6px;
    border-radius: 0 0 12px 0;
  }
  
  .qr-scan-line {
    position: absolute;
    left: 5%;
    width: 90%;
    height: 6px;
    background-color: #0066ff;
    box-shadow: 0 0 12px rgba(0, 102, 255, 0.9);
    animation: scan 3s infinite ease-in-out;
  }
  
  .scan-text {
    position: absolute;
    bottom: 10%;
    left: 0;
    right: 0;
    text-align: center;
    color: white;
    font-size: 16px;
    font-weight: 600;
    padding: 8px 15px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 25px;
    margin: 0 auto;
    width: fit-content;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
`;

// Type for QR code data
export type QRCodeData = {
  type: 'contenitore' | 'product' | 'ddt';
  id: number;
  name?: string;
  [key: string]: any;
};

interface ContainerQRScannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContainerFound: (containerId: number) => void;
}

export function ContainerQRScannerModal({
  isOpen,
  onClose,
  onContainerFound
}: ContainerQRScannerModalProps) {
  const cameraRef = useRef<any>(null);
  const { toast } = useToast();
  
  const [currentFacingMode, setCurrentFacingMode] = useState<'user' | 'environment'>('environment');
  const [isProcessing, setIsProcessing] = useState(false);
  const [qrFound, setQrFound] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [isCameraHealthy, setIsCameraHealthy] = useState(false);
  const [diagnosticLogs, setDiagnosticLogs] = useState<string[]>([]);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  
  // Gestione sicura dei timer con pulizia automatica
  const timerManager = useTimerManager();

  // Funzione per aggiungere log diagnostici
  const addDiagnosticLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDiagnosticLogs(prev => [...prev.slice(-4), `${timestamp}: ${message}`]);
  }, []);

  // Funzione per verificare lo stato di salute della camera
  const checkCameraHealth = useCallback(() => {
    if (!cameraRef.current || !isOpen) {
      return;
    }

    try {
      const video = cameraRef.current.video;
      
      // Caso 1: Video element non esiste o non ha sorgente
      if (!video) {
        const errorMsg = "Elemento video non trovato";
        console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }
      
      if (!video.srcObject) {
        const errorMsg = "MediaStream non assegnato al video";
        console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }

      // Caso 2: MediaStream esiste ma non ha tracce video
      const mediaStream = video.srcObject as MediaStream;
      const videoTracks = mediaStream.getVideoTracks();
      
      if (videoTracks.length === 0) {
        const errorMsg = "MediaStream senza tracce video";
        console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }

      // Caso 3: Tracce video esistono ma sono in stato problematico
      const activeTrack = videoTracks[0];
      const trackInfo = {
        readyState: activeTrack.readyState,
        enabled: activeTrack.enabled,
        muted: activeTrack.muted
      };
      console.log("🔍 DIAGNOSI CAMERA: Stato traccia:", trackInfo);

      if (activeTrack.readyState === 'ended') {
        const errorMsg = "Traccia video terminata";
        console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }

      if (!activeTrack.enabled) {
        const errorMsg = "Traccia video disabilitata";
        console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
        addDiagnosticLog(errorMsg);
        setIsCameraHealthy(false);
        return;
      }

      // Caso 4: Video element non riceve dati
      const videoInfo = {
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        readyState: video.readyState
      };
      console.log("🔍 DIAGNOSI CAMERA: Stato video element:", videoInfo);

      if (video.videoWidth === 0 || video.videoHeight === 0) {
        const warnMsg = "Video senza dimensioni (caricamento o problema hardware)";
        console.warn(`⚠️ DIAGNOSI CAMERA: ${warnMsg}`);
        addDiagnosticLog(warnMsg);
        // Non impostare a false immediatamente, potrebbe essere in caricamento
        return;
      }

      // Tutto OK
      setIsCameraHealthy(true);
      addDiagnosticLog("Camera funzionante correttamente");
      console.log("✅ DIAGNOSI CAMERA: Camera funzionante correttamente");

    } catch (error) {
      const errorMsg = `Errore durante controllo: ${error}`;
      console.error(`❌ DIAGNOSI CAMERA: ${errorMsg}`);
      addDiagnosticLog(errorMsg);
      setIsCameraHealthy(false);
    }
  }, [isOpen, addDiagnosticLog]);

  // Avvia il monitoraggio continuo della camera con timer sicuro
  const startHealthMonitoring = useCallback(() => {
    // Pulisce eventuali interval precedenti
    timerManager.clearSafeInterval('container-camera-health-check');

    // Avvia nuovo monitoraggio sicuro
    timerManager.setSafeInterval(() => {
      checkCameraHealth();
    }, 3000, 'container-camera-health-check');

    console.log("🔍 Container QR Scanner: Monitoraggio stato camera avviato");
  }, [checkCameraHealth, timerManager]);

  // Ferma il monitoraggio della camera
  const stopHealthMonitoring = useCallback(() => {
    timerManager.clearSafeInterval('container-camera-health-check');
    console.log("⏹️ Container QR Scanner: Monitoraggio stato camera fermato");
  }, [timerManager]);

  // Add the scan animation style when the component mounts
  useEffect(() => {
    if (!isOpen) return;
    
    const styleElement = document.createElement('style');
    styleElement.textContent = scanAnimationStyle;
    document.head.appendChild(styleElement);
    
    return () => {
      document.head.removeChild(styleElement);
    };
  }, [isOpen]);

  // Avvia il monitoraggio quando il modal è aperto
  useEffect(() => {
    if (isOpen) {
      // Assume che la camera sia sana all'inizio e mantieni così
      setIsCameraHealthy(true);
      
      // Disabilito il monitoraggio automatico per evitare falsi allarmi
      // Il controllo sarà attivato solo manualmente quando necessario
      
    } else {
      stopHealthMonitoring();
      setIsCameraHealthy(false);
    }
  }, [isOpen, startHealthMonitoring, stopHealthMonitoring]);

  // Cleanup all'unmount del componente
  useEffect(() => {
    return () => {
      stopHealthMonitoring();
    };
  }, [stopHealthMonitoring]);
  
  // Set up QR code scanning interval when camera is open
  useEffect(() => {
    if (!isOpen) return;
    
    // Wait for camera to initialize and start scanning with safe timers
    const initTimerId = timerManager.setSafeTimeout(() => {
      console.log("Avvio scanner QR code per contenitori");
      // Start scanning every 300ms per una scansione più frequente
      timerManager.setSafeInterval(() => {
        if (cameraRef.current) {
          console.log('Tentativo di scansione QR...');
          scanQRCode();
        }
      }, 300, 'container-qr-scan-interval');
    }, 1000, 'container-scanner-init'); // Attendi 1 secondo per inizializzazione
    
    // Cleanup sicuro on unmount
    return () => {
      console.log("Arresto scanner QR code");
      timerManager.clearSafeTimeout(initTimerId);
      timerManager.clearSafeInterval('container-qr-scan-interval');
    };
  }, [isOpen]);

  // Function to process QR codes from video frames
  const scanQRCode = async () => {
    if (!isOpen || isProcessing || qrFound || !cameraRef.current) return;

    setIsProcessing(true);
    try {
      // Acquisizione del frame corrente della fotocamera
      let frame;
      try {
        // Utilizziamo il metodo getScreenshot di react-camera-pro
        // che funziona meglio per la scansione QR in tempo reale
        frame = cameraRef.current.takePhoto();
        console.log('Frame acquisito correttamente');
        
        if (!frame) {
          console.log('Impossibile acquisire un frame dalla fotocamera');
          setIsProcessing(false);
          return;
        }
      } catch (error) {
        console.error('Errore nell\'acquisizione del frame dalla fotocamera:', error);
        setIsProcessing(false);
        return;
      }
      
      // Create an image from the photo for processing
      const img = new Image();
      img.src = frame;
      
      img.onload = async () => {
        try {
          // Create canvas and get image data
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            console.error('Impossibile ottenere il contesto 2D dal canvas');
            setIsProcessing(false);
            return;
          }
          
          // Draw the image to canvas
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          console.log(`Immagine disegnata su canvas: ${canvas.width}x${canvas.height}`);
          
          // Get the image data for QR code scanning
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          console.log(`Dati immagine ottenuti: ${imageData.width}x${imageData.height}`);
          
          // Use jsQR to detect QR codes
          console.log('Tentativo di decodifica QR code...');
          const code = jsQR(imageData.data, imageData.width, imageData.height);
          
          if (code) {
            console.log('QR Code individuato, contenuto:', code.data.substring(0, 100) + (code.data.length > 100 ? '...' : ''));
            try {
              // Parse QR code data - supporto sia per il formato JSON che per il formato stringa
              console.log('Parsing dei dati QR...');
              let qrData: QRCodeData;
              
              try {
                // Prova a parsare come JSON (per retrocompatibilità)
                const { safeJsonParse } = await import('@/lib/safe-json');
                const result = safeJsonParse(code.data);
                if (!result.success) {
                  throw new Error('Invalid QR JSON format');
                }
                qrData = result.data;
              } catch (err) {
                // Non è un JSON, proviamo a parsare come stringa (formato standard)
                // Formato atteso: "contenitore:ID:NOME"
                const parts = code.data.split(':');
                if (parts.length >= 2 && parts[0] === 'contenitore') {
                  // Estrai l'ID come numero
                  const id = parseInt(parts[1], 10);
                  // Crea un oggetto compatibile con QRCodeData
                  qrData = {
                    type: 'contenitore', 
                    id: id,
                    name: parts.length >= 3 ? parts[2] : ''
                  };
                } else {
                  // Non è un formato riconosciuto
                  setError('Formato QR code non valido per i contenitori');
                  timerManager.setSafeTimeout(() => {
                    setError(null);
                    setIsProcessing(false);
                  }, 2000, 'invalid-qr-error-reset-1');
                  return;
                }
              }
              
              // Check if this is a contenitore QR code
              console.log('Dati QR parsati:', qrData);
              if (qrData.type === 'contenitore') {
                console.log('Rilevato QR code di tipo contenitore con ID:', qrData.id);
                // Verify contenitore exists in the database
                const containerExists = await verifyContainer(qrData.id);
                console.log('Verifica contenitore:', containerExists ? 'TROVATO' : 'NON TROVATO');
                
                if (containerExists) {
                  console.log('Contenitore validato, procedendo...');
                  setQrFound(true);
                  // Send the contenitore ID to the parent component
                  onContainerFound(qrData.id);
                  // Close the modal after a short delay
                  timerManager.setSafeTimeout(() => {
                    onClose();
                    setQrFound(false);
                    setIsProcessing(false);
                  }, 1500, 'container-scan-success');
                  return;
                } else {
                  // If contenitore not found, show error briefly and continue scanning
                  console.log('Contenitore non trovato nel database');
                  toast({
                    title: 'Contenitore non trovato',
                    description: 'Il QR code scansionato non corrisponde a un contenitore esistente.',
                    variant: 'destructive',
                  });
                  
                  timerManager.setSafeTimeout(() => {
                    setIsProcessing(false);
                  }, 2000, 'container-not-found-reset');
                }
              } else {
                // Not a contenitore QR code
                console.log('QR code non valido, tipo rilevato:', qrData.type);
                setError('QR code non valido: non è un codice contenitore');
                timerManager.setSafeTimeout(() => {
                  setError(null);
                  setIsProcessing(false);
                }, 2000, 'invalid-qr-error-reset-2');
              }
            } catch (err) {
              // Invalid QR code format
              console.error('Errore parsing JSON del QR code:', err);
              setError('Formato QR code non valido');
              timerManager.setSafeTimeout(() => {
                setError(null);
                setIsProcessing(false);
              }, 2000, 'invalid-qr-error-reset-3');
            }
          } else {
            // No QR code found, continue scanning
            console.log('Nessun QR code trovato in questo frame');
            setIsProcessing(false);
          }
        } catch (error) {
          console.error('Errore nell\'elaborazione dell\'immagine:', error);
          setIsProcessing(false);
        }
      };
      
      img.onerror = () => {
        console.error('Error loading image for QR processing');
        setIsProcessing(false);
      };
    } catch (err) {
      setIsProcessing(false);
      console.error('Error processing QR code:', err);
    }
  };

  // Verify contenitore exists in the database
  const verifyContainer = async (containerId: number): Promise<boolean> => {
    try {
      console.log(`Verifica contenitore nel database con ID: ${containerId}`);
      const container = await apiRequest(`/api/containers/${containerId}`);
      console.log('Risposta API contenitore:', container ? 'OK' : 'Null');
      return !!container;
    } catch (err) {
      console.error('Errore API nella verifica contenitore:', err);
      return false;
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      console.log("ContainerQRScannerModal: Modal closing, stopping camera");
      setQrFound(false);
      setIsProcessing(false);
      setError(null);
      
      // Ferma la camera quando il modal si chiude
      if (cameraRef.current && cameraRef.current.stopCamera) {
        console.log("ContainerQRScannerModal: Calling stopCamera");
        cameraRef.current.stopCamera();
      }
    }
  }, [isOpen]);

  // Toggle camera facing mode
  const toggleCamera = () => {
    try {
      console.log('Tentativo cambio fotocamera...');
      
      // Se la camera ref è disponibile, usa il metodo switchCamera()
      if (cameraRef.current) {
        try {
          // Il metodo switchCamera() restituisce il nuovo facing mode ('user' o 'environment')
          const newFacingMode = cameraRef.current.switchCamera();
          console.log(`Fotocamera cambiata a: ${newFacingMode}`);
          
          // Aggiorna lo stato locale per riflettere il cambiamento
          setCurrentFacingMode(newFacingMode);
        } catch (switchError) {
          console.error('Errore durante il cambio fotocamera:', switchError);
          
          // Gestione fallback: cambia lo stato manualmente
          setCurrentFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
          
          // Mostra un toast di avviso
          toast({
            title: 'Avviso',
            description: 'Cambio fotocamera potrebbe non funzionare correttamente su questo dispositivo.',
            variant: 'destructive',
            duration: 3000
          });
        }
      } else {
        // Fallback se il ref non è disponibile
        setCurrentFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
      }
    } catch (err) {
      console.error('Errore generale nel cambio fotocamera:', err);
      // Fallback base
      setCurrentFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
    }
  };
  
  // Toggle flashlight/torch (iOS compatible)
  const toggleFlash = () => {
    if (cameraRef.current) {
      try {
        console.log('Tentativo di accensione torch...');
        
        // Verifica che la fotocamera sia disponibile
        if (cameraRef.current.getNumberOfCameras() === 0) {
          console.log('Nessuna fotocamera rilevata');
          toast({
            title: 'Fotocamera non disponibile',
            description: 'Impossibile controllare il flash: nessuna fotocamera rilevata.',
            variant: 'destructive',
            duration: 3000
          });
          return;
        }
        
        // Utilizziamo il metodo toggleTorch() del componente CameraPro
        try {
          // toggleTorch() restituisce true se il flash è stato acceso, false se è stato spento
          const newTorchState = cameraRef.current.toggleTorch();
          console.log(`Torch ${newTorchState ? 'acceso' : 'spento'}`);
          setFlashEnabled(newTorchState);
        } catch (torchError) {
          console.error('Errore durante il toggle della torcia:', torchError);
          toast({
            title: 'Flash non supportato',
            description: 'Il tuo dispositivo non supporta il controllo del flash.',
            variant: 'destructive',
            duration: 3000
          });
        }
      } catch (err) {
        console.error('Error toggling torch/flash:', err);
        toast({
          title: 'Flash non supportato',
          description: 'Il tuo dispositivo potrebbe non supportare il controllo del flash.',
          variant: 'destructive',
          duration: 3000
        });
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md w-[90%] mx-auto p-0 overflow-hidden rounded-xl shadow-2xl border-0">
        {/* Aggiunto DialogTitle nascosto per accessibilità ma non visibile all'utente */}
        <div className="sr-only">
          <DialogTitle>Scanner QR</DialogTitle>
        </div>
        
        <div className="relative w-full">
          {/* Camera view - ottimizzato per la scansione QR con aspect ratio più ampio */}
          <div className="relative w-full overflow-hidden" style={{ aspectRatio: '3/4', maxHeight: 'calc(100vh - 200px)' }}>
            {isOpen && (
              <CameraPro
                ref={cameraRef}
                facingMode={currentFacingMode}
                aspectRatio="cover"
                numberOfCamerasCallback={(number) => console.log(`Numero di fotocamere: ${number}`)}
                errorMessages={{
                  noCameraAccessible: 'Nessuna fotocamera accessibile',
                  permissionDenied: 'Permesso fotocamera negato',
                  switchCamera: 'Impossibile cambiare fotocamera',
                  canvas: 'Canvas non supportato',
                }}
                // Non utilizziamo onTakePhoto poiché il processing delle immagini 
                // avviene automaticamente tramite l'interval scanner
                videoReadyCallback={() => {
                  console.log('Camera pronta per QR scanner');
                }}
              />
            )}
            
            {/* QR scan frame with corner elements */}
            <div className="qr-corner qr-corner-top-left"></div>
            <div className="qr-corner qr-corner-top-right"></div>
            <div className="qr-corner qr-corner-bottom-left"></div>
            <div className="qr-corner qr-corner-bottom-right"></div>
            
            {/* Scan effect line */}
            <div className="qr-scan-line"></div>
            
            {/* Scanning instruction text */}
            <div className="scan-text">Posiziona il QR code all'interno dell'area</div>
            
            {/* QR found overlay - Versione migliorata */}
            {qrFound && (
              <div className="absolute inset-0 bg-emerald-600/20 backdrop-blur-sm flex items-center justify-center z-30">
                <div className="text-center bg-white p-5 rounded-xl shadow-lg border-2 border-emerald-500 transform transition-all duration-300 scale-105">
                  <div className="w-16 h-16 rounded-full bg-emerald-100 flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-10 w-10 text-emerald-500" />
                  </div>
                  <p className="text-emerald-600 font-bold text-xl mb-2">
                    Contenitore trovato!
                  </p>
                  <p className="text-gray-600">
                    Caricamento dello storico...
                  </p>
                  <div className="mt-3 flex items-center justify-center space-x-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-emerald-500 animate-pulse"></div>
                    <div className="h-1.5 w-1.5 rounded-full bg-emerald-500 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="h-1.5 w-1.5 rounded-full bg-emerald-500 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Error overlay - Versione migliorata */}
            {error && (
              <div className="absolute inset-0 bg-red-600/20 backdrop-blur-sm flex items-center justify-center z-30">
                <div className="text-center bg-white p-5 rounded-xl shadow-lg border-2 border-red-500 transform transition-all duration-300 scale-105">
                  <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
                    <XCircle className="h-10 w-10 text-red-500" />
                  </div>
                  <p className="text-red-600 font-bold text-xl mb-2">
                    Errore di Scansione
                  </p>
                  <p className="text-gray-600">
                    {error}
                  </p>
                </div>
              </div>
            )}
          </div>
          
          {/* Camera controls - migliorati con shadow e stile */}
          <div className="p-5 bg-white space-y-4">
            {/* Indicatore stato camera */}
            <div className={`flex items-center justify-center gap-2 py-2 px-4 rounded-lg border ${
              isCameraHealthy 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              {isCameraHealthy ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              <span className={`text-sm font-medium ${
                isCameraHealthy ? 'text-green-700' : 'text-red-700'
              }`}>
                {isCameraHealthy ? 'Camera funzionante' : 'Verifica camera'}
              </span>
              
              {/* Pulsante diagnostica - mostrato solo quando ci sono problemi */}
              {!isCameraHealthy && diagnosticLogs.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDiagnostics(true)}
                  className="ml-2 h-6 px-2 text-xs border-red-300 text-red-600 hover:bg-red-100"
                >
                  Diagnostica
                </Button>
              )}
            </div>
            
            {/* Modal diagnostica */}
            {showDiagnostics && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-4 m-4 max-w-md w-full max-h-96 overflow-hidden">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">Diagnostica Camera</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDiagnostics(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    <p className="text-sm text-gray-600 mb-3">
                      Problemi rilevati con la camera:
                    </p>
                    {diagnosticLogs.map((log, index) => (
                      <div key={index} className="bg-gray-50 p-2 rounded text-xs font-mono">
                        {log}
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-4 pt-3 border-t">
                    <p className="text-xs text-gray-500">
                      Condividi questi dettagli con il supporto tecnico per risolvere il problema.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-4">
              {/* Pulsante Flash */}
              <Button 
                variant="outline" 
                onClick={toggleFlash}
                className="flex items-center justify-center gap-3 py-3 h-auto text-base font-medium shadow-md rounded-lg border border-gray-200 transition-all hover:shadow-lg"
              >
                <Lightbulb className={`h-6 w-6 ${flashEnabled ? 'text-yellow-500 fill-yellow-400' : ''}`} />
                <span className="text-[14px]">Flash {flashEnabled ? 'ON' : 'OFF'}</span>
              </Button>
              
              {/* Pulsante Cambia camera */}
              <Button 
                variant="outline" 
                onClick={toggleCamera}
                className="flex items-center justify-center gap-3 py-3 h-auto text-base font-medium shadow-md rounded-lg border border-gray-200 transition-all hover:shadow-lg"
              >
                <SwitchCamera className="h-6 w-6" />
                <span className="text-[14px]">Cambia camera</span>
              </Button>
            </div>
            
            {/* Pulsante Chiudi */}
            <Button 
              variant="outline" 
              onClick={onClose}
              className="w-full flex items-center justify-center gap-3 py-3 h-auto text-base font-medium shadow-md rounded-lg border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-all hover:shadow-lg"
            >
              <X className="h-6 w-6" />
              <span>Chiudi Scanner</span>
            </Button>
          </div>
          

        </div>
      </DialogContent>
    </Dialog>
  );
}