import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Archive, Calendar, Clock, Scan, Package, User, XCircle, ShieldAlert } from 'lucide-react';
import { format, addDays, isWithinInterval } from 'date-fns';
import { it } from 'date-fns/locale';
import type { Container, ContainerProduct, ProductLabel, User as UserType } from '@shared/schema';

interface ContainerHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  containerId: number | null;
}

// Extended interface for container product with detailed information
interface ContainerProductWithDetails extends ContainerProduct {
  productLabel: ProductLabel & { ddtId: number };
  user: UserType;
}

export function ContainerHistory({ isOpen, onClose, containerId }: ContainerHistoryProps) {
  const [filteredProducts, setFilteredProducts] = useState<ContainerProductWithDetails[]>([]);
  
  // Query for container details
  const containerQuery = useQuery<Container>({
    queryKey: ['/api/containers', containerId],
    queryFn: () => apiRequest(`/api/containers/${containerId}`),
    enabled: !!containerId && isOpen,
  });
  
  // Query for products associated with this container
  const productsQuery = useQuery<ContainerProductWithDetails[]>({
    queryKey: ['/api/container-products', containerId],
    queryFn: () => apiRequest(`/api/container-products/container/${containerId}`),
    enabled: !!containerId && isOpen,
  });
  
  // Filter products to show only those within the last 30 days
  useEffect(() => {
    if (productsQuery.data) {
      const today = new Date();
      const thirtyDaysAgo = addDays(today, -30);
      
      const within30Days = productsQuery.data.filter(product => {
        const createdDate = new Date(product.createdAt);
        return isWithinInterval(createdDate, { start: thirtyDaysAgo, end: today });
      });
      
      setFilteredProducts(within30Days);
    }
  }, [productsQuery.data]);

  const isLoading = containerQuery.isLoading || productsQuery.isLoading;
  const error = containerQuery.error || productsQuery.error;
  const container = containerQuery.data;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Archive className="mr-2 h-5 w-5" />
            Storico Contenitore (ultimi 30 giorni)
          </DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
            <p className="text-gray-500">Caricamento dati in corso...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <XCircle className="h-10 w-10 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">Errore di caricamento</h3>
            <p className="text-gray-500">
              Impossibile caricare i dati del contenitore.
            </p>
            <Button variant="outline" className="mt-4" onClick={onClose}>
              Chiudi
            </Button>
          </div>
        ) : container ? (
          <div className="space-y-6">
            {/* Container details */}
            <div className="flex items-start justify-between bg-blue-50 p-4 rounded-lg">
              <div>
                <h2 className="text-xl font-bold text-blue-800">{container.name}</h2>
                <p className="text-blue-600">Tipo: {container.type}</p>
              </div>
              <div className="bg-blue-100 text-blue-800 font-semibold rounded-md px-3 py-1 text-sm">
                {container.currentItems === 0 ? "Vuoto" : `${container.currentItems} prodotti`}
              </div>
            </div>
            
            {/* History items */}
            {filteredProducts.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <ShieldAlert className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">Nessuna attività recente</h3>
                <p className="text-gray-500">
                  Nessuna operazione registrata negli ultimi 30 giorni per questo contenitore.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-800">
                  Operazioni Recenti ({filteredProducts.length})
                </h3>
                
                <div className="divide-y divide-gray-100">
                  {filteredProducts.map((product) => (
                    <div key={product.id} className="py-3 first:pt-0 last:pb-0">
                      <div className="flex items-start space-x-3">
                        <div className="bg-emerald-100 text-emerald-700 p-2 rounded-full">
                          <Package className="h-5 w-5" />
                        </div>
                        
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-800">
                            {product.productLabel.productName}
                          </h4>
                          
                          <div className="grid grid-cols-2 gap-2 mt-1">
                            <span className="text-xs bg-blue-50 text-blue-700 rounded px-2 py-1 inline-flex items-center">
                              <span className="font-semibold mr-1">Lotto:</span> {product.productLabel.batchNumber}
                            </span>
                            <span className="text-xs bg-amber-50 text-amber-700 rounded px-2 py-1 inline-flex items-center">
                              <span className="font-semibold mr-1">Scadenza:</span> {product.productLabel.expiryDate}
                            </span>
                          </div>
                          
                          <div className="flex flex-wrap text-xs text-gray-500 mt-2 gap-3">
                            <span className="flex items-center">
                              <User className="h-3 w-3 mr-1" />
                              {product.user.username}
                            </span>
                            <span className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {format(new Date(product.createdAt), 'dd MMM yyyy', { locale: it })}
                            </span>
                            <span className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {format(new Date(product.createdAt), 'HH:mm', { locale: it })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <XCircle className="h-10 w-10 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">Contenitore non trovato</h3>
            <p className="text-gray-500">
              Il contenitore richiesto non esiste o è stato rimosso.
            </p>
            <Button variant="outline" className="mt-4" onClick={onClose}>
              Chiudi
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}