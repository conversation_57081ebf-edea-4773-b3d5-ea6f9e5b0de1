import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Container } from "@/types";
import { MoreVerticalIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface ContainerCardProps {
  container: Container;
  onViewDetails: (container: Container) => void;
  onArchive?: (container: Container) => void;
  onUnarchive?: (container: Container) => void;
  onPrintQR?: (container: Container) => void;
  isArchived?: boolean;
}

export function ContainerCard({
  container,
  onViewDetails,
  onArchive,
  onUnarchive,
  onPrintQR,
  isArchived = false,
}: ContainerCardProps) {
  const getUsageStatus = () => {
    if (container.currentItems === 0) {
      return { color: "blue", text: "Zero" };
    }
    if (container.currentItems === container.maxItems) {
      return { color: "red", text: "Pieno" };
    }
    return { 
      color: "green", 
      text: `${container.currentItems} Prodotti` 
    };
  };

  const status = getUsageStatus();

  return (
    <Card className={cn(
      isArchived ? "bg-gray-100" : "bg-white",
      "transition-all duration-200 hover:shadow-md"
    )}>
      <CardContent 
        className="p-4 flex items-center justify-between cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => onViewDetails(container)}
      >
        <div>
          <h3 className={cn(
            "text-lg font-semibold",
            isArchived ? "text-gray-500" : "text-gray-800"
          )}>
            {container.name}
          </h3>
          <p className="text-sm text-gray-500">
            {isArchived 
              ? `Archiviato il ${new Date(container.createdAt).toLocaleDateString()}` 
              : `${container.type} container`}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {!isArchived && (
            <Badge
              variant={status.color === "red" ? "destructive" : "outline"}
              className={cn(
                status.color === "green" && "bg-green-100 text-green-800 hover:bg-green-100",
                status.color === "blue" && "bg-blue-100 text-blue-800 hover:bg-blue-100"
              )}
            >
              {status.text}
            </Badge>
          )}
          <DropdownMenu>
            {/* stopPropagation per evitare che il click sul pulsante attivi anche l'onClick del CardContent */}
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1 rounded-full text-gray-400 hover:text-gray-500"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVerticalIcon className="h-6 w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onViewDetails(container)}>
                Visualizza Dettagli
              </DropdownMenuItem>
              {onPrintQR && (
                <DropdownMenuItem onClick={() => onPrintQR(container)}>
                  Stampa QR Code
                </DropdownMenuItem>
              )}
              {!isArchived && onArchive && (
                <DropdownMenuItem onClick={() => onArchive(container)}>
                  Archivia Container
                </DropdownMenuItem>
              )}
              {isArchived && onUnarchive && (
                <DropdownMenuItem onClick={() => onUnarchive(container)}>
                  Ripristina Container
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
}
