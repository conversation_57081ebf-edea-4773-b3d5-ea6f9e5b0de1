import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ProductLabel } from "@shared/schema";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { Plus, Loader2 } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";

interface LabelAssociationModalProps {
  ddtId: number;
  ddtNumber: string;
  onSuccess?: () => void;
}

export function LabelAssociationModal({ ddtId, ddtNumber, onSuccess }: LabelAssociationModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all available labels (not expired and not associated with other DDTs)
  const { data: availableLabels, isLoading } = useQuery<ProductLabel[]>({
    queryKey: ["/api/product-labels/available", ddtId],
    queryFn: async () => {
      const response = await fetch(`/api/product-labels/available-for-ddt/${ddtId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch available labels");
      }
      return response.json();
    },
    enabled: isOpen
  });

  const handleAssociateLabel = async (labelId: number) => {
    setIsSubmitting(true);
    try {
      await apiRequest(`/api/product-labels/${labelId}/associate-ddt`, 'POST', {
        ddtId: ddtId
      });

      toast({
        title: "Successo",
        description: "Etichetta associata al DDT con successo",
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels/ddt", ddtId] });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels/available", ddtId] });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });

      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error associating label:", error);
      toast({
        title: "Errore",
        description: "Impossibile associare l'etichetta al DDT",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if label is expired
  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    return expiry < today;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className="flex items-center gap-2 border-green-200 text-green-700 hover:bg-green-50"
        >
          <Plus size={16} />
          Associa Etichetta
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Associa Etichetta a DDT #{ddtNumber}</DialogTitle>
        </DialogHeader>
        
        <div className="mt-4">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : availableLabels?.length === 0 ? (
            <div className="text-center py-8">
              <Package size={48} className="mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">Nessuna etichetta disponibile per l'associazione</p>
              <p className="text-sm text-gray-400 mt-1">
                Le etichette devono essere non scadute e non già associate ad altri DDT
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-3">
                {availableLabels?.map((label) => (
                  <Card key={label.id} className="p-4 hover:shadow-sm transition-shadow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium mb-2 text-[14px]">{label.productName}</h4>
                        
                        <div className="grid grid-cols-3 gap-4 mb-3">
                          <div className="space-y-1">
                            <div className="text-gray-600 text-[12px]">
                              <span className="text-[12px]">Scadenza: {label.expiryDate ? formatDate(label.expiryDate) : 'Non specificata'}</span>
                            </div>
                          </div>
                          
                          <div className="space-y-1 text-[12px]">
                            <div className="text-gray-600 ml-[47px] mr-[47px]">
                              <span className="text-[12px]">Creata: {formatDate(label.createdAt)}</span>
                            </div>
                          </div>

                          <div className="space-y-1 text-[12px]">
                            {label.batchNumber && (
                              <div className="text-gray-600 ml-[98px] mr-[98px]">
                                <span className="text-[12px]">Lotto: {label.batchNumber}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 mb-3">
                          {!isExpired(label.expiryDate) ? (
                            <Badge variant="outline" className="text-green-600 border-green-200 whitespace-nowrap text-center">
                              Valida
                            </Badge>
                          ) : (
                            <Badge variant="destructive" className="whitespace-nowrap text-center">
                              Scaduta
                            </Badge>
                          )}
                          
                          {!label.ddtId && (
                            <Badge variant="outline" className="text-blue-600 border-blue-200 whitespace-nowrap text-center">
                              Non associata
                            </Badge>
                          )}
                        </div>

                        {label.storageInstructions && (
                          <div className="mb-3 w-full">
                            <div className="text-sm text-gray-600">
                              <span className="text-[12px]">Conservazione: {label.storageInstructions}</span>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="ml-4">
                        <Button
                          onClick={() => handleAssociateLabel(label.id)}
                          disabled={isSubmitting || isExpired(label.expiryDate)}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white mt-[-30px] mb-[-10px]"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              Associando...
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-1" />
                              Associa
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}