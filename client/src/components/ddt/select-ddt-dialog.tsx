import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { DDT } from "@shared/schema";

interface SelectDDTDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectDDT: (ddt: DDT) => void;
}

interface EnrichedDDT extends DDT {
  supplier?: {
    id: number;
    companyName: string;
    vatNumber: string;
    address: string;
  } | null;
}

export function SelectDDTDialog({ open, onOpenChange, onSelectDDT }: SelectDDTDialogProps) {
  const { toast } = useToast();
  const [selectedDDTId, setSelectedDDTId] = useState<number | null>(null);

  // Carica i DDT recenti - se /api/ddt/recent non è implementato, useremo /api/ddt
  const { 
    data: recentDDTs, 
    isLoading: isLoadingRecent, 
    error: recentError 
  } = useQuery<EnrichedDDT[]>({
    queryKey: ["/api/ddt/recent"],
    enabled: open,
    retry: 1
  });
  
  // Carica tutti i DDT come fallback
  const { 
    data: allDDTs,
    isLoading: isLoadingAll
  } = useQuery<DDT[]>({
    queryKey: ["/api/ddt"],
    enabled: open
  });
  
  // Stato derivato
  const isLoading = isLoadingRecent || isLoadingAll;
  const ddtsToShow = (recentDDTs && recentDDTs.length > 0) ? recentDDTs : allDDTs;

  const handleSelectDDT = () => {
    if (!selectedDDTId) return;
    
    // Cerca prima nei DDT recenti
    let selectedDDT = recentDDTs?.find(ddt => ddt.id === selectedDDTId);
    
    // Se non trovato e abbiamo i DDT fallback, cerca lì
    if (!selectedDDT && allDDTs) {
      selectedDDT = allDDTs.find(ddt => ddt.id === selectedDDTId);
    }
    
    if (selectedDDT) {
      onSelectDDT(selectedDDT);
      // Salva nella sessionStorage per riferimento nelle altre pagine
      sessionStorage.setItem("currentDDTId", selectedDDT.id.toString());
      sessionStorage.setItem("currentDDTNumber", selectedDDT.number);
      onOpenChange(false);
    } else {
      toast({
        title: "Errore",
        description: "DDT non trovato",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Seleziona un DDT</DialogTitle>
          <DialogDescription>
            Scegli un DDT a cui associare l'etichetta del prodotto
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : ddtsToShow && ddtsToShow.length > 0 ? (
          <div className="max-h-[60vh] space-y-2">
            {ddtsToShow.map((ddt: any) => (
              <div
                key={ddt.id}
                className={cn(
                  "p-3 rounded-md border",
                  selectedDDTId === ddt.id ? "bg-primary/10 border-primary" : "border-gray-200"
                )}
              >
                <div className="flex justify-between items-center mb-1">
                  <div className="font-medium">{ddt.number}</div>
                  <div className="text-sm text-gray-500">{ddt.date}</div>
                </div>
                <div className="text-sm text-gray-600 mb-2 truncate">
                  {ddt.supplier 
                    ? ddt.supplier.companyName 
                    : ddt.companyName ? ddt.companyName : "N/A"
                  }
                </div>
                <Button
                  variant={selectedDDTId === ddt.id ? "default" : "outline"}
                  size="sm"
                  className="w-full"
                  onClick={() => setSelectedDDTId(ddt.id)}
                >
                  {selectedDDTId === ddt.id ? "✓ Selezionato" : "Seleziona"}
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">
            Non sono stati trovati DDT. Devi creare almeno un DDT per associare l'etichetta.
          </div>
        )}

        <DialogFooter className="pt-4 gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSelectDDT}
            disabled={!selectedDDTId}
            className="bg-gradient-to-r from-green-600 to-emerald-600"
          >
            Conferma selezione
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}