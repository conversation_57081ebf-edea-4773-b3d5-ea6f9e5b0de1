import React from 'react';

export const QrSearchIcon: React.FC<{ className?: string }> = ({ className = "h-6 w-6" }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      className={className}
    >
      {/* QR Code Pattern - reso più piccolo per dare più enfasi alla lente */}
      <rect x="4" y="4" width="6" height="6" />
      <rect x="14" y="4" width="6" height="6" />
      <rect x="4" y="14" width="6" height="6" />
      <rect x="14" y="14" width="6" height="6" rx="1" />
      
      {/* Lens Handle */}
      <line x1="20" y1="20" x2="23" y2="23" strokeWidth="2.5" />
      
      {/* Lens Circle */}
      <circle cx="17" cy="17" r="5" fill="none" strokeWidth="2.5" />
    </svg>
  );
};

export default QrSearchIcon;