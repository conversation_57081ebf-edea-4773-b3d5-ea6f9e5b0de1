import React, { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CheckCircle } from 'lucide-react';
import type { Container, ProductLabel } from '@shared/schema';

// Import new step components
import { ProductSelectionStep } from './product-selection-step';
import { ContainerSelectionStep } from './container-selection-step';
import { ConfirmationStep } from './confirmation-step';
import { CompletedStep } from './completed-step';

// Step enum for tracking the association process
enum AssociationStep {
  PRODUCT = 1, // First select the product
  CONTAINER = 2, // Then select the container
  CONFIRMING = 3, // Confirming the association
  COMPLETED = 4, // Association completed
}

export function ProductContainerAssociation() {
  // Main association state
  const [step, setStep] = useState<AssociationStep>(AssociationStep.PRODUCT);
  const [selectedProduct, setSelectedProduct] = useState<{id: number, name: string} | null>(null);
  const [selectedContainer, setSelectedContainer] = useState<{id: number, name: string} | null>(null);
  
  const queryClient = useQueryClient();
  
  // Mutation for creating association
  const associationMutation = useMutation({
    mutationFn: async (data: { containerId: number, productLabelId: number }) => {
      const response = await fetch('/api/container-products', { 
        method: 'POST', 
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data) 
      });
      
      // Parse error response
      if (!response.ok) {
        const errorData = await response.json();
        // Se il prodotto è già associato, non è un errore vero e proprio
        if (response.status === 400 && errorData?.message?.includes("already associated")) {
          return { 
            success: true, 
            alreadyAssociated: true,
            message: "Il prodotto è già associato a questo contenitore."
          };
        }
        throw new Error(`API error: ${response.status}`);
      }
      
      return { success: true, alreadyAssociated: false, data: await response.json() };
    },
    onSuccess: (result) => {
      if (result.alreadyAssociated) {
        // Se il prodotto era già associato, mostra un messaggio informativo invece di un errore
        toast({
          title: 'Informazione',
          description: result.message || 'Il prodotto è già associato a questo contenitore.',
          variant: 'default',
        });
      } else {
        toast({
          title: 'Associazione completata',
          description: 'Prodotto associato al contenitore con successo',
        });
      }
      
      // In entrambi i casi, procedi al completamento
      setStep(AssociationStep.COMPLETED);
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['/api/container-products'] });
      if (selectedContainer) {
        queryClient.invalidateQueries({ queryKey: ['/api/containers', selectedContainer.id] });
      }
    },
    onError: (error) => {
      toast({
        title: 'Errore',
        description: `Impossibile creare l'associazione: ${error.message || 'Errore sconosciuto'}`,
        variant: 'destructive',
      });
      // Stay on the confirming step to allow retry
    }
  });
  
  // Handle product selection
  const handleProductSelect = (productId: number, productName: string) => {
    console.log("Prodotto selezionato:", productName);
    setSelectedProduct({ id: productId, name: productName });
    setStep(AssociationStep.CONTAINER);
  };
  
  // Handle container selection
  const handleContainerSelect = (containerId: number, containerName: string) => {
    console.log("Contenitore selezionato:", containerName);
    setSelectedContainer({ id: containerId, name: containerName });
    setStep(AssociationStep.CONFIRMING);
  };
  
  // Confirm the association
  const confirmAssociation = () => {
    if (!selectedProduct || !selectedContainer) {
      toast({
        title: 'Errore',
        description: 'Dati mancanti per completare l\'associazione',
        variant: 'destructive',
      });
      return;
    }
    
    associationMutation.mutate({
      productLabelId: selectedProduct.id,
      containerId: selectedContainer.id,
    });
  };
  
  // Go back to previous step
  const goBack = () => {
    if (step === AssociationStep.CONTAINER) {
      setStep(AssociationStep.PRODUCT);
      setSelectedProduct(null);
    } else if (step === AssociationStep.CONFIRMING) {
      setStep(AssociationStep.CONTAINER);
      setSelectedContainer(null);
    }
  };
  
  // Reset the process
  const resetProcess = () => {
    setStep(AssociationStep.PRODUCT);
    setSelectedProduct(null);
    setSelectedContainer(null);
  };
  
  // Render based on current step
  const renderStep = () => {
    switch (step) {
      case AssociationStep.PRODUCT:
        return (
          <ProductSelectionStep onSelectProduct={handleProductSelect} />
        );
        
      case AssociationStep.CONTAINER:
        if (!selectedProduct) {
          // Se non c'è un prodotto selezionato, torna alla selezione prodotto
          setStep(AssociationStep.PRODUCT);
          return null;
        }
        return (
          <ContainerSelectionStep
            productId={selectedProduct.id}
            productName={selectedProduct.name}
            onSelectContainer={handleContainerSelect}
            onBack={goBack}
          />
        );
        
      case AssociationStep.CONFIRMING:
        if (!selectedProduct || !selectedContainer) {
          // Se mancano dati, torna alla selezione prodotto
          setStep(AssociationStep.PRODUCT);
          return null;
        }
        return (
          <ConfirmationStep
            productId={selectedProduct.id}
            productName={selectedProduct.name}
            containerId={selectedContainer.id}
            containerName={selectedContainer.name}
            onConfirm={confirmAssociation}
            onBack={goBack}
            isLoading={associationMutation.isPending}
          />
        );
        
      case AssociationStep.COMPLETED:
        if (!selectedProduct || !selectedContainer) {
          // Se mancano dati, torna alla selezione prodotto
          setStep(AssociationStep.PRODUCT);
          return null;
        }
        return (
          <CompletedStep
            productName={selectedProduct.name}
            containerName={selectedContainer.name}
            onReset={resetProcess}
          />
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-6 whitespace-nowrap tracking-tight">Collega Prodotto a Contenitore</h2>
      
      {/* Progress indicator */}
      <div className="flex mb-6">
        <div className="flex flex-col items-center">
          <div 
            className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
              step >= AssociationStep.PRODUCT 
                ? 'bg-blue-500 text-white border-blue-500' 
                : 'bg-white text-gray-400 border-gray-300'
            }`}
          >
            {step > AssociationStep.PRODUCT ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              1
            )}
          </div>
          <span 
            className={`text-xs mt-1 ${
              step >= AssociationStep.PRODUCT ? 'text-blue-500 font-medium' : 'text-gray-500'
            }`}
          >
            Prodotto
          </span>
        </div>
        
        <div className="flex-1 flex items-center">
          <div 
            className={`h-0.5 w-full ${
              step > AssociationStep.PRODUCT ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          />
        </div>
        
        <div className="flex flex-col items-center">
          <div 
            className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
              step >= AssociationStep.CONTAINER 
                ? 'bg-blue-500 text-white border-blue-500' 
                : 'bg-white text-gray-400 border-gray-300'
            }`}
          >
            {step > AssociationStep.CONTAINER ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              2
            )}
          </div>
          <span 
            className={`text-xs mt-1 ${
              step >= AssociationStep.CONTAINER ? 'text-blue-500 font-medium' : 'text-gray-500'
            }`}
          >
            Contenitore
          </span>
        </div>
        
        <div className="flex-1 flex items-center">
          <div 
            className={`h-0.5 w-full ${
              step > AssociationStep.CONTAINER ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          />
        </div>
        
        <div className="flex flex-col items-center">
          <div 
            className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
              step >= AssociationStep.CONFIRMING 
                ? 'bg-blue-500 text-white border-blue-500' 
                : 'bg-white text-gray-400 border-gray-300'
            }`}
          >
            {step > AssociationStep.CONFIRMING ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              3
            )}
          </div>
          <span 
            className={`text-xs mt-1 ${
              step >= AssociationStep.CONFIRMING ? 'text-blue-500 font-medium' : 'text-gray-500'
            }`}
          >
            Conferma
          </span>
        </div>
      </div>
      
      {renderStep()}
    </div>
  );
}