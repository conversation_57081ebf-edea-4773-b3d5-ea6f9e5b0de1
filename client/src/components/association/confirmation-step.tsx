import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Loader2, RotateCcw, ArrowLeft, CheckCircle } from 'lucide-react';

interface ConfirmationStepProps {
  productId: number;
  productName: string;
  containerId: number;
  containerName: string;
  onConfirm: () => void;
  onBack: () => void;
  isLoading: boolean;
}

export function ConfirmationStep({
  productId,
  productName,
  containerId,
  containerName,
  onConfirm,
  onBack,
  isLoading
}: ConfirmationStepProps) {
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Conferma Associazione</h2>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onBack}
          disabled={isLoading}
          className="text-gray-600 hover:text-gray-900 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span>Torna indietro</span>
        </Button>
      </div>
      
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-2">Prodotto:</h4>
        <div className="p-3 bg-gray-100 rounded-md">
          <p className="font-medium">{productName}</p>
          <p className="text-sm text-gray-600">ID: {productId}</p>
        </div>
      </div>
      
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-2">Contenitore:</h4>
        <div className="p-3 bg-gray-100 rounded-md">
          <p className="font-medium">{containerName}</p>
          <p className="text-sm text-gray-600">ID: {containerId}</p>
        </div>
      </div>
      
      <div className="flex flex-col gap-3">
        <Button 
          onClick={onConfirm} 
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Associazione in corso...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Conferma Associazione
            </>
          )}
        </Button>
      </div>
    </Card>
  );
}