import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { SimpleContainerSelector } from '@/components/ui/simple-container-selector';
import { QRScanner, QRCodeData } from '@/components/ui/qr-scanner';
import { toast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScanLine, ListFilter, ArrowLeft } from 'lucide-react';

interface ContainerSelectionStepProps {
  productId: number;
  productName: string;
  onSelectContainer: (containerId: number, containerName: string) => void;
  onBack: () => void;
}

export function ContainerSelectionStep({ 
  productId, 
  productName,
  onSelectContainer,
  onBack 
}: ContainerSelectionStepProps) {
  const [selectionMethod, setSelectionMethod] = useState<'scan' | 'select'>('select');
  
  // Gestisce la scansione di un contenitore
  const handleContainerScan = (data: QRCodeData) => {
    // Validazione del tipo di QR code
    if (data.type !== 'container') {
      toast({
        title: 'QR Code non valido',
        description: 'Per favore scansiona un QR code di contenitore',
        variant: 'destructive',
      });
      return false;
    }
    
    // Avviso del contenitore selezionato
    toast({
      title: 'Contenitore scansionato',
      description: data.name ? `Contenitore: ${data.name}` : `Contenitore #${data.id}`,
    });
    
    // Passa il contenitore al gestore esterno
    onSelectContainer(data.id, data.name || `Contenitore #${data.id}`);
    return true;
  };
  
  // Gestisce la selezione di un contenitore dalla tendina
  const handleContainerSelect = (containerId: number, containerName: string) => {
    console.log("Contenitore selezionato dalla dropdown:", containerName);
    onSelectContainer(containerId, containerName);
  };
  
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Seleziona un Contenitore</h2>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onBack}
          className="text-gray-600 hover:text-gray-900 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span>Torna indietro</span>
        </Button>
      </div>
      
      <div className="mb-4 p-3 bg-blue-50 rounded-md">
        <p className="text-sm text-blue-700 font-medium">
          Prodotto selezionato: {productName}
        </p>
        <p className="text-xs text-blue-600">
          Ora scegli un contenitore dove posizionare questo prodotto
        </p>
      </div>
      
      <Tabs 
        defaultValue="select" 
        value={selectionMethod} 
        onValueChange={(value) => setSelectionMethod(value as 'scan' | 'select')}
        className="w-full"
      >
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="select" className="flex items-center gap-2">
            <ListFilter className="h-4 w-4" />
            <span>Seleziona</span>
          </TabsTrigger>
          <TabsTrigger value="scan" className="flex items-center gap-2">
            <ScanLine className="h-4 w-4" />
            <span>Scansiona</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="select" className="mt-2">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">
              Seleziona un contenitore dal menu a tendina:
            </p>
            <SimpleContainerSelector 
              onSelect={handleContainerSelect} 
              productId={productId}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="scan" className="mt-2">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">
              Utilizza la fotocamera per scansionare il QR code di un contenitore:
            </p>
            <QRScanner
              onScan={handleContainerScan}
              buttonText="Scan QR Code Contenitore"
              title="Scan QR Code"
              description="Inquadra il QR code del contenitore"
            />
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
}