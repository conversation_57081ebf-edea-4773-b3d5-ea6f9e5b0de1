import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckCircle } from 'lucide-react';

interface CompletedStepProps {
  productName: string;
  containerName: string;
  onReset: () => void;
}

export function CompletedStep({
  productName,
  containerName,
  onReset,
}: CompletedStepProps) {
  return (
    <Card className="p-4">
      <div className="flex flex-col items-center text-center">
        <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
        <h3 className="text-xl font-semibold mb-2">Associazione Completata</h3>
        <div className="bg-green-50 p-4 rounded-md border border-green-200 mb-6 text-left w-full">
          <h4 className="font-semibold text-green-700 mb-2">Riepilogo dell'operazione:</h4>
          <ul className="text-gray-700 space-y-2">
            <li><span className="font-medium">Prodotto:</span> {productName}</li>
            <li className="pt-1"><span className="font-medium">Contenitore:</span> {containerName}</li>
          </ul>
        </div>
        
        <Button onClick={onReset} className="w-full">
          Nuova Associazione
        </Button>
      </div>
    </Card>
  );
}