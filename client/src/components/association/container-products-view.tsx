import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Info, User, Calendar, Trash2, AlertCircle, AlertTriangle } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { Container, ContainerProduct, ProductLabel, User as UserType } from '@shared/schema';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { useToast } from '@/hooks/use-toast';
import { isExpired } from '@/lib/dateUtils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";

interface ContainerProductsViewProps {
  containerId: number;
}

// Type for the container product with expanded details
interface ContainerProductWithDetails extends ContainerProduct {
  productLabel: ProductLabel;
  user: UserType;
}

export function ContainerProductsView({ containerId }: ContainerProductsViewProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [openDeleteDialog, setOpenDeleteDialog] = React.useState(false);
  const [selectedProduct, setSelectedProduct] = React.useState<ContainerProductWithDetails | null>(null);
  
  // Query for container details
  const containerQuery = useQuery<Container>({
    queryKey: ['/api/containers', containerId],
    queryFn: () => apiRequest(`/api/containers/${containerId}`),
  });
  
  // Query for products associated with this container
  const productsQuery = useQuery<ContainerProductWithDetails[]>({
    queryKey: ['/api/container-products', containerId],
    queryFn: () => apiRequest(`/api/container-products/container/${containerId}`),
  });
  
  // Mutation for removing a product from a container
  const removeProductMutation = useMutation({
    mutationFn: async (associationId: number) => {
      return await apiRequest('DELETE', `/api/container-products/${associationId}`);
    },
    onSuccess: () => {
      // Invalidate and refetch queries related to this container
      queryClient.invalidateQueries({ queryKey: ['/api/container-products', containerId] });
      queryClient.invalidateQueries({ queryKey: ['/api/containers', containerId] });
      
      // Show success toast
      toast({
        title: "Prodotto rimosso",
        description: "Il prodotto è stato rimosso con successo dal container.",
      });
      
      // Close the confirmation dialog
      setOpenDeleteDialog(false);
      setSelectedProduct(null);
    },
    onError: (error) => {
      console.error('Error removing product from container:', error);
      
      // Show error toast
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante la rimozione del prodotto.",
        variant: "destructive",
      });
      
      // Close the confirmation dialog
      setOpenDeleteDialog(false);
    }
  });
  
  // Function to handle clicking the remove button
  const handleRemoveProduct = (product: ContainerProductWithDetails) => {
    setSelectedProduct(product);
    setOpenDeleteDialog(true);
  };
  
  // Function to confirm removal of product
  const confirmRemoveProduct = () => {
    if (selectedProduct) {
      removeProductMutation.mutate(selectedProduct.id);
    }
  };
  
  if (containerQuery.isLoading || productsQuery.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p>Caricamento informazioni...</p>
      </div>
    );
  }
  
  if (containerQuery.isError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p>Errore nel caricamento delle informazioni del contenitore</p>
      </div>
    );
  }
  
  if (productsQuery.isError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p>Errore nel caricamento dei prodotti associati</p>
      </div>
    );
  }
  
  const container = containerQuery.data;
  const containerProducts = productsQuery.data || [];
  
  if (!container) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p>Dati del contenitore non disponibili</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Alert dialog per confermare la rimozione del prodotto */}
      <AlertDialog open={openDeleteDialog} onOpenChange={setOpenDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              Conferma rimozione
            </AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler rimuovere 
              <strong className="font-semibold mx-1">
                {selectedProduct?.productLabel.productName}
              </strong> 
              dal container
              <strong className="font-semibold mx-1">
                {container.name}
              </strong>?
              <div className="text-red-500 mt-2 text-sm">
                Questa azione non può essere annullata.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={removeProductMutation.isPending}
            >
              Annulla
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveProduct}
              disabled={removeProductMutation.isPending}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              {removeProductMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rimozione...
                </>
              ) : (
                "Rimuovi"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold">{container.name}</h2>
          <p className="text-gray-500">Tipo: {container.type}</p>
        </div>
        <div className="bg-blue-100 text-blue-800 font-semibold rounded-md px-3 py-1 text-sm">
          {container.currentItems === 0 ? "Zero" : `${container.currentItems} prodotti`}
        </div>
      </div>
      
      {containerProducts.length === 0 ? (
        <Card className="p-6 text-center">
          <Info className="h-12 w-12 text-gray-400 mx-auto mb-2" />
          <h3 className="text-lg font-medium text-gray-800">Nessun prodotto associato</h3>
          <p className="text-gray-500 mt-1">
            Questo contenitore non contiene ancora prodotti.
          </p>
        </Card>
      ) : (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Prodotti Associati</h3>
          {containerProducts.map((containerProduct) => {
            // Verifica se il prodotto è scaduto
            const productExpired = isExpired(containerProduct.productLabel.expiryDate);
            
            return (
              <Card 
                key={containerProduct.id} 
                className={`p-4 hover:shadow-md transition-shadow ${
                  productExpired ? 'border-red-500 border-2' : ''
                }`}
              >
                <div className="flex flex-col space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold text-md">
                        {containerProduct.productLabel.productName}
                      </h4>
                      <div className="flex flex-wrap gap-2 mt-1 text-sm">
                        <span className="bg-blue-50 text-blue-700 rounded px-2 py-0.5">
                          Lotto: {containerProduct.productLabel.batchNumber}
                        </span>
                        <div className="flex items-center gap-1">
                          <span className={`${productExpired 
                            ? 'bg-red-50 text-red-700' 
                            : 'bg-amber-50 text-amber-700'} rounded px-2 py-0.5`}
                          >
                            Scadenza: {containerProduct.productLabel.expiryDate}
                          </span>
                          {productExpired && (
                            <Badge variant="destructive" className="ml-1 text-xs py-0">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Scaduto
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="text-gray-500 hover:text-red-500 hover:bg-red-50 self-start"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveProduct(containerProduct);
                      }}
                      title="Rimuovi prodotto"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="text-xs text-gray-500 flex items-center gap-4">
                    <div className="flex items-center">
                      <User className="h-3 w-3 mr-1 inline" />
                      <span>{containerProduct.user.username}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1 inline" />
                      <span>
                        {containerProduct.createdAt
                          ? format(new Date(containerProduct.createdAt), 'dd MMM yyyy, HH:mm', { locale: it })
                          : 'Data non disponibile'}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}