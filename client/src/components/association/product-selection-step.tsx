import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { SimpleProductSelector } from '@/components/ui/simple-product-selector';
import { QRScanner, QRCodeData } from '@/components/ui/qr-scanner';
import { toast } from '@/hooks/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScanLine, ListFilter } from 'lucide-react';

interface ProductSelectionStepProps {
  onSelectProduct: (productId: number, productName: string) => void;
}

export function ProductSelectionStep({ onSelectProduct }: ProductSelectionStepProps) {
  const [selectionMethod, setSelectionMethod] = useState<'scan' | 'select'>('select');
  
  // Gestisce la scansione di un prodotto
  const handleProductScan = (data: QRCodeData) => {
    // Validazione del tipo di QR code
    if (data.type !== 'product') {
      toast({
        title: 'QR Code non valido',
        description: 'Per favore scansiona un QR code di prodotto',
        variant: 'destructive',
      });
      return false;
    }
    
    // Avviso del prodotto selezionato
    toast({
      title: 'Prodotto scansionato',
      description: data.name ? `Prodotto: ${data.name}` : `Prodotto #${data.id}`,
    });
    
    // Passa il prodotto al gestore esterno
    onSelectProduct(data.id, data.name || `Prodotto #${data.id}`);
    return true;
  };
  
  // Gestisce la selezione di un prodotto dalla tendina
  const handleProductSelect = (productId: number, productName: string) => {
    console.log("Prodotto selezionato dalla dropdown:", productName);
    onSelectProduct(productId, productName);
  };
  
  return (
    <Card className="p-4">
      <h2 className="text-xl font-bold mb-4">Seleziona un Prodotto</h2>
      
      <Tabs 
        defaultValue="select" 
        value={selectionMethod} 
        onValueChange={(value) => setSelectionMethod(value as 'scan' | 'select')}
        className="w-full"
      >
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="select" className="flex items-center gap-2">
            <ListFilter className="h-4 w-4" />
            <span>Seleziona</span>
          </TabsTrigger>
          <TabsTrigger value="scan" className="flex items-center gap-2">
            <ScanLine className="h-4 w-4" />
            <span>Scansiona</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="select" className="mt-2">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">
              Seleziona un prodotto dal menu a tendina:
            </p>
            <SimpleProductSelector onSelect={handleProductSelect} />
          </div>
        </TabsContent>
        
        <TabsContent value="scan" className="mt-2">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">
              Utilizza la fotocamera per scansionare il QR code di un prodotto:
            </p>
            <QRScanner
              onScan={handleProductScan}
              buttonText="Scan QR Code Prodotto"
              title="Scan QR Code"
              description="Inquadra il QR code sull'etichetta del prodotto"
            />
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
}