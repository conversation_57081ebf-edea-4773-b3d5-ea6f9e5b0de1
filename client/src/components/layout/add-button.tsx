import React from 'react';
import { <PERSON> } from 'wouter';
import { PlusIcon } from 'lucide-react';

interface AddButtonProps {
  href: string;
  label?: string;
}

export function AddButton({ href, label = 'Aggiungi' }: AddButtonProps) {
  return (
    <Link href={href}>
      <div className="fixed bottom-24 right-6 z-50 inline-flex items-center justify-center w-14 h-14 rounded-full bg-gradient-to-br from-blue-600 to-indigo-700 text-white shadow-lg shadow-blue-600/40 active:shadow-blue-600/50 active:scale-95 transition-all duration-150">
        <PlusIcon className="h-7 w-7" />
        <span className="sr-only">{label}</span>
      </div>
    </Link>
  );
}