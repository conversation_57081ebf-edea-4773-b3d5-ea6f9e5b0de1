import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Button3D } from "@/components/ui/button-3d-new";
import { APP_VERSION } from "@/lib/appUpdateManager";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BellIcon, UserIcon, ChevronLeftIcon, ActivityIcon, WrenchIcon, Settings, Building2, X, LogOut, ArchiveIcon, Megaphone, Users, FileText, ArrowLeft } from "lucide-react";
import { useAuth } from "@/context/auth-context";
import { Card } from "@/components/ui/card";
import { Container } from "@/components/ui/container";
import { toast } from "@/hooks/use-toast";
import { SyncStatus } from "@/components/ui/sync-status";
import { useOfflineApi } from "@/hooks/use-offline-api";
import { useImpersonation } from "@/hooks/use-impersonation";
import { FeedbackModal } from "@/components/feedback/feedback-modal";

// Logo will be loaded directly from public folder

interface HeaderProps {
  title?: string; // Ora titolo è opzionale
  showBack?: boolean;
  backPath?: string;
  showNotification?: boolean;
  showUserMenu?: boolean;
}

export function Header({
  title, // Titolo non utilizzato
  showBack = false,
  backPath = "/",
  showNotification = true,
  showUserMenu = true,
}: HeaderProps) {
  const [location, setLocation] = useLocation();
  const { user, logout, isAdmin } = useAuth();
  

  const { isOnline, pendingOperations } = useOfflineApi();
  const [notificationsCount, setNotificationsCount] = useState(0);
  const [showNotificationsPanel, setShowNotificationsPanel] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const { isImpersonating } = useImpersonation();
  
  // Applica stile all'header quando l'impersonazione è attiva
  useEffect(() => {
    const headerElement = document.querySelector('header');
    if (headerElement) {
      if (isImpersonating) {
        headerElement.style.marginTop = '56px'; // Altezza del banner di impersonazione
        headerElement.style.position = 'relative';
        headerElement.style.zIndex = '40';
      } else {
        headerElement.style.marginTop = '0';
        headerElement.style.position = 'sticky';
        headerElement.style.zIndex = '30';
      }
    }
  }, [isImpersonating]);

  // TEMPORANEAMENTE DISABILITATO per debug del problema a 5 secondi
  /*
  useEffect(() => {
    const forceDropdownPosition = () => {
      const headerDropdownContent = document.querySelector('header [data-radix-dropdown-menu-content].desktop-menu-right') as HTMLElement;
      if (headerDropdownContent) {
        const isHeaderDropdown = headerDropdownContent.closest('header') !== null;
        const isNotUserCardDropdown = !headerDropdownContent.closest('[data-user-card-dropdown]');
        
        if (isHeaderDropdown && isNotUserCardDropdown) {
          headerDropdownContent.style.setProperty('transform-origin', 'top right', 'important');
          headerDropdownContent.style.setProperty('right', '0px', 'important');
          headerDropdownContent.style.setProperty('left', 'auto', 'important');
        }
      }
    };

    const headerElement = document.querySelector('header');
    if (headerElement) {
      const observer = new MutationObserver((mutations) => {
        const isHeaderDropdownChange = mutations.some(mutation => {
          if (mutation.target instanceof Element) {
            return mutation.target.closest('header') && 
                   (mutation.target.matches('[data-radix-dropdown-menu-content]') ||
                    mutation.target.querySelector('[data-radix-dropdown-menu-content]'));
          }
          return false;
        });
        
        if (isHeaderDropdownChange) {
          setTimeout(forceDropdownPosition, 10);
        }
      });
      
      observer.observe(headerElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['data-state', 'data-align']
      });

      return () => observer.disconnect();
    }
  }, []); 
  */
  
  // Verifica se siamo nella home page
  const isHomePage = location === "/" || location === "";
  
  // Mostra il pulsante back automaticamente su tutte le pagine tranne la home
  const shouldShowBack = showBack || !isHomePage;

  const handleBackClick = () => {
    // Se è specificato un backPath, usalo direttamente per garantire navigazione corretta
    if (backPath && backPath !== "/") {
      setLocation(backPath);
    } else if (window.history.length > 1) {
      // Altrimenti utilizza la cronologia del browser
      window.history.back();
    } else {
      // Fallback alla home
      setLocation("/");
    }
  };

  const handleLogout = async () => {
    await logout();
    setLocation("/");
  };
  
  const handleNotificationsClick = () => {
    setShowNotificationsPanel(!showNotificationsPanel);
    // Se ci sono notifiche, le consideriamo come lette al click
    if (notificationsCount > 0) {
      setNotificationsCount(0);
    }
  };

  return (
    <header className={`fixed left-0 right-0 z-50 px-4 pt-2 pb-2 ${isImpersonating ? 'top-14' : 'top-0'}`}>
      <Container variant="header" className="relative flex items-center justify-between px-3 py-2 mx-auto max-w-md h-[60px] bg-transparent">
        <div className="flex items-center">
          {shouldShowBack && (
            <Button3D
              icon={ChevronLeftIcon}
              iconSize={26}
              className="mr-2"
              onClick={handleBackClick}
            />
          )}
          {/* Mostra logo in tutte le pagine */}
          <div className="flex items-center">
            <img 
              src="/logo.png" 
              alt="HACCP Tracker" 
              className="mr-3 h-9 max-w-none" 
              style={{ display: 'block', visibility: 'visible' }}
              onLoad={() => console.log('Logo caricato con successo')}
              onError={(e) => {
                console.error('Logo non caricabile:', e);
                console.error('Src attuale:', e.currentTarget.src);
                // Non nascondere per debug
                // e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        </div>
        
        {/* Spazio vuoto al centro */}
        <div className="flex-1"></div>
        
        <div className="flex items-center space-x-4">
          {/* Indicatore di sincronizzazione */}
          <SyncStatus showBadge={false} className="mr-1" alwaysVisible={true} />
          
          {/* Pulsante feedback */}
          <Button3D
            icon={Megaphone}
            iconSize={24}
            aria-label="Invia feedback"
            onClick={() => setShowFeedbackModal(true)}
            className="bg-[#ff0303]"
          />
          
          {/* Menu opzioni */}
          {showUserMenu && user && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button3D
                  icon={Settings}
                  iconSize={24}
                  aria-label="Menu utente"
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end" 
                side="bottom"
                sideOffset={8}
                avoidCollisions={true}
                alignOffset={0}
                className="w-72 rounded-xl p-2 bg-white/90 backdrop-blur-md border border-gray-100 shadow-lg desktop-menu-right"
              >
                {/* Header informazioni utente */}
                <div className="rounded-lg py-3.5 px-4">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <div style={{ width: '28px', height: '28px', perspective: '100px', filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.15))', transform: 'translateZ(2px)' }} className="rounded-full bg-blue-100 flex items-center justify-center mr-4">
                        <div className="relative">
                          <UserIcon style={{ width: '15px', height: '15px', filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} className="text-blue-600" />
                          <WrenchIcon style={{ width: '15px', height: '15px', position: 'absolute', bottom: '-1px', right: '-1px', transform: 'rotate(45deg) translateZ(5px)', filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.2))' }} className="text-blue-600" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-base">{user.username}</p>
                        {user.isAdmin && <p className="text-sm text-gray-500">Amministratore</p>}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Pulsante di Logout separato */}
                <DropdownMenuItem onClick={handleLogout} className="rounded-lg py-3 px-4 text-red-600 hover:text-red-700 hover:bg-red-50">
                  <LogOut style={{ width: '16px', height: '16px', marginRight: '10px' }} />
                  Logout
                </DropdownMenuItem>
                <DropdownMenuSeparator className="my-1.5" />
                

                
                {/* Rimosso selettore tema per migliorare le performance */}
                
                <DropdownMenuItem onClick={() => setLocation("/profile")} className="rounded-lg py-3 px-4 text-xl font-bold">
                  <UserIcon style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                  Profilo
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocation("/suppliers")} className="rounded-lg py-3 px-4 text-xl font-bold">
                  <Building2 style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                  Fornitori
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocation("/activities")} className="rounded-lg py-3 px-4 text-xl font-bold">
                  <ActivityIcon style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                  Attività recenti
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocation("/retired-products")} className="rounded-lg py-3 px-4 text-xl font-bold">
                  <ArchiveIcon style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                  Prodotti Ritirati
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocation("/settings")} className="rounded-lg py-3 px-4 text-xl font-bold">
                  <Settings style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                  Impostazioni
                </DropdownMenuItem>
                
                {/* Torna al Dashboard - mostrato solo quando admin accede a un tenant */}
                {user.isAdmin && new URLSearchParams(window.location.search).get('adminAccess') === 'true' && (
                  <DropdownMenuItem onClick={() => window.location.href = '/admin-dashboard'} className="rounded-lg py-3 px-4 text-xl font-bold bg-blue-50 hover:bg-blue-100">
                    <ArrowLeft style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} className="text-blue-600" />
                    <span className="text-blue-600">Torna al Dashboard</span>
                  </DropdownMenuItem>
                )}

                {/* Admin-only options */}
                {user.isAdmin && (
                  <>
                    <DropdownMenuItem onClick={() => setLocation("/users")} className="rounded-lg py-3 px-4 text-xl font-bold">
                      <Users style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                      Gestione Utenti
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setLocation("/reports")} className="rounded-lg py-3 px-4 text-xl font-bold">
                      <FileText style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                      Rapporti
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setLocation("/admin-dashboard")} className="rounded-lg py-3 px-4 text-xl font-bold">
                      <WrenchIcon style={{ width: '24px', height: '24px', marginRight: '15px', filter: 'drop-shadow(0 2px 2px rgba(0,0,0,0.2))', transform: 'translateZ(3px)' }} />
                      Dashboard Admin
                    </DropdownMenuItem>
                  </>
                )}
                
                <DropdownMenuSeparator className="my-1.5" />
                
                {/* Informazioni sulla versione dell'app e tenant */}
                <div className="rounded-lg py-2 px-4 text-xs text-center text-gray-500">
                  <div>HACCP Tracker v{APP_VERSION}</div>
                  {user.tenant && (
                    <div className="mt-1 text-xs text-gray-400">
                      {user.tenant.name} ({user.tenant.code})
                    </div>
                  )}
                </div>

              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </Container>
      {/* Feedback Modal */}
      <FeedbackModal 
        open={showFeedbackModal}
        onOpenChange={setShowFeedbackModal}
      />
    </header>
  );
}