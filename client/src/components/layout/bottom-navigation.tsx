import React, { memo, useMemo, useCallback } from "react";
import { useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { HomeIcon, Truck, Package, Search, QrCode } from "lucide-react";
import { motion } from "framer-motion";

interface BottomNavigationProps {
  activeItem?: "home" | "goods" | "containers" | "search" | "activities" | "suppliers" | "qrscan";
}

const BottomNavigationComponent = memo(function BottomNavigation({ activeItem }: BottomNavigationProps) {
  const [, setLocation] = useLocation();

  const navigateTo = useCallback((path: string) => {
    setLocation(path);
  }, [setLocation]);

  // Memoizza la configurazione delle tab per evitare re-render inutili
  const navigationItems = useMemo(() => [
    {
      id: "home",
      label: "Home", 
      icon: HomeIcon,
      path: "/",
      isActive: activeItem === "home"
    },
    {
      id: "goods", 
      label: "Merci",
      icon: Truck,
      path: "/incoming-goods",
      isActive: activeItem === "goods"
    },
    {
      id: "containers",
      label: "Contenitori", 
      icon: Package,
      path: "/containers",
      isActive: activeItem === "containers"
    },
    {
      id: "search",
      label: "Cerca",
      icon: Search, 
      path: "/search",
      isActive: activeItem === "search"
    },
    {
      id: "qrscan",
      label: "QR Scan",
      icon: QrCode,
      path: "/container-history-scanner", 
      isActive: activeItem === "qrscan"
    }
  ], [activeItem]);

  return (
    <motion.nav 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-black/80 backdrop-blur-xl fixed bottom-0 left-0 right-0 z-30 border-t border-gray-800/40 pb-safe px-4 pt-3 pb-[40px] mt-10"
      data-ui="ready"
    >
      <div className="flex justify-around max-w-md mx-auto">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          return (
            <button
              key={item.id}
              type="button"
              onClick={() => navigateTo(item.path)}

              className={cn(
                "group flex items-center justify-center py-3 px-3 rounded-full transition-all duration-200 w-14 h-14",
                item.isActive 
                  ? "text-white bg-indigo-600 shadow-lg" 
                  : "text-white/70 hover:text-white hover:bg-white/10 hover:shadow-md"
              )}
            >
              <Icon className="h-5.5 w-5.5" />
            </button>
          );
        })}
      </div>
    </motion.nav>
  );
});

export { BottomNavigationComponent as BottomNavigation };