import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Building2, ExternalLink, X } from 'lucide-react';

interface TenantAdminBannerProps {
  tenantCode?: string;
  onClose?: () => void;
}

export function TenantAdminBanner({ tenantCode, onClose }: TenantAdminBannerProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // SEMPRE rimuovi il padding del body perché ora il banner non viene mai mostrato
    document.body.style.paddingTop = '0';
    
    // Cleanup function
    return () => {
      document.body.style.paddingTop = '0';
    };
  }, [tenantCode, isVisible]);

  const handleClose = () => {
    setIsVisible(false);
    
    // Reset body padding
    document.body.style.paddingTop = '0';
    
    // Remove admin access parameters from URL
    const url = new URL(window.location.href);
    url.searchParams.delete('adminAccess');
    url.searchParams.delete('tenant');
    url.searchParams.delete('timestamp');
    
    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());
    
    onClose?.();
  };

  const handleReturnToAdmin = () => {
    // Return to admin dashboard
    window.location.href = '/admin-dashboard';
  };

  // Nascondi sempre la banda blu - funzionalità spostata nel menu
  return null;
}