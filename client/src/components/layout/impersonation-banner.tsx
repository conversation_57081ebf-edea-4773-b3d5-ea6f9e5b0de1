import { useLocation } from "wouter";
import { ArrowLef<PERSON>, UserCog } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/auth-context";
import { useImpersonation } from "@/hooks/use-impersonation";
import { useMutation } from "@tanstack/react-query";

export function ImpersonationBanner() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user, refreshUser } = useAuth();
  const { isImpersonating } = useImpersonation();
  


  // Mutation per terminare l'impersonificazione - approccio semplificato con redirect diretto
  const stopImpersonationMutation = useMutation({
    mutationFn: async () => {
      // Salviamo lo stato locale prima di procedere
      localStorage.removeItem('user_impersonation');
      
      // Usiamo un redirect diretto invece della chiamata API tramite fetch
      window.location.href = '/api/users/stop-impersonating?redirect=/users';
      
      // Ritorniamo un risultato fittizio per completare la mutazione
      return {
        success: true,
        message: "Terminando impersonificazione...",
        redirecting: true
      };
    },
    onSuccess: () => {
      // Mostriamo solo un toast perché il redirect è gestito dal server
      toast({
        title: "Terminazione impersonificazione...",
        description: "Stai tornando al tuo account originale.",
      });
    },
    onError: (error) => {
      console.error("Errore durante la terminazione dell'impersonificazione:", error);
      toast({
        title: "Errore",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante la terminazione dell'impersonificazione.",
        variant: "destructive",
      });
      
      // In caso di errore, refreshUser per sicurezza
      refreshUser();
    }
  });

  if (!isImpersonating) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-blue-600 py-2 px-3 text-white text-sm flex items-center justify-between">
      <div className="flex items-center">
        <UserCog className="h-4 w-4 mr-2" />
        <span>Stai impersonando l'utente <strong>{user?.username}</strong></span>
      </div>
      <Button
        size="sm"
        variant="outline"
        className="bg-white text-blue-600 border-white hover:bg-blue-50 hover:text-blue-700 text-xs py-0 h-7"
        onClick={() => stopImpersonationMutation.mutate()}
        disabled={stopImpersonationMutation.isPending}
      >
        {stopImpersonationMutation.isPending ? (
          <span className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Uscendo...
          </span>
        ) : (
          <span className="flex items-center">
            <ArrowLeft className="h-3 w-3 mr-1" />
            Torna al tuo account
          </span>
        )}
      </Button>
    </div>
  );
}