import React from 'react';
import { BottomNavigation } from './bottom-navigation';
import { APP_VERSION } from "@/lib/appUpdateManager";
import { useLocation } from "wouter";
import { useSPANavigation } from "@/lib/spa-navigation";

// Previeni ricaricamenti accidentali
const handleNavigation = (path: string, setLocation: (path: string) => void) => (e: React.MouseEvent) => {
  e.preventDefault();
  e.stopPropagation();
  setLocation(path);
};

interface FooterProps {
  activeItem?: 'home' | 'goods' | 'containers' | 'search' | 'activities' | 'suppliers' | 'qrscan';
  showVersion?: boolean;
  className?: string;
}

export function Footer({ activeItem }: FooterProps) {
  const { navigate, preload } = useSPANavigation();

  // Funzione helper per navigazione SPA ultra-ottimizzata
  const navigateTo = (path: string) => (e: React.MouseEvent) => {
    // Feedback aptico immediato e sottile
    if ('vibrate' in navigator) {
      navigator.vibrate([3]);
    }
    
    // Precarica immediatamente con priorità alta
    preload(path);
    
    // Aggiungi classe per feedback visivo immediato
    const target = e.currentTarget as HTMLElement;
    target.style.transform = 'scale(0.96)';
    target.style.transition = 'transform 0.1s ease-out';
    
    // Reset del feedback visivo
    setTimeout(() => {
      target.style.transform = '';
      target.style.transition = '';
    }, 100);
    
    // Naviga con transizioni ultra-fluide
    navigate(path)(e);
  };

  return (
    <footer>
      {/* Rimosso showVersion per tenere l'informazione solo nel menu a tendina */}
      <BottomNavigation activeItem={activeItem} />
    </footer>
  );
}