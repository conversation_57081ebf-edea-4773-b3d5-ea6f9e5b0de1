import React from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Home, Package, Truck, ClipboardList, Settings, Scan } from 'lucide-react';

interface TopNavBarProps {
  currentPage?: string;
}

export function TopNavBar({ currentPage }: TopNavBarProps) {
  const [, navigate] = useLocation();
  
  const menuItems = [
    { 
      icon: <Home className="w-4 h-4 mr-2" />, 
      label: 'Home', 
      path: '/' 
    },
    { 
      icon: <Package className="w-4 h-4 mr-2" />, 
      label: 'Contenitori', 
      path: '/containers' 
    },
    { 
      icon: <Truck className="w-4 h-4 mr-2" />, 
      label: 'Merci', 
      path: '/incoming-goods' 
    },
    { 
      icon: <Scan className="w-4 h-4 mr-2" />, 
      label: 'Scanner QR', 
      path: '/qr-scanner' 
    },
    { 
      icon: <ClipboardList className="w-4 h-4 mr-2" />, 
      label: 'Attività', 
      path: '/activities' 
    },
    { 
      icon: <Settings className="w-4 h-4 mr-2" />, 
      label: 'Impostazioni', 
      path: '/settings' 
    }
  ];
  
  return (
    <div className="bg-gradient-to-br from-blue-600 to-blue-700 shadow-md py-3 px-4 overflow-x-auto flex items-center space-x-3 sticky top-0 z-30">
      {menuItems.map((item) => (
        <Button
          key={item.path}
          variant={currentPage === item.path ? "secondary" : "ghost"}
          size="sm"
          className={`whitespace-nowrap rounded-full ${currentPage === item.path ? 'bg-white/20 hover:bg-white/30 text-white' : 'text-white/90 hover:text-white hover:bg-white/10'}`}
          onClick={() => navigate(item.path)}
        >
          {item.icon}
          <span className="hidden sm:inline">{item.label}</span>
        </Button>
      ))}
    </div>
  );
}