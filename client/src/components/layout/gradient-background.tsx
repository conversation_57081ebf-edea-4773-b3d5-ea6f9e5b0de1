import { ReactNode } from 'react';

interface GradientBackgroundProps {
  children: ReactNode;
}

export function GradientBackground({ children }: GradientBackgroundProps) {
  return (
    <div className="flex flex-col h-screen bg-gradient-to-tl from-gray-500 via-gray-400 to-gray-200 relative overflow-hidden">
      {/* Contenitore semplice senza elementi grafici decorativi */}
      <div className="relative flex flex-col h-full overflow-hidden">
        {children}
      </div>
    </div>
  );
}