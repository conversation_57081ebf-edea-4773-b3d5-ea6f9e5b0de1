import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface CreateContainerTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: { value: string; label: string; description?: string }) => void;
}

export function CreateContainerTypeDialog({
  open,
  onOpenChange,
  onSave,
}: CreateContainerTypeDialogProps) {
  const [value, setValue] = useState("");
  const [label, setLabel] = useState("");
  const [description, setDescription] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!value.trim()) newErrors.value = "Il valore è obbligatorio";
    if (!label.trim()) newErrors.label = "L'etichetta è obbligatoria";
    if (value.includes(" ")) newErrors.value = "Il valore non può contenere spazi";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      const data = { 
        value: value.trim().toLowerCase(), 
        label: label.trim(),
        description: description.trim() || undefined 
      };
      onSave(data);
      resetForm();
    }
  };

  const resetForm = () => {
    setValue("");
    setLabel("");
    setDescription("");
    setErrors({});
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Nuovo Tipo di Contenitore</DialogTitle>
            <DialogDescription>
              Aggiungi un nuovo tipo di contenitore per organizzare i prodotti.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="value" className="text-right">
                Nome*
              </Label>
              <div className="col-span-3">
                <Input
                  id="value"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  className={errors.value ? "border-red-500" : ""}
                />
                {errors.value && (
                  <p className="text-red-500 text-sm mt-1">{errors.value}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Identificatore univoco (es: steel, glass, paper)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="label" className="text-right">
                Etichetta*
              </Label>
              <div className="col-span-3">
                <Input
                  id="label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                  className={errors.label ? "border-red-500" : ""}
                />
                {errors.label && (
                  <p className="text-red-500 text-sm mt-1">{errors.label}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Nome visualizzato (es: Acciaio, Vetro, Carta)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Descrizione
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Annulla
            </Button>
            <Button type="submit">Salva</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
