import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ContainerType } from "@/types";

interface DeleteContainerTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  containerType: ContainerType;
  onConfirm: () => void;
}

export function DeleteContainerTypeDialog({
  open,
  onOpenChange,
  containerType,
  onConfirm,
}: DeleteContainerTypeDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {containerType.isActive ? "Disattiva" : "Riattiva"} tipo di contenitore?
          </AlertDialogTitle>
          <AlertDialogDescription>
            {containerType.isActive
              ? "Stai per disattivare il tipo di contenitore. I contenitori esistenti di questo tipo rimarranno, ma non potrai crearne di nuovi. Questo è reversibile."
              : "Stai per riattivare il tipo di contenitore. Potrai nuovamente utilizzarlo per creare nuovi contenitori."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Annulla</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>
            {containerType.isActive ? "Disattiva" : "Riattiva"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
