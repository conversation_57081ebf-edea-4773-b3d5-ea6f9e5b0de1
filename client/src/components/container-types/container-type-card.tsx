import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash, Activity } from "lucide-react";
import { ContainerType } from "@/types";

interface ContainerTypeCardProps {
  containerType: ContainerType;
  onEdit: () => void;
  onDelete: () => void;
  onToggleActive: (id: number, isActive: boolean) => void;
}

export function ContainerTypeCard({ 
  containerType, 
  onEdit, 
  onDelete,
  onToggleActive 
}: ContainerTypeCardProps) {
  const { id, value, label, description, isActive } = containerType;

  // Formatta la data come dd/mm/yyyy
  const formattedDate = new Date(containerType.createdAt).toLocaleDateString('it-IT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  console.log("Rendering container type card:", { id, value, label, description, isActive });

  return (
    <div className={`bg-white rounded-md shadow border-b mb-px overflow-hidden ${!isActive ? 'opacity-70' : ''}`}>
      {/* Intestazione */}
      <div className="px-4 py-4 border-b">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-bold">{label}</h3>
          <Badge variant={isActive ? "default" : "outline"} className={isActive ? "bg-green-600" : "text-gray-500"}>
            {isActive ? "Attivo" : "Disattivato"}
          </Badge>
        </div>
      </div>
      
      {/* Contenuto */}
      <div className="px-4 py-2">
        <div className="mb-3">
          <p className="text-sm font-semibold text-gray-700">Nome:</p>
          <div className="bg-gray-100 px-3 py-2 rounded-md mt-1">
            <p className="text-sm text-gray-800">{value}</p>
          </div>
        </div>
        
        {description && (
          <div className="mb-3">
            <p className="text-sm font-semibold text-gray-700">Descrizione:</p>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
        )}
        
        <div className="mb-3">
          <p className="text-xs text-gray-500">Creato il {formattedDate}</p>
        </div>
      </div>
      
      {/* Azioni */}
      <div className="px-4 py-3 space-y-2 border-t">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onEdit}
          className="w-full justify-center border-gray-300 bg-transparent hover:bg-gray-50"
        >
          <Edit className="h-4 w-4 mr-2" />
          Modifica
        </Button>
        
        <div className="w-full">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onToggleActive(id, !isActive)}
            className={`w-full justify-center border-gray-300 bg-transparent ${
              isActive 
                ? "text-amber-600 hover:bg-amber-50" 
                : "text-green-600 hover:bg-green-50"
            }`}
          >
            {isActive 
              ? <><Activity className="h-4 w-4 mr-2" />Disattiva</>
              : <><Activity className="h-4 w-4 mr-2" />Attiva</>
            }
          </Button>
        </div>
      </div>
    </div>
  );
}
