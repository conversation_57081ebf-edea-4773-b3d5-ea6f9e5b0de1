import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ContainerType } from "@/types";

interface EditContainerTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  containerType: ContainerType;
  onSave: (data: { value: string; label: string; description?: string; isActive: boolean }) => void;
}

export function EditContainerTypeDialog({
  open,
  onOpenChange,
  containerType,
  onSave,
}: EditContainerTypeDialogProps) {
  const [value, setValue] = useState("");
  const [label, setLabel] = useState("");
  const [description, setDescription] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (containerType) {
      setValue(containerType.value);
      setLabel(containerType.label);
      setDescription(containerType.description || "");
      setIsActive(containerType.isActive);
      setErrors({});
    }
  }, [containerType, open]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!value.trim()) newErrors.value = "Il valore è obbligatorio";
    if (!label.trim()) newErrors.label = "L'etichetta è obbligatoria";
    if (value.includes(" ")) newErrors.value = "Il valore non può contenere spazi";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      const data = { 
        value: value.trim().toLowerCase(), 
        label: label.trim(),
        description: description.trim() || undefined,
        isActive 
      };
      onSave(data);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Modifica Tipo di Contenitore</DialogTitle>
            <DialogDescription>
              Modifica le proprietà del tipo di contenitore.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="value" className="text-right">
                Nome*
              </Label>
              <div className="col-span-3">
                <Input
                  id="value"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  className={errors.value ? "border-red-500" : ""}
                />
                {errors.value && (
                  <p className="text-red-500 text-sm mt-1">{errors.value}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Identificatore univoco (es: steel, glass, paper)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="label" className="text-right">
                Etichetta*
              </Label>
              <div className="col-span-3">
                <Input
                  id="label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                  className={errors.label ? "border-red-500" : ""}
                />
                {errors.label && (
                  <p className="text-red-500 text-sm mt-1">{errors.label}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Nome visualizzato (es: Acciaio, Vetro, Carta)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Descrizione
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="isActive" className="text-right">
                Attivo
              </Label>
              <div className="col-span-3 flex items-center">
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={setIsActive}
                />
                <span className="ml-2 text-sm text-gray-700">
                  {isActive ? "Attivo" : "Disattivato"}
                </span>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Annulla
            </Button>
            <Button type="submit">Salva Modifiche</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
