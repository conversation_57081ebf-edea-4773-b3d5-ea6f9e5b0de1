import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { insertUserFeedbackSchema } from "@shared/schema";
import { z } from "zod";

interface FeedbackModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const feedbackFormSchema = insertUserFeedbackSchema.extend({
  message: z.string().min(1, "Il messaggio è obbligatorio"),
});

export function FeedbackModal({ open, onOpenChange }: FeedbackModalProps) {
  const [message, setMessage] = useState("");
  const queryClient = useQueryClient();

  const feedbackMutation = useMutation({
    mutationFn: async (data: { message: string }) => {
      return await apiRequest("/api/feedback", "POST", data);
    },
    onSuccess: () => {
      toast({
        title: "Feedback inviato",
        description: "Grazie per il tuo feedback! Lo abbiamo ricevuto correttamente.",
      });
      setMessage("");
      onOpenChange(false);
      queryClient.invalidateQueries({ queryKey: ["/api/feedback"] });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante l'invio del feedback. Riprova.",
        variant: "destructive",
      });
      console.error("Errore invio feedback:", error);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) {
      toast({
        title: "Campo obbligatorio",
        description: "Inserisci un messaggio prima di inviare il feedback.",
        variant: "destructive",
      });
      return;
    }

    feedbackMutation.mutate({ message: message.trim() });
  };

  const handleClose = () => {
    setMessage("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invia Feedback</DialogTitle>
          <DialogDescription>
            Condividi i tuoi pensieri, suggerimenti o segnala problemi. Il tuo feedback ci aiuta a migliorare l'applicazione.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Textarea
              placeholder="Scrivi qui il tuo feedback..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="min-h-[120px] resize-none"
              disabled={feedbackMutation.isPending}
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={feedbackMutation.isPending}
            >
              Annulla
            </Button>
            <Button
              type="submit"
              disabled={feedbackMutation.isPending || !message.trim()}
            >
              {feedbackMutation.isPending ? "Invio..." : "Invia Feedback"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}