import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MessageSquare, Clock, User, Trash2 } from "lucide-react";
import { UserFeedback } from "@shared/schema";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export function FeedbackList() {
  const { toast } = useToast();
  
  const { data: feedback, isLoading, error } = useQuery<UserFeedback[]>({
    queryKey: ["/api/feedback"],
    queryFn: () => apiRequest("GET", "/api/feedback"),
    staleTime: 30000, // 30 seconds
  });

  const deleteFeedbackMutation = useMutation({
    mutationFn: async (feedbackId: number) => {
      return apiRequest(`/api/feedback/${feedbackId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/feedback"] });
      toast({
        title: "Feedback eliminato",
        description: "Il messaggio di feedback è stato eliminato con successo.",
      });
    },
    onError: (error) => {
      console.error('Errore eliminazione feedback:', error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare il feedback. Riprova.",
        variant: "destructive",
      });
    },
  });

  const handleDeleteFeedback = (feedbackId: number) => {
    if (window.confirm('Sei sicuro di voler eliminare questo feedback?')) {
      deleteFeedbackMutation.mutate(feedbackId);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Feedback degli Utenti
          </CardTitle>
          <CardDescription>
            Gestione e visualizzazione dei feedback inviati dagli utenti
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Feedback degli Utenti
          </CardTitle>
          <CardDescription>
            Gestione e visualizzazione dei feedback inviati dagli utenti
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            Errore nel caricamento dei feedback
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Feedback degli Utenti
        </CardTitle>
        <CardDescription>
          Gestione e visualizzazione dei feedback inviati dagli utenti
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!feedback || feedback.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nessun feedback ricevuto ancora</p>
            <p className="text-sm mt-2">I feedback degli utenti appariranno qui quando saranno inviati</p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] w-full">
            <div className="space-y-4">
              {feedback.map((item) => (
                <div
                  key={item.id}
                  className="border rounded-lg p-4 space-y-2 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Utente ID: {item.userId}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Clock className="h-4 w-4" />
                        {format(new Date(item.createdAt), "dd MMM yyyy, HH:mm", {
                          locale: it,
                        })}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteFeedback(item.id)}
                        disabled={deleteFeedbackMutation.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="text-gray-900 leading-relaxed">
                    {item.message}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
        
        {feedback && feedback.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>Totale feedback: {feedback.length}</span>
              <Badge variant="outline">
                Ultimo aggiornamento: {format(new Date(), "HH:mm", { locale: it })}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}