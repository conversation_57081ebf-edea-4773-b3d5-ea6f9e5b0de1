import { useState, useEffect } from 'react';
import { User, UserRole, UserRoleType } from '@shared/schema';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/context/auth-context';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface EditUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (user: Partial<User>) => void;
}

export function EditUserDialog({ user, open, onOpenChange, onSave }: EditUserDialogProps) {
  // Otteniamo lo stato dell'utente corrente dal contesto di autenticazione
  const { user: currentUser } = useAuth();
  
  // Nota: Dichiarare gli stati senza valori iniziali qui
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [role, setRole] = useState<UserRoleType>(UserRole.USER);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Utilizziamo l'effetto React per aggiornare i valori del form quando user o open cambiano
  useEffect(() => {
    if (user && open) {
      console.log("Setting form with current user data:", JSON.stringify(user));
      setUsername(user.username || '');
      setEmail(user.email || ''); // Email potrebbe essere null
      setPassword('');
      setIsAdmin(user.isAdmin || false);
      
      // Determina il ruolo appropriato
      const determinedRole = user.role as UserRoleType | undefined;
      if (determinedRole && Object.values(UserRole).includes(determinedRole as any)) {
        setRole(determinedRole);
      } else {
        // Fallback per retrocompatibilità
        setRole(user.isAdmin ? UserRole.ADMIN : UserRole.USER);
      }
      
      setErrors({});
    }
  }, [user, open]);

  // Resetta il form con i valori correnti dell'utente
  const resetForm = () => {
    if (user) {
      console.log("Resetting form with user data:", JSON.stringify(user));
      setUsername(user.username || '');
      setEmail(user.email || ''); // Email potrebbe essere null
      setPassword('');
      setIsAdmin(user.isAdmin || false);
      
      // Determina il ruolo appropriato
      const determinedRole = user.role as UserRoleType | undefined;
      if (determinedRole && Object.values(UserRole).includes(determinedRole as any)) {
        setRole(determinedRole);
      } else {
        // Fallback per retrocompatibilità
        setRole(user.isAdmin ? UserRole.ADMIN : UserRole.USER);
      }
    } else {
      console.log("No user data provided for form reset");
      setUsername('');
      setEmail('');
      setPassword('');
      setIsAdmin(false);
      setRole(UserRole.USER);
    }
    setErrors({});
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      resetForm();
    }
    onOpenChange(open);
  };

  const validate = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!username || username.length < 3) {
      newErrors.username = 'Il nome utente deve avere almeno 3 caratteri';
    }

    if (email && !email.includes('@')) {
      newErrors.email = 'L\'email non è valida';
    }

    if (password && password.length < 6) {
      newErrors.password = 'La password deve avere almeno 6 caratteri';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validate()) return;

    const updatedUser: any = {
      id: user?.id
    };

    // Aggiungiamo sempre username e email 
    // perché questi campi devono essere sempre presenti nella richiesta
    updatedUser.username = username;
    
    // Solo gli admin possono cambiare il ruolo e lo stato admin di un utente
    if (currentUser?.isAdmin) {
      updatedUser.isAdmin = isAdmin;
      updatedUser.role = role;
    } else {
      // Se non sei admin, non puoi cambiare il ruolo
      // Conserviamo i valori originali
      updatedUser.isAdmin = user?.isAdmin || false;
      updatedUser.role = user?.role || (user?.isAdmin ? UserRole.ADMIN : UserRole.USER);
    }
    
    // Email: aggiungiamo sempre, anche se vuota (per supportare sia stringa vuota che null)
    updatedUser.email = email;
    console.log(`Email nel form: '${email}'`);
    
    // Password: aggiungiamo solo se inserita
    if (password && password.trim() !== "") {
      updatedUser.password = password;
      console.log("Password aggiornata");
    }
    
    // Controllo se ci sono modifiche effettive (per debug)
    const hasChanges = 
      username !== user?.username || 
      isAdmin !== user?.isAdmin || 
      role !== user?.role ||
      email !== user?.email || 
      (password && password.trim() !== "");
      
    if (!hasChanges) {
      console.log("Nessuna modifica rilevata ma procediamo comunque con l'aggiornamento");
    }

    console.log("Invio aggiornamento utente:", updatedUser);
    onSave(updatedUser);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px] mobile-modal-fix">
        <DialogHeader>
          <DialogTitle>Modifica Utente</DialogTitle>
          <DialogDescription>
            Modifica i dati dell'utente. Lascia vuoto il campo password per non modificarla.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="username" className="text-right">
              Username
            </Label>
            <Input
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="col-span-3"
            />
            {errors.username && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.username}</p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <Input
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
            />
            {errors.email && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.email}</p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="password" className="text-right">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Lascia vuoto per non modificare"
              className="col-span-3"
            />
            {errors.password && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.password}</p>
            )}
          </div>

          {/* Solo gli admin possono modificare i ruoli utente */}
          {currentUser?.isAdmin && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Ruolo
              </Label>
              <div className="col-span-3">
                <Select 
                  value={role} 
                  onValueChange={(value: UserRoleType) => {
                    setRole(value);
                    // Aggiorna isAdmin per retrocompatibilità
                    setIsAdmin(value === UserRole.ADMIN);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Seleziona un ruolo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={UserRole.ADMIN}>Amministratore</SelectItem>
                    <SelectItem value={UserRole.MANAGER}>Manager</SelectItem>
                    <SelectItem value={UserRole.USER}>Utente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          
          {/* Se non sei admin, mostra solo il ruolo senza possibilità di modifica */}
          {!currentUser?.isAdmin && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Ruolo
              </Label>
              <div className="col-span-3 flex items-center">
                <span className="text-sm">
                  {role === UserRole.ADMIN ? 'Amministratore' : 
                   role === UserRole.MANAGER ? 'Manager' : 'Utente'}
                </span>
              </div>
            </div>
          )}
          
          {/* Manteniamo questo per retrocompatibilità, nascosto agli utenti */}
          <div className="hidden">
            <Label htmlFor="isAdmin" className="text-right">
              Admin
            </Label>
            <div className="col-span-3 flex items-center">
              <Switch
                id="isAdmin"
                checked={isAdmin}
                onCheckedChange={setIsAdmin}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Annulla
          </Button>
          <Button type="submit" onClick={handleSave}>
            Salva
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
