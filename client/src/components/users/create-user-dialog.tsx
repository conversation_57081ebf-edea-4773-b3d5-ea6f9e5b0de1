import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole, UserRoleType } from '@shared/schema';

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (userData: { username: string; email: string; password: string; isAdmin: boolean; role: UserRoleType }) => void;
}

export function CreateUserDialog({ open, onOpenChange, onSave }: CreateUserDialogProps) {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [role, setRole] = useState<UserRoleType>(UserRole.USER);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const resetForm = () => {
    setUsername('');
    setEmail('');
    setPassword('');
    setIsAdmin(false);
    setRole(UserRole.USER);
    setErrors({});
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  const validate = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!username || username.length < 3) {
      newErrors.username = 'Il nome utente deve avere almeno 3 caratteri';
    }

    if (email && !email.includes('@')) {
      newErrors.email = 'L\'email non è valida';
    }

    if (!password || password.length < 6) {
      newErrors.password = 'La password deve avere almeno 6 caratteri';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validate()) return;

    // Se il ruolo è admin, imposta anche isAdmin a true per retrocompatibilità
    const updatedIsAdmin = role === UserRole.ADMIN ? true : isAdmin;

    onSave({
      username,
      email,
      password,
      isAdmin: updatedIsAdmin,
      role,
    });

    resetForm();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px] mobile-modal-fix">
        <DialogHeader>
          <DialogTitle>Crea Nuovo Utente</DialogTitle>
          <DialogDescription>
            Inserisci i dati per creare un nuovo utente nel sistema.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="create-username" className="text-right">
              Username*
            </Label>
            <Input
              id="create-username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="col-span-3"
            />
            {errors.username && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.username}</p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="create-email" className="text-right">
              Email
            </Label>
            <Input
              id="create-email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
            />
            {errors.email && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.email}</p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="create-password" className="text-right">
              Password*
            </Label>
            <Input
              id="create-password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="col-span-3"
            />
            {errors.password && (
              <p className="text-red-500 text-sm col-span-3 col-start-2">{errors.password}</p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="create-role" className="text-right">
              Ruolo
            </Label>
            <div className="col-span-3">
              <Select 
                value={role} 
                onValueChange={(value: UserRoleType) => {
                  setRole(value);
                  // Aggiorna isAdmin per retrocompatibilità
                  setIsAdmin(value === UserRole.ADMIN);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Seleziona un ruolo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={UserRole.ADMIN}>Amministratore</SelectItem>
                  <SelectItem value={UserRole.MANAGER}>Manager</SelectItem>
                  <SelectItem value={UserRole.USER}>Utente</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Manteniamo questo per retrocompatibilità, nascosto agli utenti */}
          <div className="hidden">
            <Label htmlFor="create-isAdmin" className="text-right">
              Admin
            </Label>
            <div className="col-span-3 flex items-center">
              <Switch
                id="create-isAdmin"
                checked={isAdmin}
                onCheckedChange={setIsAdmin}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Annulla
          </Button>
          <Button type="submit" onClick={handleSave}>
            Crea
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
