import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { User, UserRole } from "@shared/schema";
import { UserIcon, MoreVertical, UserCog } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";

interface UserCardProps {
  user: User;
  onEdit: (user: User) => void;
  onDelete: (userId: string) => void;
  onImpersonate?: (userId: string) => void;
  isCurrentUserAdmin?: boolean;
  currentUser?: User | null;
}

export function UserCard({ 
  user, 
  onEdit, 
  onDelete, 
  onImpersonate, 
  isCurrentUserAdmin = false,
  currentUser
}: UserCardProps) {
  const { toast } = useToast();

  return (
    <Card className="bg-white shadow-sm border border-gray-100">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-full bg-gradient-to-br from-gray-700 to-black flex items-center justify-center text-white">
              <UserIcon className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{user.username}</h3>
              <p className="text-xs text-gray-500">
                {user.role === UserRole.ADMIN ? "Amministratore" : 
                 user.role === UserRole.MANAGER ? "Manager" : 
                 // Fallback per retrocompatibilità
                 user.isAdmin ? "Amministratore" : "Utente"}
              </p>
              <p className="text-sm text-gray-500">{user.email === null || user.email === undefined || user.email === "" ? "Nessuna email" : user.email}</p>
            </div>
          </div>

          <div className="flex items-center" data-user-card-dropdown>
            {isCurrentUserAdmin && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" data-user-card-dropdown>
                  <DropdownMenuItem onClick={() => onEdit(user)}>
                    Modifica
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={() => onDelete(user.id)}
                  >
                    Elimina
                  </DropdownMenuItem>
                  
                  {/* Mostra l'opzione di impersonificazione solo se è stata fornita la funzione e l'utente non è l'admin corrente */}
                  {onImpersonate && currentUser && currentUser.id !== user.id && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => {
                          if (onImpersonate) {
                            onImpersonate(user.id);
                          }
                        }}
                        className="text-blue-600"
                      >
                        <UserCog className="h-4 w-4 mr-2" />
                        Impersona utente
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
