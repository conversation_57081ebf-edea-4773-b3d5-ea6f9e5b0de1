/**
 * Componente semplificato per ricaricamento manuale della pagina
 */
import { RefreshCw } from 'lucide-react';

export function HotReloadMonitor() {
  // Funzione per forzare un ricaricamento
  const forceReload = () => {
    console.log('Ricaricamento pagina manuale');
    window.location.reload();
  };

  // Stile direttamente incorporato per evitare problemi
  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      zIndex: 9999,
    }}>
      <button 
        onClick={forceReload}
        style={{
          backgroundColor: 'red',
          color: 'white',
          padding: '15px 20px',
          border: 'none',
          borderRadius: '12px',
          cursor: 'pointer',
          fontSize: '18px',
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
        }}
      >
        <RefreshCw size={24} style={{ marginRight: '5px' }} />
        RICARICA
      </button>
    </div>
  );
}

export default HotReloadMonitor;