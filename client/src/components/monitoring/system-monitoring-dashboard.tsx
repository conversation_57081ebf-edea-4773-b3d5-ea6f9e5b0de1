/**
 * Dashboard Monitoring di Sistema per HACCP Tracker
 * 
 * @description Componente dashboard completo per monitoring:
 * - Health checks in tempo reale
 * - Metriche di performance
 * - Metriche business
 * - Alerting status
 * - Grafici e indicatori visivi
 * 
 * <AUTHOR> di Monitoring Dashboard HACCP Tracker
 * @version 1.0.0 - Dashboard monitoring avanzato
 * @date 2025-07-26
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Activity, 
  Database, 
  Server, 
  Cpu, 
  MemoryStick, 
  Globe, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  Container
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

// Interfacce TypeScript per i dati
interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: number;
  message?: string;
  lastCheck: string;
  details?: Record<string, any>;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    database: HealthCheck;
    external_apis: HealthCheck;
    storage: HealthCheck;
    memory: HealthCheck;
    cpu: HealthCheck;
  };
  performance: {
    avgResponseTime: number;
    requestsPerMinute: number;
    errorRate: number;
  };
}

interface BusinessMetrics {
  timestamp: string;
  metrics: {
    activeUsers: number;
    ddtProcessed: number;
    productLabelsCreated: number;
    containersManaged: number;
    errorCount: number;
    performanceScore: number;
  };
  trends: {
    userGrowth: number;
    processingEfficiency: number;
    systemStability: number;
  };
}

interface DashboardData {
  timestamp: string;
  system: {
    status: string;
    uptime: number;
    performance: {
      avgResponseTime: number;
      requestsPerMinute: number;
      errorRate: number;
    };
    checks: {
      database: string;
      apis: string;
      storage: string;
      memory: string;
    };
  };
  business: {
    activeUsers: number;
    dailyProcessing: {
      ddts: number;
      labels: number;
      containers: number;
    };
    trends: {
      userGrowth: number;
      processingEfficiency: number;
      systemStability: number;
    };
    performanceScore: number;
  };
  alerts: {
    active: number;
    lastHour: number;
  };
}

// Utility per formattare l'uptime
const formatUptime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
};

// Utility per colori status
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'healthy': return 'text-green-600';
    case 'degraded': return 'text-yellow-600';
    case 'unhealthy': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'healthy': return 'default';
    case 'degraded': return 'secondary';
    case 'unhealthy': return 'destructive';
    default: return 'outline';
  }
};

// Componente per icona di status
const StatusIcon: React.FC<{ status: string; className?: string }> = ({ status, className = "w-4 h-4" }) => {
  switch (status) {
    case 'healthy':
      return <CheckCircle className={`${className} text-green-600`} />;
    case 'degraded':
      return <AlertTriangle className={`${className} text-yellow-600`} />;
    case 'unhealthy':
      return <XCircle className={`${className} text-red-600`} />;
    default:
      return <Activity className={`${className} text-gray-600`} />;
  }
};

export const SystemMonitoringDashboard: React.FC = () => {
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 secondi
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Query per health check generale
  const { data: systemHealth, refetch: refetchHealth, isLoading: healthLoading } = useQuery<SystemHealth>({
    queryKey: ['/api/monitoring/health'],
    refetchInterval: autoRefresh ? refreshInterval : false,
    refetchOnWindowFocus: false,
  });

  // Query per dashboard data
  const { data: dashboardData, refetch: refetchDashboard, isLoading: dashboardLoading } = useQuery<DashboardData>({
    queryKey: ['/api/monitoring/metrics/dashboard'],
    refetchInterval: autoRefresh ? refreshInterval : false,
    refetchOnWindowFocus: false,
  });

  // Query per business metrics
  const { data: businessMetrics, refetch: refetchBusiness, isLoading: businessLoading } = useQuery<BusinessMetrics>({
    queryKey: ['/api/monitoring/metrics/business'],
    refetchInterval: autoRefresh ? refreshInterval * 2 : false, // Meno frequente
    refetchOnWindowFocus: false,
  });

  // Refresh manuale
  const handleManualRefresh = () => {
    refetchHealth();
    refetchDashboard();
    refetchBusiness();
  };

  // Gestione auto-refresh
  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  if (healthLoading || dashboardLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Caricamento metriche di sistema...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Monitoring di Sistema</h1>
          <p className="text-muted-foreground">
            Dashboard completo per monitoraggio HACCP Tracker
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleAutoRefresh}
            className={autoRefresh ? 'bg-green-50' : ''}
          >
            <Activity className="w-4 h-4 mr-2" />
            {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
          </Button>
          <Button variant="outline" size="sm" onClick={handleManualRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Aggiorna
          </Button>
        </div>
      </div>

      {/* Status Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {/* System Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sistema</CardTitle>
            <StatusIcon status={systemHealth?.status || 'unknown'} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusBadgeVariant(systemHealth?.status || 'unknown')}>
                {systemHealth?.status || 'unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Uptime: {systemHealth ? formatUptime(systemHealth.uptime) : 'N/A'}
            </p>
          </CardContent>
        </Card>

        {/* Database Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusBadgeVariant(systemHealth?.checks.database.status || 'unknown')}>
                {systemHealth?.checks.database.status || 'unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Latency: {systemHealth?.checks.database.latency || 'N/A'}ms
            </p>
          </CardContent>
        </Card>

        {/* APIs Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">APIs Esterne</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusBadgeVariant(systemHealth?.checks.external_apis.status || 'unknown')}>
                {systemHealth?.checks.external_apis.status || 'unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Claude/Gemini
            </p>
          </CardContent>
        </Card>

        {/* Memory Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memoria</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusBadgeVariant(systemHealth?.checks.memory.status || 'unknown')}>
                {systemHealth?.checks.memory.status || 'unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {systemHealth?.checks.memory.details?.heapUsed 
                ? `${Math.round(systemHealth.checks.memory.details.heapUsed / 1024 / 1024)}MB` 
                : 'N/A'}
            </p>
          </CardContent>
        </Card>

        {/* CPU Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusBadgeVariant(systemHealth?.checks.cpu.status || 'unknown')}>
                {systemHealth?.checks.cpu.status || 'unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Resp: {systemHealth?.checks.cpu.latency || 'N/A'}ms
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="w-5 h-5 mr-2" />
              Performance di Sistema
            </CardTitle>
            <CardDescription>
              Metriche di performance in tempo reale
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Tempo Risposta Medio</span>
                <span className="text-sm">{systemHealth?.performance.avgResponseTime || 0}ms</span>
              </div>
              <Progress 
                value={Math.min((systemHealth?.performance.avgResponseTime || 0) / 2, 100)} 
                className="w-full" 
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Richieste al Minuto</span>
                <span className="text-sm">{systemHealth?.performance.requestsPerMinute || 0}</span>
              </div>
              <Progress 
                value={Math.min((systemHealth?.performance.requestsPerMinute || 0) / 2, 100)} 
                className="w-full" 
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Tasso di Errore</span>
                <span className={`text-sm ${(systemHealth?.performance.errorRate || 0) > 5 ? 'text-red-600' : 'text-green-600'}`}>
                  {systemHealth?.performance.errorRate || 0}%
                </span>
              </div>
              <Progress 
                value={systemHealth?.performance.errorRate || 0} 
                className="w-full" 
              />
            </div>
          </CardContent>
        </Card>

        {/* Business Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Metriche Business
            </CardTitle>
            <CardDescription>
              KPIs e metriche di business (ultime 24h)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold">{businessMetrics?.metrics.activeUsers || dashboardData?.business.activeUsers || 0}</div>
                  <div className="text-xs text-muted-foreground">Utenti Attivi</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Package className="w-4 h-4 text-green-600" />
                <div>
                  <div className="text-2xl font-bold">{businessMetrics?.metrics.ddtProcessed || dashboardData?.business.dailyProcessing.ddts || 0}</div>
                  <div className="text-xs text-muted-foreground">DDT Processati</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Activity className="w-4 h-4 text-orange-600" />
                <div>
                  <div className="text-2xl font-bold">{businessMetrics?.metrics.productLabelsCreated || dashboardData?.business.dailyProcessing.labels || 0}</div>
                  <div className="text-xs text-muted-foreground">Etichette Create</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Container className="w-4 h-4 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold">{businessMetrics?.metrics.containersManaged || dashboardData?.business.dailyProcessing.containers || 0}</div>
                  <div className="text-xs text-muted-foreground">Contenitori Gestiti</div>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Performance Score</span>
                <span className="text-sm font-bold">{businessMetrics?.metrics.performanceScore || dashboardData?.business.performanceScore || 0}%</span>
              </div>
              <Progress 
                value={businessMetrics?.metrics.performanceScore || dashboardData?.business.performanceScore || 0} 
                className="w-full" 
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Details */}
      <Card>
        <CardHeader>
          <CardTitle>Dettagli di Sistema</CardTitle>
          <CardDescription>
            Informazioni dettagliate sui componenti del sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {systemHealth?.checks && Object.entries(systemHealth.checks).map(([component, check]) => (
              <div key={component} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium capitalize">{component.replace('_', ' ')}</h4>
                  <StatusIcon status={check.status} />
                </div>
                <p className="text-sm text-muted-foreground mb-2">{check.message}</p>
                <div className="text-xs text-muted-foreground">
                  Ultimo Check: {new Date(check.lastCheck).toLocaleTimeString('it-IT')}
                </div>
                {check.latency && (
                  <div className="text-xs text-muted-foreground">
                    Latency: {check.latency}ms
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Footer Info */}
      <div className="text-center text-sm text-muted-foreground">
        <p>
          Versione: {systemHealth?.version || 'N/A'} | 
          Ultimo aggiornamento: {systemHealth ? new Date(systemHealth.timestamp).toLocaleString('it-IT') : 'N/A'} |
          Auto-refresh: {autoRefresh ? `${refreshInterval/1000}s` : 'Disabilitato'}
        </p>
      </div>
    </div>
  );
};