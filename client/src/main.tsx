// Inizializza il performance blocker nucleare PRIMA di tutto
import "./lib/ultimate-performance-blocker";

import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { initPWA } from "./lib/pwaManager";
import { fixWebSocketForReplit } from "./lib/hmr-fix";
import { clientLogger } from "./lib/clientLogger";

// Environment detection
const isDevMode = window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' ||
                  window.location.hostname.includes('.replit.dev');

// Apply WebSocket fix for Replit environment
fixWebSocketForReplit();

// Performance measurement
const startTime = performance.now();

// Development mode indicator
clientLogger.dev('Application initializing', { mode: 'development' });

// Performance monitoring for optimization
if ('PerformanceObserver' in window) {
  const perfObserver = new PerformanceObserver((entryList) => {
    for (const entry of entryList.getEntries()) {
      if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
        clientLogger.performance('First Contentful Paint', Math.round(entry.startTime));
      }
    }
  });
  perfObserver.observe({ type: 'paint', buffered: true });
}

// Rendering immediato dell'applicazione
const root = createRoot(document.getElementById("root")!);
root.render(<App />);

// PWA initialization - production only
Promise.resolve().then(() => {
  if (isDevMode) {
    clientLogger.dev('Development mode: skipping PWA initialization');
    const loadTime = performance.now() - startTime;
    clientLogger.performance('App initialization', Math.round(loadTime));
    return;
  }
  
  // Production: initialize service worker
  if (window.requestIdleCallback) {
    window.requestIdleCallback(() => {
      setTimeout(() => {
        initPWA();
        const loadTime = performance.now() - startTime;
        clientLogger.performance('App initialization with PWA', Math.round(loadTime));
      }, 1500);
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      initPWA();
      const loadTime = performance.now() - startTime;
      clientLogger.performance('App initialization with PWA (fallback)', Math.round(loadTime));
    }, 1500);
  }
});
