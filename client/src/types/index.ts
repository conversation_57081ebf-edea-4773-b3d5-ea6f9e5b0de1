export interface User {
  id: number;
  username: string;
  isAdmin: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface Supplier {
  id: number;
  companyName: string;
  vatNumber: string;
  address: string;
}

export interface DDT {
  id: number;
  supplierId: number;
  companyName: string;
  vatNumber: string;
  address: string;
  number: string;
  date: string;
  createdAt: string;
  createdBy: number;
  image?: string;
}

export interface ProductLabel {
  id: number;
  ddtId: number;
  productName: string;
  expiryDate: string;
  batchNumber: string;
  storageInstructions: string;
  createdAt: string;
  createdBy: number;
  image?: string;
  qrCode?: string;
  isRetired: boolean;
  retiredAt?: string;
  retiredBy?: number;
  retiredReason?: string;
  notes?: string;
}

// Import Container type from schema to ensure consistency
export type { Container } from '../../../shared/schema';

export interface ActivityLog {
  id: number;
  userId: number;
  username: string;
  action: string;
  details: string;
  timestamp: string;
}

export interface DDTFormData {
  companyName: string;
  vatNumber: string;
  address: string;
  number: string;
  date: string;
  image?: string;
}

export interface ProductLabelFormData {
  productName: string;
  expiryDate: string;
  batchNumber: string;
  storageInstructions: string;
  notes?: string;
  qrCodeName?: string;
  image?: string;
}

export interface ContainerFormData {
  name: string;
  type: string;
  typeId?: number;
  maxItems: number;
  qrCode?: string;
}

export interface ContainerProduct {
  id: number;
  containerId: number;
  productLabelId: number;
  createdAt: string;
  createdBy: number;
}

export interface ContainerType {
  id: number;
  value: string;
  label: string;
  isActive: boolean;
  createdAt?: string;
}
