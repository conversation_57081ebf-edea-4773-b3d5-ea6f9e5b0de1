import { apiRequest } from "@/lib/queryClient";

export interface AIPrompt {
  id: string;
  name: string;
  content: string;
  description?: string;
  category: "ddt" | "label" | "general";
  isDefault?: boolean;
}

// Funzioni per interagire con l'API dei prompt
export const promptService = {
  // Recupera tutti i prompt
  getAllPrompts: async (): Promise<AIPrompt[]> => {
    return await apiRequest<AIPrompt[]>("/api/prompts", "GET");
  },
  
  // Recupera prompt per categoria
  getPromptsByCategory: async (category: string): Promise<AIPrompt[]> => {
    return await apiRequest<AIPrompt[]>(`/api/prompts/category/${category}`, "GET");
  },
  
  // Crea un nuovo prompt
  createPrompt: async (prompt: Omit<AIPrompt, "id">): Promise<AIPrompt> => {
    return await apiRequest<AIPrompt>("/api/prompts", "POST", prompt);
  },
  
  // Aggiorna un prompt esistente
  updatePrompt: async (id: string, prompt: Omit<AIPrompt, "id">): Promise<AIPrompt> => {
    return await apiRequest<AIPrompt>(`/api/prompts/${id}`, "PUT", { ...prompt, id });
  },
  
  // Elimina un prompt
  deletePrompt: async (id: string): Promise<{ success: boolean }> => {
    return await apiRequest<{ success: boolean }>(`/api/prompts/${id}`, "DELETE");
  },
  
  // Ripristina i prompt predefiniti
  resetDefaultPrompts: async (): Promise<{ success: boolean }> => {
    return await apiRequest<{ success: boolean }>("/api/prompts/reset", "POST");
  },
};
