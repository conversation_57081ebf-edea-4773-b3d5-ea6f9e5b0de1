/**
 * Funzioni di utilità per gestire il service worker e la PWA
 * NOTA: La registrazione del service worker è gestita da pwaManager.ts
 */

// Funzione per controllare gli aggiornamenti del service worker
export async function checkForServiceWorkerUpdates(): Promise<boolean> {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.update();
      if (registration.waiting) {
        // C'è un nuovo service worker in attesa di essere attivato
        console.log('Nuovo Service Worker disponibile');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Errore durante il controllo degli aggiornamenti del Service Worker:', error);
      return false;
    }
  }
  return false;
}

// Funzione per forzare l'aggiornamento del service worker
export function forceServiceWorkerUpdate(): Promise<void> {
  return new Promise((resolve, reject) => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready
        .then(registration => {
          if (registration.waiting) {
            // Invia un messaggio al service worker per attivare l'aggiornamento
            registration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            // Attendi che il nuovo service worker diventi attivo
            const handleControllerChange = () => {
              console.log('Nuovo Service Worker attivato');
              navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
              resolve();
            };
            
            navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
          } else {
            resolve();
          }
        })
        .catch(error => {
          console.error('Errore durante l\'aggiornamento forzato del Service Worker:', error);
          reject(error);
        });
    } else {
      resolve();
    }
  });
}

// Funzione per verificare se la PWA è installata
export function isPwaInstalled(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches || 
         ('standalone' in window.navigator && (window.navigator as any).standalone === true);
}

// Funzione per verificare se il browser supporta il Background Sync
export function supportsBackgroundSync(): boolean {
  return 'serviceWorker' in navigator && 
         'SyncManager' in window;
}

// Funzione per verificare se il browser supporta IndexedDB
export function supportsIndexedDB(): boolean {
  return 'indexedDB' in window;
}

// Funzione per verificare se supporta tutte le funzionalità offline
export function supportsOfflineFeatures(): boolean {
  return supportsIndexedDB() && 'caches' in window && 'serviceWorker' in navigator;
}

// Funzione per verificare se la modalità app è disponibile
export function canInstallPwa(): Promise<boolean> {
  return new Promise(resolve => {
    if (isPwaInstalled()) {
      resolve(false);
      return;
    }
    
    // Verifica se il browser supporta l'installazione della PWA
    const handleBeforeInstallPrompt = () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      resolve(true);
    };
    
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    
    // Timeout per rimuovere l'event listener se il prompt non viene attivato
    setTimeout(() => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      resolve(false);
    }, 3000);
  });
}

// Controlla se la PWA è stata avviata da un'app installata
export function isLaunchedFromInstalledApp(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches || 
         ('standalone' in window.navigator && (window.navigator as any).standalone === true);
}