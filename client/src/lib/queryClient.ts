import { QueryClient, QueryFunction } from "@tanstack/react-query";
import QueryInvalidationManager from "./queryInvalidation";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest<T>(
  urlOrMethod: string,
  methodOrUrlOrOptions?: string | RequestInit,
  body?: any
): Promise<T> {
  // Assicuriamoci che i parametri siano nel giusto ordine
  let url: string;
  let methodOrOptions: string | RequestInit = "GET";
  
  // Determina se il primo parametro è un URL o un metodo
  if (urlOrMethod.startsWith("/")) {
    // Se inizia con "/", è un URL
    url = urlOrMethod;
    if (methodOrUrlOrOptions !== undefined) {
      methodOrOptions = methodOrUrlOrOptions;
    }
  } else if (urlOrMethod.toUpperCase() === "GET" || 
             urlOrMethod.toUpperCase() === "POST" || 
             urlOrMethod.toUpperCase() === "PUT" || 
             urlOrMethod.toUpperCase() === "DELETE" || 
             urlOrMethod.toUpperCase() === "PATCH") {
    // Se è un metodo HTTP, allora il secondo parametro deve essere l'URL
    if (typeof methodOrUrlOrOptions !== "string" || !methodOrUrlOrOptions.startsWith("/")) {
      throw new Error("Se il primo parametro è un metodo HTTP, il secondo parametro deve essere un URL");
    }
    url = methodOrUrlOrOptions;
    methodOrOptions = urlOrMethod;
    // In questo caso, il body rimane lo stesso
  } else {
    // Se non è né un URL né un metodo HTTP riconosciuto, assumiamo che sia un URL
    url = urlOrMethod;
    if (methodOrUrlOrOptions !== undefined) {
      methodOrOptions = methodOrUrlOrOptions;
    }
  }
  
  // Determina il metodo e le opzioni
  let finalMethod: string;
  let requestOptions: RequestInit = {
    credentials: "include", // Importante per inviare i cookie di autenticazione
    headers: {
      'Accept': 'application/json',
    }
  };
  
  if (typeof methodOrOptions === 'string') {
    // Caso 1: Metodo passato come stringa
    finalMethod = methodOrOptions;
    requestOptions.method = finalMethod;
    
    // Aggiungiamo il body se fornito
    if (body && finalMethod !== 'GET' && finalMethod !== 'HEAD') {
      requestOptions.body = JSON.stringify(body);
      requestOptions.headers = {
        ...requestOptions.headers,
        'Content-Type': 'application/json'
      };
    }
  } else {
    // Caso 2: Opzioni passate come oggetto
    requestOptions = {
      ...requestOptions,
      ...methodOrOptions
    };
    finalMethod = requestOptions.method || 'GET';
  }
  
  console.log(`Making ${finalMethod} request to ${url}`, body ? { body } : '');
  
  // Aggiungi sistema di retry per gestire connessioni instabili
  const maxRetries = 2;
  let retries = 0;
  let lastError: Error | null = null;

  while (retries <= maxRetries) {
    try {
      const res = await fetch(url, requestOptions);

      // Controlla errori prima di processare
      await throwIfResNotOk(res);
      
      // Se è una risposta 204 No Content (per qualsiasi metodo HTTP)
      if (res.status === 204) {
        return {} as T;
      }

      // Gestisci Bad Request (400) in modo speciale
      if (res.status === 400) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Errore di validazione dati");
      }

      // Restituisci i dati
      return await res.json();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Se è un errore 400 (Bad Request), non ha senso riprovare
      if (lastError.message.includes("400:") || lastError.message.includes("Product is already")) {
        throw lastError;
      }
      
      if (retries < maxRetries) {
        retries++;
        console.log(`Riprovo richiesta a ${url} (tentativo ${retries}/${maxRetries})`);
        // Attendi con backoff esponenziale
        await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retries - 1)));
      } else {
        retries++;
      }
    }
  }
  
  // Se arriviamo qui, abbiamo esaurito i tentativi
  throw lastError || new Error("Richiesta fallita dopo vari tentativi");
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
      headers: {
        'Accept': 'application/json',
      },
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    
    // Se è una risposta 204 No Content (per qualsiasi metodo HTTP)
    if (res.status === 204) {
      return {} as any;
    }
    
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      staleTime: 30000, // 30 secondi - riduce chiamate API ridondanti
      gcTime: 5 * 60 * 1000, // 5 minuti - mantiene cache più a lungo
      refetchOnWindowFocus: false, // Evita refetch inutili
      refetchOnReconnect: false, // Evita refetch automatici
      refetchOnMount: false, // Evita re-fetch automatici al mount
      retry: 1, // Riduce tentativi di retry
      retryDelay: 500,
      networkMode: 'online', // Solo online, evita comportamenti strani offline
    },
    mutations: {
      retry: 1,
      networkMode: 'online',
    },
  },
});

// Inizializza il gestore intelligente delle invalidazioni
export const queryInvalidationManager = new QueryInvalidationManager(queryClient);
