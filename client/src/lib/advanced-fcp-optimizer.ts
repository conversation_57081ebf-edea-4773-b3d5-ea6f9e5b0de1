/**
 * Advanced FCP Optimizer
 * Target: FCP 7652ms → <3000ms (60% improvement needed)
 * Implements advanced critical rendering path optimizations
 */

import { clientLogger } from './clientLogger';

export class AdvancedFCPOptimizer {
  private static instance: AdvancedFCPOptimizer;
  private optimizationsApplied = new Set<string>();
  private fcpImprovement = 0;

  private constructor() {
    this.initializeAdvancedOptimizations();
  }

  public static getInstance(): AdvancedFCPOptimizer {
    if (!AdvancedFCPOptimizer.instance) {
      AdvancedFCPOptimizer.instance = new AdvancedFCPOptimizer();
    }
    return AdvancedFCPOptimizer.instance;
  }

  private initializeAdvancedOptimizations(): void {
    // Execute optimizations in order of impact
    this.inlineAboveTheFoldCSS();
    this.optimizeResourceLoading();
    this.implementResourceHints();
    this.optimizeFontLoading();
    this.removeRenderBlocking();
    
    clientLogger.info('AdvancedFCPOptimizer: All optimizations applied');
  }

  private inlineAboveTheFoldCSS(): void {
    if (this.optimizationsApplied.has('above-fold-css')) return;

    const aboveTheFoldCSS = `
      <style id="above-fold-critical">
        /* Critical path CSS - immediate rendering */
        html { 
          font-size: 16px; 
          line-height: 1.5; 
        }
        
        body { 
          margin: 0; 
          padding: 0; 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #f8fafc;
          color: #1f2937;
          overflow-x: hidden;
          -webkit-text-size-adjust: 100%;
          -webkit-tap-highlight-color: transparent;
        }
        
        /* Immediate layout containers */
        .app-root {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          will-change: auto;
        }
        
        /* Header - highest priority */
        .app-header {
          position: sticky;
          top: 0;
          z-index: 1000;
          background: #ffffff;
          border-bottom: 1px solid #e5e7eb;
          height: 60px;
          display: flex;
          align-items: center;
          padding: 0 1rem;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* Main content area */
        .app-main {
          flex: 1;
          padding: 1rem;
          padding-bottom: 80px;
          max-width: 100vw;
        }
        
        /* Bottom navigation */
        .app-nav {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          height: 64px;
          background: #ffffff;
          border-top: 1px solid #e5e7eb;
          display: flex;
          align-items: center;
          justify-content: space-around;
          z-index: 100;
          box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
        }
        
        /* Loading skeleton with optimized animation */
        .skeleton {
          background: #f3f4f6;
          background-image: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
          background-size: 200px 100%;
          background-repeat: no-repeat;
          animation: skeleton-loading 1.2s ease-in-out infinite;
        }
        
        @keyframes skeleton-loading {
          0% { background-position: -200px 0; }
          100% { background-position: calc(200px + 100%) 0; }
        }
        
        /* Critical button styles */
        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0.5rem 1rem;
          border: 1px solid transparent;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          line-height: 1.25rem;
          cursor: pointer;
          transition: none; /* Remove during critical path */
          white-space: nowrap;
        }
        
        .btn-primary {
          background: #3b82f6;
          color: #ffffff;
        }
        
        .btn-secondary {
          background: #6b7280;
          color: #ffffff;
        }
        
        /* Grid and layout utilities */
        .container {
          width: 100%;
          max-width: 1280px;
          margin: 0 auto;
          padding-left: 1rem;
          padding-right: 1rem;
        }
        
        .grid {
          display: grid;
          gap: 1rem;
        }
        
        .flex {
          display: flex;
        }
        
        .items-center {
          align-items: center;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        /* Typography */
        h1, h2, h3, h4, h5, h6 {
          margin: 0 0 0.5rem 0;
          font-weight: 600;
          line-height: 1.25;
        }
        
        h1 { font-size: 1.875rem; }
        h2 { font-size: 1.5rem; }
        h3 { font-size: 1.25rem; }
        
        p {
          margin: 0 0 1rem 0;
        }
        
        /* Form elements */
        input, select, textarea {
          appearance: none;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          padding: 0.5rem 0.75rem;
          font-size: 0.875rem;
          line-height: 1.25rem;
          background: #ffffff;
          width: 100%;
        }
        
        input:focus, select:focus, textarea:focus {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
          border-color: #3b82f6;
        }
        
        /* Mobile optimizations */
        @media (max-width: 768px) {
          .app-header {
            height: 56px;
            padding: 0 0.75rem;
          }
          
          .app-main {
            padding: 0.75rem;
            padding-bottom: 72px;
          }
          
          .app-nav {
            height: 60px;
          }
          
          .container {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
          }
        }
        
        /* Hide content until ready */
        .app-loading {
          opacity: 0;
        }
        
        .app-ready {
          opacity: 1;
          transition: opacity 0.2s ease-in-out;
        }
        
        /* Remove all non-essential visual effects during load */
        * {
          box-sizing: border-box;
        }
        
        *:before,
        *:after {
          box-sizing: border-box;
        }
        
        /* Replit preview fixes */
        .preview-container {
          transform: none !important;
          width: 100% !important;
          height: 100% !important;
          overflow: visible !important;
        }
      </style>
    `;

    // Inject immediately into head
    document.head.insertAdjacentHTML('afterbegin', aboveTheFoldCSS);
    
    this.optimizationsApplied.add('above-fold-css');
    clientLogger.debug('Above-the-fold CSS inlined');
  }

  private optimizeResourceLoading(): void {
    if (this.optimizationsApplied.has('resource-loading')) return;

    // Preload most critical resources with highest priority
    const criticalResources = [
      { href: '/api/auth/me', as: 'fetch', type: 'api' },
      { href: '/logo.png', as: 'image', type: 'image' },
      { href: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2', as: 'font', type: 'font' }
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      
      if (resource.type === 'font') {
        link.crossOrigin = 'anonymous';
      } else if (resource.type === 'api') {
        link.crossOrigin = 'same-origin';
      }
      
      // Insert with highest priority
      if (document.head.firstChild) {
        document.head.insertBefore(link, document.head.firstChild);
      } else {
        document.head.appendChild(link);
      }
    });

    this.optimizationsApplied.add('resource-loading');
    clientLogger.debug('Critical resource preloading optimized');
  }

  private implementResourceHints(): void {
    if (this.optimizationsApplied.has('resource-hints')) return;

    // DNS prefetch for external domains
    const domains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });

    // Preconnect to same-origin for API calls
    const preconnect = document.createElement('link');
    preconnect.rel = 'preconnect';
    preconnect.href = window.location.origin;
    document.head.appendChild(preconnect);

    this.optimizationsApplied.add('resource-hints');
    clientLogger.debug('Resource hints implemented');
  }

  private optimizeFontLoading(): void {
    if (this.optimizationsApplied.has('font-loading')) return;

    // Inject font-display: swap for all Google Fonts
    const fontCSS = `
      <style id="font-display-swap">
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        /* Fallback font stack */
        .font-fallback {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }
        
        /* Apply to body until Inter loads */
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }
        
        /* Switch to Inter when loaded */
        .fonts-loaded body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
      </style>
    `;

    document.head.insertAdjacentHTML('beforeend', fontCSS);

    // Font loading detection
    if ('FontFace' in window) {
      const inter400 = new FontFace('Inter', 'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2)', {
        weight: '400',
        display: 'swap'
      });

      inter400.load().then(() => {
        document.documentElement.classList.add('fonts-loaded');
        clientLogger.debug('Inter font loaded');
      }).catch(() => {
        clientLogger.debug('Inter font failed to load, using fallback');
      });
    }

    this.optimizationsApplied.add('font-loading');
    clientLogger.debug('Font loading optimized');
  }

  private removeRenderBlocking(): void {
    if (this.optimizationsApplied.has('render-blocking')) return;

    // Convert blocking stylesheets to non-blocking
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');
    stylesheets.forEach((link) => {
      const href = link.getAttribute('href');
      if (href && !href.includes('critical') && !href.includes('fonts.googleapis.com')) {
        // Make non-blocking
        link.setAttribute('media', 'print');
        link.addEventListener('load', () => {
          link.setAttribute('media', 'all');
        });
      }
    });

    // Defer non-critical JavaScript
    const scripts = document.querySelectorAll('script[src]:not([data-critical])');
    scripts.forEach((script) => {
      const src = script.getAttribute('src');
      if (src && (src.includes('admin') || src.includes('charts'))) {
        script.setAttribute('defer', 'true');
      }
    });

    this.optimizationsApplied.add('render-blocking');
    clientLogger.debug('Render-blocking resources optimized');
  }

  public getOptimizationStatus(): {
    applied: string[];
    fcpTarget: number;
    estimatedImprovement: string;
  } {
    return {
      applied: Array.from(this.optimizationsApplied),
      fcpTarget: 3000,
      estimatedImprovement: '40-60%'
    };
  }

  public measureFCPImprovement(): Promise<number> {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const fcpEntry = list.getEntries().find(entry => entry.name === 'first-contentful-paint');
          if (fcpEntry) {
            const currentFCP = fcpEntry.startTime;
            resolve(currentFCP);
            observer.disconnect();
          }
        });
        
        observer.observe({ entryTypes: ['paint'] });
        
        setTimeout(() => {
          observer.disconnect();
          resolve(0);
        }, 10000);
      } else {
        resolve(0);
      }
    });
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  AdvancedFCPOptimizer.getInstance();
}

export const advancedFCPOptimizer = AdvancedFCPOptimizer.getInstance();