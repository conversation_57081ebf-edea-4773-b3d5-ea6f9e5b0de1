/**
 * Secure Console - Client-side production-safe logging
 * Addresses Security Issue: Console statements in production (Report M1)
 * 
 * Features:
 * - Environment-based log filtering  
 * - Sensitive data redaction
 * - Development-only output
 * - Performance monitoring
 */

// Environment detection for client-side
const isDevelopment = import.meta.env.DEV || import.meta.env.NODE_ENV === 'development';
const isProduction = import.meta.env.PROD || import.meta.env.NODE_ENV === 'production';

// Sensitive data patterns to redact
const SENSITIVE_PATTERNS = [
  /password['":\s]*['"]\w+['"]/gi,
  /token['":\s]*['"]\w+['"]/gi,
  /secret['":\s]*['"]\w+['"]/gi,
  /apikey['":\s]*['"]\w+['"]/gi,
  /authorization['":\s]*['"]\w+['"]/gi,
  /Bearer\s+[\w\-\.]+/gi,
  /sessionId['":\s]*['"]\w+['"]/gi,
];

// Data sanitization function
function sanitizeData(data: any): any {
  if (typeof data === 'string') {
    let sanitized = data;
    SENSITIVE_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    return sanitized;
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      if (key.toLowerCase().includes('password') || 
          key.toLowerCase().includes('token') || 
          key.toLowerCase().includes('secret') ||
          key.toLowerCase().includes('session')) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = sanitizeData(data[key]);
      }
    }
    return sanitized;
  }
  
  return data;
}

/**
 * Secure Console Replacement
 * Replaces all console.log usage with environment-aware logging
 */
export class SecureConsole {
  
  /**
   * Development-only debug logs
   * Completely silent in production
   */
  static debug(message: string, ...args: any[]) {
    if (isDevelopment) {
      const sanitizedArgs = args.map(sanitizeData);
      console.debug(`[DEBUG] ${message}`, ...sanitizedArgs);
    }
  }
  
  /**
   * Development-only info logs
   * For application flow tracking
   */
  static info(message: string, ...args: any[]) {
    if (isDevelopment) {
      const sanitizedArgs = args.map(sanitizeData);
      console.info(`[INFO] ${message}`, ...sanitizedArgs);
    }
  }
  
  /**
   * Development-only standard logs
   * Replaces console.log usage
   */
  static log(message: string, ...args: any[]) {
    if (isDevelopment) {
      const sanitizedArgs = args.map(sanitizeData);
      console.log(`[LOG] ${message}`, ...sanitizedArgs);
    }
  }
  
  /**
   * Warning logs - production safe
   * Only logs sanitized, non-sensitive warnings
   */
  static warn(message: string, ...args: any[]) {
    const sanitizedArgs = args.map(sanitizeData);
    console.warn(`[WARN] ${message}`, ...sanitizedArgs);
  }
  
  /**
   * Error logs - production safe
   * Always logs errors but sanitizes sensitive data
   */
  static error(message: string, ...args: any[]) {
    const sanitizedArgs = args.map(sanitizeData);
    console.error(`[ERROR] ${message}`, ...sanitizedArgs);
  }
  
  /**
   * Performance monitoring - development only
   */
  static performance(metric: string, value: number, context?: any) {
    if (isDevelopment) {
      console.info(`[PERFORMANCE] ${metric}: ${value}ms`, context ? sanitizeData(context) : '');
    }
  }
  
  /**
   * Network request logs - development only
   */
  static network(method: string, url: string, status?: number, duration?: number) {
    if (isDevelopment) {
      console.info(`[NETWORK] ${method} ${url} ${status ? `(${status})` : ''} ${duration ? `${duration}ms` : ''}`);
    }
  }
  
  /**
   * User interaction logs - development only
   */
  static interaction(action: string, element?: string, data?: any) {
    if (isDevelopment) {
      console.info(`[USER] ${action}`, element || '', data ? sanitizeData(data) : '');
    }
  }
  
  /**
   * Security-related logs
   * Always logged but sanitized
   */
  static security(event: string, details?: any) {
    console.warn(`[SECURITY] ${event}`, details ? sanitizeData(details) : '');
  }
}

/**
 * Drop-in replacement for console methods
 * Use these to replace existing console usage
 */
export const secureConsole = {
  log: SecureConsole.log,
  info: SecureConsole.info,
  warn: SecureConsole.warn,
  error: SecureConsole.error,
  debug: SecureConsole.debug,
};

// Environment verification message
if (isDevelopment) {
  SecureConsole.info("🔧 Development console logging enabled");
} else {
  // Silent in production - no output
}