
/**
 * Utility per garantire una navigazione SPA fluida senza ricaricamenti
 * Versione ottimizzata con transizioni ultra-fluide e preloading intelligente
 */

export const SPANavigation = {
  /**
   * Navigazione ottimizzata con prevenzione ricaricamenti e transizioni ultra-fluide
   */
  navigate: (setLocation: (path: string) => void, path: string) => (e?: React.MouseEvent | Event) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Prevenire doppi click e navigazioni ridondanti
    if (window.location.pathname === path) {
      return;
    }
    
    // Feedback aptico immediato su dispositivi mobili
    if ('vibrate' in navigator) {
      navigator.vibrate([5]);
    }
    
    // Precaricamento anticipato del componente
    SPANavigation.preloadRoute(path);
    
    // Aggiorna immediatamente la URL per esperienza nativa
    window.history.pushState(null, '', path);
    
    // Transizione ultra-fluida con spring animation
    const startTime = performance.now();
    const animateTransition = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / 120, 1); // 120ms per transizione fluida
      
      // Easing spring per sensazione nativa
      const easeOutBack = 1 - Math.pow(1 - progress, 3);
      document.documentElement.style.setProperty('--transition-progress', easeOutBack.toString());
      
      if (progress < 1) {
        requestAnimationFrame(animateTransition);
      } else {
        // Completa la navigazione
        setLocation(path);
        document.documentElement.style.removeProperty('--transition-progress');
      }
    };
    
    requestAnimationFrame(animateTransition);
  },

  /**
   * Precaricamento intelligente delle rotte con priorità dinamica
   */
  preloadRoute: (path: string) => {
    const routeMap: { [key: string]: () => Promise<unknown> } = {
      '/containers': () => import('@/pages/containers'),
      '/incoming-goods': () => import('@/pages/incoming-goods'),
      '/qr-scanner': () => import('@/pages/qr-scanner'),
      '/search': () => import('@/pages/search'),
      '/settings': () => import('@/pages/settings'),
      '/profile': () => import('@/pages/profile'),
      '/activities': () => import('@/pages/activities'),
      '/suppliers': () => import('@/pages/suppliers'),
      '/container-types': () => import('@/pages/container-types'),
    };

    if (routeMap[path]) {
      // Precarica con priorità alta per navigazione immediata
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.as = 'script';
      document.head.appendChild(link);
      
      routeMap[path]()
        .then(() => {
          console.log(`[SPANav] ✅ Precaricato: ${path}`);
          document.head.removeChild(link);
        })
        .catch(() => {
          // Rimuovi link anche in caso di errore
          if (document.head.contains(link)) {
            document.head.removeChild(link);
          }
        });
    }
  },

  /**
   * Prevenzione ricaricamenti accidentali
   */
  preventReload: (e: Event) => {
    if (e.type === 'beforeunload') {
      // Solo in casi critici
      return;
    }
    e.preventDefault();
  },

  /**
   * Gestione link esterni
   */
  handleExternalLink: (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  },

  /**
   * Gesture swipe avanzata per navigazione native-like iOS/Android
   */
  setupSwipeNavigation: (setLocation: (path: string) => void) => {
    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;
    let startTime = 0;
    let isSwipeGestureActive = false;

    const handleTouchStart = (e: TouchEvent) => {
      // Solo se il touch inizia dal bordo sinistro dello schermo
      if (e.changedTouches[0].clientX < 20) {
        startX = e.changedTouches[0].clientX;
        startY = e.changedTouches[0].clientY;
        startTime = Date.now();
        isSwipeGestureActive = true;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isSwipeGestureActive) return;
      
      const currentX = e.changedTouches[0].clientX;
      const diffX = currentX - startX;
      
      // Mostra indicatore visivo di swipe back se il gesto è valido
      if (diffX > 10 && diffX < 150) {
        document.documentElement.style.setProperty('--swipe-progress', (diffX / 150).toString());
        // Feedback aptico leggero durante il gesto
        if (diffX > 30 && 'vibrate' in navigator) {
          navigator.vibrate(1);
        }
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (!isSwipeGestureActive) return;
      
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;
      const endTime = Date.now();
      
      const diffX = endX - startX;
      const diffY = Math.abs(endY - startY);
      const duration = endTime - startTime;
      const velocity = Math.abs(diffX) / duration; // px/ms
      
      // Reset visual indicator
      document.documentElement.style.removeProperty('--swipe-progress');
      
      // Gesture valido: swipe orizzontale, velocità adeguata, distanza minima
      if (diffX > 80 && diffY < 100 && velocity > 0.3 && duration < 300) {
        // Swipe verso destra - torna indietro (iOS-like)
        SPANavigation.handleSwipeBack();
      }
      
      isSwipeGestureActive = false;
    };

    // Event listeners con opzioni ottimizzate
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  },

  /**
   * Gestione swipe indietro (iOS-like)
   */
  handleSwipeBack: () => {
    if (window.history.length > 1) {
      window.history.back();
    }
  },

  /**
   * Gestione swipe avanti con route prediction
   */
  handleSwipeForward: (setLocation: (path: string) => void) => {
    const currentPath = window.location.pathname;
    const commonRoutes = ['/', '/containers', '/incoming-goods', '/qr-scanner'];
    const currentIndex = commonRoutes.indexOf(currentPath);
    
    if (currentIndex !== -1 && currentIndex < commonRoutes.length - 1) {
      const nextRoute = commonRoutes[currentIndex + 1];
      SPANavigation.navigate(setLocation, nextRoute)();
    }
  },

  /**
   * Transizioni ultra-fluide con spring physics avanzata
   */
  addSpringTransitions: () => {
    const style = document.createElement('style');
    style.textContent = `
      /* Transizioni SPA ultra-fluide */
      .spa-transition {
        transition: transform 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                   opacity 0.15s ease-out;
        will-change: transform, opacity;
      }
      
      /* Animazioni pagina con spring physics */
      .spa-page-enter {
        transform: translateX(100%) translateZ(0);
        opacity: 0;
      }
      .spa-page-enter-active {
        transform: translateX(0) translateZ(0);
        opacity: 1;
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                   opacity 0.2s ease-out;
      }
      .spa-page-exit {
        transform: translateX(0) translateZ(0);
        opacity: 1;
      }
      .spa-page-exit-active {
        transform: translateX(-30%) translateZ(0);
        opacity: 0.8;
        transition: transform 0.25s ease-out,
                   opacity 0.15s ease-out;
      }
      
      /* Indicatore visivo swipe back */
      .swipe-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 4px;
        height: 100vh;
        background: linear-gradient(to bottom, transparent, var(--primary), transparent);
        opacity: var(--swipe-progress, 0);
        transform: translateX(-2px);
        transition: opacity 0.1s ease-out;
        z-index: 9999;
        pointer-events: none;
      }
      
      /* Ottimizzazioni touch per iOS */
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      input, textarea {
        -webkit-user-select: text;
        user-select: text;
      }
      
      /* Accelerazione hardware per smooth scrolling */
      .scroll-optimized {
        -webkit-overflow-scrolling: touch;
        transform: translateZ(0);
        will-change: scroll-position;
      }
      
      /* Transizione globale fluida */
      html {
        --transition-progress: 0;
      }
      
      body {
        transform: scale(calc(1 - var(--transition-progress, 0) * 0.02));
        filter: brightness(calc(1 - var(--transition-progress, 0) * 0.1));
        transition: transform 0.1s ease-out, filter 0.1s ease-out;
      }
    `;
    document.head.appendChild(style);
    
    // Aggiungi indicatore swipe
    const swipeIndicator = document.createElement('div');
    swipeIndicator.className = 'swipe-indicator';
    document.body.appendChild(swipeIndicator);
  }
};

// Hook per navigation SPA con gesture support
export const useSPANavigation = () => {
  const [, setLocation] = useLocation();
  
  useEffect(() => {
    // Setup gesture navigation
    const cleanup = SPANavigation.setupSwipeNavigation(setLocation);
    SPANavigation.addSpringTransitions();
    
    return cleanup;
  }, [setLocation]);
  
  return {
    navigate: (path: string) => SPANavigation.navigate(setLocation, path),
    preload: SPANavigation.preloadRoute,
    swipeBack: SPANavigation.handleSwipeBack,
    swipeForward: () => { SPANavigation.handleSwipeForward(setLocation); },
  };
};

// Import necessario
import { useEffect } from 'react';
import { useLocation } from 'wouter';
