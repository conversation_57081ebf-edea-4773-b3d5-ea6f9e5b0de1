import { normalizeDateToStandard } from './dateNormalization';
import { expirationMemoizer, formatDate as memoizedFormatDate } from './memoizedDateUtils';

/**
 * Utility per la gestione e la validazione delle date con memoizzazione ottimizzata
 */

/**
 * Verifica se una data di scadenza è già passata (prodotto scaduto)
 * Utilizza il sistema di memoizzazione per prevenire calcoli ridondanti
 * @param dateStr La data di scadenza in formato stringa (DD/MM/YYYY)
 * @returns true se il prodotto è scaduto, false se è ancora valido
 */
export function isExpired(dateStr: string | null | undefined): boolean {
  return expirationMemoizer.isExpired(dateStr);
}

/**
 * Formatta una data nel formato italiano (DD/MM/YYYY)
 * @param date L'oggetto data da formattare
 * @returns La data formattata come stringa
 */
export function formatDate(date: Date): string {
  return memoizedFormatDate(date);
}