/**
 * Utility per debouncing e throttling delle operazioni per ottimizzare performance
 */

// Cache per i timer di debounce
const debounceTimers = new Map<string, NodeJS.Timeout>();

/**
 * Debounce una funzione con chiave univoca
 * @param key Chiave univoca per identificare la funzione
 * @param fn Funzione da eseguire dopo il debounce
 * @param delay Ritardo in millisecondi
 */
export function debounce<T extends (...args: any[]) => any>(
  key: string,
  fn: T,
  delay: number = 300
): void {
  // Cancella il timer precedente se esiste
  const existingTimer = debounceTimers.get(key);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }

  // Imposta un nuovo timer
  const newTimer = setTimeout(() => {
    fn();
    debounceTimers.delete(key);
  }, delay);

  debounceTimers.set(key, newTimer);
}

/**
 * Throttle per limitare la frequenza di esecuzione di una funzione
 * @param key Chiave univoca per identificare la funzione
 * @param fn Funzione da eseguire
 * @param limit Limite di tempo in millisecondi
 */
const throttleTimers = new Map<string, number>();

export function throttle<T extends (...args: any[]) => any>(
  key: string,
  fn: T,
  limit: number = 1000
): boolean {
  const now = Date.now();
  const lastRun = throttleTimers.get(key) || 0;

  if (now - lastRun >= limit) {
    fn();
    throttleTimers.set(key, now);
    return true;
  }
  
  return false;
}

/**
 * Pulisce tutti i timer attivi
 */
export function clearAllTimers(): void {
  debounceTimers.forEach(timer => clearTimeout(timer));
  debounceTimers.clear();
  throttleTimers.clear();
}

/**
 * Debounced console.log per evitare spam nei log
 */
export function debouncedLog(key: string, message: string, ...args: any[]): void {
  if (process.env.NODE_ENV === 'development') {
    debounce(`log_${key}`, () => {
      console.log(message, ...args);
    }, 500);
  }
}