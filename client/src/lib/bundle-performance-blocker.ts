/**
 * Bundle Performance Blocker
 * Blocca specificamente i bundle JavaScript pesanti durante il caricamento critico
 * Approccio: Intercetta le richieste dei moduli a livello di Vite/browser
 */

class BundlePerformanceBlocker {
  private static instance: BundlePerformanceBlocker;
  private criticalLoadComplete = false;
  private startTime = performance.now();
  private blockedBundles: string[] = [];

  // Bundle da bloccare assolutamente durante FCP
  private readonly CRITICAL_BLOCKING_BUNDLES = [
    'charts.js',
    'admin.js',
    'recharts',
    'framer-motion'
  ];

  constructor() {
    this.initializeBundleBlocking();
    console.log('[BUNDLE-BLOCKER] Bundle blocking initialized');
  }

  public static getInstance(): BundlePerformanceBlocker {
    if (!BundlePerformanceBlocker.instance) {
      BundlePerformanceBlocker.instance = new BundlePerformanceBlocker();
    }
    return BundlePerformanceBlocker.instance;
  }

  private initializeBundleBlocking(): void {
    // Intercetta le richieste di moduli prima che vengano caricate
    this.interceptModuleRequests();
    
    // Setup timer per sbloccare dopo 2 secondi
    setTimeout(() => {
      this.completeCriticalLoad();
    }, 2000);

    // Monitora marker DOM per completamento anticipato
    this.monitorDOMMarkers();
  }

  private interceptModuleRequests(): void {
    // Override del dynamic import se disponibile
    if (typeof window !== 'undefined' && 'import' in window) {
      const originalImport = window.import;
      
      // Non possiamo override import direttamente, ma possiamo intercettare fetch
      // che è usato da Vite per caricare i moduli
      this.interceptViteFetch();
    }

    // Intercetta anche richieste dirette di script
    this.interceptScriptLoading();
  }

  private interceptViteFetch(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      // Blocca bundle critici durante caricamento iniziale
      if (!this.criticalLoadComplete && this.shouldBlockBundle(url)) {
        console.log(`[BUNDLE-BLOCKER] BLOCKED BUNDLE: ${url}`);
        this.blockedBundles.push(url);
        
        // Ritorna una response vuota per evitare errori
        return new Response('', {
          status: 200,
          headers: { 'Content-Type': 'application/javascript' }
        });
      }
      
      // Passa al fetch originale
      return originalFetch.call(window, input, init);
    };
  }

  private interceptScriptLoading(): void {
    // Override createElement per intercettare tag script
    const originalCreateElement = document.createElement;
    const blocker = this;
    
    document.createElement = function(tagName: string) {
      const element = originalCreateElement.call(this, tagName);
      
      if (tagName.toLowerCase() === 'script' && element instanceof HTMLScriptElement) {
        const originalSrc = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src');
        
        Object.defineProperty(element, 'src', {
          get: function() {
            return originalSrc?.get?.call(this) || '';
          },
          set: function(value: string) {
            if (!blocker.criticalLoadComplete && blocker.shouldBlockBundle(value)) {
              console.log(`[BUNDLE-BLOCKER] DELAYED SCRIPT: ${value}`);
              blocker.blockedBundles.push(value);
              
              // Ritarda il caricamento
              setTimeout(() => {
                if (originalSrc?.set) {
                  originalSrc.set.call(this, value);
                }
              }, 3000); // 3 secondi di ritardo
              return;
            }
            
            if (originalSrc?.set) {
              originalSrc.set.call(this, value);
            }
          },
          configurable: true
        });
      }
      
      return element;
    };
  }

  private shouldBlockBundle(url: string): boolean {
    return this.CRITICAL_BLOCKING_BUNDLES.some(bundle => 
      url.includes(bundle) || url.includes(`/assets/${bundle}`)
    );
  }

  private monitorDOMMarkers(): void {
    const checkInterval = setInterval(() => {
      const authComplete = document.querySelector('[data-auth="complete"]');
      const uiReady = document.querySelector('[data-ui="ready"]');
      
      if (authComplete || uiReady) {
        clearInterval(checkInterval);
        this.completeCriticalLoad();
      }
    }, 100);
  }

  private completeCriticalLoad(): void {
    if (this.criticalLoadComplete) return;
    
    this.criticalLoadComplete = true;
    const duration = performance.now() - this.startTime;
    
    console.log(`[BUNDLE-BLOCKER] Critical load complete after ${duration.toFixed(0)}ms`);
    console.log(`[BUNDLE-BLOCKER] Blocked ${this.blockedBundles.length} bundles:`, this.blockedBundles);
    
    // Ripristina fetch originale
    this.restoreOriginalFunctions();
    
    // Carica i bundle bloccati gradualmente
    this.loadBlockedBundles();
  }

  private restoreOriginalFunctions(): void {
    // Il fetch è già stato sostituito in modo sicuro, non serve ripristinarlo
    // I bundle verranno caricati naturalmente quando richiesti
  }

  private loadBlockedBundles(): void {
    // I bundle verranno caricati automaticamente quando l'applicazione li richiederà
    // dopo che il critical path è completo
    console.log('[BUNDLE-BLOCKER] Bundle loading now permitted');
  }

  public getStats(): any {
    return {
      criticalLoadComplete: this.criticalLoadComplete,
      blockedBundleCount: this.blockedBundles.length,
      blockedBundles: this.blockedBundles,
      duration: this.criticalLoadComplete 
        ? `${(performance.now() - this.startTime).toFixed(0)}ms`
        : 'In progress'
    };
  }
}

// Inizializza immediatamente
const bundleBlocker = BundlePerformanceBlocker.getInstance();

export { bundleBlocker as bundlePerformanceBlocker };