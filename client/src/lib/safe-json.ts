/**
 * Utilità per JSON parsing sicuro lato client
 * Previene crash dell'applicazione con input malformato
 */

export interface SafeJsonResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Parse sicuro di JSON con error handling
 */
export function safeJsonParse<T = any>(jsonString: string, fallback?: T): SafeJsonResult<T> {
  try {
    if (!jsonString || typeof jsonString !== 'string') {
      return {
        success: false,
        error: 'Input non valido: deve essere una stringa non vuota',
        data: fallback
      };
    }

    const trimmed = jsonString.trim();
    if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) {
      return {
        success: false,
        error: 'Input non è un JSON valido',
        data: fallback
      };
    }

    const parsed = JSON.parse(trimmed);
    return {
      success: true,
      data: parsed
    };
  } catch (error) {
    console.error('JSON parsing error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Errore parsing JSON',
      data: fallback
    };
  }
}

/**
 * Parse sicuro con fallback automatico
 */
export function safeJsonParseWithFallback<T>(jsonString: string, fallback: T): T {
  const result = safeJsonParse<T>(jsonString, fallback);
  return result.data ?? fallback;
}

/**
 * Parse sicuro per localStorage/sessionStorage
 */
export function safeStorageParse<T>(key: string, fallback: T, storage: Storage = localStorage): T {
  try {
    const item = storage.getItem(key);
    if (!item) return fallback;
    
    const result = safeJsonParse<T>(item, fallback);
    return result.data ?? fallback;
  } catch (error) {
    console.error(`Error parsing storage item ${key}:`, error);
    return fallback;
  }
}

/**
 * Stringify sicuro per storage
 */
export function safeStorageStringify(key: string, value: any, storage: Storage = localStorage): boolean {
  try {
    const stringified = JSON.stringify(value);
    storage.setItem(key, stringified);
    return true;
  } catch (error) {
    console.error(`Error stringifying storage item ${key}:`, error);
    return false;
  }
}