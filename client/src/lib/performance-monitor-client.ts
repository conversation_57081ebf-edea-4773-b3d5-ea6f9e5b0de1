/**
 * Client-Side Performance Monitor
 * Integra con il sistema di ottimizzazione server per migliorare FCP
 * Implementa critical CSS inlining e resource preloading
 */

import { clientLogger } from './clientLogger';

interface PerformanceMetrics {
  fcp: number;
  lcp: number;
  cls: number;
  fid: number;
  ttfb: number;
  loadComplete: number;
}

interface ResourceTiming {
  name: string;
  duration: number;
  size?: number;
  type: 'script' | 'style' | 'image' | 'api' | 'font';
}

export class ClientPerformanceMonitor {
  private static instance: ClientPerformanceMonitor;
  private observer: PerformanceObserver | null = null;
  private metrics: Partial<PerformanceMetrics> = {};
  private resourceTimings: ResourceTiming[] = [];
  private startTime = performance.now();

  private constructor() {
    this.initializeMonitoring();
    this.setupCriticalResourcePreloading();
    clientLogger.debug('ClientPerformanceMonitor initialized');
  }

  public static getInstance(): ClientPerformanceMonitor {
    if (!ClientPerformanceMonitor.instance) {
      ClientPerformanceMonitor.instance = new ClientPerformanceMonitor();
    }
    return ClientPerformanceMonitor.instance;
  }

  /**
   * Inizializza monitoring Web Vitals
   */
  private initializeMonitoring(): void {
    if ('PerformanceObserver' in window) {
      // Monitora FCP, LCP, CLS
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      try {
        this.observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift', 'navigation'] });
      } catch (error) {
        clientLogger.warn('Performance monitoring not fully supported', { error });
      }
    }

    // Monitora risorse critiche
    this.monitorResourceLoading();
    
    // Monitora FID manualmente
    this.monitorFirstInputDelay();
  }

  /**
   * Processa entry performance
   */
  private processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.metrics.fcp = entry.startTime;
          clientLogger.debug(`Performance: First Contentful Paint took ${Math.round(entry.startTime)}ms`);
          
          // Alert se sopra target - Solo debug in development
          if (entry.startTime > 3000) {
            clientLogger.debug(`FCP above target: ${Math.round(entry.startTime)}ms (target: <3000ms) - Development mode, safe to ignore`);
          }
        }
        break;

      case 'largest-contentful-paint':
        this.metrics.lcp = entry.startTime;
        clientLogger.debug(`Performance: Largest Contentful Paint took ${Math.round(entry.startTime)}ms`);
        break;

      case 'layout-shift':
        if (!(entry as any).hadRecentInput) {
          this.metrics.cls = (this.metrics.cls || 0) + (entry as any).value;
        }
        break;

      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
        this.metrics.loadComplete = navEntry.loadEventEnd - navEntry.fetchStart;
        break;
    }
  }

  /**
   * Monitora caricamento risorse
   */
  private monitorResourceLoading(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const resource = entry as PerformanceResourceTiming;
        
        this.resourceTimings.push({
          name: resource.name,
          duration: resource.duration,
          size: resource.transferSize,
          type: this.getResourceType(resource.name)
        });

        // Log risorse lente - Solo debug in development
        if (resource.duration > 1000) {
          clientLogger.debug(`Slow resource loading: ${resource.name} took ${Math.round(resource.duration)}ms - Development mode, safe to ignore`);
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['resource'] });
    } catch (error) {
      clientLogger.warn('Resource monitoring not supported');
    }
  }

  /**
   * Determina tipo risorsa
   */
  private getResourceType(url: string): ResourceTiming['type'] {
    if (url.includes('/api/')) return 'api';
    if (url.match(/\.(js|mjs|jsx|ts|tsx)$/)) return 'script';
    if (url.match(/\.(css|scss|sass)$/)) return 'style';
    if (url.match(/\.(jpg|jpeg|png|gif|svg|webp)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    return 'script';
  }

  /**
   * Monitora First Input Delay
   */
  private monitorFirstInputDelay(): void {
    let firstInputProcessed = false;

    const processFirstInput = (event: Event) => {
      if (firstInputProcessed) return;
      firstInputProcessed = true;

      const processingStart = performance.now();
      
      // Use requestIdleCallback with fallback for browser compatibility
      const idleCallback = window.requestIdleCallback || ((callback: () => void) => {
        setTimeout(callback, 1);
      });
      
      idleCallback(() => {
        this.metrics.fid = processingStart - event.timeStamp;
        clientLogger.debug(`Performance: First Input Delay was ${Math.round(this.metrics.fid)}ms`);
      });
    };

    ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
      document.addEventListener(type, processFirstInput, { once: true, passive: true });
    });
  }

  /**
   * Setup preloading risorse critiche
   */
  private setupCriticalResourcePreloading(): void {
    // Preload API endpoints critici
    const criticalEndpoints = [
      '/api/auth/me',
      '/api/containers',
      '/api/product-labels'
    ];

    criticalEndpoints.forEach(endpoint => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = endpoint;
      link.as = 'fetch';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });

    // Preconnect domini esterni
    const externalDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    externalDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });

    clientLogger.debug('Critical resource preloading setup completed');
  }

  /**
   * Applica critical CSS per FCP veloce
   */
  public applyCriticalCSS(): void {
    const criticalCSS = `
      /* Critical above-the-fold CSS */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f8fafc;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e2e8f0;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Header critico */
      .header-container {
        position: sticky;
        top: 0;
        z-index: 1000;
        background: white;
        border-bottom: 1px solid #e2e8f0;
        height: 64px;
      }
      
      /* Navigation container */
      .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e2e8f0;
        height: 60px;
        z-index: 999;
      }
      
      /* Prevenire layout shift */
      .main-content {
        padding-top: 64px;
        padding-bottom: 60px;
        min-height: 100vh;
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);

    clientLogger.debug('Critical CSS applied for improved FCP');
  }

  /**
   * Ottimizza caricamento font
   */
  public optimizeFontLoading(): void {
    // Font display swap per evitare FOIT
    const fontLink = document.createElement('link');
    fontLink.rel = 'preload';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
    fontLink.as = 'style';
    fontLink.onload = () => {
      fontLink.rel = 'stylesheet';
    };
    document.head.appendChild(fontLink);

    clientLogger.debug('Font loading optimized with display:swap');
  }

  /**
   * Implementa lazy loading intelligente per immagini
   */
  public setupIntelligentImageLoading(): void {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px'
      });

      // Osserva tutte le immagini lazy
      document.querySelectorAll('img.lazy').forEach(img => {
        imageObserver.observe(img);
      });

      clientLogger.debug('Intelligent image lazy loading initialized');
    }
  }

  /**
   * Ottimizza bundle loading con code splitting
   */
  public optimizeBundleLoading(): void {
    // Preload chunk critici
    const criticalChunks = [
      '/assets/vendor.js',
      '/assets/ui.js'
    ];

    criticalChunks.forEach(chunk => {
      const link = document.createElement('link');
      link.rel = 'modulepreload';
      link.href = chunk;
      document.head.appendChild(link);
    });

    // Lazy load chunk non critici
    const deferredChunks = [
      '/assets/charts.js',
      '/assets/admin.js'
    ];

    // Use requestIdleCallback with fallback for browsers that don't support it
    const idleCallback = window.requestIdleCallback || ((callback: () => void) => {
      setTimeout(callback, 1);
    });
    
    idleCallback(() => {
      deferredChunks.forEach(chunk => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = chunk;
        document.head.appendChild(link);
      });
    });

    clientLogger.debug('Bundle loading optimization applied');
  }

  /**
   * Invia metriche al server per monitoring
   */
  public reportMetrics(): void {
    // Attendi che tutte le metriche siano disponibili
    setTimeout(() => {
      const completeMetrics = {
        ...this.metrics,
        userAgent: navigator.userAgent,
        connection: (navigator as any).connection?.effectiveType,
        timestamp: Date.now(),
        pageLoadTime: performance.now() - this.startTime
      };

      // Invia al server (non bloccante)
      fetch('/api/performance/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(completeMetrics)
      }).catch(error => {
        clientLogger.debug('Failed to send performance metrics', { error });
      });

      clientLogger.info('Performance metrics reported', completeMetrics);
    }, 5000);
  }

  /**
   * Ottimizzazioni immediate per FCP
   */
  public applyImmediateOptimizations(): void {
    this.applyCriticalCSS();
    this.optimizeFontLoading();
    this.setupIntelligentImageLoading();
    this.optimizeBundleLoading();
    
    // Report metriche dopo caricamento
    this.reportMetrics();

    clientLogger.info('Immediate performance optimizations applied');
  }

  /**
   * Statistiche performance correnti
   */
  public getPerformanceStats(): {
    fcp: number;
    lcp: number;
    cls: number;
    fid: number;
    slowResources: ResourceTiming[];
    recommendations: string[];
  } {
    const slowResources = this.resourceTimings.filter(r => r.duration > 1000);
    const recommendations: string[] = [];

    if ((this.metrics.fcp || 0) > 3000) {
      recommendations.push('Implement critical CSS inlining');
      recommendations.push('Optimize largest resources');
    }

    if ((this.metrics.lcp || 0) > 2500) {
      recommendations.push('Optimize largest contentful paint element');
    }

    if ((this.metrics.cls || 0) > 0.1) {
      recommendations.push('Reduce cumulative layout shift');
    }

    return {
      fcp: Math.round(this.metrics.fcp || 0),
      lcp: Math.round(this.metrics.lcp || 0),
      cls: Math.round((this.metrics.cls || 0) * 1000) / 1000,
      fid: Math.round(this.metrics.fid || 0),
      slowResources: slowResources.slice(0, 5), // Top 5 worst
      recommendations
    };
  }
}

// Export singleton
export const clientPerformanceMonitor = ClientPerformanceMonitor.getInstance();