import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { formatDateForDisplay } from './dateNormalization';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(dateInput: string | Date): string {
  try {
    // Se è già una data, formatta direttamente
    if (dateInput instanceof Date) {
      if (isNaN(dateInput.getTime())) {
        return 'Data non valida';
      }
      return format(dateInput, "dd/MM/yyyy");
    }
    
    // Se è una stringa, usa la normalizzazione standard
    if (typeof dateInput === 'string') {
      // Se è stringa vuota o null
      if (!dateInput || dateInput.trim() === '') {
        return 'Data non valida';
      }
      
      // Usa la funzione di normalizzazione per convertire al formato standard
      return formatDateForDisplay(dateInput);
    }
    
    return 'Data non valida';
  } catch (error) {
    console.error("Error formatting date:", error);
    return typeof dateInput === 'string' ? dateInput : 'Data non valida';
  }
}

export function formatTimestamp(dateInput: string | Date): string {
  try {
    // Se è già una data, formatta direttamente
    if (dateInput instanceof Date) {
      return format(dateInput, "dd/MM/yyyy HH:mm");
    }
    
    // Se è una stringa, verifica se è già nel formato corretto
    if (typeof dateInput === 'string') {
      // Check if the date is already in the format dd/MM/yy HH:mm or dd/MM/yyyy HH:mm
      if (/^\d{1,2}\/\d{1,2}\/\d{2,4} \d{1,2}:\d{1,2}$/.test(dateInput)) {
        return dateInput; // Already in the correct format
      }
      
      // Handle standard date formats
      const date = new Date(dateInput);
      return format(date, "dd/MM/yyyy HH:mm");
    }
    
    return 'Data non valida';
  } catch (error) {
    console.error("Error formatting timestamp:", error);
    return typeof dateInput === 'string' ? dateInput : 'Data non valida';
  }
}

/**
 * Questa funzione è obsoleta. Usa le funzioni in qr-utils.ts per la gestione dei QR code.
 * @deprecated
 */
export function generateQRCodeUrl(data: string): string {
  console.warn('generateQRCodeUrl è obsoleta. Usa le funzioni in qr-utils.ts per la gestione dei QR code.');
  const encoded = encodeURIComponent(data);
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encoded}`;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/**
 * Converte un file a base64
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // Remove data:image/jpeg;base64, prefix
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = (error) => reject(error);
  });
}

/**
 * Download un file blob con un nome specifico
 */
export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Converte un array di byte in blob
 */
export function bytesToBlob(bytes: Uint8Array, mimeType: string): Blob {
  return new Blob([bytes], { type: mimeType });
}

/**
 * Formatta un numero come valuta euro
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
}

/**
 * Formatta una percentuale
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Capitalizza la prima lettera di ogni parola
 */
export function capitalizeWords(str: string): string {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

/**
 * Genera un ID casuale
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Genera un ID unico con timestamp e numero casuale
 */
export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now().toString();
  const randomNumber = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${timestamp}${randomNumber}`;
}

/**
 * Genera il valore QR per un container
 */
export function generateContainerQRValue(container: any): string {
  return `container:${container.id}:${container.name.replace(/\s+/g, '_')}`;
}

/**
 * Stampa un QR code
 */
export const printQRCode = async (elementId?: string, title?: string, subtitle?: string, qrValue?: string): Promise<boolean> => {
  try {
    if (!qrValue) {
      console.error('QR value is required for printing');
      return false;
    }
    
    // Crea una nuova finestra per la stampa
    const printWindow = window.open('', '_blank', 'width=400,height=400');
    if (!printWindow) {
      console.error('Impossibile aprire la finestra di stampa');
      return false;
    }
    
    // Scrivi il contenuto HTML per la stampa
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Stampa QR Code</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              font-family: Arial, sans-serif;
            }
            .qr-container {
              text-align: center;
            }
            img {
              max-width: 200px;
              height: auto;
            }
            .title {
              font-size: 16px;
              font-weight: bold;
              margin-top: 10px;
            }
            .subtitle {
              font-size: 12px;
              margin-top: 5px;
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div id="qrcode"></div>
            ${title ? `<div class="title">${title}</div>` : ''}
            ${subtitle ? `<div class="subtitle">${subtitle}</div>` : ''}
          </div>
          <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
          <script>
            QRCode.toDataURL('${qrValue}', { width: 200 }, function (err, url) {
              if (err) console.error(err);
              const img = document.createElement('img');
              img.src = url;
              document.getElementById('qrcode').appendChild(img);
              
              setTimeout(function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 500);
            });
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    return true;
  } catch (error) {
    console.error('Errore durante la stampa del QR code:', error);
    return false;
  }
};

/**
 * Scarica un QR code come immagine
 */
export const downloadQRCode = async (elementId: string, filename: string, containerName: string): Promise<boolean> => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      console.error('Elemento QR code non trovato:', elementId);
      return false;
    }
    
    // Usa html2canvas per convertire l'elemento in immagine
    const html2canvas = (await import('html2canvas')).default;
    const canvas = await html2canvas(element);
    
    // Crea il link per il download
    const link = document.createElement('a');
    link.download = `${filename}_QR_${new Date().toISOString().split('T')[0]}.png`;
    link.href = canvas.toDataURL('image/png');
    
    // Simula il click per avviare il download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    return true;
  } catch (error) {
    console.error('Errore durante il download del QR code:', error);
    return false;
  }
};

/**
 * Controlla se una stringa è un URL valido
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  waitFor: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>): void => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), waitFor);
  };
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>): void => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}