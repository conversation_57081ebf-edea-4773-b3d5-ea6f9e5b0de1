/**
 * Sistema di lazy loading avanzato con precaricamento intelligente
 * Obiettivo: -60% initial load time, caricamento ottimizzato basato su priorità
 */

import { lazy, ComponentType } from 'react';
import { unifiedCache } from './unifiedCache';
import { logger } from './logger';

// Priorità di caricamento per i componenti
export enum LoadPriority {
  CRITICAL = 0,     // Caricamento immediato
  HIGH = 1,         // Precaricamento rapido
  MEDIUM = 2,       // Precaricamento su richiesta
  LOW = 3,          // Caricamento on-demand
  BACKGROUND = 4    // Caricamento in background
}

// Configurazione lazy loading per ogni route
export const ROUTE_LOAD_CONFIG: Record<string, { priority: LoadPriority; preload: boolean }> = {
  // Critiche - caricamento immediato
  '/': { priority: LoadPriority.CRITICAL, preload: true },
  '/login': { priority: LoadPriority.CRITICAL, preload: true },
  
  // Frequenti - precaricamento rapido
  '/search': { priority: LoadPriority.HIGH, preload: true },
  '/containers': { priority: LoadPriority.HIGH, preload: true },
  '/incoming-goods': { priority: LoadPriority.HIGH, preload: true },
  
  // Moderate - precaricamento su richiesta
  '/settings': { priority: LoadPriority.MEDIUM, preload: false },
  '/profile': { priority: LoadPriority.MEDIUM, preload: false },
  '/suppliers': { priority: LoadPriority.MEDIUM, preload: false },
  
  // Basse - on-demand
  '/users': { priority: LoadPriority.LOW, preload: false },
  '/activities': { priority: LoadPriority.LOW, preload: false },
  '/admin-dashboard': { priority: LoadPriority.LOW, preload: false },
  
  // Background - caricamento differito
  '/test-page': { priority: LoadPriority.BACKGROUND, preload: false },
  '/debug': { priority: LoadPriority.BACKGROUND, preload: false },
  '/diagnostics': { priority: LoadPriority.BACKGROUND, preload: false }
};

// Cache per i componenti caricati
const componentCache = new Map<string, ComponentType<any>>();

// Stato del precaricamento
const preloadState = new Map<string, Promise<ComponentType<any>>>();

/**
 * Lazy loader ottimizzato con cache e precaricamento
 */
export function createOptimizedLazy<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  routePath: string
): React.LazyExoticComponent<T> {
  const config = ROUTE_LOAD_CONFIG[routePath as keyof typeof ROUTE_LOAD_CONFIG] || { priority: LoadPriority.MEDIUM, preload: false };
  
  // Se è già in cache, restituisci immediamente
  if (componentCache.has(routePath)) {
    const cachedComponent = componentCache.get(routePath)! as T;
    return lazy(() => Promise.resolve({ default: cachedComponent }));
  }

  // Crea il componente lazy con ottimizzazioni
  const lazyComponent = lazy(async () => {
    try {
      logger.log(`[LazyLoad] Caricamento ${routePath} (priorità: ${LoadPriority[config.priority]})`);
      
      const start = performance.now();
      const module = await importFn();
      const loadTime = performance.now() - start;
      
      // Cache il componente per riutilizzo futuro
      componentCache.set(routePath, module.default);
      
      // Traccia performance
      if (loadTime > 100) {
        logger.log(`[LazyLoad] ⚠️ Caricamento lento per ${routePath}: ${loadTime.toFixed(2)}ms`);
      } else {
        logger.log(`[LazyLoad] ✅ ${routePath} caricato in ${loadTime.toFixed(2)}ms`);
      }
      
          // Salva statistiche nella cache unificata
      const stats = unifiedCache.get('lazy_load_stats') || {};
      (stats as any)[routePath] = { loadTime, timestamp: Date.now(), priority: config.priority };
      unifiedCache.set('lazy_load_stats', stats, 'SETTINGS');
      
      return module;
    } catch (error) {
      logger.log(`[LazyLoad] ❌ Errore caricamento ${routePath}:`, error);
      throw error;
    }
  });

  return lazyComponent;
}

/**
 * Precarica componenti basato sulla priorità
 */
export async function preloadComponents(): Promise<void> {
  const routes = Object.entries(ROUTE_LOAD_CONFIG)
    .filter(([_, config]) => config.preload && config.priority <= LoadPriority.HIGH)
    .sort(([_, a], [__, b]) => a.priority - b.priority);

  logger.log(`[LazyLoad] Precaricamento ${routes.length} componenti ad alta priorità`);

  for (const [route, config] of routes) {
    if (componentCache.has(route) || preloadState.has(route)) {
      continue; // Già caricato o in caricamento
    }

    try {
      // Determina la funzione di import basata sulla route
      const importFn = getImportFunction(route);
      if (!importFn) continue;

      // Avvia precaricamento con delay basato su priorità
      const delay = config.priority * 50; // 0ms, 50ms, 100ms, etc.
      
      const preloadPromise = new Promise<ComponentType<any>>(resolve => {
        setTimeout(async () => {
          try {
            const module = await importFn();
            componentCache.set(route, module.default);
            resolve(module.default);
            logger.log(`[LazyLoad] 🚀 Precaricato: ${route}`);
          } catch (error) {
            logger.log(`[LazyLoad] ⚠️ Errore precaricamento ${route}:`, error);
            resolve(null as any);
          }
        }, delay);
      });

      preloadState.set(route, preloadPromise);
    } catch (error) {
      logger.log(`[LazyLoad] Errore setup precaricamento per ${route}:`, error);
    }
  }
}

/**
 * Precarica componenti in background basato sull'utilizzo
 */
export function preloadBackgroundComponents(): void {
  // Precarica solo se l'utente è inattivo per 2 secondi
  let inactivityTimer: NodeJS.Timeout;
  
  const resetTimer = () => {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(() => {
      const backgroundRoutes = Object.entries(ROUTE_LOAD_CONFIG)
        .filter(([_, config]) => config.priority >= LoadPriority.LOW)
        .slice(0, 3); // Massimo 3 componenti in background
      
      backgroundRoutes.forEach(([route]) => {
        if (!componentCache.has(route) && !preloadState.has(route)) {
          const importFn = getImportFunction(route);
          if (importFn) {
            importFn().then(module => {
              componentCache.set(route, module.default);
              logger.log(`[LazyLoad] 🌙 Precaricato in background: ${route}`);
            }).catch(() => {});
          }
        }
      });
    }, 2000);
  };

  // Setup listeners per inattività
  ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
    document.addEventListener(event, resetTimer, { passive: true });
  });

  resetTimer();
}

/**
 * Ottiene la funzione di import per una route specifica
 */
function getImportFunction(route: string): (() => Promise<{ default: ComponentType<any> }>) | null {
  const imports: Record<string, () => Promise<{ default: ComponentType<any> }>> = {
    '/search': () => import('@/pages/search'),
    '/containers': () => import('@/pages/containers'),
    '/incoming-goods': () => import('@/pages/incoming-goods'),
    '/settings': () => import('@/pages/settings'),
    '/profile': () => import('@/pages/profile'),
    '/suppliers': () => import('@/pages/suppliers'),
    '/users': () => import('@/pages/users'),
    '/activities': () => import('@/pages/activities'),
    '/admin-dashboard': () => import('@/pages/admin-dashboard'),
    '/security-admin': () => import('@/pages/security-admin'),
    '/test-page': () => import('@/pages/test-page'),
    '/debug': () => import('@/pages/debug'),
    '/diagnostics': () => import('@/pages/diagnostics'),
    '/ddt-processing': () => import('@/pages/ddt-processing'),
    '/product-label': () => import('@/pages/product-label'),
    '/new-container': () => import('@/pages/new-container'),
    '/container-types': () => import('@/pages/container-types'),
    '/prompts': () => import('@/pages/prompts'),
    '/retired-products': () => import('@/pages/retired-products'),
    '/qr-scanner': () => import('@/pages/qr-scanner')
  };

  return imports[route] || null;
}

/**
 * Hook per monitorare l'utilizzo delle route
 */
export function trackRouteUsage(route: string): void {
  const stats = unifiedCache.get('route_usage_stats') || {};
  const routeStats = (stats as any)[route] || { visits: 0, lastVisit: 0 };
  
  routeStats.visits += 1;
  routeStats.lastVisit = Date.now();
  (stats as any)[route] = routeStats;
  
  unifiedCache.set('route_usage_stats', stats, 'SETTINGS');
  
  // Aggiorna priorità di precaricamento basato sull'utilizzo
  if (routeStats.visits >= 3) {
    const config = ROUTE_LOAD_CONFIG[route as keyof typeof ROUTE_LOAD_CONFIG];
    if (config && config.priority > LoadPriority.HIGH) {
      config.priority = LoadPriority.HIGH;
      config.preload = true;
      logger.log(`[LazyLoad] 📈 Aggiornata priorità per ${route} a HIGH (${routeStats.visits} visite)`);
    }
  }
}

/**
 * Pulisci componenti inutilizzati dalla cache
 */
export function cleanupUnusedComponents(): void {
  const stats = unifiedCache.get('route_usage_stats') || {};
  const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
  
  let cleaned = 0;
  for (const [route] of Array.from(componentCache.entries())) {
    const routeStats = (stats as any)[route];
    if (!routeStats || routeStats.lastVisit < oneWeekAgo) {
      componentCache.delete(route);
      preloadState.delete(route);
      cleaned++;
    }
  }
  
  if (cleaned > 0) {
    logger.log(`[LazyLoad] 🧹 Rimossi ${cleaned} componenti inutilizzati dalla cache`);
  }
}

/**
 * Ottieni statistiche lazy loading
 */
export function getLazyLoadStats() {
  const loadStats = unifiedCache.get('lazy_load_stats') || {};
  const usageStats = unifiedCache.get('route_usage_stats') || {};
  
  return {
    totalCachedComponents: componentCache.size,
    preloadingComponents: preloadState.size,
    loadStats,
    usageStats,
    cacheHitRate: Object.keys(loadStats).length > 0 
      ? (componentCache.size / Object.keys(loadStats).length) * 100 
      : 0
  };
}

// Inizializzazione automatica
if (typeof window !== 'undefined') {
  // Precarica componenti critici dopo 100ms
  setTimeout(preloadComponents, 100);
  
  // Avvia precaricamento in background dopo 2 secondi
  setTimeout(preloadBackgroundComponents, 2000);
  
  // Cleanup ogni ora
  setInterval(cleanupUnusedComponents, 60 * 60 * 1000);
  
  logger.log('[LazyLoad] Sistema lazy loading avanzato inizializzato');
}