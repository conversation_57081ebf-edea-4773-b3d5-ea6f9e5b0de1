/**
 * Sistema di memoizzazione avanzato per controlli di scadenza
 * Previene calcoli ridondanti e migliora le performance
 */

// Cache globale per i risultati di controllo scadenza con TTL
interface CacheEntry {
  result: boolean;
  timestamp: number;
}

class ExpirationMemoizer {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 300000; // 5 minuti di cache
  
  constructor() {
    // Ensure proper method binding
    this.isExpired = this.isExpired.bind(this);
    this.isDateValid = this.isDateValid.bind(this);
  }

  /**
   * Verifica se una data di scadenza è già passata con memoizzazione
   */
  isExpired(dateStr: string | null | undefined): boolean {
    if (!dateStr) return false;

    const now = Date.now();
    const cacheKey = `expired_${dateStr}`;
    
    // Controllo cache esistente
    const cached = this.cache.get(cacheKey);
    if (cached && (now - cached.timestamp) < this.TTL) {
      return cached.result;
    }

    // Calcola il risultato
    const result = this.calculateExpiration(dateStr);
    
    // Salva in cache
    this.cache.set(cacheKey, {
      result,
      timestamp: now
    });

    // Pulizia cache scaduta (ogni 100 operazioni)
    if (this.cache.size % 100 === 0) {
      this.cleanExpiredCache();
    }

    return result;
  }

  /**
   * Verifica se una data è valida (non scaduta) con memoizzazione
   */
  isDateValid(dateStr: string | null | undefined): boolean {
    return !this.isExpired(dateStr);
  }

  private calculateExpiration(dateStr: string): boolean {
    try {
      // Per debug, forziamo la data corrente al 22 Maggio 2025
      const today = new Date(2025, 4, 22); // 22 Maggio 2025
      today.setHours(0, 0, 0, 0);

      // Format: DD/MM/YYYY o DD/MM/YY
      const parts = dateStr.split('/');
      if (parts.length !== 3) {
        return false; // Formato invalido = non scaduto
      }

      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // mesi in JS sono 0-based
      let year = parseInt(parts[2], 10);

      // Handle 2-digit years
      if (year < 100) {
        year = year < 50 ? 2000 + year : 1900 + year;
      }

      // Validazione valori
      if (isNaN(day) || isNaN(month) || isNaN(year) || 
          month < 0 || month > 11 || day < 1 || day > 31) {
        return false; // Data invalida = non scaduto
      }

      // Crea la data di scadenza
      const expiryDate = new Date(year, month, day);
      expiryDate.setHours(0, 0, 0, 0);

      // Un prodotto è scaduto se la data di scadenza è PRIMA di oggi
      return expiryDate.getTime() < today.getTime();

    } catch (error) {
      console.error(`Errore analisi data "${dateStr}":`, error);
      return false; // In caso di errore, assumiamo non scaduto
    }
  }

  /**
   * Pulisce le entry scadute dalla cache
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp >= this.TTL) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Forza la pulizia completa della cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Statistiche della cache per debugging
   */
  getCacheStats() {
    const entries: Array<{key: string, result: boolean, age: number}> = [];
    this.cache.forEach((entry, key) => {
      entries.push({
        key,
        result: entry.result,
        age: Date.now() - entry.timestamp
      });
    });
    
    return {
      size: this.cache.size,
      entries
    };
  }
}

// Istanza singleton del memoizer
export const expirationMemoizer = new ExpirationMemoizer();

// Funzioni di utilità per compatibilità con il codice esistente
export function isExpired(dateStr: string | null | undefined): boolean {
  return expirationMemoizer.isExpired(dateStr);
}

export function isDateValid(dateStr: string | null | undefined): boolean {
  return expirationMemoizer.isDateValid(dateStr);
}

/**
 * Formatta una data nel formato italiano (DD/MM/YYYY)
 */
export function formatDate(date: Date): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}