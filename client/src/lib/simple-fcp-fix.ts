/**
 * Simple FCP Fix
 * Direct implementation to improve FCP from 8388ms to target <3000ms
 * Focuses on immediate critical path optimization
 */

// Inject ultra-minimal CSS immediately (blocking execution)
const minimalCSS = `
  *{box-sizing:border-box}
  html,body{margin:0;padding:0;font:16px/1.5 -apple-system,system-ui,sans-serif;background:#f8fafc}
  .app{min-height:100vh;display:flex;flex-direction:column}
  .header{position:sticky;top:0;background:#fff;height:60px;border-bottom:1px solid #e5e7eb;z-index:100}
  .main{flex:1;padding:1rem;padding-bottom:80px}
  .nav{position:fixed;bottom:0;left:0;right:0;height:64px;background:#fff;border-top:1px solid #e5e7eb;z-index:50}
  .loading{background:linear-gradient(90deg,#f3f4f6,#e5e7eb,#f3f4f6);background-size:200px 100%;animation:load 1.2s infinite}
  @keyframes load{0%{background-position:-200px 0}100%{background-position:200px 0}}
`;

// Create and inject style immediately
const style = document.createElement('style');
style.id = 'simple-fcp-fix';
style.innerHTML = minimalCSS;

// Insert as first element for highest priority
if (document.head.firstChild) {
  document.head.insertBefore(style, document.head.firstChild);
} else {
  document.head.appendChild(style);
}

// Hide content until critical CSS is loaded
document.documentElement.style.visibility = 'hidden';

// Show content immediately after CSS injection
setTimeout(() => {
  document.documentElement.style.visibility = 'visible';
}, 0);

// Preload critical resources immediately
const criticalResources = ['/api/auth/me', '/api/containers', '/api/product-labels'];
criticalResources.forEach(resource => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = resource;
  link.as = 'fetch';
  link.crossOrigin = 'same-origin';
  document.head.appendChild(link);
});

// Block heavy resources during initial load
const blockHeavyResources = () => {
  const scripts = document.querySelectorAll('script[src*="charts"], script[src*="admin"]');
  scripts.forEach(script => {
    script.setAttribute('defer', 'true');
    script.setAttribute('data-blocked', 'true');
  });
};

// Apply blocking immediately if DOM is ready, otherwise on DOMContentLoaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', blockHeavyResources);
} else {
  blockHeavyResources();
}

// Initialize final performance optimizer
setTimeout(() => {
  try {
    import('./final-performance-optimizer').then(({ finalPerformanceOptimizer }) => {
      finalPerformanceOptimizer.measureFinalImpact().then((impact) => {
        console.log(`[SIMPLE-FCP-FIX] Final optimizer applied: ${impact.fcpImprovement.toFixed(1)}% improvement`);
        
        // Apply emergency mode if FCP still too high
        if (impact.fcpImprovement < 50) {
          setTimeout(() => {
            try {
              finalPerformanceOptimizer.emergencyMode();
            } catch (error) {
              console.warn('[SIMPLE-FCP-FIX] Emergency mode failed:', error);
            }
          }, 100);
        }
      });
    }).catch(error => {
      console.warn('[SIMPLE-FCP-FIX] Final optimizer failed to load:', error);
    });
  } catch (e) {
    console.warn('[SIMPLE-FCP-FIX] Final optimizer import failed');
  }
}, 2000);

console.log('[SIMPLE-FCP-FIX] Applied immediate FCP optimizations with advanced optimizers');

export {}; // Make this a module