/**
 * Questo file risolve i problemi di connessione WebSocket in ambienti Replit
 * Sistemando gli errori 502 Bad Gateway che si verificano durante lo sviluppo
 */

// Funzione per correggere le connessioni WebSocket in Replit
export function fixWebSocketForReplit() {
  // Esegui solo in ambiente di sviluppo
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.log('[HMR] Applying WebSocket connection fix for Replit environment');

  // Intercetta la creazione di WebSocket per correggere gli URL
  const originalWebSocket = window.WebSocket;
  
  // @ts-ignore - Sovrascriviamo temporaneamente il costruttore di WebSocket
  window.WebSocket = function(url: string, protocols?: string | string[]) {
    // Correggi URL WebSocket malformati mantenendo il protocollo sicuro appropriato
    let fixedUrl = url;
    
    try {
      // Gestione più robusta degli URL WebSocket problematici in Replit
      const urlObj = new URL(url);
      
      // Risolvi errori comuni in ambienti Replit
      if (url.includes('token=')) {
        // Gli URL problematici possono includere: localhost, undefined, hostname non corretto
        if (url.includes('wss://localhost') || 
            url.includes('undefined') || 
            (url.includes('wss://') && !url.includes(window.location.hostname))) {
          
          // Estrai il token dall'URL originale
          const tokenMatch = url.match(/token=([^&]+)/);
          const token = tokenMatch ? tokenMatch[1] : '';
          
          // Costruisci un URL WebSocket sicuro basato sul protocollo della pagina
          const wsHost = window.location.hostname;
          const wsPort = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
          const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          fixedUrl = `${wsProtocol}//${wsHost}:${wsPort}/?token=${token}`;
          
          console.log(`[HMR] WebSocket URL corrected: ${url} → ${fixedUrl}`);
        }
      }
    } catch (e) {
      // Se l'URL non è valido, prova a correggerlo
      console.error('[HMR] URL WebSocket non valido:', url);
      
      // Estrai il token se esiste
      const tokenMatch = url.match(/token=([^&]+)/);
      if (tokenMatch) {
        const token = tokenMatch[1];
        const wsHost = window.location.hostname;
        const wsPort = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        fixedUrl = `${wsProtocol}//${wsHost}:${wsPort}/?token=${token}`;
        console.log(`[HMR] Tentativo di correzzione WebSocket URL: ${fixedUrl}`);
      }
    }
    
    // Implementa una logica di retry per gestire connessioni fallite
    let ws: WebSocket;
    
    try {
      // Crea il WebSocket con l'URL corretto
      ws = new originalWebSocket(fixedUrl, protocols);
    } catch (error) {
      console.error('[HMR] Errore nella creazione del WebSocket:', error);
      
      // Nessun fallback insicuro - mantieni la sicurezza del protocollo
      console.warn('[HMR] WebSocket connection failed, no insecure fallback allowed for security');
      throw error; // Rilancia l'errore per mantenere la sicurezza
    }
    
    return ws;
  };
  
  // Mantieni le proprietà statiche
  window.WebSocket.prototype = originalWebSocket.prototype;
  // Copia le proprietà utili dal costruttore originale
  Object.defineProperties(window.WebSocket, Object.getOwnPropertyDescriptors(originalWebSocket));
}