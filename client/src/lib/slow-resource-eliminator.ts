/**
 * Slow Resource Eliminator
 * Target: Eliminate all 1000ms+ resource loading times
 * Ultra-aggressive optimization for slow APIs and bundles
 */

interface ResourceMetrics {
  url: string;
  loadTime: number;
  retryCount: number;
  optimized: boolean;
}

export class SlowResourceEliminator {
  private static instance: SlowResourceEliminator;
  private slowResources = new Set<string>();
  private resourceMetrics = new Map<string, ResourceMetrics>();
  private preloadedResources = new Set<string>();

  private constructor() {
    this.initializeSlowResourceElimination();
  }

  public static getInstance(): SlowResourceEliminator {
    if (!SlowResourceEliminator.instance) {
      SlowResourceEliminator.instance = new SlowResourceEliminator();
    }
    return SlowResourceEliminator.instance;
  }

  private initializeSlowResourceElimination(): void {
    // Identify and preload slow resources
    this.identifySlowResources();
    
    // Implement ultra-aggressive preloading
    this.implementUltraAggressivePreloading();
    
    // Setup resource interception and optimization
    this.setupResourceInterception();
    
    // Monitor and auto-fix slow resources
    this.monitorAndAutoFix();
    
    console.log('[SLOW-RESOURCE-ELIMINATOR] Ultra-aggressive optimization initialized');
  }

  private identifySlowResources(): void {
    // Known slow resources from logs
    const knownSlowResources = [
      '/api/product-labels',
      '/api/containers', 
      '/api/auth/me',
      '/assets/charts.js',
      '/assets/admin.js'
    ];

    knownSlowResources.forEach(resource => {
      this.slowResources.add(resource);
      this.resourceMetrics.set(resource, {
        url: resource,
        loadTime: 1500, // Assume worst case
        retryCount: 0,
        optimized: false
      });
    });
  }

  private implementUltraAggressivePreloading(): void {
    // Preload critical APIs immediately
    const criticalAPIs = [
      '/api/auth/me',
      '/api/containers',
      '/api/product-labels'
    ];

    criticalAPIs.forEach(api => {
      this.preloadResource(api);
    });

    // Preload scripts with ultra-high priority
    this.preloadScripts();
  }

  private preloadResource(url: string): void {
    if (this.preloadedResources.has(url)) return;

    // Create ultra-high priority fetch
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 2000); // 2s timeout

    fetch(url, {
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'max-age=3600', // 1 hour cache
        'Pragma': 'cache'
      },
      signal: controller.signal,
      priority: 'high' as any // Chrome supports this
    })
    .then(response => {
      clearTimeout(timeoutId);
      if (response.ok) {
        this.preloadedResources.add(url);
        console.log(`[SLOW-RESOURCE-ELIMINATOR] Successfully preloaded: ${url}`);
      }
    })
    .catch(error => {
      clearTimeout(timeoutId);
      if (error.name !== 'AbortError') {
        console.warn(`[SLOW-RESOURCE-ELIMINATOR] Failed to preload ${url}:`, error.message);
      }
    });
  }

  private preloadScripts(): void {
    const scripts = [
      '/assets/charts.js',
      '/assets/admin.js'
    ];

    scripts.forEach(script => {
      // Use link rel=preload for scripts
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = script;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
      
      console.log(`[SLOW-RESOURCE-ELIMINATOR] Preloading script: ${script}`);
    });
  }

  private setupResourceInterception(): void {
    // Intercept and optimize slow resources
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString();
      const startTime = performance.now();
      
      // Apply optimization to known slow resources
      if (this.isSlowResource(url)) {
        init = this.optimizeRequestInit(init);
      }
      
      try {
        const response = await originalFetch(input, init);
        const loadTime = performance.now() - startTime;
        
        // Track and warn about slow resources
        if (loadTime > 1000) {
          console.warn(`[SLOW-RESOURCE-ELIMINATOR] Slow resource: ${url} took ${loadTime.toFixed(0)}ms`);
          this.recordSlowResource(url, loadTime);
        }
        
        return response;
      } catch (error) {
        const loadTime = performance.now() - startTime;
        console.error(`[SLOW-RESOURCE-ELIMINATOR] Failed resource: ${url} after ${loadTime.toFixed(0)}ms`, error);
        throw error;
      }
    };
  }

  private isSlowResource(url: string): boolean {
    return Array.from(this.slowResources).some(slowUrl => url.includes(slowUrl));
  }

  private optimizeRequestInit(init?: RequestInit): RequestInit {
    return {
      ...init,
      credentials: 'include',
      cache: 'force-cache', // Aggressive caching
      headers: {
        ...init?.headers,
        'Accept': 'application/json',
        'Cache-Control': 'max-age=3600, must-revalidate',
        'Pragma': 'cache'
      }
    };
  }

  private recordSlowResource(url: string, loadTime: number): void {
    const existing = this.resourceMetrics.get(url);
    if (existing) {
      existing.loadTime = Math.max(existing.loadTime, loadTime);
      existing.retryCount++;
    } else {
      this.resourceMetrics.set(url, {
        url,
        loadTime,
        retryCount: 1,
        optimized: false
      });
    }
    
    this.slowResources.add(url);
  }

  private monitorAndAutoFix(): void {
    // Auto-fix slow resources every 10 seconds
    setInterval(() => {
      this.autoFixSlowResources();
    }, 10000);
  }

  private autoFixSlowResources(): void {
    this.resourceMetrics.forEach((metrics, url) => {
      if (metrics.loadTime > 1000 && !metrics.optimized && metrics.retryCount > 2) {
        console.log(`[SLOW-RESOURCE-ELIMINATOR] Auto-fixing slow resource: ${url}`);
        
        // Apply emergency optimization
        if (url.includes('/api/')) {
          // Preload API endpoint
          this.preloadResource(url);
        }
        
        metrics.optimized = true;
      }
    });
  }

  public getSlowResourceStats(): any {
    return {
      slowResourceCount: this.slowResources.size,
      preloadedCount: this.preloadedResources.size,
      metrics: Array.from(this.resourceMetrics.values())
    };
  }
}

// Initialize immediately
export const slowResourceEliminator = SlowResourceEliminator.getInstance();