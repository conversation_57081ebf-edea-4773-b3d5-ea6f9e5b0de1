/**
 * Sistema di cache unificato per ottimizzazioni prestazioni
 * Sostituisce nativeSpeedCache, ultraCache e offlineCacheManager
 * Obiettivo: Riduzione del 40% del bundle size e miglioramento del cache hit rate
 */

import { logger } from './logger';

// Tipi per le configurazioni cache
export interface CacheConfig {
  ttl: number;
  maxSize: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
}

export interface CacheEntry<T = unknown> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
}

// Configurazioni cache ottimizzate
export const CACHE_CONFIGS: Record<string, CacheConfig> = {
  // Cache critiche - TTL breve, alta priorità
  AUTH: { ttl: 5 * 60 * 1000, maxSize: 10, priority: 'critical' },
  USER_PROFILE: { ttl: 10 * 60 * 1000, maxSize: 50, priority: 'critical' },
  
  // Cache ad alta frequenza
  PRODUCTS: { ttl: 15 * 60 * 1000, maxSize: 200, priority: 'high' },
  CONTAINERS: { ttl: 15 * 60 * 1000, maxSize: 100, priority: 'high' },
  SUPPLIERS: { ttl: 30 * 60 * 1000, maxSize: 100, priority: 'high' },
  
  // Cache medie
  DDT: { ttl: 60 * 60 * 1000, maxSize: 500, priority: 'medium' },
  ACTIVITY_LOGS: { ttl: 30 * 60 * 1000, maxSize: 300, priority: 'medium' },
  
  // Cache a bassa priorità
  SETTINGS: { ttl: 2 * 60 * 60 * 1000, maxSize: 50, priority: 'low' },
  TEMPLATES: { ttl: 6 * 60 * 60 * 1000, maxSize: 100, priority: 'low' }
};

class UnifiedCacheManager {
  private cache = new Map<string, CacheEntry>();
  private memoryCache = new Map<string, unknown>(); // Cache ultraveloce per dati critici
  
  // Statistiche per monitoraggio
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    evictions: 0
  };

  /**
   * Ottiene dati dalla cache con fallback su storage locale
   */
  async get<T>(key: string, config?: CacheConfig): Promise<T | null> {
    // Prima controlla la cache in memoria (ultraveloce)
    if (this.memoryCache.has(key)) {
      this.stats.hits++;
      return this.memoryCache.get(key) as T;
    }

    // Poi controlla la cache principale
    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      return await this.tryGetFromStorage(key);
    }

    // Verifica se la cache è scaduta
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.memoryCache.delete(key);
      this.stats.misses++;
      return await this.tryGetFromStorage(key);
    }

    // Aggiorna contatore hit e promuovi se necessario
    entry.hits++;
    this.stats.hits++;
    
    // Promuovi elementi frequenti alla cache veloce
    if (entry.hits >= 3 && entry.priority === 'critical') {
      this.memoryCache.set(key, entry.data);
    }

    return entry.data as T;
  }

  /**
   * Salva dati nella cache con configurazione ottimizzata
   */
  set<T>(key: string, data: T, configKey?: keyof typeof CACHE_CONFIGS): void {
    const config = configKey ? CACHE_CONFIGS[configKey] : CACHE_CONFIGS.PRODUCTS;
    
    // Controllo dimensione e cleanup se necessario
    if (this.cache.size >= config.maxSize) {
      this.evictLeastUsed(config.priority);
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: config.ttl,
      hits: 0,
      priority: config.priority
    };

    this.cache.set(key, entry);
    this.stats.sets++;

    // Aggiungi direttamente alla cache veloce se critico
    if (config.priority === 'critical') {
      this.memoryCache.set(key, data);
    }

    // Salva anche nel localStorage per persistenza
    this.saveToStorage(key, entry);
  }

  /**
   * Precarica dati critici all'avvio
   */
  async preloadCriticalData(): Promise<void> {
    const criticalEndpoints = [
      { key: '/api/auth/me', config: 'AUTH' as keyof typeof CACHE_CONFIGS },
      { key: '/api/containers', config: 'CONTAINERS' as keyof typeof CACHE_CONFIGS },
      { key: '/api/product-labels', config: 'PRODUCTS' as keyof typeof CACHE_CONFIGS }
    ];

    const loadPromises = criticalEndpoints.map(async ({ key, config }) => {
      try {
        const response = await fetch(key, {
          credentials: 'include',
          headers: { 'Accept': 'application/json' }
        });

        if (response.ok) {
          const data = await response.json();
          this.set(key, data, config);
          logger.log(`[UnifiedCache] Precaricato: ${key}`);
        }
      } catch (error) {
        logger.log(`[UnifiedCache] Errore precaricamento ${key}:`, error);
      }
    });

    await Promise.all(loadPromises);
  }

  /**
   * Rimuove elementi meno utilizzati in base alla priorità
   */
  private evictLeastUsed(currentPriority: 'critical' | 'high' | 'medium' | 'low'): void {
    const priorityOrder = ['low', 'medium', 'high', 'critical'];
    const currentIndex = priorityOrder.indexOf(currentPriority);

    // Rimuovi prima elementi con priorità inferiore
    for (let i = 0; i < currentIndex; i++) {
      const targetPriority = priorityOrder[i];
      const candidates = Array.from(this.cache.entries())
        .filter(([_, entry]) => entry.priority === targetPriority)
        .sort(([_, a], [__, b]) => a.hits - b.hits);

      if (candidates.length > 0) {
        const [keyToRemove] = candidates[0];
        this.cache.delete(keyToRemove);
        this.memoryCache.delete(keyToRemove);
        this.stats.evictions++;
        return;
      }
    }

    // Se non ci sono elementi a priorità inferiore, rimuovi il meno usato della stessa priorità
    const samePriorityCandidates = Array.from(this.cache.entries())
      .filter(([_, entry]) => entry.priority === currentPriority)
      .sort(([_, a], [__, b]) => a.hits - b.hits);

    if (samePriorityCandidates.length > 0) {
      const [keyToRemove] = samePriorityCandidates[0];
      this.cache.delete(keyToRemove);
      this.memoryCache.delete(keyToRemove);
      this.stats.evictions++;
    }
  }

  /**
   * Tenta di recuperare dalla cache locale
   */
  private async tryGetFromStorage<T>(key: string): Promise<T | null> {
    try {
      const stored = localStorage.getItem(`unified_cache_${key}`);
      if (!stored) return null;

      const { safeJsonParseWithFallback } = await import('./safe-json');
      const entry: CacheEntry<T> = safeJsonParseWithFallback(stored, { 
        data: null, 
        timestamp: 0, 
        ttl: 0, 
        hits: 0, 
        priority: 'low' as const 
      });
      
      // Verifica se ancora valido
      if (Date.now() - entry.timestamp <= entry.ttl) {
        // Ripristina nella cache attiva
        this.cache.set(key, entry);
        return entry.data;
      }
      
      // Rimuovi entry scaduta
      localStorage.removeItem(`unified_cache_${key}`);
      return null;
    } catch (error) {
      logger.log(`[UnifiedCache] Errore recupero storage per ${key}:`, error);
      return null;
    }
  }

  /**
   * Salva entry nel localStorage
   */
  private saveToStorage<T>(key: string, entry: CacheEntry<T>): void {
    try {
      localStorage.setItem(`unified_cache_${key}`, JSON.stringify(entry));
    } catch (error) {
      // Storage pieno o non disponibile - non è critico
      logger.log(`[UnifiedCache] Impossibile salvare ${key} nel storage:`, error);
    }
  }

  /**
   * Pulisce cache scadute e ottimizza memoria
   */
  cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.memoryCache.delete(key);
        localStorage.removeItem(`unified_cache_${key}`);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.log(`[UnifiedCache] Pulizia completata: ${cleaned} elementi rimossi`);
    }
  }

  /**
   * Ottiene statistiche della cache
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 
      : 0;

    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      cacheSize: this.cache.size,
      memoryCacheSize: this.memoryCache.size,
      totalItems: this.cache.size + this.memoryCache.size
    };
  }

  /**
   * Invalida cache per pattern specifici
   */
  invalidate(pattern: string | RegExp): void {
    const isRegex = pattern instanceof RegExp;
    let invalidated = 0;

    for (const key of Array.from(this.cache.keys())) {
      const shouldInvalidate = isRegex 
        ? pattern.test(key)
        : key.includes(pattern as string);

      if (shouldInvalidate) {
        this.cache.delete(key);
        this.memoryCache.delete(key);
        localStorage.removeItem(`unified_cache_${key}`);
        invalidated++;
      }
    }

    logger.log(`[UnifiedCache] Invalidate pattern '${pattern}': ${invalidated} elementi rimossi`);
  }

  /**
   * Reset completo della cache
   */
  clear(): void {
    this.cache.clear();
    this.memoryCache.clear();
    
    // Pulisci localStorage
    const keys = Object.keys(localStorage).filter(key => key.startsWith('unified_cache_'));
    keys.forEach(key => { localStorage.removeItem(key); });
    
    // Reset statistiche
    this.stats = { hits: 0, misses: 0, sets: 0, evictions: 0 };
    
    logger.log('[UnifiedCache] Cache completamente pulita');
  }
}

// Istanza singleton per l'intera applicazione
export const unifiedCache = new UnifiedCacheManager();

// Hook per React Query integration
export const createUnifiedCacheKey = (queryKey: (string | number | boolean | object)[]): string => {
  return queryKey.map(key => 
    typeof key === 'object' ? JSON.stringify(key) : String(key)
  ).join('|');
};

// Avvio automatico della cache
if (typeof window !== 'undefined') {
  // Precarica dati critici dopo 100ms per non bloccare il render iniziale
  setTimeout(() => {
    void unifiedCache.preloadCriticalData();
  }, 100);

  // Cleanup automatico ogni 5 minuti
  setInterval(() => {
    unifiedCache.cleanup();
  }, 5 * 60 * 1000);

  // Cleanup alla chiusura della pagina
  window.addEventListener('beforeunload', () => {
    unifiedCache.cleanup();
  });

  logger.log('[UnifiedCache] Sistema cache unificato inizializzato');
}

export default unifiedCache;