/**
 * Sistema di gestione aggiornamenti app semplificato
 * Utilizza versioni semantic e data di deploy
 */

import { toast } from "@/hooks/use-toast";

// Sistema di versioning semplificato
import { getCurrentVersion, checkForNewVersion, saveCurrentVersion, getVersionInfo } from './version';
export const APP_VERSION = getCurrentVersion();

/**
 * Verifica e forza l'aggiornamento dell'app all'avvio
 * Modificato per includere feedback automatico all'utente
 */
export const checkAndUpdateApp = async (): Promise<void> => {
  try {
    // Verifica se c'è un parametro di cache-busting nell'URL
    const hasForceReload = window.location.href.includes('forcereload=');
    
    if (hasForceReload) {
      console.log('Pagina già ricaricata con cache-busting');
      return;
    }
    
    // Controlla se c'è una nuova versione
    if (checkForNewVersion()) {
      const versionInfo = getVersionInfo();
      console.log(`Nuova versione rilevata: ${versionInfo.version} (precedente: ${versionInfo.storedVersion || 'nessuna'})`);
      
      // Salva la nuova versione
      saveCurrentVersion();
      
      // In produzione, gestisci l'aggiornamento completo
      if (!import.meta.env.DEV) {
        // Pulisci le cache se possibile
        if ('caches' in window) {
          const cacheKeys = await caches.keys();
          console.log(`Pulizia cache: ${cacheKeys.length} cache trovate`);
          
          await Promise.all(
            cacheKeys.map(key => {
              console.log(`Eliminazione cache: ${key}`);
              return caches.delete(key);
            })
          );
        }
      }
      
      // Aggiorna il service worker se presente
      if ('serviceWorker' in navigator) {
        // Cerca di ottenere tutte le registrazioni
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        if (registrations.length > 0) {
          console.log(`Trovati ${registrations.length} service worker registrati`);
          
          // Per ogni registrazione, invia messaggio di aggiornamento e forza update
          await Promise.all(
            registrations.map(async registration => {
              // Forza aggiornamento
              await registration.update();
              
              // Se c'è un worker in attesa, invia comando di attivazione
              if (registration.waiting) {
                console.log('Trovato service worker in attesa, invio comando di aggiornamento');
                
                try {
                  // Prova entrambi i formati per maggiore compatibilità
                  registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                  setTimeout(() => {
                    if (registration.waiting) {
                      registration.waiting.postMessage('SKIP_WAITING');
                    }
                  }, 100);
                } catch (error) {
                  console.error('Errore durante invio messaggio al service worker:', error);
                }
              }
            })
          );
        }
      }
      
      // Salva la nuova versione
      localStorage.setItem('app_version', APP_VERSION);
      
      // Mostra toast informativo
      toast({
        title: "Nuova versione disponibile",
        description: `Aggiornamento alla versione ${APP_VERSION} in corso...`,
        duration: 3000
      });
      
      // Forza ricarica dopo breve timeout per dare tempo al service worker di attivarsi
      setTimeout(() => {
        const url = window.location.href.split('?')[0];
        window.location.href = `${url}?forcereload=${Date.now()}`;
      }, 1500);
    } else {
      console.log(`App già aggiornata alla versione ${APP_VERSION}`);
    }
  } catch (error) {
    console.error('Errore durante il controllo degli aggiornamenti:', error);
  }
};

/**
 * Forza un aggiornamento immediato dell'app senza controlli
 */
export const forceAppUpdate = (): void => {
  // Mostra toast informativo
  toast({
    title: "Aggiornamento forzato",
    description: "Pulizia cache e ricaricamento pagina in corso...",
    duration: 2000
  });
  
  // Pulisci le cache
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      cacheNames.forEach(cacheName => {
        console.log(`Pulizia cache: ${cacheName}`);
        caches.delete(cacheName);
      });
    }).catch(error => {
      console.error('Errore durante pulizia cache forzata:', error);
      toast({
        title: 'Errore pulizia cache',
        description: 'Cache non completamente pulita, ricaricamento comunque in corso.',
        variant: 'destructive',
        duration: 2000
      });
    });
  }
  
  // Forza ricarica con parametro cache-busting
  setTimeout(() => {
    const url = window.location.href.split('?')[0];
    window.location.href = `${url}?forcereload=${Date.now()}`;
  }, 800);
};