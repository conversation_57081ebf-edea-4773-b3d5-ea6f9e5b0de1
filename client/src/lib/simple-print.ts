/**
 * Funzione semplice per stampare un QR code usando una pagina HTML dedicata
 * Utilizza l'immagine QR esistente anziché generarne una nuova
 */
export function simplePrintQR(title: string, subtitle: string, qrImageSrc?: string): boolean {
  try {
    // Prepara i parametri per la pagina di stampa
    const encodedTitle = encodeURIComponent(title);
    const encodedSubtitle = encodeURIComponent(subtitle);
    
    // Utilizziamo un URL per la stampa che include l'immagine del QR code se disponibile
    let printURL = `/print-qr-direct.html?title=${encodedTitle}&subtitle=${encodedSubtitle}`;
    
    if (qrImageSrc) {
      // Includere l'URL dell'immagine QR code se fornito
      printURL += `&qrimage=${encodeURIComponent(qrImageSrc)}`;
    }
    
    // Apri una nuova finestra per la stampa con la pagina dedicata
    const printWindow = window.open(printURL, '_blank', 'width=400,height=400');
    
    if (!printWindow) {
      console.error("Impossibile aprire la finestra di stampa. Verifica che i popup non siano bloccati.");
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Errore durante l'apertura della finestra di stampa:", error);
    return false;
  }
}