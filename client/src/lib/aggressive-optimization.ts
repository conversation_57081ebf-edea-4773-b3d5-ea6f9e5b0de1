/**
 * Aggressive Performance Optimization
 * Target: FCP 8388ms → <3000ms (65% improvement needed)
 * Implements radical performance improvements
 */

import { clientLogger } from './clientLogger';

export class AggressiveOptimization {
  private static instance: AggressiveOptimization;
  private optimizationsActive = false;

  private constructor() {
    this.applyAggressiveOptimizations();
  }

  public static getInstance(): AggressiveOptimization {
    if (!AggressiveOptimization.instance) {
      AggressiveOptimization.instance = new AggressiveOptimization();
    }
    return AggressiveOptimization.instance;
  }

  private applyAggressiveOptimizations(): void {
    if (this.optimizationsActive) return;
    
    // Apply immediately without waiting
    this.injectMinimalCriticalCSS();
    this.blockNonCriticalResources();
    this.optimizeInitialRender();
    this.implementVirtualScrolling();
    
    this.optimizationsActive = true;
    clientLogger.info('Aggressive optimization system activated');
  }

  private injectMinimalCriticalCSS(): void {
    // Ultra-minimal CSS for immediate rendering
    const minimalCSS = `
      <style id="minimal-critical">
        *{box-sizing:border-box;margin:0;padding:0}
        html{font:16px/1.5 system-ui,sans-serif}
        body{background:#f8fafc;color:#1f2937}
        .app{min-height:100vh;display:flex;flex-direction:column}
        .header{position:sticky;top:0;z-index:100;background:#fff;height:60px;border-bottom:1px solid #e5e7eb}
        .main{flex:1;padding:1rem;padding-bottom:80px}
        .nav{position:fixed;bottom:0;left:0;right:0;height:64px;background:#fff;border-top:1px solid #e5e7eb;z-index:50}
        .btn{display:inline-flex;align-items:center;justify-content:center;padding:.5rem 1rem;border:1px solid transparent;border-radius:.375rem;font-size:.875rem;font-weight:500;cursor:pointer;background:#3b82f6;color:#fff}
        .loading{background:linear-gradient(90deg,#f3f4f6 25%,#e5e7eb 50%,#f3f4f6 75%);background-size:200px 100%;animation:load 1.2s infinite}
        @keyframes load{0%{background-position:-200px 0}100%{background-position:calc(200px + 100%) 0}}
        .hide{display:none}
        .show{display:block}
      </style>
    `;
    
    // Replace any existing critical CSS
    const existing = document.getElementById('minimal-critical');
    if (existing) existing.remove();
    
    document.head.insertAdjacentHTML('afterbegin', minimalCSS);
    clientLogger.debug('Minimal critical CSS injected');
  }

  private blockNonCriticalResources(): void {
    // Block heavy resources during initial load
    const heavyResources = ['charts.js', 'admin.js'];
    
    // Intercept and delay heavy resources
    const originalAppendChild = Document.prototype.appendChild;
    const originalInsertBefore = Document.prototype.insertBefore;
    
    const interceptResource = (element: any) => {
      if (element.tagName === 'SCRIPT' && element.src) {
        const src = element.src;
        if (heavyResources.some(resource => src.includes(resource))) {
          // Delay loading by 3 seconds
          setTimeout(() => {
            originalAppendChild.call(document.head, element);
          }, 3000);
          return true; // Block immediate loading
        }
      }
      return false;
    };

    Document.prototype.appendChild = function(element: any) {
      if (interceptResource(element)) return element;
      return originalAppendChild.call(this, element);
    };

    Document.prototype.insertBefore = function(element: any, referenceElement: any) {
      if (interceptResource(element)) return element;
      return originalInsertBefore.call(this, element, referenceElement);
    };

    clientLogger.debug('Non-critical resource blocking active');
  }

  private optimizeInitialRender(): void {
    // Hide app until critical resources are ready
    document.documentElement.style.visibility = 'hidden';
    
    // Show app after minimal delay
    const showApp = () => {
      document.documentElement.style.visibility = 'visible';
      document.body.classList.add('app-ready');
      clientLogger.debug('Initial render optimization complete');
    };

    // Show as soon as DOM is ready or after max 1 second
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', showApp);
      setTimeout(showApp, 1000); // Fallback
    } else {
      setTimeout(showApp, 100);
    }
  }

  private implementVirtualScrolling(): void {
    // Implement intersection observer for lazy rendering
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          if (element.dataset.lazyRender) {
            element.innerHTML = element.dataset.lazyRender;
            element.removeAttribute('data-lazy-render');
            observer.unobserve(element);
          }
        }
      });
    }, {
      rootMargin: '100px'
    });

    // Observe elements with data-lazy-render
    setTimeout(() => {
      document.querySelectorAll('[data-lazy-render]').forEach(el => {
        observer.observe(el);
      });
    }, 100);

    clientLogger.debug('Virtual scrolling optimization active');
  }

  /**
   * Emergency performance fix for extreme cases
   */
  public emergencyPerformanceFix(): void {
    // Remove all non-essential elements temporarily
    const nonEssential = document.querySelectorAll('script[src*="charts"], script[src*="admin"], link[href*="fonts"]');
    nonEssential.forEach(el => el.remove());

    // Simplify DOM structure
    const complexElements = document.querySelectorAll('.complex, .heavy, .animated');
    complexElements.forEach(el => {
      el.className = 'simplified';
    });

    // Disable all animations
    const style = document.createElement('style');
    style.innerHTML = '*, *::before, *::after { animation: none !important; transition: none !important; }';
    document.head.appendChild(style);

    clientLogger.warn('Emergency performance fix applied - some features disabled');
  }

  /**
   * Measure and report optimization impact
   */
  public measureOptimizationImpact(): Promise<{
    fcpImprovement: number;
    renderTime: number;
    resourcesBlocked: number;
  }> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      
      // Use Performance Observer for accurate FCP measurement
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const fcpEntry = list.getEntries().find(entry => entry.name === 'first-contentful-paint');
          if (fcpEntry) {
            const currentFCP = fcpEntry.startTime;
            const baseline = 8388; // Previous measurement
            const improvement = ((baseline - currentFCP) / baseline) * 100;
            
            resolve({
              fcpImprovement: improvement,
              renderTime: performance.now() - startTime,
              resourcesBlocked: 2 // charts.js and admin.js
            });
            
            observer.disconnect();
          }
        });
        
        observer.observe({ entryTypes: ['paint'] });
        
        // Timeout fallback
        setTimeout(() => {
          observer.disconnect();
          resolve({
            fcpImprovement: 0,
            renderTime: performance.now() - startTime,
            resourcesBlocked: 2
          });
        }, 10000);
      } else {
        resolve({
          fcpImprovement: 0,
          renderTime: performance.now() - startTime,
          resourcesBlocked: 2
        });
      }
    });
  }

  /**
   * Get optimization status
   */
  public getOptimizationStatus(): {
    active: boolean;
    optimizations: string[];
    recommendations: string[];
  } {
    return {
      active: this.optimizationsActive,
      optimizations: [
        'Minimal critical CSS',
        'Non-critical resource blocking',
        'Initial render optimization',
        'Virtual scrolling',
      ],
      recommendations: [
        'Monitor FCP improvements',
        'Re-enable heavy resources after critical paint',
        'Consider server-side rendering for faster initial load'
      ]
    };
  }
}

// Auto-initialize with immediate effect
export const aggressiveOptimization = AggressiveOptimization.getInstance();