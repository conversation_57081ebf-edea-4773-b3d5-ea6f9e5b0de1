/**
 * API Cache Optimizer
 * Target: Reduce API call times from 1000ms+ to <300ms
 * Implements aggressive caching and request optimization
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
  hits: number;
}

interface RequestMetrics {
  url: string;
  method: string;
  duration: number;
  timestamp: number;
  cached: boolean;
}

export class APICacheOptimizer {
  private static instance: APICacheOptimizer;
  private cache = new Map<string, CacheEntry>();
  private requestMetrics: RequestMetrics[] = [];
  private pendingRequests = new Map<string, Promise<any>>();

  // MAXIMUM-aggressive TTL settings (in milliseconds) to reduce API calls
  private cacheTTL = {
    '/api/auth/me': 1800000,       // 30 minutes - maximum cache for user data
    '/api/containers': 3600000,    // 60 minutes - maximum cache for containers
    '/api/product-labels': 1800000, // 30 minutes - maximum cache for products
    '/api/suppliers': 7200000,     // 2 hours - maximum cache for suppliers
    '/api/activity-logs': 900000,  // 15 minutes - long cache for activity
    'default': 1800000             // 30 minutes default
  };

  private constructor() {
    this.initializeAPIOptimization();
  }

  public static getInstance(): APICacheOptimizer {
    if (!APICacheOptimizer.instance) {
      APICacheOptimizer.instance = new APICacheOptimizer();
    }
    return APICacheOptimizer.instance;
  }

  private initializeAPIOptimization(): void {
    // Override fetch for caching and optimization
    this.overrideFetch();
    
    // Setup cache cleanup
    this.setupCacheCleanup();
    
    // Preload critical APIs
    this.preloadCriticalAPIs();
    
    console.log('[API-CACHE-OPTIMIZER] Advanced API caching initialized');
  }

  private overrideFetch(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method || 'GET';
      const cacheKey = this.generateCacheKey(url, method, init);
      
      const startTime = performance.now();
      
      // Check cache for GET requests
      if (method === 'GET') {
        const cachedEntry = this.cache.get(cacheKey);
        if (cachedEntry && this.isCacheValid(cachedEntry)) {
          cachedEntry.hits++;
          
          // Record cache hit metrics
          this.recordMetrics({
            url,
            method,
            duration: performance.now() - startTime,
            timestamp: Date.now(),
            cached: true
          });
          
          // Return cached response
          return new Response(JSON.stringify(cachedEntry.data), {
            status: 200,
            statusText: 'OK',
            headers: { 'Content-Type': 'application/json', 'x-cache': 'HIT' }
          });
        }
      }

      // Check for pending requests to avoid duplicate calls
      if (this.pendingRequests.has(cacheKey)) {
        const cachedResponse = await this.pendingRequests.get(cacheKey);
        return cachedResponse;
      }

      // Create request promise
      const requestPromise = originalFetch(input, init).then(async (response) => {
        const duration = performance.now() - startTime;
        
        // Record metrics
        this.recordMetrics({
          url,
          method,
          duration,
          timestamp: Date.now(),
          cached: false
        });

        // Cache successful GET responses
        if (method === 'GET' && response.ok) {
          try {
            const responseClone = response.clone();
            const data = await responseClone.json();
            
            // Cache with appropriate TTL
            const ttl = this.getTTLForEndpoint(url);
            this.cache.set(cacheKey, {
              data,
              timestamp: Date.now(),
              ttl,
              hits: 0
            });
          } catch (error) {
            // Ignore JSON parsing errors for non-JSON responses
          }
        }

        // Log slow requests
        if (duration > 500) {
          console.warn(`[API-CACHE-OPTIMIZER] Slow API call: ${url} took ${duration.toFixed(0)}ms`);
        }

        return response;
      }).finally(() => {
        // Remove from pending requests
        this.pendingRequests.delete(cacheKey);
      });

      // Store pending request
      this.pendingRequests.set(cacheKey, requestPromise);
      
      return requestPromise;
    };
  }

  private generateCacheKey(url: string, method: string, init?: RequestInit): string {
    // Include relevant request parameters in cache key
    const body = init?.body ? JSON.stringify(init.body) : '';
    const headers = init?.headers ? JSON.stringify(init.headers) : '';
    return `${method}:${url}:${body}:${headers}`;
  }

  private isCacheValid(entry: CacheEntry): boolean {
    return (Date.now() - entry.timestamp) < entry.ttl;
  }

  private getTTLForEndpoint(url: string): number {
    for (const [endpoint, ttl] of Object.entries(this.cacheTTL)) {
      if (url.includes(endpoint)) {
        return ttl;
      }
    }
    return this.cacheTTL.default;
  }

  private setupCacheCleanup(): void {
    // Clean expired entries every 2 minutes
    setInterval(() => {
      const now = Date.now();
      let cleanedCount = 0;
      
      for (const [key, entry] of this.cache.entries()) {
        if (!this.isCacheValid(entry)) {
          this.cache.delete(key);
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        console.log(`[API-CACHE-OPTIMIZER] Cleaned ${cleanedCount} expired cache entries`);
      }
    }, 120000);
  }

  private preloadCriticalAPIs(): void {
    // Preload most critical APIs immediately
    const criticalAPIs = [
      '/api/auth/me',
      '/api/containers',
      '/api/product-labels'
    ];

    // Wait for initial page load, then preload
    setTimeout(() => {
      criticalAPIs.forEach(async (api) => {
        try {
          await fetch(api, { credentials: 'include' });
          console.log(`[API-CACHE-OPTIMIZER] Preloaded ${api}`);
        } catch (error) {
          console.warn(`[API-CACHE-OPTIMIZER] Failed to preload ${api}:`, error);
        }
      });
    }, 1000);
  }

  private recordMetrics(metrics: RequestMetrics): void {
    this.requestMetrics.push(metrics);
    
    // Keep only recent metrics (last 1000 or 10 minutes)
    const maxAge = 10 * 60 * 1000;
    const now = Date.now();
    this.requestMetrics = this.requestMetrics
      .filter(m => now - m.timestamp < maxAge)
      .slice(-1000);
  }

  /**
   * Get cache performance statistics
   */
  public getCacheStats(): {
    cacheSize: number;
    hitRate: number;
    averageResponseTime: number;
    slowRequests: RequestMetrics[];
    recommendations: string[];
  } {
    const recentMetrics = this.requestMetrics.filter(
      m => Date.now() - m.timestamp < 300000 // Last 5 minutes
    );

    const totalRequests = recentMetrics.length;
    const cachedRequests = recentMetrics.filter(m => m.cached).length;
    const hitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

    const averageResponseTime = totalRequests > 0
      ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests
      : 0;

    const slowRequests = recentMetrics
      .filter(m => m.duration > 500)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    const recommendations: string[] = [];
    
    if (hitRate < 60) {
      recommendations.push('Low cache hit rate - consider increasing TTL values');
    }
    
    if (averageResponseTime > 300) {
      recommendations.push('High average response time - implement request optimization');
    }
    
    if (slowRequests.length > 5) {
      recommendations.push(`${slowRequests.length} slow requests detected - review endpoint performance`);
    }

    return {
      cacheSize: this.cache.size,
      hitRate,
      averageResponseTime,
      slowRequests,
      recommendations
    };
  }

  /**
   * Invalidate cache for specific endpoints
   */
  public invalidateCache(pattern: string): void {
    let invalidatedCount = 0;
    
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }
    
    console.log(`[API-CACHE-OPTIMIZER] Invalidated ${invalidatedCount} cache entries for pattern: ${pattern}`);
  }

  /**
   * Warm up cache with critical data
   */
  public async warmUpCache(): Promise<void> {
    const criticalEndpoints = [
      '/api/auth/me',
      '/api/containers',
      '/api/product-labels',
      '/api/suppliers'
    ];

    const warmupPromises = criticalEndpoints.map(async (endpoint) => {
      try {
        await fetch(endpoint, { credentials: 'include' });
        return { endpoint, success: true };
      } catch (error) {
        return { endpoint, success: false, error };
      }
    });

    const results = await Promise.all(warmupPromises);
    const successful = results.filter(r => r.success).length;
    
    console.log(`[API-CACHE-OPTIMIZER] Cache warmed up: ${successful}/${criticalEndpoints.length} endpoints`);
  }

  /**
   * Apply emergency cache optimization
   */
  public emergencyOptimization(): void {
    // Increase all TTL values by 5x for emergency caching
    Object.keys(this.cacheTTL).forEach(key => {
      if (key !== 'default') {
        this.cacheTTL[key as keyof typeof this.cacheTTL] *= 5;
      }
    });
    this.cacheTTL.default *= 5;

    console.warn('[API-CACHE-OPTIMIZER] Emergency optimization applied - TTL values increased 5x');
  }
}

// Initialize and export
export const apiCacheOptimizer = APICacheOptimizer.getInstance();

// Auto-warm cache on load
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      apiCacheOptimizer.warmUpCache();
    }, 2000);
  });
}