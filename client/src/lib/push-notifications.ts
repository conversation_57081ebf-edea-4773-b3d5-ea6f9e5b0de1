/**
 * Push Notifications Utility
 * 
 * This module provides functions for registering and handling push notifications
 * in the HACCP Tracker PWA application.
 */

/**
 * Check if push notifications are supported in this browser
 */
export function arePushNotificationsSupported(): boolean {
  return 'serviceWorker' in navigator && 
         'PushManager' in window && 
         'Notification' in window;
}

/**
 * Request permission for push notifications
 * @returns Promise that resolves to the permission status
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!arePushNotificationsSupported()) {
    throw new Error('Push notifications are not supported in this browser');
  }
  
  return await Notification.requestPermission();
}

/**
 * Subscribe the user to push notifications
 * @returns Promise that resolves to the PushSubscription object
 */
export async function subscribeToPushNotifications(): Promise<PushSubscription | null> {
  try {
    if (!arePushNotificationsSupported()) {
      console.warn('Push notifications are not supported in this browser');
      return null;
    }
    
    const permission = await requestNotificationPermission();
    
    if (permission !== 'granted') {
      console.warn('Notification permission not granted');
      return null;
    }
    
    // Get the service worker registration
    const registration = await navigator.serviceWorker.ready;
    
    // Generate server keys for VAPID
    // In a real app, these keys would be generated on the server and stored securely
    const vapidPublicKey = 'BLBx-hf2WrJ_-HqCxwVsH_9_3FbWFhVbt7NrPqpDVuxyd3qjXs16GV3WQj9FM-yFH6PomYV0_ZB9c1lrkJu_8Ug';
    
    // Convert the public key to the required format
    const applicationServerKey = urlBase64ToUint8Array(vapidPublicKey);
    
    // Subscribe
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: applicationServerKey
    });
    
    // In a real app, you'd send this subscription to your server
    console.log('Push notification subscription:', JSON.stringify(subscription));
    
    return subscription;
  } catch (error) {
    console.error('Error subscribing to push notifications:', error);
    return null;
  }
}

/**
 * Unsubscribe from push notifications
 * @returns Promise that resolves to a boolean indicating success
 */
export async function unsubscribeFromPushNotifications(): Promise<boolean> {
  try {
    if (!arePushNotificationsSupported()) {
      return false;
    }
    
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();
    
    if (!subscription) {
      return false;
    }
    
    const success = await subscription.unsubscribe();
    
    // In a real app, you'd notify your server about the unsubscription
    console.log('Unsubscribed from push notifications');
    
    return success;
  } catch (error) {
    console.error('Error unsubscribing from push notifications:', error);
    return false;
  }
}

/**
 * Utility function to convert a base64 string to a Uint8Array
 * @param base64String The base64 string to convert
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  
  return outputArray;
}

/**
 * Send a local notification (for testing purposes)
 * @param title Notification title
 * @param options Notification options
 */
export function sendLocalNotification(title: string, options: NotificationOptions = {}): void {
  if (!('Notification' in window)) {
    console.warn('Notifications not supported');
    return;
  }
  
  if (Notification.permission === 'granted') {
    new Notification(title, {
      body: options.body || 'Test notification',
      icon: options.icon || '/logo192.svg',
      ...options
    });
  } else {
    console.warn('Notification permission not granted');
  }
}