/**
 * Sistema di versioning semplificato per la PWA
 * Utilizza versioni semantic incrementali controllate
 */

// Versione dell'applicazione (major.minor.patch)
const APP_VERSION = "1.2.17";
// Build: 2025-06-04

// Build number per development (cambia ogni ora, max 99)
const DEV_BUILD_NUMBER = Math.floor(Date.now() / 3600000) % 100;

// Funzione per generare data di build/deploy
export function getBuildDate(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  return `${year}.${month}.${day}-${hours}:${minutes}`;
}

// Funzione per rilevare se siamo in deployment automatico
function isAutoDeployment(): boolean {
  // Rileva deployment Replit attraverso variabili ambiente
  return !import.meta.env.DEV && 
         (import.meta.env.PROD || 
          typeof window !== 'undefined' && 
          !window.location.hostname.includes('replit.dev'));
}

// Funzione per auto-incremento in deployment
function getAutoIncrementedVersion(): string {
  if (isAutoDeployment()) {
    // In deployment automatico, incrementa in base al tempo di build
    const buildTime = Math.floor(Date.now() / 86400000); // Giorni da epoch
    const parts = APP_VERSION.split('.').map(Number);
    
    // Se è un nuovo giorno, incrementa patch
    const storedBuildTime = localStorage.getItem('last_build_time');
    if (storedBuildTime && buildTime > parseInt(storedBuildTime)) {
      parts[2] = (parts[2] || 0) + 1;
      localStorage.setItem('last_build_time', buildTime.toString());
      const newVersion = parts.join('.');
      console.log(`Auto-incremented version for deployment: ${newVersion}`);
      return newVersion;
    }
  }
  return APP_VERSION;
}

// Funzione per ottenere la versione corrente
export function getCurrentVersion(): string {
  // In produzione, usa la versione dal build time se disponibile
  const envVersion = import.meta.env.VITE_APP_VERSION;
  if (envVersion && !import.meta.env.DEV) {
    return envVersion;
  }
  
  // In development, aggiungi suffisso build
  if (import.meta.env.DEV) {
    return `${APP_VERSION}-dev.${DEV_BUILD_NUMBER.toString().padStart(2, '0')}`;
  }
  
  // In deployment automatico, prova auto-incremento
  return getAutoIncrementedVersion();
}

// Funzione per confrontare versioni semantic
export function isNewerVersion(current: string, stored: string): boolean {
  if (!stored) return true;
  if (current === stored) return false;
  
  // Rimuovi suffissi dev se presenti
  const cleanCurrent = current.replace(/-dev\.\d+$/, '');
  const cleanStored = stored.replace(/-dev\.\d+$/, '').replace(/\.25\d{8}$/, '');
  
  const currentParts = cleanCurrent.split('.').map(part => parseInt(part, 10) || 0);
  const storedParts = cleanStored.split('.').map(part => parseInt(part, 10) || 0);
  
  // Confronta major, minor, patch
  for (let i = 0; i < 3; i++) {
    const currentPart = currentParts[i] || 0;
    const storedPart = storedParts[i] || 0;
    
    if (currentPart > storedPart) return true;
    if (currentPart < storedPart) return false;
  }
  
  // Se le versioni base sono uguali, considera versioni dev come più vecchie
  if (current.includes('-dev') && !stored.includes('-dev')) {
    return false;
  }
  if (!current.includes('-dev') && stored.includes('-dev')) {
    return true;
  }
  
  return false;
}

// Funzione per salvare la versione corrente nel localStorage
export function saveCurrentVersion(): void {
  const version = getCurrentVersion();
  const buildDate = getBuildDate();
  
  localStorage.setItem('app_version', version);
  localStorage.setItem('app_build_date', buildDate);
  
  console.log(`Versione applicazione salvata: ${version} (${buildDate})`);
}

// Funzione per controllare se c'è una nuova versione
export function checkForNewVersion(): boolean {
  const currentVersion = getCurrentVersion();
  const storedVersion = localStorage.getItem('app_version');
  
  console.log(`Versione corrente: ${currentVersion}`);
  console.log(`Versione salvata: ${storedVersion || 'nessuna'}`);
  
  // Solo in sviluppo, considera sempre come nuova versione per testing
  if (import.meta.env.DEV) {
    return storedVersion !== currentVersion;
  }
  
  return isNewerVersion(currentVersion, storedVersion || '');
}

// Funzione per ottenere informazioni complete sulla versione
export function getVersionInfo() {
  return {
    version: getCurrentVersion(),
    buildDate: getBuildDate(),
    storedVersion: localStorage.getItem('app_version'),
    storedBuildDate: localStorage.getItem('app_build_date')
  };
}