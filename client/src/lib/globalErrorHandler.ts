/**
 * Global Error Handler per HACCP Tracker
 * 
 * @description Sistema completo di gestione errori globale:
 * - Cattura errori non gestiti (unhandled errors)
 * - Logging strutturato con livelli
 * - Integrazione con sistema di monitoring
 * - Recovery automatico per errori comuni
 * - Retry logic per chiamate API
 * 
 * <AUTHOR> di Error Handling Globale HACCP Tracker
 * @version 1.0.0 - Global error handler
 * @date 2025-07-26
 */

import { QueryClient } from '@tanstack/react-query';

// Interfacce per error handling
export interface ErrorContext {
  timestamp: string;
  url: string;
  userAgent: string;
  userId?: number;
  sessionId?: string;
  component?: string;
  action?: string;
  additionalInfo?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  level: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  stack?: string;
  context: ErrorContext;
  handled: boolean;
  retryable: boolean;
}

// Cache per evitare spam di errori identici
const errorCache = new Map<string, { count: number; lastSeen: number }>();
const MAX_CACHE_SIZE = 100; // Limite massimo di errori unici in cache
const MAX_SAME_ERROR_COUNT = 5;
const ERROR_CACHE_TIMEOUT = 60000; // 1 minuto

// Configurazione retry per diversi tipi di errore
const RETRY_CONFIG = {
  network: { maxRetries: 3, baseDelay: 1000, backoffMultiplier: 2 },
  api: { maxRetries: 2, baseDelay: 500, backoffMultiplier: 1.5 },
  default: { maxRetries: 1, baseDelay: 2000, backoffMultiplier: 1 }
};

/**
 * Genera ID unico per errore
 */
const generateErrorId = (): string => {
  return `err_global_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Determina il livello di severità dell'errore
 */
const determineErrorLevel = (error: Error, context: ErrorContext): ErrorReport['level'] => {
  const message = error.message.toLowerCase();
  const stack = error.stack?.toLowerCase() || '';

  // Errori critici
  if (
    message.includes('script error') ||
    message.includes('permission denied') ||
    message.includes('network error') ||
    stack.includes('critical') ||
    context.component?.includes('auth')
  ) {
    return 'critical';
  }

  // Errori ad alta priorità
  if (
    message.includes('failed to fetch') ||
    message.includes('timeout') ||
    message.includes('database') ||
    stack.includes('server') ||
    context.component?.includes('payment')
  ) {
    return 'high';
  }

  // Errori medi
  if (
    message.includes('validation') ||
    message.includes('parsing') ||
    message.includes('format') ||
    stack.includes('component')
  ) {
    return 'medium';
  }

  // Errori bassi (default)
  return 'low';
};

/**
 * Verifica se un errore è retryable
 */
const isRetryableError = (error: Error): boolean => {
  const message = error.message.toLowerCase();
  
  return (
    message.includes('network') ||
    message.includes('timeout') ||
    message.includes('fetch') ||
    message.includes('connection') ||
    message.includes('503') ||
    message.includes('502') ||
    message.includes('500')
  );
};

/**
 * Crea fingerprint per errore (per evitare duplicati)
 */
const createErrorFingerprint = (error: Error, context: ErrorContext): string => {
  const key = `${error.name}_${error.message}_${context.component || 'global'}`;
  return btoa(key).substr(0, 20);
};

/**
 * Verifica se l'errore dovrebbe essere ignorato (rate limiting)
 */
const shouldIgnoreError = (fingerprint: string): boolean => {
  const now = Date.now();
  const cached = errorCache.get(fingerprint);
  
  if (!cached) {
    // Se la cache è piena, rimuovi l'elemento più vecchio
    if (errorCache.size >= MAX_CACHE_SIZE) {
      const oldestKey = errorCache.keys().next().value;
      errorCache.delete(oldestKey);
    }
    errorCache.set(fingerprint, { count: 1, lastSeen: now });
    return false;
  }
  
  // Reset counter se l'errore non è visto da tempo
  if (now - cached.lastSeen > ERROR_CACHE_TIMEOUT) {
    errorCache.set(fingerprint, { count: 1, lastSeen: now });
    return false;
  }
  
  // Incrementa contatore
  cached.count++;
  cached.lastSeen = now;
  
  // Ignora se supera il limite
  return cached.count > MAX_SAME_ERROR_COUNT;
};

/**
 * Invia report errore al sistema di monitoring
 */
const sendErrorReport = async (errorReport: ErrorReport): Promise<void> => {
  try {
    // Log locale
    console.error('🚨 Global Error Handler:', errorReport);
    
    // Salva in localStorage per debugging
    try {
      const { safeStorageParse } = await import('./safe-json');
      const errorLogs = safeStorageParse('haccp_global_errors', []);
      errorLogs.push(errorReport);
      
      // Mantieni solo gli ultimi 100 errori
      if (errorLogs.length > 100) {
        errorLogs.splice(0, errorLogs.length - 100);
      }
      
      localStorage.setItem('haccp_global_errors', JSON.stringify(errorLogs));
    } catch (storageError) {
      console.warn('Failed to store global error:', storageError);
    }
    
    // Invia al monitoring system (solo in produzione per evitare spam)
    if (process.env.NODE_ENV === 'production') {
      await fetch('/api/monitoring/metrics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'GLOBAL_ERROR',
          properties: errorReport
        })
      });
      
      // Alert critico se necessario
      if (errorReport.level === 'critical') {
        await fetch('/api/monitoring/alerts/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            severity: 'critical',
            message: `Global Error: ${errorReport.message}`,
            context: errorReport.context
          })
        });
      }
    }
  } catch (reportingError) {
    console.error('Failed to send error report:', reportingError);
  }
};

/**
 * Gestisce errori JavaScript non catturati
 */
const handleUnhandledError = (event: ErrorEvent): void => {
  const error = event.error || new Error(event.message);
  const context: ErrorContext = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    component: 'global',
    action: 'unhandled_error',
    additionalInfo: {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }
  };
  
  processError(error, context, false);
};

/**
 * Gestisce promise rejection non catturate
 */
const handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
  const error = event.reason instanceof Error 
    ? event.reason 
    : new Error(String(event.reason));
    
  const context: ErrorContext = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    component: 'global',
    action: 'unhandled_rejection',
    additionalInfo: {
      type: 'promise_rejection',
      reason: event.reason
    }
  };
  
  processError(error, context, false);
};

/**
 * Processa un errore e decide come gestirlo
 */
const processError = async (
  error: Error, 
  context: ErrorContext, 
  handled: boolean = true
): Promise<void> => {
  const fingerprint = createErrorFingerprint(error, context);
  
  // Rate limiting
  if (shouldIgnoreError(fingerprint)) {
    console.warn('🚨 Ignoring repeated error:', fingerprint);
    return;
  }
  
  const errorReport: ErrorReport = {
    id: generateErrorId(),
    level: determineErrorLevel(error, context),
    message: error.message,
    stack: error.stack,
    context,
    handled,
    retryable: isRetryableError(error)
  };
  
  // Invia report
  await sendErrorReport(errorReport);
  
  // Tenta recovery automatico se possibile
  if (errorReport.retryable && !handled) {
    attemptErrorRecovery(error, context);
  }
};

/**
 * Tenta recovery automatico per errori retryable
 */
const attemptErrorRecovery = (error: Error, context: ErrorContext): void => {
  console.log('🔄 Attempting automatic error recovery...');
  
  // Recovery per errori di rete
  if (error.message.toLowerCase().includes('network')) {
    console.log('🔄 Network error detected, will retry API calls...');
    // Il QueryClient gestirà automaticamente i retry
    return;
  }
  
  // Recovery per errori di storage
  if (error.message.toLowerCase().includes('storage')) {
    console.log('🔄 Storage error detected, clearing corrupted data...');
    try {
      // Pulisci storage corrotto
      localStorage.removeItem('corrupted_data');
      sessionStorage.clear();
    } catch (cleanupError) {
      console.error('Failed to cleanup storage:', cleanupError);
    }
    return;
  }
  
  // Recovery per errori di stato
  if (context.component?.includes('query') || error.message.includes('stale')) {
    console.log('🔄 Query error detected, invalidating cache...');
    // Sarà gestito dal QueryClient
    return;
  }
};

/**
 * Configura retry logic per React Query
 */
export const configureQueryRetry = (queryClient: QueryClient): void => {
  queryClient.setDefaultOptions({
    queries: {
      retry: (failureCount, error: any) => {
        // Non ritentare per errori 4xx (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        
        // Ritenta fino a 3 volte per errori di rete/server
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => {
        // Exponential backoff: 1s, 2s, 4s
        return Math.min(1000 * 2 ** attemptIndex, 30000);
      },
      onError: (error: any) => {
        const context: ErrorContext = {
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          component: 'react-query',
          action: 'query_error'
        };
        
        processError(error, context, true);
      }
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Non ritentare mutazioni per errori client
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        
        // Ritenta 1 volta per errori server
        return failureCount < 1;
      },
      onError: (error: any) => {
        const context: ErrorContext = {
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          component: 'react-query',
          action: 'mutation_error'
        };
        
        processError(error, context, true);
      }
    }
  });
};

/**
 * Utility per reporting manuale errori
 */
export const reportError = async (
  error: Error, 
  component?: string, 
  action?: string,
  additionalInfo?: Record<string, any>
): Promise<void> => {
  const context: ErrorContext = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    component,
    action,
    additionalInfo
  };
  
  await processError(error, context, true);
};

/**
 * Inizializza il sistema di error handling globale
 */
export const initializeGlobalErrorHandler = (): void => {
  console.log('🚨 Initializing Global Error Handler...');
  
  // Handler per errori JavaScript non catturati
  window.addEventListener('error', handleUnhandledError);
  
  // Handler per promise rejection non catturate
  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  
  // Pulisci error cache periodicamente
  setInterval(() => {
    const now = Date.now();
    for (const [key, value] of errorCache.entries()) {
      if (now - value.lastSeen > ERROR_CACHE_TIMEOUT) {
        errorCache.delete(key);
      }
    }
  }, ERROR_CACHE_TIMEOUT);
  
  console.log('✅ Global Error Handler initialized');
};

/**
 * Pulisce i listener quando l'app viene smontata
 */
export const cleanupGlobalErrorHandler = (): void => {
  window.removeEventListener('error', handleUnhandledError);
  window.removeEventListener('unhandledrejection', handleUnhandledRejection);
  errorCache.clear();
  
  console.log('🧹 Global Error Handler cleaned up');
};

/**
 * Hook per ottenere statistiche errori
 */
export const getErrorStats = async (): Promise<{
  totalErrors: number;
  recentErrors: number;
  errorsByLevel: Record<string, number>;
}> => {
  try {
    const { safeStorageParse } = await import('./safe-json');
    const errorLogs = safeStorageParse('haccp_global_errors', []);
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 ora
    
    const recentErrors = errorLogs.filter((error: ErrorReport) => 
      new Date(error.context.timestamp).getTime() > oneHourAgo
    );
    
    const errorsByLevel = errorLogs.reduce((acc: Record<string, number>, error: ErrorReport) => {
      acc[error.level] = (acc[error.level] || 0) + 1;
      return acc;
    }, {});
    
    return {
      totalErrors: errorLogs.length,
      recentErrors: recentErrors.length,
      errorsByLevel
    };
  } catch (error) {
    console.error('Failed to get error stats:', error);
    return { totalErrors: 0, recentErrors: 0, errorsByLevel: {} };
  }
};