/**
 * SMART LAZY LOADING WITH INTELLIGENT PREFETCHING
 * 
 * Ottimizza il lazy loading con prefetching intelligente basato su:
 * - Pattern di navigazione dell'utente
 * - Route più frequentemente usate
 * - Precaricamento in background per migliorare UX
 */

import { lazy, ComponentType } from 'react';

interface RouteMetrics {
  path: string;
  visits: number;
  lastVisit: number;
  avgTimeSpent: number;
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
}

interface SmartLazyOptions {
  prefetch?: boolean;
  priority?: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  preloadDelay?: number;
}

class SmartLazyLoadingManager {
  private routeMetrics: Map<string, RouteMetrics> = new Map();
  private preloadedComponents: Set<string> = new Set();
  private componentFactories: Map<string, () => Promise<any>> = new Map();

  constructor() {
    this.loadMetricsFromStorage();
    this.setupPrefetchScheduler();
  }

  /**
   * Crea un componente lazy con prefetching intelligente
   */
  smartLazy<T extends ComponentType<any>>(
    factory: () => Promise<{ default: T }>,
    routePath: string,
    options: SmartLazyOptions = {}
  ): ComponentType<T> {
    // Registra la factory per prefetching futuro
    this.componentFactories.set(routePath, factory);
    
    // Imposta priorità default basata su analytics
    const metrics = this.routeMetrics.get(routePath);
    const defaultPriority = this.calculatePriority(metrics);
    const priority = options.priority || defaultPriority;

    // Aggiorna metriche route
    this.updateRouteMetrics(routePath, priority);

    // Crea componente lazy standard
    const LazyComponent = lazy(factory);

    // Schedula prefetch se necessario
    if (options.prefetch !== false && priority === 'CRITICAL') {
      this.schedulePrefetch(routePath, factory, options.preloadDelay || 0);
    } else if (priority === 'HIGH') {
      this.schedulePrefetch(routePath, factory, options.preloadDelay || 2000);
    }

    return LazyComponent;
  }

  /**
   * Calcola priorità basata su metriche di utilizzo
   */
  private calculatePriority(metrics?: RouteMetrics): 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' {
    if (!metrics) return 'LOW';

    const { visits, lastVisit, avgTimeSpent } = metrics;
    const daysSinceLastVisit = (Date.now() - lastVisit) / (1000 * 60 * 60 * 24);

    // Route critiche: visite frequenti e recenti
    if (visits > 50 && daysSinceLastVisit < 1) return 'CRITICAL';
    if (visits > 20 && daysSinceLastVisit < 3) return 'HIGH';
    if (visits > 5 && daysSinceLastVisit < 7) return 'MEDIUM';
    
    return 'LOW';
  }

  /**
   * Aggiorna metriche di utilizzo route
   */
  private updateRouteMetrics(routePath: string, priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW') {
    const existing = this.routeMetrics.get(routePath);
    const now = Date.now();

    const updated: RouteMetrics = {
      path: routePath,
      visits: (existing?.visits || 0) + 1,
      lastVisit: now,
      avgTimeSpent: existing?.avgTimeSpent || 0,
      priority
    };

    this.routeMetrics.set(routePath, updated);
    this.saveMetricsToStorage();
  }

  /**
   * Schedula prefetch di un componente
   */
  private schedulePrefetch(
    routePath: string, 
    factory: () => Promise<any>, 
    delay: number = 0
  ) {
    if (this.preloadedComponents.has(routePath)) return;

    setTimeout(async () => {
      try {
        console.log(`🚀 Smart prefetching component: ${routePath}`);
        await factory();
        this.preloadedComponents.add(routePath);
        console.log(`✅ Prefetched successfully: ${routePath}`);
      } catch (error) {
        console.warn(`⚠️ Prefetch failed for ${routePath}:`, error);
      }
    }, delay);
  }

  /**
   * Setup scheduler per prefetch automatico
   */
  private setupPrefetchScheduler() {
    // Prefetch componenti critici dopo 1 secondo dall'app start
    setTimeout(() => {
      this.prefetchCriticalRoutes();
    }, 1000);

    // Prefetch componenti ad alta priorità dopo 5 secondi
    setTimeout(() => {
      this.prefetchHighPriorityRoutes();
    }, 5000);
  }

  /**
   * Prefetch route critiche
   */
  private prefetchCriticalRoutes() {
    const criticalRoutes = Array.from(this.routeMetrics.entries())
      .filter(([_, metrics]) => metrics.priority === 'CRITICAL')
      .map(([path]) => path);

    criticalRoutes.forEach(path => {
      const factory = this.componentFactories.get(path);
      if (factory && !this.preloadedComponents.has(path)) {
        this.schedulePrefetch(path, factory, 0);
      }
    });
  }

  /**
   * Prefetch route ad alta priorità
   */
  private prefetchHighPriorityRoutes() {
    const highPriorityRoutes = Array.from(this.routeMetrics.entries())
      .filter(([_, metrics]) => metrics.priority === 'HIGH')
      .map(([path]) => path);

    highPriorityRoutes.forEach(path => {
      const factory = this.componentFactories.get(path);
      if (factory && !this.preloadedComponents.has(path)) {
        this.schedulePrefetch(path, factory, 1000);
      }
    });
  }

  /**
   * Carica metriche da localStorage
   */
  private loadMetricsFromStorage() {
    try {
      const stored = localStorage.getItem('smartLazy:routeMetrics');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.routeMetrics = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.warn('Failed to load route metrics:', error);
    }
  }

  /**
   * Salva metriche su localStorage
   */
  private saveMetricsToStorage() {
    try {
      const metricsObj = Object.fromEntries(this.routeMetrics.entries());
      localStorage.setItem('smartLazy:routeMetrics', JSON.stringify(metricsObj));
    } catch (error) {
      console.warn('Failed to save route metrics:', error);
    }
  }

  /**
   * Ottieni statistiche prefetching
   */
  getStats() {
    return {
      totalRoutes: this.routeMetrics.size,
      preloadedComponents: this.preloadedComponents.size,
      criticalRoutes: Array.from(this.routeMetrics.values()).filter(m => m.priority === 'CRITICAL').length,
      highPriorityRoutes: Array.from(this.routeMetrics.values()).filter(m => m.priority === 'HIGH').length
    };
  }
}

// Istanza singleton del manager
export const smartLazyManager = new SmartLazyLoadingManager();

/**
 * Hook per lazy loading intelligente con prefetching
 */
export function useSmartLazy<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  routePath: string,
  options?: SmartLazyOptions
): ComponentType<T> {
  return smartLazyManager.smartLazy(factory, routePath, options);
}

/**
 * Utility per marcare visita route (da chiamare nei componenti)
 */
export function trackRouteVisit(routePath: string) {
  const manager = smartLazyManager;
  // @ts-ignore - Accesso privato per tracking
  manager.updateRouteMetrics(routePath, manager.routeMetrics.get(routePath)?.priority || 'MEDIUM');
}