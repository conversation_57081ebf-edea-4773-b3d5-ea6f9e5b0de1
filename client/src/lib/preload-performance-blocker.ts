/**
 * Preload Performance Blocker
 * Blocca le risorse critiche PRIMA che vengano caricate
 * Approccio: Override delle funzioni native PRIMA dell'inizializzazione di React
 */

// Lista delle risorse da bloccare durante il caricamento critico
const BLOCKED_RESOURCES = [
  'charts.js',
  'admin.js',
  '/api/product-labels',
  '/api/containers',
  '/api/activity-logs',
  '/api/suppliers',
  'fonts/',
  '.woff',
  '.woff2',
  '/assets/charts',
  '/assets/admin'
];

// Tempo massimo per il percorso critico (2 secondi)
const CRITICAL_PATH_MAX_DURATION = 2000;

class PreloadPerformanceBlocker {
  private startTime = performance.now();
  private criticalPathComplete = false;
  private originalFetch = window.fetch;
  private blockedRequests: string[] = [];

  constructor() {
    this.blockResourcesImmediately();
    this.setupCriticalPathTimer();
    console.log('[PRELOAD-BLOCKER] Performance blocker initialized immediately');
  }

  private blockResourcesImmediately(): void {
    // Salva il binding corretto di fetch
    const originalFetch = this.originalFetch.bind(window);
    
    // Override fetch IMMEDIATAMENTE con binding corretto
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      if (!blocker.criticalPathComplete && blocker.shouldBlockResource(url)) {
        console.log(`[PRELOAD-BLOCKER] BLOCKED: ${url}`);
        blocker.blockedRequests.push(url);
        
        // Ritorna una risposta fake immediata
        return new Response(
          JSON.stringify(url.includes('/api/') ? [] : ''),
          {
            status: 200,
            headers: {
              'Content-Type': url.includes('/api/') ? 'application/json' : 'text/plain'
            }
          }
        );
      }
      
      // Passa al fetch originale per risorse critiche con binding corretto
      return originalFetch(input, init);
    };

    // Override anche XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      const urlStr = typeof url === 'string' ? url : url.toString();
      
      if (!blocker.criticalPathComplete && blocker.shouldBlockResource(urlStr)) {
        console.log(`[PRELOAD-BLOCKER] BLOCKED XHR: ${urlStr}`);
        blocker.blockedRequests.push(urlStr);
        return; // Non chiamare l'originale - blocca completamente
      }
      
      return originalXHROpen.call(this, method, url, args[0], args[1]);
    };

    // Blocca anche il caricamento dinamico dei moduli JavaScript
    this.blockDynamicImports();
  }

  private shouldBlockResource(url: string): boolean {
    return BLOCKED_RESOURCES.some(pattern => url.includes(pattern));
  }

  private blockDynamicImports(): void {
    // Override del createElement per bloccare script non critici
    const originalCreateElement = document.createElement;
    
    document.createElement = function(tagName: string) {
      const element = originalCreateElement.call(this, tagName);
      
      if (tagName.toLowerCase() === 'script' && element instanceof HTMLScriptElement) {
        const originalSrcDescriptor = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src');
        
        Object.defineProperty(element, 'src', {
          get: function() {
            return originalSrcDescriptor?.get?.call(this) || '';
          },
          set: function(value: string) {
            if (!blocker.criticalPathComplete && blocker.shouldBlockResource(value)) {
              console.log(`[PRELOAD-BLOCKER] BLOCKED SCRIPT: ${value}`);
              blocker.blockedRequests.push(value);
              
              // Ritarda il caricamento dello script
              setTimeout(() => {
                if (blocker.criticalPathComplete && originalSrcDescriptor?.set) {
                  originalSrcDescriptor.set.call(this, value);
                }
              }, 2500); // Dopo il completamento del critical path
              return;
            }
            
            if (originalSrcDescriptor?.set) {
              originalSrcDescriptor.set.call(this, value);
            }
          },
          configurable: true
        });
      }
      
      return element;
    };
  }

  private setupCriticalPathTimer(): void {
    // Timer per sbloccare automaticamente dopo 2 secondi
    setTimeout(() => {
      this.completeCriticalPath();
    }, CRITICAL_PATH_MAX_DURATION);

    // Controlla anche marker DOM ogni 100ms
    const checkInterval = setInterval(() => {
      if (this.checkDOMMarkers()) {
        clearInterval(checkInterval);
        this.completeCriticalPath();
      }
    }, 100);
  }

  private checkDOMMarkers(): boolean {
    // Verifica se i marker DOM indicano completamento
    const authComplete = document.querySelector('[data-auth="complete"]');
    const uiReady = document.querySelector('[data-ui="ready"]');
    
    return Boolean(authComplete || uiReady);
  }

  private completeCriticalPath(): void {
    if (this.criticalPathComplete) return;
    
    this.criticalPathComplete = true;
    const duration = performance.now() - this.startTime;
    
    console.log(`[PRELOAD-BLOCKER] Critical path complete after ${duration.toFixed(0)}ms`);
    console.log(`[PRELOAD-BLOCKER] Blocked ${this.blockedRequests.length} resources:`, this.blockedRequests);

    // Ripristina il fetch originale con binding corretto
    window.fetch = this.originalFetch.bind(window);

    // Carica gradualmente le risorse bloccate
    this.loadBlockedResources();
  }

  private loadBlockedResources(): void {
    this.blockedRequests.forEach((url, index) => {
      setTimeout(() => {
        if (url.includes('/api/')) {
          // Ricarica API endpoints
          this.originalFetch(url, {
            credentials: 'include',
            headers: { 'Accept': 'application/json' }
          }).catch(() => {
            // Fail silenzioso
          });
        }
        console.log(`[PRELOAD-BLOCKER] Unblocked: ${url}`);
      }, index * 300); // Intervalli di 300ms
    });
  }

  public getStats(): any {
    return {
      criticalPathComplete: this.criticalPathComplete,
      blockedCount: this.blockedRequests.length,
      blockedResources: this.blockedRequests,
      duration: this.criticalPathComplete 
        ? performance.now() - this.startTime 
        : 'In progress'
    };
  }
}

// Inizializza IMMEDIATAMENTE - prima di tutto il resto
const blocker = new PreloadPerformanceBlocker();

// Export per debug
export { blocker as preloadPerformanceBlocker };