
/**
 * Sistema per garantire un'esperienza nativa su dispositivi mobili
 */

export class NativeExperience {
  private static isInitialized = false;

  /**
   * Inizializza tutte le ottimizzazioni per esperienza nativa
   */
  static init() {
    if (this.isInitialized) return;
    
    this.setupViewportOptimization();
    this.setupTouchOptimization();
    this.setupScrollOptimization();
    this.setupPerformanceOptimization();
    this.setupStatusBarIntegration();
    
    this.isInitialized = true;
    console.log('🚀 Esperienza nativa inizializzata');
  }

  /**
   * Ottimizzazione viewport per dispositivi mobili
   */
  private static setupViewportOptimization() {
    // Previeni zoom accidentale
    document.addEventListener('gesturestart', (e) => { e.preventDefault(); });
    document.addEventListener('gesturechange', (e) => { e.preventDefault(); });
    
    // Viewport dinamico per notch e safe areas
    const updateViewport = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      
      // Safe area per iPhone X e successivi
      if (CSS.supports('padding: env(safe-area-inset-top)')) {
        document.body.style.paddingTop = 'env(safe-area-inset-top)';
        document.body.style.paddingBottom = 'env(safe-area-inset-bottom)';
      }
    };
    
    window.addEventListener('resize', updateViewport);
    window.addEventListener('orientationchange', updateViewport);
    updateViewport();
  }

  /**
   * Ottimizzazione touch per risposta immediata
   */
  private static setupTouchOptimization() {
    // Rimuovi delay touch su iOS
    document.body.style.touchAction = 'manipulation';
    
    // Feedback visivo istantaneo
    const style = document.createElement('style');
    style.textContent = `
      .native-touch {
        -webkit-tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
      }
      
      .native-button {
        transform: scale(1);
        transition: transform 0.1s ease-out;
      }
      
      .native-button:active {
        transform: scale(0.95);
      }
      
      .native-ripple {
        position: relative;
        overflow: hidden;
      }
      
      .native-ripple::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
      }
      
      .native-ripple:active::before {
        width: 300px;
        height: 300px;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Ottimizzazione scroll nativo
   */
  private static setupScrollOptimization() {
    // Momentum scrolling iOS
    (document.body.style as any).webkitOverflowScrolling = 'touch';
    
    // Smooth scrolling nativo
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Ottimizza scroll performance
    const scrollElements = document.querySelectorAll('.scroll-container');
    scrollElements.forEach(el => {
      (el as HTMLElement).style.transform = 'translateZ(0)';
      (el as HTMLElement).style.willChange = 'scroll-position';
    });
  }

  /**
   * Ottimizzazioni performance runtime
   */
  private static setupPerformanceOptimization() {
    // Prefetch DNS per risorse critiche
    const prefetchDNS = ['fonts.googleapis.com', 'fonts.gstatic.com'];
    prefetchDNS.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });

    // Preload risorse critiche
    const preloadResources = [
      { href: '/icons/icon-192x192.png', as: 'image' },
      { href: '/manifest.json', as: 'manifest' }
    ];
    
    preloadResources.forEach(({ href, as }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;
      document.head.appendChild(link);
    });
  }

  /**
   * Integrazione status bar nativa
   */
  private static setupStatusBarIntegration() {
    // Meta tag per status bar iOS
    const statusBarMeta = document.createElement('meta');
    statusBarMeta.name = 'apple-mobile-web-app-status-bar-style';
    statusBarMeta.content = 'default';
    document.head.appendChild(statusBarMeta);

    // Theme color dinamico
    const updateThemeColor = () => {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const themeColor = isDark ? '#1a1a1a' : '#ffffff';
      
      let meta = document.querySelector('meta[name="theme-color"]');
      if (!meta) {
        meta = document.createElement('meta');
        (meta as HTMLMetaElement).name = 'theme-color';
        document.head.appendChild(meta);
      }
      (meta as HTMLMetaElement).content = themeColor;
    };
    
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateThemeColor);
    updateThemeColor();
  }

  /**
   * Applica classi CSS per esperienza nativa
   */
  static applyNativeClasses(element: HTMLElement) {
    element.classList.add('native-touch', 'native-button', 'native-ripple');
  }

  /**
   * Feedback aptico leggero
   */
  static lightHaptic() {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  }

  /**
   * Feedback aptico medio
   */
  static mediumHaptic() {
    if ('vibrate' in navigator) {
      navigator.vibrate([10, 10, 10]);
    }
  }

  /**
   * Feedback aptico forte
   */
  static strongHaptic() {
    if ('vibrate' in navigator) {
      navigator.vibrate([20, 10, 20]);
    }
  }
}

// Auto-init se siamo su mobile
if (typeof window !== 'undefined' && /Mobi|Android/i.test(navigator.userAgent)) {
  document.addEventListener('DOMContentLoaded', () => {
    NativeExperience.init();
  });
}

export default NativeExperience;
