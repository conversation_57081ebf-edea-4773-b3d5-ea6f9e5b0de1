/**
 * Critical CSS Optimizer
 * Target: Reduce First Contentful Paint by inlining critical CSS
 * Eliminates render-blocking CSS for faster initial render
 */

export class CriticalCSSOptimizer {
  private static instance: CriticalCSSOptimizer;
  private criticalCSSInlined = false;

  private constructor() {
    this.initializeCriticalCSSOptimization();
  }

  public static getInstance(): CriticalCSSOptimizer {
    if (!CriticalCSSOptimizer.instance) {
      CriticalCSSOptimizer.instance = new CriticalCSSOptimizer();
    }
    return CriticalCSSOptimizer.instance;
  }

  private initializeCriticalCSSOptimization(): void {
    // Inline critical CSS immediately
    this.inlineCriticalCSS();
    
    // Defer non-critical CSS
    this.deferNonCriticalCSS();
    
    // Remove unused CSS
    this.removeUnusedCSS();
    
    console.log('[CRITICAL-CSS-OPTIMIZER] CSS optimization applied');
  }

  private inlineCriticalCSS(): void {
    if (this.criticalCSSInlined) return;

    // Critical CSS for above-the-fold content
    const criticalCSS = `
      /* Reset and base styles */
      * { box-sizing: border-box; }
      html, body { margin: 0; padding: 0; min-height: 100vh; }
      body { font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
      
      /* Critical layout */
      .container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .justify-center { justify-content: center; }
      .items-center { align-items: center; }
      .text-center { text-align: center; }
      
      /* Critical header styles */
      .header { position: sticky; top: 0; z-index: 50; background: white; border-bottom: 1px solid #e5e7eb; }
      .logo { height: 2.5rem; width: auto; }
      
      /* Critical button styles */
      .btn { display: inline-flex; align-items: center; justify-content: center; 
             padding: 0.5rem 1rem; border-radius: 0.375rem; font-weight: 500;
             transition: all 0.2s; cursor: pointer; border: none; }
      .btn-primary { background: #3b82f6; color: white; }
      .btn-primary:hover { background: #2563eb; }
      
      /* Critical navigation */
      .bottom-nav { position: fixed; bottom: 0; left: 0; right: 0; z-index: 50;
                   background: white; border-top: 1px solid #e5e7eb; }
      
      /* Loading states */
      .loading { display: flex; align-items: center; justify-content: center; 
                min-height: 50vh; }
      .spinner { width: 2rem; height: 2rem; border: 2px solid #f3f4f6;
                border-top: 2px solid #3b82f6; border-radius: 50%;
                animation: spin 1s linear infinite; }
      @keyframes spin { to { transform: rotate(360deg); } }
      
      /* Critical form styles */
      .form-group { margin-bottom: 1rem; }
      .form-label { display: block; font-weight: 500; margin-bottom: 0.25rem; }
      .form-input { width: 100%; padding: 0.5rem; border: 1px solid #d1d5db;
                   border-radius: 0.375rem; }
      .form-input:focus { outline: none; border-color: #3b82f6; }
      
      /* Critical typography */
      h1, h2, h3 { font-weight: 600; line-height: 1.25; }
      h1 { font-size: 1.875rem; }
      h2 { font-size: 1.5rem; }
      h3 { font-size: 1.25rem; }
      
      /* Critical utilities */
      .hidden { display: none; }
      .sr-only { position: absolute; width: 1px; height: 1px; 
                 padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0);
                 white-space: nowrap; border: 0; }
    `;

    // Inject critical CSS immediately
    const style = document.createElement('style');
    style.innerHTML = criticalCSS;
    style.setAttribute('data-critical', 'true');
    document.head.insertBefore(style, document.head.firstChild);
    
    this.criticalCSSInlined = true;
    console.log('[CRITICAL-CSS-OPTIMIZER] Critical CSS inlined');
  }

  private deferNonCriticalCSS(): void {
    // Find all stylesheets and make them non-render-blocking
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    
    stylesheets.forEach(link => {
      const linkElement = link as HTMLLinkElement;
      
      // Skip if already optimized
      if (linkElement.hasAttribute('data-optimized')) return;
      
      // Make stylesheet non-render-blocking
      linkElement.media = 'print';
      linkElement.onload = () => {
        linkElement.media = 'all';
      };
      
      linkElement.setAttribute('data-optimized', 'true');
    });
    
    console.log('[CRITICAL-CSS-OPTIMIZER] Non-critical CSS deferred');
  }

  private removeUnusedCSS(): void {
    // Remove unused CSS classes after load with proper fallback
    const idleCallback = window.requestIdleCallback || ((callback: () => void) => {
      setTimeout(callback, 1);
    });
    
    idleCallback(() => {
      this.cleanupUnusedCSS();
    });
  }

  private cleanupUnusedCSS(): void {
    // This would typically use a tool like PurgeCSS
    // For now, we'll do basic cleanup of obvious unused styles
    const stylesheets = document.styleSheets;
    
    for (let i = 0; i < stylesheets.length; i++) {
      try {
        const stylesheet = stylesheets[i];
        if (!stylesheet.href || stylesheet.href.includes('critical')) continue;
        
        // Basic unused class detection and removal would go here
        // This is a simplified version for demonstration
        console.log(`[CRITICAL-CSS-OPTIMIZER] Analyzed stylesheet: ${stylesheet.href}`);
        
      } catch (error) {
        // Cross-origin stylesheets can't be accessed
        continue;
      }
    }
  }

  public getCriticalCSSStats(): any {
    return {
      criticalCSSInlined: this.criticalCSSInlined,
      stylesheetCount: document.querySelectorAll('link[rel="stylesheet"]').length,
      optimizedCount: document.querySelectorAll('link[data-optimized]').length
    };
  }
}

// Initialize immediately for maximum performance benefit
export const criticalCSSOptimizer = CriticalCSSOptimizer.getInstance();