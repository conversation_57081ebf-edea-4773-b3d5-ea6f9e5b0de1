/**
 * Sistema di versioning semplificato per la PWA
 * Utilizza versioni semantic incrementali controllate
 */

// Versione dell'applicazione (major.minor.patch)
const APP_VERSION = "1.2.15";
// Build info: 2025-06-04

// Build number per development (cambia ogni 10 minuti)
const DEV_BUILD_NUMBER = Math.floor(Date.now() / 600000) % 100;

// Funzione per ottenere la versione corrente
export function getCurrentVersion(): string {
  // In produzione, usa la versione dal build time se disponibile
  const envVersion = import.meta.env.VITE_APP_VERSION;
  if (envVersion && !import.meta.env.DEV) {
    return envVersion;
  }
  
  // In development, aggiungi suffisso build
  if (import.meta.env.DEV) {
    return `${APP_VERSION}-dev.${DEV_BUILD_NUMBER.toString().padStart(2, '0')}`;
  }
  
  // Fallback: versione base
  return APP_VERSION;
}

// Funzione per incrementare la versione per deployment
export function incrementVersion(type: 'patch' | 'minor' | 'major' = 'patch'): string {
  const parts = APP_VERSION.split('.').map(Number);
  
  switch (type) {
    case 'major':
      parts[0]++;
      parts[1] = 0;
      parts[2] = 0;
      break;
    case 'minor':
      parts[1]++;
      parts[2] = 0;
      break;
    case 'patch':
      parts[2]++;
      break;
  }
  
  return parts.join('.');
}

// Funzione per confrontare versioni semantic
export function isNewerVersion(current: string, stored: string): boolean {
  if (!stored) return true;
  if (current === stored) return false;
  
  // Rimuovi suffissi dev se presenti
  const cleanCurrent = current.replace(/-dev\.\d+$/, '');
  const cleanStored = stored.replace(/-dev\.\d+$/, '');
  
  const currentParts = cleanCurrent.split('.').map(part => parseInt(part, 10) || 0);
  const storedParts = cleanStored.split('.').map(part => parseInt(part, 10) || 0);
  
  // Confronta major, minor, patch
  for (let i = 0; i < 3; i++) {
    const currentPart = currentParts[i] || 0;
    const storedPart = storedParts[i] || 0;
    
    if (currentPart > storedPart) return true;
    if (currentPart < storedPart) return false;
  }
  
  // Se le versioni base sono uguali, considera versioni dev come più vecchie
  if (current.includes('-dev') && !stored.includes('-dev')) {
    return false;
  }
  if (!current.includes('-dev') && stored.includes('-dev')) {
    return true;
  }
  
  return false;
}

// Funzione per ottenere data di build leggibile
export function getBuildDate(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  return `${year}.${month}.${day}-${hours}:${minutes}`;
}

// Funzione per salvare la versione corrente
export function saveCurrentVersion(): void {
  const version = getCurrentVersion();
  const buildDate = getBuildDate();
  
  localStorage.setItem('app_version', version);
  localStorage.setItem('app_build_date', buildDate);
  
  console.log(`Versione applicazione salvata: ${version} (${buildDate})`);
}

// Funzione per controllare se c'è una nuova versione
export function checkForNewVersion(): boolean {
  const currentVersion = getCurrentVersion();
  const storedVersion = localStorage.getItem('app_version');
  
  console.log(`Versione corrente: ${currentVersion}`);
  console.log(`Versione salvata: ${storedVersion || 'nessuna'}`);
  
  return isNewerVersion(currentVersion, storedVersion || '');
}

// Funzione per ottenere informazioni complete sulla versione
export function getVersionInfo() {
  return {
    version: getCurrentVersion(),
    buildDate: getBuildDate(),
    storedVersion: localStorage.getItem('app_version'),
    storedBuildDate: localStorage.getItem('app_build_date'),
    isProduction: !import.meta.env.DEV
  };
}