/**
 * Gestore aggiornamenti PWA - forza l'aggiornamento delle app installate
 */

import { toast } from "@/hooks/use-toast";
import { getCurrentVersion, saveCurrentVersion } from './version';

export class PWAUpdateManager {
  private registration: ServiceWorkerRegistration | null = null;
  private hasUpdated = false;

  async initialize() {
    if ('serviceWorker' in navigator) {
      try {
        // Registra o ottieni la registrazione esistente
        this.registration = await navigator.serviceWorker.getRegistration() || null;
        
        if (!this.registration) {
          // Se non c'è una registrazione, creala
          this.registration = await navigator.serviceWorker.register('/service-worker.js');
          console.log('🔧 Service Worker registrato per la prima volta');
        }

        // Ascolta per nuovi service worker
        this.registration.addEventListener('updatefound', () => {
          console.log('🆕 Nuovo Service Worker trovato!');
          this.handleUpdate();
        });

        // Controlla immediatamente per aggiornamenti
        await this.checkForUpdates();
        
      } catch (error) {
        console.error('❌ Errore inizializzazione PWA Update Manager:', error);
      }
    }
  }

  async checkForUpdates() {
    if (!this.registration) return;

    try {
      console.log('🔍 Controllo aggiornamenti PWA...');
      
      // Forza il controllo di aggiornamenti
      await this.registration.update();
      
      // Se c'è un worker in attesa, attivalo
      if (this.registration.waiting && !this.hasUpdated) {
        console.log('⏳ Service Worker in attesa rilevato, attivazione...');
        this.activateWaitingWorker();
      }
      
    } catch (error) {
      console.error('❌ Errore controllo aggiornamenti:', error);
    }
  }

  private handleUpdate() {
    if (!this.registration || this.hasUpdated) return;

    const newWorker = this.registration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        console.log('✅ Nuovo Service Worker installato!');
        this.showUpdatePrompt();
      }
    });
  }

  private showUpdatePrompt() {
    if (this.hasUpdated) return;

    const currentVersion = getCurrentVersion();
    
    toast({
      title: '🚀 Aggiornamento disponibile!',
      description: `Nuova versione ${currentVersion} pronta. L'app verrà aggiornata automaticamente.`,
      duration: 5000,
    });

    // Attiva automaticamente l'aggiornamento dopo un breve delay
    setTimeout(() => {
      this.activateWaitingWorker();
    }, 2000);
  }

  private activateWaitingWorker() {
    if (!this.registration?.waiting || this.hasUpdated) return;

    this.hasUpdated = true;
    
    console.log('🔄 Attivazione nuovo Service Worker...');
    
    // Ascolta quando il nuovo worker prende il controllo
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('✅ Nuovo Service Worker attivo!');
      saveCurrentVersion();
      
      toast({
        title: '✅ Aggiornamento completato!',
        description: 'App aggiornata con successo. Ricaricamento...',
        duration: 2000,
      });

      // Ricarica la pagina per applicare l'aggiornamento
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    });

    // Invia il messaggio per attivare il worker
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }

  // Metodo pubblico per forzare un aggiornamento manuale
  async forceUpdate() {
    console.log('🔧 Forzatura aggiornamento manuale...');
    
    try {
      // Cancella tutte le cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('🗑️ Cache cancellate');
      }

      // Disregistra il service worker
      if (this.registration) {
        await this.registration.unregister();
        console.log('🗑️ Service Worker disregistrato');
      }

      // Salva la nuova versione
      saveCurrentVersion();

      toast({
        title: '🔄 Forzatura aggiornamento',
        description: 'App forzata al ricaricamento con la versione più recente...',
        duration: 3000,
      });

      // Ricarica completamente
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('❌ Errore forzatura aggiornamento:', error);
      // Fallback: ricarica semplice
      window.location.reload();
    }
  }
}

// Istanza singleton
export const pwaUpdateManager = new PWAUpdateManager();