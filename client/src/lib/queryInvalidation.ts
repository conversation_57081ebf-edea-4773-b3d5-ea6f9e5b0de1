/**
 * Sistema di gestione intelligente delle invalidazioni di query
 * Previene loop di aggiornamento con debounce e batch operations
 */

import { QueryClient } from "@tanstack/react-query";

interface InvalidationConfig {
  queryKey: string[];
  exact?: boolean;
  delay?: number;
}

class QueryInvalidationManager {
  private queryClient: QueryClient;
  private pendingInvalidations = new Map<string, NodeJS.Timeout>();
  private batchTimeouts = new Map<string, NodeJS.Timeout>();
  
  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    
    // Ensure proper binding
    this.invalidateWithDebounce = this.invalidateWithDebounce.bind(this);
    this.batchInvalidate = this.batchInvalidate.bind(this);
    this.invalidateImmediate = this.invalidateImmediate.bind(this);
  }
  
  /**
   * Invalida query con debounce per prevenire chiamate multiple
   */
  invalidateWithDebounce(config: InvalidationConfig) {
    const key = JSON.stringify(config.queryKey);
    const delay = config.delay || 100; // 100ms di default
    
    // Cancella invalidazione precedente se esistente
    if (this.pendingInvalidations.has(key)) {
      clearTimeout(this.pendingInvalidations.get(key)!);
    }
    
    // Schedula nuova invalidazione
    const timeout = setTimeout(() => {
      this.queryClient.invalidateQueries({
        queryKey: config.queryKey,
        exact: config.exact || true
      });
      this.pendingInvalidations.delete(key);
    }, delay);
    
    this.pendingInvalidations.set(key, timeout);
  }
  
  /**
   * Batch di invalidazioni con delay ridotto
   */
  batchInvalidate(configs: InvalidationConfig[], batchDelay = 150) {
    const batchKey = configs.map(c => JSON.stringify(c.queryKey)).join('|');
    
    // Cancella batch precedente se esistente
    if (this.batchTimeouts.has(batchKey)) {
      clearTimeout(this.batchTimeouts.get(batchKey)!);
    }
    
    // Schedula batch di invalidazioni
    const timeout = setTimeout(() => {
      configs.forEach(config => {
        this.queryClient.invalidateQueries({
          queryKey: config.queryKey,
          exact: config.exact || true
        });
      });
      this.batchTimeouts.delete(batchKey);
    }, batchDelay);
    
    this.batchTimeouts.set(batchKey, timeout);
  }
  
  /**
   * Invalidazione immediata per casi critici
   */
  invalidateImmediate(config: InvalidationConfig) {
    this.queryClient.invalidateQueries({
      queryKey: config.queryKey,
      exact: config.exact || true
    });
  }
  
  /**
   * Cancella tutte le invalidazioni pending
   */
  cancelAllPending() {
    this.pendingInvalidations.forEach(timeout => clearTimeout(timeout));
    this.pendingInvalidations.clear();
    
    this.batchTimeouts.forEach(timeout => clearTimeout(timeout));
    this.batchTimeouts.clear();
  }
}

export default QueryInvalidationManager;