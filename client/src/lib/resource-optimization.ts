/**
 * Resource Optimization System
 * Target: Reduce slow resource loading (API calls 1000ms+, assets 1500ms+)
 * Implements aggressive resource caching and optimization
 */

import { clientLogger } from './clientLogger';

interface ResourceMetrics {
  url: string;
  loadTime: number;
  size?: number;
  cached: boolean;
  timestamp: number;
}

export class ResourceOptimizer {
  private static instance: ResourceOptimizer;
  private resourceMetrics: ResourceMetrics[] = [];
  private preloadCache = new Map<string, any>();
  private slowResourceThreshold = 500; // ms

  private constructor() {
    this.initializeResourceOptimization();
    clientLogger.info('ResourceOptimizer initialized for aggressive resource optimization');
  }

  public static getInstance(): ResourceOptimizer {
    if (!ResourceOptimizer.instance) {
      ResourceOptimizer.instance = new ResourceOptimizer();
    }
    return ResourceOptimizer.instance;
  }

  private initializeResourceOptimization(): void {
    // Override fetch for monitoring and optimization
    this.setupFetchMonitoring();
    
    // Preload critical resources immediately
    this.preloadCriticalResources();
    
    // Setup image lazy loading optimization
    this.optimizeImageLoading();
    
    // Setup service worker for caching
    this.setupServiceWorkerCaching();
  }

  private setupFetchMonitoring(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const [url] = args;
      const startTime = performance.now();
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        // Record metrics
        this.recordResourceMetric({
          url: url.toString(),
          loadTime,
          cached: response.headers.get('x-cache') === 'HIT',
          timestamp: Date.now()
        });
        
        // Log slow resources
        if (loadTime > this.slowResourceThreshold) {
          clientLogger.warn(`Slow resource: ${url} took ${loadTime.toFixed(0)}ms`);
        }
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        this.recordResourceMetric({
          url: url.toString(),
          loadTime,
          cached: false,
          timestamp: Date.now()
        });
        
        throw error;
      }
    };
  }

  private preloadCriticalResources(): void {
    const criticalResources = [
      '/api/auth/me',
      '/api/containers',
      '/api/product-labels',
      '/logo.png'
    ];

    criticalResources.forEach(async (resource) => {
      try {
        const startTime = performance.now();
        const response = await fetch(resource, {
          method: 'GET',
          credentials: 'include',
          cache: 'force-cache'
        });
        
        if (response.ok) {
          const data = resource.startsWith('/api/') ? await response.json() : await response.blob();
          this.preloadCache.set(resource, data);
          
          const loadTime = performance.now() - startTime;
          clientLogger.debug(`Preloaded critical resource: ${resource} (${loadTime.toFixed(0)}ms)`);
        }
      } catch (error) {
        clientLogger.warn(`Failed to preload critical resource: ${resource}`, { error: String(error) });
      }
    });
  }

  private optimizeImageLoading(): void {
    // Implement intersection observer for lazy loading
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            const startTime = performance.now();
            
            img.src = img.dataset.src;
            img.onload = () => {
              const loadTime = performance.now() - startTime;
              this.recordResourceMetric({
                url: img.src,
                loadTime,
                cached: false,
                timestamp: Date.now()
              });
              
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            };
          }
        }
      });
    }, {
      rootMargin: '50px'
    });

    // Observe all lazy images
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }

  private setupServiceWorkerCaching(): void {
    if ('serviceWorker' in navigator && import.meta.env.PROD) {
      navigator.serviceWorker.register('/sw.js').then(registration => {
        clientLogger.debug('Service Worker registered for resource caching');
        
        // Send cache configuration to service worker
        registration.active?.postMessage({
          type: 'CACHE_CONFIG',
          config: {
            apiCache: {
              ttl: 300000, // 5 minutes
              maxEntries: 100
            },
            staticCache: {
              ttl: 86400000, // 24 hours
              maxEntries: 200
            }
          }
        });
      }).catch(error => {
        clientLogger.warn('Service Worker registration failed', { error: String(error) });
      });
    }
  }

  private recordResourceMetric(metric: ResourceMetrics): void {
    this.resourceMetrics.push(metric);
    
    // Keep only recent metrics (last 1000 or 10 minutes)
    const maxAge = 10 * 60 * 1000; // 10 minutes
    const now = Date.now();
    this.resourceMetrics = this.resourceMetrics
      .filter(m => now - m.timestamp < maxAge)
      .slice(-1000);
  }

  /**
   * Get performance statistics for slow resources
   */
  public getResourcePerformanceStats(): {
    slowResources: Array<{ url: string; avgLoadTime: number; count: number }>;
    totalRequests: number;
    averageLoadTime: number;
    cacheHitRate: number;
    recommendations: string[];
  } {
    const recentMetrics = this.resourceMetrics.filter(
      m => Date.now() - m.timestamp < 300000 // Last 5 minutes
    );

    // Group by URL and calculate averages
    const urlStats = new Map<string, { totalTime: number; count: number; cached: number }>();
    
    recentMetrics.forEach(metric => {
      const existing = urlStats.get(metric.url) || { totalTime: 0, count: 0, cached: 0 };
      existing.totalTime += metric.loadTime;
      existing.count += 1;
      if (metric.cached) existing.cached += 1;
      urlStats.set(metric.url, existing);
    });

    // Find slow resources
    const slowResources = Array.from(urlStats.entries())
      .map(([url, stats]) => ({
        url,
        avgLoadTime: stats.totalTime / stats.count,
        count: stats.count
      }))
      .filter(stat => stat.avgLoadTime > this.slowResourceThreshold)
      .sort((a, b) => b.avgLoadTime - a.avgLoadTime);

    const totalRequests = recentMetrics.length;
    const averageLoadTime = totalRequests > 0 
      ? recentMetrics.reduce((sum, m) => sum + m.loadTime, 0) / totalRequests 
      : 0;
    
    const cachedRequests = recentMetrics.filter(m => m.cached).length;
    const cacheHitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

    const recommendations: string[] = [];
    
    if (slowResources.length > 0) {
      recommendations.push(`${slowResources.length} slow resources detected. Consider caching or optimization.`);
    }
    
    if (cacheHitRate < 50) {
      recommendations.push('Low cache hit rate. Implement more aggressive caching.');
    }
    
    if (averageLoadTime > 300) {
      recommendations.push('High average load time. Review resource sizes and compression.');
    }

    return {
      slowResources: slowResources.slice(0, 10), // Top 10
      totalRequests,
      averageLoadTime,
      cacheHitRate,
      recommendations
    };
  }

  /**
   * Apply immediate optimizations based on detected issues
   */
  public applyImmediateOptimizations(): void {
    const stats = this.getResourcePerformanceStats();
    
    // Apply optimizations based on detected issues
    stats.slowResources.forEach(resource => {
      if (resource.url.includes('/api/')) {
        // API optimization
        this.optimizeAPIEndpoint(resource.url);
      } else if (resource.url.includes('.js') || resource.url.includes('.css')) {
        // Asset optimization
        this.optimizeAsset(resource.url);
      }
    });

    clientLogger.info(`Applied optimizations for ${stats.slowResources.length} slow resources`);
  }

  private optimizeAPIEndpoint(url: string): void {
    // Implement API-specific optimizations
    if (url.includes('product-labels') || url.includes('containers')) {
      // These endpoints can be cached more aggressively
      const cacheKey = `optimized-${url}`;
      
      // Check if we have cached data
      if (this.preloadCache.has(cacheKey)) {
        clientLogger.debug(`Using cached data for optimized endpoint: ${url}`);
      }
    }
  }

  private optimizeAsset(url: string): void {
    // Asset-specific optimizations
    if (url.includes('charts.js') || url.includes('admin.js')) {
      // These heavy assets should be loaded with lower priority
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
      
      clientLogger.debug(`Prefetching heavy asset: ${url}`);
    }
  }

  /**
   * Get cached resource if available
   */
  public getCachedResource(url: string): any | null {
    return this.preloadCache.get(url) || null;
  }

  /**
   * Clear old cached resources to free memory
   */
  public clearOldCache(): void {
    // Clear cache entries older than 10 minutes
    const maxAge = 10 * 60 * 1000;
    const now = Date.now();
    
    let clearedCount = 0;
    for (const [key, value] of this.preloadCache.entries()) {
      if (value.timestamp && (now - value.timestamp > maxAge)) {
        this.preloadCache.delete(key);
        clearedCount++;
      }
    }
    
    if (clearedCount > 0) {
      clientLogger.debug(`Cleared ${clearedCount} old cache entries`);
    }
  }
}

// Export singleton instance
export const resourceOptimizer = ResourceOptimizer.getInstance();

// Initialize optimization on load
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    resourceOptimizer.applyImmediateOptimizations();
  });
  
  // Periodic cache cleanup
  setInterval(() => {
    resourceOptimizer.clearOldCache();
  }, 5 * 60 * 1000); // Every 5 minutes
}