/**
 * SECURE CLIENT-SIDE STORAGE SYSTEM
 * Addresses the insecure token storage vulnerability
 */

// List of forbidden keys that should never be stored in localStorage
const FORBIDDEN_STORAGE_KEYS = new Set([
  'token', 'jwt', 'access_token', 'refresh_token', 'session_token',
  'auth_token', 'bearer_token', 'api_key', 'secret', 'password',
  'user_credentials', 'auth_data', 'session_data', 'csrf_token'
]);

// Patterns that indicate sensitive data
const SENSITIVE_PATTERNS = [
  /^[A-Za-z0-9+/]{20,}={0,2}$/, // Base64 pattern (likely token)
  /^[a-f0-9]{32,}$/i, // Hex pattern (likely hash/token)
  /bearer\s+/i, // Bearer token
  /^sk-/, // API key pattern
  /^jwt\./, // JWT pattern
];

interface StorageValidationResult {
  allowed: boolean;
  reason?: string;
  secureAlternative?: string;
}

class SecureStorageManager {
  /**
   * Validates whether a key-value pair can be safely stored in localStorage
   */
  static validateStorageOperation(key: string, value: any): StorageValidationResult {
    const lowerKey = key.toLowerCase();
    
    // Check if key is explicitly forbidden
    if (FORBIDDEN_STORAGE_KEYS.has(lowerKey)) {
      return {
        allowed: false,
        reason: `Direct storage of sensitive key '${key}' is not allowed`,
        secureAlternative: 'Use secure server-side session storage instead'
      };
    }
    
    // Check if key contains forbidden patterns
    const forbiddenPattern = Array.from(FORBIDDEN_STORAGE_KEYS).find(pattern =>
      lowerKey.includes(pattern)
    );
    
    if (forbiddenPattern) {
      return {
        allowed: false,
        reason: `Storage key '${key}' contains sensitive pattern '${forbiddenPattern}'`,
        secureAlternative: 'Use encrypted server-side storage or session-only data'
      };
    }
    
    // Check if value contains sensitive data
    if (typeof value === 'string') {
      const foundPattern = SENSITIVE_PATTERNS.find(pattern => pattern.test(value));
      if (foundPattern) {
        return {
          allowed: false,
          reason: `Value appears to be a sensitive token or credential`,
          secureAlternative: 'Use secure HTTP-only cookies or server-side sessions'
        };
      }
    }
    
    return { allowed: true };
  }
  
  /**
   * Secure wrapper for localStorage.setItem
   */
  static setItem(key: string, value: string): boolean {
    const validation = this.validateStorageOperation(key, value);
    
    if (!validation.allowed) {
      console.error(`[SECURITY] Storage blocked: ${validation.reason}`);
      console.warn(`[SECURITY] Alternative: ${validation.secureAlternative}`);
      return false;
    }
    
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.error('[SECURITY] Storage operation failed:', error);
      return false;
    }
  }
  
  /**
   * Secure wrapper for localStorage.getItem
   */
  static getItem(key: string): string | null {
    const validation = this.validateStorageOperation(key, '');
    
    if (!validation.allowed) {
      console.error(`[SECURITY] Storage access blocked: ${validation.reason}`);
      return null;
    }
    
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.error('[SECURITY] Storage access failed:', error);
      return null;
    }
  }
  
  /**
   * Secure wrapper for localStorage.removeItem
   */
  static removeItem(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('[SECURITY] Storage removal failed:', error);
      return false;
    }
  }
  
  /**
   * Clear all potentially sensitive data from localStorage
   */
  static clearSensitiveData(): void {
    const keysToRemove: string[] = [];
    
    // Check all existing keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        const validation = this.validateStorageOperation(key, value);
        
        if (!validation.allowed) {
          keysToRemove.push(key);
        }
      }
    }
    
    // Remove sensitive keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.warn(`[SECURITY] Removed sensitive data from localStorage: ${key}`);
    });
    
    if (keysToRemove.length > 0) {
      console.log(`[SECURITY] Cleared ${keysToRemove.length} sensitive items from localStorage`);
    }
  }
  
  /**
   * Audit current localStorage contents for security issues
   */
  static auditLocalStorage(): {
    totalItems: number;
    secureItems: number;
    sensitiveItems: string[];
    recommendations: string[];
  } {
    const audit = {
      totalItems: localStorage.length,
      secureItems: 0,
      sensitiveItems: [] as string[],
      recommendations: [] as string[]
    };
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        const validation = this.validateStorageOperation(key, value);
        
        if (validation.allowed) {
          audit.secureItems++;
        } else {
          audit.sensitiveItems.push(key);
          if (validation.secureAlternative) {
            audit.recommendations.push(`${key}: ${validation.secureAlternative}`);
          }
        }
      }
    }
    
    return audit;
  }
}

/**
 * Enhanced localStorage proxy that automatically validates all operations
 */
const createSecureLocalStorage = () => {
  return {
    setItem: (key: string, value: string) => SecureStorageManager.setItem(key, value),
    getItem: (key: string) => SecureStorageManager.getItem(key),
    removeItem: (key: string) => SecureStorageManager.removeItem(key),
    clear: () => localStorage.clear(),
    key: (index: number) => localStorage.key(index),
    get length() { return localStorage.length; }
  };
};

// Export the secure storage manager and proxy
export { SecureStorageManager, createSecureLocalStorage };

// Automatically clear any existing sensitive data on module load
if (typeof window !== 'undefined' && window.localStorage) {
  SecureStorageManager.clearSensitiveData();
}

// Set up periodic security audits (every 5 minutes)
if (typeof window !== 'undefined') {
  setInterval(() => {
    const audit = SecureStorageManager.auditLocalStorage();
    if (audit.sensitiveItems.length > 0) {
      console.warn(`[SECURITY] Found ${audit.sensitiveItems.length} sensitive items in localStorage`);
      SecureStorageManager.clearSensitiveData();
    }
  }, 5 * 60 * 1000); // 5 minutes
}