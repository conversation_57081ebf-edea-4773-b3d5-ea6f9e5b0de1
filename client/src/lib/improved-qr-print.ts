import QRCode from 'qrcode';

/**
 * Funzione migliorata per stampare un QR code senza duplicazioni di testo e su una sola pagina
 * 
 * @param qrValue Il valore da codificare nel QR code
 * @param title Il titolo da mostrare
 * @returns Promise<boolean> true se la stampa è avvenuta con successo
 */
export async function printQRCodeSinglePage(qrValue: string, title: string): Promise<boolean> {
  try {
    // Dimensione del QR code
    const size = 300;
    
    // Genera il QR code sulla canvas
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    
    // Genera il QR code
    await QRCode.toCanvas(canvas, qrValue, {
      width: size,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });
    
    // Ottieni l'immagine come data URL
    const qrDataUrl = canvas.toDataURL('image/png');
    
    // Determina il tipo di oggetto dal valore
    let objectType = "CODICE";
    if (qrValue.startsWith('ddt:')) {
      objectType = "DDT";
    } else if (qrValue.startsWith('product:')) {
      objectType = "ETICHETTA";
    } else if (qrValue.startsWith('container:')) {
      objectType = "CONTENITORE";
    }
    
    // Controlla se il titolo contiene già il tipo
    const titleContainsType = title.toUpperCase().includes(objectType);
    
    // Divide il titolo in parti
    const titleParts = title.toUpperCase().split(' ');
    let formattedTitle: string[] = [];
    
    if (titleParts.length > 3) {
      // Titolo lungo, dividi in due parti
      const midpoint = Math.ceil(titleParts.length / 2);
      formattedTitle.push(titleParts.slice(0, midpoint).join(' '));
      formattedTitle.push(titleParts.slice(midpoint).join(' '));
    } else {
      // Titolo corto, usa una sola riga
      formattedTitle.push(titleParts.join(' '));
    }
    
    // Apri una nuova finestra per la stampa
    const printWindow = window.open('', '_blank');
    
    if (!printWindow) {
      console.error("Popup bloccato. Impossibile aprire la finestra di stampa");
      alert("Abilita i popup per stampare il QR code");
      return false;
    }
    
    // Scrivi il contenuto HTML nella finestra di stampa
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Stampa QR Code</title>
          <style>
            @page {
              size: 89mm 36mm; /* Dimensione standard per etichette */
              margin: 0;
            }
            html, body {
              margin: 0;
              padding: 0;
              width: 100%;
              height: 100%;
              overflow: hidden;
            }
            .print-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              width: 100%;
              padding: 5mm;
              box-sizing: border-box;
            }
            .qr-image {
              width: 30mm;
              height: 30mm;
              object-fit: contain;
            }
            .text-container {
              width: 100%;
              text-align: center;
              margin-top: 2mm;
            }
            .type-label {
              font-family: Arial, sans-serif;
              font-weight: bold;
              font-size: 14px;
              margin-bottom: 2mm;
              text-transform: uppercase;
            }
            .title-line {
              font-family: Arial, sans-serif;
              font-weight: bold;
              font-size: 18px;
              line-height: 6mm;
              text-transform: uppercase;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <img src="${qrDataUrl}" alt="QR Code" class="qr-image">
            <div class="text-container">
              ${!titleContainsType ? `<div class="type-label">${objectType}</div>` : ''}
              ${formattedTitle.map(line => `<div class="title-line">${line}</div>`).join('')}
            </div>
          </div>
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 300);
            };
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    return true;
  } catch (error) {
    console.error("Errore durante la stampa del QR code:", error);
    return false;
  }
}