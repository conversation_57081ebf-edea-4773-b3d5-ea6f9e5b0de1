/**
 * Sistema di gestione errori centralizzato per Promise
 * Fornisce wrapper sicuri per tutte le operazioni asincrone
 */

import { toast } from "@/hooks/use-toast";
import { clientLogger } from './clientLogger';

/**
 * Wrapper sicuro per Promise che gestisce automaticamente gli errori
 */
export async function safePromise<T>(
  promise: Promise<T>,
  context: string,
  showUserError: boolean = true,
  fallbackValue?: T
): Promise<T | undefined> {
  try {
    return await promise;
  } catch (error) {
    clientLogger.error(`Errore in ${context}:`, { error });
    
    if (showUserError) {
      toast({
        title: 'Errore operazione',
        description: `${context}: ${error instanceof Error ? error.message : 'Errore sconosciuto'}`,
        variant: 'destructive',
      });
    }
    
    return fallbackValue;
  }
}

/**
 * Wrapper per Promise che non devono bloccare l'esecuzione
 */
export function safePromiseNoWait<T>(
  promise: Promise<T>,
  context: string
): void {
  promise.catch(error => {
    clientLogger.error(`Errore non bloccante in ${context}:`, { error });
  });
}

/**
 * Wrapper per operazioni API con retry automatico
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  context: string,
  maxRetries: number = 2
): Promise<T | undefined> {
  let lastError: Error | unknown;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      clientLogger.warn(`Tentativo ${attempt + 1} fallito per ${context}:`, { error });
      
      if (attempt < maxRetries) {
        // Attesa esponenziale tra i tentativi
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }
  
  clientLogger.error(`Tutti i tentativi falliti per ${context}:`, { error: lastError });
  toast({
    title: 'Errore persistente',
    description: `${context} ha fallito dopo ${maxRetries + 1} tentativi`,
    variant: 'destructive',
  });
  
  return undefined;
}

/**
 * Wrapper per operazioni cache che non devono causare errori visibili
 */
export async function safeCacheOperation<T>(
  operation: () => Promise<T>,
  context: string
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    clientLogger.debug(`Operazione cache fallita per ${context}:`, { error });
    return undefined;
  }
}

/**
 * Wrapper per Service Worker operations
 */
export async function safeServiceWorkerCall(
  swCall: () => Promise<any>,
  context: string
): Promise<boolean> {
  try {
    await swCall();
    return true;
  } catch (error) {
    clientLogger.warn(`Service Worker ${context} fallito:`, { error });
    return false;
  }
}

/**
 * Gestione sicura per clipboard operations
 */
export async function safeClipboardWrite(
  text: string,
  successMessage?: string
): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    if (successMessage) {
      toast({
        title: 'Copiato',
        description: successMessage,
      });
    }
    return true;
  } catch (error) {
    clientLogger.error('Errore copia clipboard:', { error });
    // Fallback per browser più vecchi
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successMessage) {
        toast({
          title: 'Copiato',
          description: successMessage,
        });
      }
      return true;
    } catch (fallbackError) {
      toast({
        title: 'Errore copia',
        description: 'Impossibile copiare negli appunti. Seleziona manualmente il testo.',
        variant: 'destructive',
      });
      return false;
    }
  }
}