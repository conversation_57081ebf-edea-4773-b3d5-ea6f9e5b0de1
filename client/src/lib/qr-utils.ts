/**
 * Utility per la gestione dei QR Code
 */
import QRCode from 'qrcode';

/**
 * Genera un'immagine QR Code come PNG dataURL ottimizzata per le specifiche richieste
 * 
 * @param qrValue Valore da codificare nel QR code
 * @param title Testo da mostrare sotto il QR code (opzionale)
 * @returns Promise<string> DataURL dell'immagine PNG
 */
export async function generateQRCodeImage(qrValue: string, title?: string): Promise<string> {
  if (!qrValue) throw new Error("Valore QR code mancante");
  
  // Dimensioni standard
  const size = 300; // Dimensione QR code
  const qrSize = 240; // Dimensione effettiva QR (per aggiungere margine laterale)
  const padding = 10; // Padding interno
  const lateralMargin = (size - qrSize) / 2; // Margine laterale del QR code
  
  // Determina il tipo di oggetto dal QR value
  let objectType = "CODICE";
  if (qrValue.startsWith('ddt:')) {
    objectType = "DDT";
  } else if (qrValue.startsWith('product:')) {
    objectType = "ETICHETTA";
  } else if (qrValue.startsWith('container:')) {
    objectType = "CONTENITORE";
  }
  
  // Calcola altezza totale del canvas
  const bottomMargin = 70; // Spazio per il testo sotto il QR code
  let totalHeight = size + bottomMargin; // Dimensione QR + spazio per testo
  
  // Crea canvas e genera QR code
  const qrCanvas = document.createElement('canvas');
  qrCanvas.width = qrSize;
  qrCanvas.height = qrSize;
  
  await QRCode.toCanvas(qrCanvas, qrValue, {
    width: qrSize,
    margin: padding,
    color: {
      dark: '#000000',
      light: '#ffffff'
    }
  });
  
  // Se non c'è titolo, ritorna direttamente il QR code ma con margini
  if (!title) {
    // Aggiungi margini al QR code
    const finalCanvas = document.createElement('canvas');
    finalCanvas.width = size;
    finalCanvas.height = size;
    
    const ctx = finalCanvas.getContext('2d');
    if (!ctx) {
      throw new Error("Canvas context non disponibile");
    }
    
    // Sfondo bianco
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, size, size);
    
    // Disegna QR code centrato con margini
    ctx.drawImage(qrCanvas, lateralMargin, lateralMargin);
    
    return finalCanvas.toDataURL('image/png');
  }
  
  // Altrimenti crea un canvas con testo
  const finalCanvas = document.createElement('canvas');
  finalCanvas.width = size;
  finalCanvas.height = totalHeight;
  
  const ctx = finalCanvas.getContext('2d');
  if (!ctx) {
    throw new Error("Canvas context non disponibile");
  }
  
  // Sfondo bianco
  ctx.fillStyle = 'white';
  ctx.fillRect(0, 0, size, totalHeight);
  
  // Disegna QR code centrato con margini
  ctx.drawImage(qrCanvas, lateralMargin, lateralMargin);
  
  // Tipo oggetto (prima riga, più piccola)
  ctx.font = '700 20px Arial Narrow, Arial, sans-serif';
  ctx.fillStyle = 'black';
  ctx.textAlign = 'center';
  ctx.fillText(objectType, size / 2, size + 25);
  
  // Titolo (seconda riga, più grande e ravvicinata)
  ctx.font = '700 24px Arial Narrow, Arial, sans-serif';
  ctx.fillText(title.toUpperCase(), size / 2, size + 55);
  
  return finalCanvas.toDataURL('image/png');
}

/**
 * Scarica un'immagine come file
 * 
 * @param dataUrl DataURL dell'immagine da scaricare
 * @param filename Nome del file (senza estensione)
 * @returns Promise<void>
 */
export function downloadImage(dataUrl: string, filename: string): void {
  // Assicurati che il filename non contenga estensione
  filename = filename.replace(/\.(png|jpg|jpeg|gif|svg)$/i, '');
  
  // Crea e simula il click su un link per scaricare
  const link = document.createElement('a');
  link.download = `${filename}.png`;
  link.href = dataUrl;
  link.style.display = 'none';
  document.body.appendChild(link);
  link.click();
  
  // Rimuovi il link dopo un breve ritardo
  setTimeout(() => document.body.removeChild(link), 100);
}

/**
 * Genera un QR Code e lo scarica come un normale file
 * 
 * @param qrValue Valore da codificare nel QR code
 * @param filename Nome del file da scaricare (senza estensione)
 * @param title Testo da mostrare sotto il QR code (opzionale)
 * @returns Promise<boolean> true se l'operazione è riuscita
 */
export async function generateAndDownloadQRCode(
  title: string,
  filename: string, 
  qrValue: string
): Promise<boolean> {
  if (!filename) filename = "qrcode";
  
  try {
    // Genera l'immagine QR code
    const imageData = await generateQRCodeImage(qrValue, title);
    
    // Scarica l'immagine
    downloadImage(imageData, filename);
    
    return true;
  } catch (error) {
    console.error("Errore durante la generazione del QR code:", error);
    return false;
  }
}

/**
 * Stampa un QR Code in una finestra di stampa dedicata secondo le specifiche
 * Utilizza formato A4 con layout ottimizzato per la stampa
 * 
 * @param qrValue Valore da codificare nel QR code
 * @param title Titolo da mostrare (opzionale)
 * @returns Promise<boolean> true se la stampa è avvenuta con successo
 */
export async function printQRCode(qrValue: string, title?: string): Promise<boolean> {
  try {
    // Genera l'immagine QR code come data URL
    const qrDataUrl = await generateQRCodeImage(qrValue);
    
    // Estrai il nome del prodotto dal QR value
    let productName = "";
    if (title) {
      // Estrai solo il nome del prodotto senza lotto e scadenza
      productName = title.split(' - ')[0].trim();
    } else if (qrValue.includes(':')) {
      // Se non c'è titolo, prova a estrarre dal QR value
      const parts = qrValue.split(':');
      if (parts.length > 2) {
        productName = parts[2].replace(/_/g, ' ');
      }
    }
    
    // Determina il tipo di oggetto dal QR value
    let objectType = "CODICE";
    if (qrValue.startsWith('ddt:')) {
      objectType = "DDT";
    } else if (qrValue.startsWith('product:')) {
      objectType = "ETICHETTA";
    } else if (qrValue.startsWith('container:')) {
      objectType = "CONTENITORE";
    }
    
    // Apri una nuova finestra per la stampa
    const printWindow = window.open('', '_blank');
    
    if (!printWindow) {
      console.error("Popup bloccato. Impossibile aprire la finestra di stampa");
      return false;
    }
    
    // Scrivi il contenuto HTML nella finestra di stampa con formato A4
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>QR Code ${productName || ''}</title>
          <style>
            @page {
              size: A4;
              margin: 10mm;
            }
            @font-face {
              font-family: 'Arial Condensed';
              src: local('Arial Narrow'), local('ArialNarrow');
              font-stretch: condensed;
            }
            body {
              margin: 0;
              padding: 0;
              background: white;
              font-family: Arial, sans-serif;
            }
            .page {
              width: 210mm;
              min-height: 297mm;
              padding: 20mm;
              margin: 0 auto;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .qr-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              width: 100%;
              max-width: 150mm;
              margin: 0 auto;
            }
            .qr-image {
              width: 100%;
              max-width: 100mm;
              height: auto;
              margin-bottom: 5mm;
            }
            .qr-type {
              font-family: 'Arial Condensed', 'Arial Narrow', Arial, sans-serif;
              font-size: 36px;
              line-height: 36px;
              font-weight: bold;
              text-align: center;
              text-transform: uppercase;
              width: 100%;
              margin-bottom: 5mm;
            }
            .qr-name {
              font-family: 'Arial Condensed', 'Arial Narrow', Arial, sans-serif;
              font-size: 48px;
              line-height: 48px;
              font-weight: bold;
              text-align: center;
              text-transform: uppercase;
              width: 100%;
            }
            @media print {
              body {
                width: 100%;
                height: 100%;
              }
              .page {
                page-break-after: always;
              }
            }
          </style>
        </head>
        <body>
          <div class="page">
            <div class="qr-container">
              <img src="${qrDataUrl}" alt="QR Code" class="qr-image" />
              <div class="qr-type">${objectType}</div>
              ${productName ? `<div class="qr-name">${productName.toUpperCase()}</div>` : ''}
            </div>
          </div>
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 500);
            }
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    return true;
  } catch (error) {
    console.error("Errore durante la stampa del QR code:", error);
    return false;
  }
}