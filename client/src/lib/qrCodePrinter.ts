import QRCode from 'qrcode';

/**
 * Funzione per generare e stampare un QR code con una spaziatura migliorata tra le righe
 * 
 * @param qrValue Valore da codificare nel QR code
 * @param title <PERSON>lo da mostrare sotto il QR code
 * @returns Promise<boolean> true se la stampa è avvenuta con successo
 */
export async function printQRCodeWithImprovedSpacing(qrValue: string, title: string): Promise<boolean> {
  try {
    // Dimensione del QR code
    const size = 300;
    
    // Genera il QR code sulla canvas usando la libreria QRCode
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    
    // Genera il QR code con la libreria
    await QRCode.toCanvas(canvas, qrValue, {
      width: size,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });
    
    // Determina il tipo di oggetto dal valore QR
    let objectType = "CODICE";
    if (qrValue.startsWith('ddt:')) {
      objectType = "DDT";
    } else if (qrValue.startsWith('product:')) {
      objectType = "ETICHETTA";
    } else if (qrValue.startsWith('container:')) {
      objectType = "CONTENITORE";
    }
    
    // Divide il titolo in parti se necessario
    const titleParts = splitTitle(title.toUpperCase());
    
    // Ottieni l'immagine del QR code come data URL
    const qrDataUrl = canvas.toDataURL('image/png');
    
    // Crea una nuova finestra per la stampa con spaziatura migliorata
    const printWindow = window.open('', '_blank');
    
    if (!printWindow) {
      console.warn("Popup bloccato. Impossibile aprire la finestra di stampa");
      alert("Abilita i popup per stampare il QR code");
      return false;
    }
    
    // Dimensiona la nuova finestra per una stampa ottimale
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Stampa QR Code</title>
          <style>
            body { 
              margin: 0; 
              padding: 0; 
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              background-color: white;
            }
            .qr-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;
              padding: 20px;
            }
            .qr-image {
              width: ${size}px;
              height: ${size}px;
              margin-bottom: 10px;
            }
            .qr-type {
              font-family: Arial, sans-serif;
              font-weight: bold;
              font-size: 14px;
              margin-top: 10px;
              margin-bottom: 10px;
              text-transform: uppercase;
            }
            .qr-title-line {
              font-family: Arial, sans-serif;
              font-weight: bold;
              font-size: 20px;
              line-height: 36px; /* Spazio verticale aumentato */
              text-transform: uppercase;
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <img src="${qrDataUrl}" alt="QR Code" class="qr-image" />
            <div class="qr-type">${objectType}</div>
            ${titleParts.map(part => `<div class="qr-title-line">${part}</div>`).join('')}
          </div>
          <script>
            // Avvia la stampa automaticamente
            window.onload = function() {
              setTimeout(function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 200);
            };
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    return true;
  } catch (error) {
    console.error("Errore durante la stampa del QR code:", error);
    return false;
  }
}

/**
 * Divide il titolo in parti per la visualizzazione
 * @param title Il titolo da dividere
 * @returns Array di stringhe rappresentanti le parti del titolo
 */
function splitTitle(title: string): string[] {
  const words = title.split(' ');
  const result: string[] = [];
  
  if (words.length <= 2) {
    // Titolo molto corto, tienilo su una riga
    return [title];
  } else if (words.length <= 4) {
    // Titolo corto, divide in massimo 2 righe
    const midpoint = Math.ceil(words.length / 2);
    result.push(words.slice(0, midpoint).join(' '));
    result.push(words.slice(midpoint).join(' '));
  } else {
    // Titolo lungo, analizza in modo più dettagliato
    let currentLine = '';
    const maxLength = 20; // Lunghezza massima per riga
    
    for (const word of words) {
      if ((currentLine + ' ' + word).length <= maxLength) {
        currentLine = currentLine ? currentLine + ' ' + word : word;
      } else {
        if (currentLine) result.push(currentLine);
        currentLine = word;
      }
    }
    
    if (currentLine) result.push(currentLine);
  }
  
  return result;
}