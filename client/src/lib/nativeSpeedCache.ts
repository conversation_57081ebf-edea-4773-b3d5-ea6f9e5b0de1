/**
 * Sistema di cache ad alta velocità per simulare prestazioni native
 * Implementa strategie multiple di caching e precaricamento
 */

import { getCachedData, saveCachedData } from "./offlineAPI";

// Cache in memoria ultra-veloce per dati critici
const INSTANT_CACHE = new Map<string, {
  data: any;
  timestamp: number;
  expiry: number;
}>();

// Cache dei risultati delle query più frequenti
const QUERY_CACHE = new Map<string, {
  data: any;
  timestamp: number;
  hits: number;
}>();

// Costanti per ottimizzazione prestazioni
const INSTANT_CACHE_TTL = 2 * 60 * 1000; // 2 minuti per cache istantanea
const MEMORY_CACHE_TTL = 10 * 60 * 1000; // 10 minuti per cache normale
const MAX_INSTANT_CACHE_SIZE = 50; // Massimo 50 elementi in cache istantanea
const PRELOAD_DELAY = 100; // Delay per precaricamento in ms

// Lista delle API critiche da precaricare all'avvio
const CRITICAL_ENDPOINTS = [
  '/api/auth/me',
  '/api/containers',
  '/api/products',
  '/api/suppliers',
  '/api/container-types',
  '/api/product-templates'
];

/**
 * Cache istantanea per dati critici - zero latency
 */
export class InstantCache {
  static set(key: string, data: any, ttl: number = INSTANT_CACHE_TTL): void {
    // Rimuovi elementi vecchi se la cache è piena
    if (INSTANT_CACHE.size >= MAX_INSTANT_CACHE_SIZE) {
      const oldestKey = Array.from(INSTANT_CACHE.entries())
        .sort(([,a], [,b]) => a.timestamp - b.timestamp)[0][0];
      INSTANT_CACHE.delete(oldestKey);
    }

    INSTANT_CACHE.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  static get<T>(key: string): T | null {
    const cached = INSTANT_CACHE.get(key);
    if (!cached) return null;

    // Verifica scadenza
    if (Date.now() > cached.expiry) {
      INSTANT_CACHE.delete(key);
      return null;
    }

    return cached.data as T;
  }

  static has(key: string): boolean {
    const cached = INSTANT_CACHE.get(key);
    if (!cached) return false;
    
    if (Date.now() > cached.expiry) {
      INSTANT_CACHE.delete(key);
      return false;
    }
    
    return true;
  }

  static clear(): void {
    INSTANT_CACHE.clear();
  }
}

/**
 * Sistema di precaricamento intelligente
 */
export class PreloadManager {
  private static preloadQueue: string[] = [];
  private static isPreloading = false;

  static async preloadCriticalData(): Promise<void> {
    if (this.isPreloading) return;
    this.isPreloading = true;

    logger.log('NativeCache', 'Avvio precaricamento dati critici');

    try {
      // Precarica in parallelo i dati critici
      const preloadPromises = CRITICAL_ENDPOINTS.map(async (endpoint, index) => {
        // Aggiungi un piccolo delay scaglionato per non sovraccaricare
        await new Promise(resolve => setTimeout(resolve, index * PRELOAD_DELAY));
        
        try {
          const cacheKey = `GET-${endpoint}`;
          
          // Controlla se abbiamo già i dati in cache istantanea
          if (InstantCache.has(cacheKey)) {
            return;
          }

          // Controlla cache offline
          const cachedData = await getCachedData(cacheKey);
          if (cachedData) {
            // Metti in cache istantanea per accesso veloce
            InstantCache.set(cacheKey, cachedData);
            logger.log('NativeCache', `Precaricato da cache offline: ${endpoint}`);
          }
        } catch (error) {
          console.error(`[NativeCache] Errore precaricamento ${endpoint}:`, error);
        }
      });

      await Promise.all(preloadPromises);
      logger.log('NativeCache', 'Precaricamento completato');
    } finally {
      this.isPreloading = false;
    }
  }

  static schedulePreload(endpoint: string): void {
    if (!this.preloadQueue.includes(endpoint)) {
      this.preloadQueue.push(endpoint);
    }

    // Elabora la coda dopo un breve delay
    setTimeout(() => this.processPreloadQueue(), 50);
  }

  private static async processPreloadQueue(): Promise<void> {
    if (this.preloadQueue.length === 0) return;

    const endpoint = this.preloadQueue.shift()!;
    const cacheKey = `GET-${endpoint}`;

    try {
      if (!InstantCache.has(cacheKey)) {
        const cachedData = await getCachedData(cacheKey);
        if (cachedData) {
          InstantCache.set(cacheKey, cachedData);
        }
      }
    } catch (error) {
      logger.error('NativeCache', `Errore elaborazione coda precaricamento: ${endpoint}`, error);
    }

    // Continua con il prossimo elemento
    if (this.preloadQueue.length > 0) {
      setTimeout(() => this.processPreloadQueue(), 25);
    }
  }
}

/**
 * Cache intelligente delle query con tracking degli accessi
 */
export class QueryCache {
  static track(queryKey: string, data: any): void {
    const existing = QUERY_CACHE.get(queryKey);
    
    QUERY_CACHE.set(queryKey, {
      data,
      timestamp: Date.now(),
      hits: existing ? existing.hits + 1 : 1
    });
  }

  static get<T>(queryKey: string): T | null {
    const cached = QUERY_CACHE.get(queryKey);
    if (!cached) return null;

    // Aggiorna il contatore di hit
    cached.hits++;
    cached.timestamp = Date.now();

    return cached.data as T;
  }

  static getMostAccessed(limit: number = 10): string[] {
    const entries: [string, any][] = [];
    for (const entry of QUERY_CACHE.entries()) {
      entries.push(entry);
    }
    return entries
      .sort(([,a], [,b]) => b.hits - a.hits)
      .slice(0, limit)
      .map(([key]) => key);
  }

  static cleanup(): void {
    const now = Date.now();
    const cutoff = now - MEMORY_CACHE_TTL;

    const entries: [string, any][] = [];
    for (const entry of QUERY_CACHE.entries()) {
      entries.push(entry);
    }
    
    for (const [key, entry] of entries) {
      if (entry.timestamp < cutoff) {
        QUERY_CACHE.delete(key);
      }
    }
  }
}

/**
 * Wrapper per fetch con cache intelligente
 */
export async function nativeFetch<T>(
  endpoint: string,
  options: {
    method?: string;
    body?: any;
    headers?: HeadersInit;
    useInstantCache?: boolean;
    cacheKey?: string;
  } = {}
): Promise<T> {
  const {
    method = 'GET',
    body,
    headers,
    useInstantCache = true,
    cacheKey: customCacheKey
  } = options;

  const cacheKey = customCacheKey || `${method}-${endpoint}`;

  // Per GET requests, prova prima la cache istantanea
  if (method === 'GET' && useInstantCache) {
    const instantData = InstantCache.get<T>(cacheKey);
    if (instantData) {
      // Prenota un precaricamento in background per aggiornare
      PreloadManager.schedulePreload(endpoint);
      return instantData;
    }
  }

  try {
    // Esegui la richiesta
    const response = await fetch(endpoint, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : undefined,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache solo per GET requests di successo
    if (method === 'GET') {
      InstantCache.set(cacheKey, data);
      await saveCachedData(cacheKey, data);
      QueryCache.track(cacheKey, data);
    }

    return data;
  } catch (error) {
    // Fallback su cache offline per GET requests
    if (method === 'GET') {
      const fallbackData = await getCachedData<T>(cacheKey);
      if (fallbackData) {
        InstantCache.set(cacheKey, fallbackData);
        return fallbackData;
      }
    }
    
    throw error;
  }
}

/**
 * Inizializzazione sistema cache nativo
 */
export async function initNativeSpeedCache(): Promise<void> {
  logger.log('NativeCache', 'Inizializzazione sistema cache ad alta velocità');

  // Avvia precaricamento dati critici
  PreloadManager.preloadCriticalData();

  // Pianifica pulizia periodica
  setInterval(() => {
    QueryCache.cleanup();
  }, 5 * 60 * 1000); // Ogni 5 minuti

  // Listener per gestire visibilità pagina
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Quando la pagina diventa visibile, precarica dati critici
        PreloadManager.preloadCriticalData();
      }
    });
  }

  logger.log('NativeCache', 'Sistema cache nativo inizializzato');
}

/**
 * Hook per React Query con cache nativo
 */
export function createNativeCacheKey(queryKey: (string | number | boolean | object)[]): string {
  return queryKey.map(key => 
    typeof key === 'object' ? JSON.stringify(key) : String(key)
  ).join('-');
}

// Auto-avvio inizializzazione quando il modulo viene importato
if (typeof window !== 'undefined') {
  // Avvia l'inizializzazione dopo che il DOM è pronto
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initNativeSpeedCache, 500);
    });
  } else {
    setTimeout(initNativeSpeedCache, 500);
  }
}