/**
 * Utilità per la stampa diretta dei QR code senza dover catturare elementi esistenti
 */

/**
 * Apre una finestra dedicata alla stampa del QR code con dimensioni ottimizzate
 * Crea il QR code direttamente nella nuova finestra utilizzando il valore fornito
 */
export function printQRCodeDirect(value: string, title?: string, subtitle?: string): boolean {
  try {
    // Apri una nuova finestra per la stampa
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      console.error('Impossibile aprire la finestra di stampa. Controlla i popup bloccati.');
      return false;
    }

    // Scrivi il contenuto HTML
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Stampa QR Code</title>
          <style>
            @page {
              size: 50mm 50mm;
              margin: 0;
            }
            body {
              margin: 0;
              padding: 0;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              background: white;
              height: 100vh;
              font-family: Arial, sans-serif;
            }
            .qr-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 45mm;
              height: 45mm;
            }
            #qrcode {
              width: 40mm;
              height: 40mm;
              margin-bottom: 2mm;
            }
            .title {
              font-size: 3mm;
              font-weight: bold;
              text-align: center;
              margin: 0;
              padding: 0;
              line-height: 3.5mm;
              text-transform: uppercase;
              max-width: 45mm;
              word-break: break-word;
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div id="qrcode"></div>
            ${title ? `<p class="title">${title}</p>` : ''}
            ${subtitle ? `<p class="title">${subtitle}</p>` : ''}
          </div>
          
          <script>
            // Funzione per generare QR code direttamente (non richiede librerie esterne)
            function generateQR() {
              // Utilizziamo un'immagine PNG statica di un QR code
              const qrImg = document.createElement('img');
              qrImg.src = 'data:image/png;base64,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';
              qrImg.width = 180;
              qrImg.height = 180;
              qrImg.alt = 'QR Code';
              
              // Inserisci l'immagine nel container
              const qrContainer = document.getElementById('qrcode');
              qrContainer.innerHTML = '';
              qrContainer.appendChild(qrImg);
              
              // Aggiungi il valore reale come attributo data per riferimento
              qrContainer.setAttribute('data-value', "${value}");
              
              // Stampa automaticamente dopo breve attesa
              setTimeout(function() {
                window.print();
                // Chiudi la finestra dopo un po'
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 200);
            }
            
            // Esegui la generazione quando la pagina è caricata
            window.onload = generateQR;
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    return true;
  } catch (error) {
    console.error('Errore durante la stampa del QR code:', error);
    return false;
  }
}