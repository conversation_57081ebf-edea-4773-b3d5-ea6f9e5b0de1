/**
 * Logger centralizzato per debug e risoluzione problemi
 * Ottimizzato per funzionare sia in sviluppo che in produzione
 */

// Configurazione base
const LOG_LEVEL = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// Livello di default - in produzione possiamo limitare a INFO
const currentLevel = import.meta.env.PROD ? LOG_LEVEL.INFO : LOG_LEVEL.DEBUG;

// Storage per i log in produzione
const MAX_LOGS = 1000;
const logStorage: Array<{level: string, message: string, data?: any, timestamp: number}> = [];

// Funzioni di utility per il logging
const addToStorage = (level: string, message: string, data?: any) => {
  // Aggiungiamo il log allo storage con timestamp
  logStorage.push({
    level,
    message,
    data,
    timestamp: Date.now()
  });
  
  // Manteniamo la dimensione massima
  if (logStorage.length > MAX_LOGS) {
    logStorage.shift();
  }
};

// Logger esportato
export const logger = {
  debug: (message: string, data?: any) => {
    if (currentLevel <= LOG_LEVEL.DEBUG) {
      console.debug(message, data !== undefined ? data : '');
    }
    addToStorage('debug', message, data);
  },
  
  log: (message: string, data?: any) => {
    if (currentLevel <= LOG_LEVEL.INFO) {
      console.log(message, data !== undefined ? data : '');
    }
    addToStorage('info', message, data);
  },
  
  warn: (message: string, data?: any) => {
    if (currentLevel <= LOG_LEVEL.WARN) {
      console.warn(message, data !== undefined ? data : '');
    }
    addToStorage('warn', message, data);
  },
  
  error: (message: string, data?: any) => {
    // Gli errori vengono sempre loggati
    console.error(message, data !== undefined ? data : '');
    addToStorage('error', message, data);
  },
  
  // Ottiene tutti i log dallo storage
  getLogs: () => {
    return [...logStorage];
  },
  
  // Ottiene i log filtrati per livello
  getLogsByLevel: (level: string) => {
    return logStorage.filter(log => log.level === level);
  },
  
  // Ottiene solo gli errori (utile per debug)
  getErrors: () => {
    return logStorage.filter(log => log.level === 'error');
  },
  
  // Esporta i log in formato JSON
  exportLogs: () => {
    return JSON.stringify(logStorage, null, 2);
  },
  
  // Pulisce i log
  clearLogs: () => {
    logStorage.length = 0;
  }
};

// Espone lo storage dei log come proprietà
(window as any).__appLogs = logStorage;