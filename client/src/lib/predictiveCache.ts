/**
 * Sistema di cache predittivo per anticipare le richieste dell'utente
 * Analizza i pattern di navigazione per precaricare i dati necessari
 */

import { logger } from "./logger";
import { nativeFetch } from "./nativeSpeedCache";

// Pattern di navigazione dell'utente
interface NavigationPattern {
  from: string;
  to: string;
  count: number;
  lastAccess: number;
}

// Previsioni di caricamento
interface LoadPrediction {
  endpoint: string;
  probability: number;
  priority: number;
}

class PredictiveCache {
  private navigationPatterns = new Map<string, NavigationPattern>();
  private currentRoute = '';
  private prefetchQueue: LoadPrediction[] = [];
  private isPrefetching = false;
  
  constructor() {
    // Ensure proper method binding
    this.recordNavigation = this.recordNavigation.bind(this);
    this.predictNextRoutes = this.predictNextRoutes.bind(this);
  }

  // Mappa delle rotte alle API correlate
  private routeApiMap: Record<string, string[]> = {
    '/': ['/api/auth/me', '/api/activity-logs'],
    '/products': ['/api/products', '/api/suppliers', '/api/container-types'],
    '/containers': ['/api/containers', '/api/container-types'],
    '/suppliers': ['/api/suppliers'],
    '/inventory': ['/api/products', '/api/containers'],
    '/reports': ['/api/product-labels', '/api/activity-logs'],
    '/scanning': ['/api/products', '/api/containers', '/api/suppliers'],
    '/settings': ['/api/auth/me']
  };

  /**
   * Registra una transizione di navigazione
   */
  recordNavigation(from: string, to: string): void {
    const key = `${from}->${to}`;
    const existing = this.navigationPatterns.get(key);
    
    if (existing) {
      existing.count++;
      existing.lastAccess = Date.now();
    } else {
      this.navigationPatterns.set(key, {
        from,
        to,
        count: 1,
        lastAccess: Date.now()
      });
    }

    this.currentRoute = to;
    this.schedulePreloadForRoute(to);
  }

  /**
   * Predice le prossime rotte probabili
   */
  predictNextRoutes(currentRoute: string): LoadPrediction[] {
    const predictions: LoadPrediction[] = [];
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // Ultimi 24 ore

    // Analizza i pattern di navigazione
    for (const [key, pattern] of this.navigationPatterns.entries()) {
      if (pattern.from === currentRoute && pattern.lastAccess > cutoff) {
        const apis = this.routeApiMap[pattern.to] || [];
        
        apis.forEach(endpoint => {
          predictions.push({
            endpoint,
            probability: Math.min(pattern.count / 10, 1), // Max probabilità 1
            priority: pattern.count
          });
        });
      }
    }

    // Ordina per priorità decrescente
    return predictions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Pianifica il precaricamento per una rotta
   */
  private schedulePreloadForRoute(route: string): void {
    const predictions = this.predictNextRoutes(route);
    
    // Aggiungi anche le API della rotta corrente
    const currentApis = this.routeApiMap[route] || [];
    currentApis.forEach(endpoint => {
      predictions.unshift({
        endpoint,
        probability: 1,
        priority: 100
      });
    });

    this.prefetchQueue = predictions;
    this.startPrefetching();
  }

  /**
   * Avvia il processo di prefetch
   */
  private async startPrefetching(): Promise<void> {
    if (this.isPrefetching || this.prefetchQueue.length === 0) {
      return;
    }

    this.isPrefetching = true;

    try {
      // Processa la coda con priorità
      for (const prediction of this.prefetchQueue.slice(0, 5)) { // Max 5 per volta
        if (prediction.probability > 0.3) { // Solo se probabilità > 30%
          await this.prefetchEndpoint(prediction);
        }
      }
    } finally {
      this.isPrefetching = false;
    }
  }

  /**
   * Precarica un endpoint specifico
   */
  private async prefetchEndpoint(prediction: LoadPrediction): Promise<void> {
    try {
      logger.log('PredictiveCache', `Prefetch: ${prediction.endpoint} (prob: ${prediction.probability})`);
      
      await nativeFetch(prediction.endpoint, {
        method: 'GET',
        useInstantCache: true
      });
    } catch (error) {
      logger.error('PredictiveCache', `Errore prefetch ${prediction.endpoint}:`, error);
    }
  }

  /**
   * Pulisce i pattern vecchi
   */
  cleanup(): void {
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 giorni
    
    for (const [key, pattern] of this.navigationPatterns.entries()) {
      if (pattern.lastAccess < cutoff) {
        this.navigationPatterns.delete(key);
      }
    }
  }

  /**
   * Ottieni statistiche sui pattern
   */
  getStats(): {
    totalPatterns: number;
    mostFrequentTransitions: Array<{route: string; count: number}>;
  } {
    const transitions = Array.from(this.navigationPatterns.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(p => ({ route: `${p.from} → ${p.to}`, count: p.count }));

    return {
      totalPatterns: this.navigationPatterns.size,
      mostFrequentTransitions: transitions
    };
  }
}

// Istanza globale
const predictiveCache = new PredictiveCache();

/**
 * Hook per router per tracciare navigazione
 */
export function trackNavigation(from: string, to: string): void {
  predictiveCache.recordNavigation(from, to);
}

/**
 * Precarica dati per rotta specifica
 */
export function preloadRoute(route: string): void {
  predictiveCache.schedulePreloadForRoute(route);
}

/**
 * Ottieni statistiche cache predittiva
 */
export function getPredictiveCacheStats() {
  return predictiveCache.getStats();
}

// Pulizia automatica ogni ora
setInterval(() => {
  predictiveCache.cleanup();
}, 60 * 60 * 1000);

export default predictiveCache;