/**
 * Sistema di cache ultra-veloce per prestazioni native
 * Ottimizzato per TypeScript e integrazione perfetta
 */

import { getCachedData, saveCachedData } from "./offlineAPI";

// Cache in memoria ultra-veloce
const INSTANT_CACHE = new Map<string, {
  data: any;
  timestamp: number;
  expiry: number;
}>();

// Cache delle query con statistiche
const QUERY_STATS = new Map<string, {
  data: any;
  timestamp: number;
  hits: number;
}>();

// Configurazione cache
const CONFIG = {
  INSTANT_TTL: 2 * 60 * 1000, // 2 minuti
  MEMORY_TTL: 10 * 60 * 1000, // 10 minuti
  MAX_INSTANT_SIZE: 50,
  PRELOAD_DELAY: 100,
  CRITICAL_ENDPOINTS: [
    '/api/auth/me',
    '/api/containers',
    '/api/products',
    '/api/suppliers',
    '/api/container-types',
    '/api/product-templates'
  ]
};

/**
 * Cache istantanea per zero latency
 */
export class UltraCache {
  static set(key: string, data: any, ttl: number = CONFIG.INSTANT_TTL): void {
    // Pulisci se la cache è piena
    if (INSTANT_CACHE.size >= CONFIG.MAX_INSTANT_SIZE) {
      const entries = Array.from(INSTANT_CACHE.entries());
      const oldest = entries.sort(([,a], [,b]) => a.timestamp - b.timestamp)[0];
      if (oldest) {
        INSTANT_CACHE.delete(oldest[0]);
      }
    }

    INSTANT_CACHE.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  static get<T>(key: string): T | null {
    const cached = INSTANT_CACHE.get(key);
    if (!cached) return null;

    if (Date.now() > cached.expiry) {
      INSTANT_CACHE.delete(key);
      return null;
    }

    return cached.data as T;
  }

  static has(key: string): boolean {
    const cached = INSTANT_CACHE.get(key);
    if (!cached) return false;
    
    if (Date.now() > cached.expiry) {
      INSTANT_CACHE.delete(key);
      return false;
    }
    
    return true;
  }

  static clear(): void {
    INSTANT_CACHE.clear();
  }
}

/**
 * Gestore statistiche query
 */
export class QueryStats {
  static track(key: string, data: any): void {
    const existing = QUERY_STATS.get(key);
    
    QUERY_STATS.set(key, {
      data,
      timestamp: Date.now(),
      hits: existing ? existing.hits + 1 : 1
    });
  }

  static get<T>(key: string): T | null {
    const cached = QUERY_STATS.get(key);
    if (!cached) return null;

    // Aggiorna statistiche
    cached.hits++;
    cached.timestamp = Date.now();

    return cached.data as T;
  }

  static getMostAccessed(limit: number = 10): string[] {
    const entries = Array.from(QUERY_STATS.entries());
    return entries
      .sort(([,a], [,b]) => b.hits - a.hits)
      .slice(0, limit)
      .map(([key]) => key);
  }

  static cleanup(): void {
    const now = Date.now();
    const cutoff = now - CONFIG.MEMORY_TTL;

    const entries = Array.from(QUERY_STATS.entries());
    for (const [key, entry] of entries) {
      if (entry.timestamp < cutoff) {
        QUERY_STATS.delete(key);
      }
    }
  }
}

/**
 * Precaricatore intelligente
 */
export class SmartPreloader {
  private static preloadQueue: string[] = [];
  private static isPreloading = false;

  static async preloadCritical(): Promise<void> {
    if (this.isPreloading) return;
    this.isPreloading = true;

    console.log('[UltraCache] Avvio precaricamento critico');

    try {
      for (let i = 0; i < CONFIG.CRITICAL_ENDPOINTS.length; i++) {
        const endpoint = CONFIG.CRITICAL_ENDPOINTS[i];
        
        // Delay scaglionato
        await new Promise(resolve => setTimeout(resolve, i * CONFIG.PRELOAD_DELAY));
        
        try {
          const cacheKey = `GET-${endpoint}`;
          
          if (UltraCache.has(cacheKey)) {
            continue;
          }

          const cachedData = await getCachedData(cacheKey);
          if (cachedData) {
            UltraCache.set(cacheKey, cachedData);
            console.log(`[UltraCache] Precaricato: ${endpoint}`);
          }
        } catch (error) {
          console.error(`[UltraCache] Errore precaricamento ${endpoint}:`, error);
        }
      }
    } finally {
      this.isPreloading = false;
    }
  }

  static schedulePreload(endpoint: string): void {
    if (!this.preloadQueue.includes(endpoint)) {
      this.preloadQueue.push(endpoint);
    }

    setTimeout(() => this.processQueue(), 50);
  }

  private static async processQueue(): Promise<void> {
    if (this.preloadQueue.length === 0) return;

    const endpoint = this.preloadQueue.shift()!;
    const cacheKey = `GET-${endpoint}`;

    try {
      if (!UltraCache.has(cacheKey)) {
        const cachedData = await getCachedData(cacheKey);
        if (cachedData) {
          UltraCache.set(cacheKey, cachedData);
        }
      }
    } catch (error) {
      console.error(`[UltraCache] Errore processamento coda: ${endpoint}`, error);
    }

    if (this.preloadQueue.length > 0) {
      setTimeout(() => this.processQueue(), 25);
    }
  }
}

/**
 * Fetch ultra-veloce con cache intelligente
 */
export async function ultraFetch<T>(
  endpoint: string,
  options: {
    method?: string;
    body?: any;
    headers?: HeadersInit;
    useCache?: boolean;
    cacheKey?: string;
  } = {}
): Promise<T> {
  const {
    method = 'GET',
    body,
    headers,
    useCache = true,
    cacheKey: customCacheKey
  } = options;

  const cacheKey = customCacheKey || `${method}-${endpoint}`;

  // Controllo cache istantanea per GET
  if (method === 'GET' && useCache) {
    const instantData = UltraCache.get<T>(cacheKey);
    if (instantData) {
      // Prenota precaricamento in background
      SmartPreloader.schedulePreload(endpoint);
      return instantData;
    }
  }

  try {
    // Esegui richiesta
    const response = await fetch(endpoint, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : undefined,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache per GET di successo
    if (method === 'GET') {
      UltraCache.set(cacheKey, data);
      await saveCachedData(cacheKey, data);
      QueryStats.track(cacheKey, data);
    }

    return data;
  } catch (error) {
    // Fallback per GET
    if (method === 'GET') {
      const fallbackData = await getCachedData<T>(cacheKey);
      if (fallbackData) {
        UltraCache.set(cacheKey, fallbackData);
        return fallbackData;
      }
    }
    
    throw error;
  }
}

/**
 * Inizializzazione sistema ultra-cache
 */
export async function initUltraCache(): Promise<void> {
  console.log('[UltraCache] Inizializzazione sistema ultra-veloce');

  // Avvia precaricamento
  SmartPreloader.preloadCritical();

  // Pulizia periodica
  setInterval(() => {
    QueryStats.cleanup();
  }, 5 * 60 * 1000);

  // Listener visibilità pagina
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        SmartPreloader.preloadCritical();
      }
    });
  }

  console.log('[UltraCache] Sistema inizializzato');
}

// Pattern di navigazione per precaricamento predittivo
const ROUTE_PATTERNS: Record<string, string[]> = {
  '/': ['/api/auth/me', '/api/activity-logs'],
  '/products': ['/api/products', '/api/suppliers', '/api/container-types'],
  '/containers': ['/api/containers', '/api/container-types'],
  '/suppliers': ['/api/suppliers'],
  '/inventory': ['/api/products', '/api/containers'],
  '/reports': ['/api/product-labels', '/api/activity-logs'],
  '/scanning': ['/api/products', '/api/containers', '/api/suppliers'],
  '/settings': ['/api/auth/me']
};

/**
 * Precarica dati per una rotta specifica
 */
export function preloadForRoute(route: string): void {
  const endpoints = ROUTE_PATTERNS[route] || [];
  endpoints.forEach(endpoint => {
    SmartPreloader.schedulePreload(endpoint);
  });
}

/**
 * Statistiche cache
 */
export function getCacheStats() {
  return {
    instantCacheSize: INSTANT_CACHE.size,
    queryStatsSize: QUERY_STATS.size,
    mostAccessed: QueryStats.getMostAccessed(5)
  };
}

// Auto-inizializzazione
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initUltraCache, 300);
    });
  } else {
    setTimeout(initUltraCache, 300);
  }
}