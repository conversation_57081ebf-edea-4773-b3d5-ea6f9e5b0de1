/**
 * PWA Manager - Gestione migliorata per l'integrazione PWA
 * Questo modulo centralizza tutte le funzionalità relative alla PWA
 * 
 * MODIFICATO: migliorata la gestione degli errori e aggiunta compatibilità con diversi browser
 */

import { clientLogger } from './clientLogger';

// Environment detection with improved reliability
const isDevMode = 
  typeof window !== 'undefined' && (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('.replit.dev') ||
    import.meta.env.DEV
  );

// Log environment state for debugging
clientLogger.dev('PWA Manager environment detected', { 
  isDevelopment: isDevMode,
  hostname: typeof window !== 'undefined' ? window.location.hostname : 'N/A'
});

// Oggetto per mantenere lo stato della PWA
interface PWAState {
  needRefresh: boolean;
  offlineReady: boolean;
  registration: ServiceWorkerRegistration | null;
  updateSW: (() => Promise<void>) | null;
}

export const pwaState: PWAState = {
  needRefresh: false,
  offlineReady: false,
  registration: null,
  updateSW: null
};

/**
 * Inizializza il service worker
 * con supporto per aggiornamenti e notifiche
 * Ottimizzato per prestazioni migliori
 */
export function initPWA(): void {
  // In ambiente di sviluppo, assicuriamoci di disattivare completamente il service worker
  if (isDevMode) {
    clientLogger.dev('Development environment - disabling service workers completely');
    
    // Disattiviamo in modo più aggressivo i service worker esistenti
    if ('serviceWorker' in navigator) {
      // Prima proviamo a disattivare i service worker
      navigator.serviceWorker.getRegistrations()
        .then(registrations => {
          if (registrations.length === 0) {
            clientLogger.dev('No service workers to unregister');
            return;
          }
          
          clientLogger.dev('Found service workers to unregister', { count: registrations.length });
          
          // Disattiviamo tutti i service worker registrati
          return Promise.all(
            registrations.map(registration => {
              clientLogger.dev('Unregistering service worker', { scope: registration.scope });
              return registration.unregister()
                .then(success => {
                  if (success) {
                    clientLogger.dev('Service worker unregistered successfully', { scope: registration.scope });
                  } else {
                    clientLogger.error('Failed to unregister service worker', { scope: registration.scope });
                  }
                  return success;
                });
            })
          );
        })
        .then(() => {
          // Dopo la disattivazione, cancella tutte le cache
          if ('caches' in window) {
            clientLogger.dev('Cleaning browser caches in development environment');
            return caches.keys()
              .then(cacheNames => {
                if (cacheNames.length === 0) {
                  clientLogger.dev('No caches to delete');
                  return;
                }
                
                clientLogger.dev('Found caches to delete', { count: cacheNames.length });
                
                return Promise.all(
                  cacheNames.map(cacheName => {
                    clientLogger.dev('Deleting cache', { cacheName });
                    return caches.delete(cacheName)
                      .then(success => {
                        if (success) {
                          clientLogger.dev('Cache deleted successfully', { cacheName });
                        } else {
                          clientLogger.error('Failed to delete cache', { cacheName });
                        }
                        return success;
                      });
                  })
                );
              });
          }
        })
        .catch(err => {
          clientLogger.error('Error during service worker and cache cleanup', { error: err });
        });
    }
    
    // In ambiente di sviluppo, NON proseguiamo con l'inizializzazione del service worker
    return;
  }
  
  // Solo in produzione registriamo il service worker
  try {
    // Verifichiamo prima che il service worker sia supportato
    if (!('serviceWorker' in navigator)) {
      clientLogger.error('Service Workers not supported in this browser');
      return;
    }
    
    // Registriamo direttamente il service worker con controllo eventi
    if ('serviceWorker' in navigator) {
      // Ottimizzato: utilizzo avanzato delle opzioni di registrazione
      navigator.serviceWorker.register('/service-worker.js', {
        updateViaCache: 'none', // Non utilizziamo la cache del browser per il service worker
        scope: '/' // Assicuriamo che il service worker controlli l'intera applicazione
      }).then((registration: ServiceWorkerRegistration) => {
        pwaState.registration = registration;
        clientLogger.info('Service Worker registered successfully', { scope: registration.scope });
        
        // Promise.all per configurare in parallelo le API di sincronizzazione
        const syncPromises = [];
        
        // Configura la gestione della sincronizzazione in background se supportata
        // Corretto l'errore di tipo utilizzando cast e verifiche di tipo più sicure
        if (registration && 'sync' in registration) {
          try {
            // Utilizziamo un casting di tipo per evitare errori TypeScript
            const syncManager = (registration as any).sync;
            if (syncManager && typeof syncManager.register === 'function') {
              syncPromises.push(
                syncManager.register('haccp-sync')
                  .then(() => clientLogger.info('Background sync registered'))
                  .catch((err: Error) => clientLogger.error('Background sync registration error', { error: err }))
              );
            }
          } catch (error) {
            clientLogger.error('Error configuring background sync', { error: error instanceof Error ? error.message : String(error) });
          }
        }
        
        // Configura la sincronizzazione periodica se supportata
        if (registration && 'periodicSync' in registration) {
          try {
            // Utilizziamo un casting di tipo più sicuro
            const periodicSyncManager = (registration as any).periodicSync;
            if (periodicSyncManager && typeof periodicSyncManager.register === 'function') {
              syncPromises.push(
                periodicSyncManager.register('haccp-periodic', {
                  minInterval: 24 * 60 * 60 * 1000 // 24 ore
                })
                  .then(() => clientLogger.info('Periodic sync registered'))
                  .catch((err: Error) => clientLogger.error('Periodic sync registration error', { error: err }))
              );
            }
          } catch (error) {
            clientLogger.error('Error configuring periodic sync', { error: error instanceof Error ? error.message : String(error) });
          }
        }

        // Ascoltiamo gli eventi di aggiornamento con gestione errori migliorata
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              try {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  pwaState.needRefresh = true;
                  clientLogger.info('New content available, click on reload button to update');
                } else if (newWorker.state === 'activated') {
                  pwaState.offlineReady = true;
                  clientLogger.info('App ready to work offline');
                } else {
                  clientLogger.dev('Service worker state changed', { state: newWorker.state });
                }
              } catch (error) {
                clientLogger.error('Service worker state change error', { error: error instanceof Error ? error.message : String(error) });
              }
            });
          }
        });

        // Definisci la funzione di aggiornamento con gestione errori migliorata
        pwaState.updateSW = () => {
          return new Promise<void>((resolve, reject) => {
            if (registration.waiting) {
              // Inviamo un messaggio al service worker in due formati per supportare diverse implementazioni
              try {
                // Prima proviamo con il formato standard (oggetto)
                registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                
                // Poi inviamo anche come stringa diretta (approccio alternativo)
                // Alcuni browser e implementazioni di service worker potrebbero gestire meglio questo formato
                setTimeout(() => {
                  if (registration.waiting) {
                    registration.waiting.postMessage('SKIP_WAITING');
                  }
                }, 100);
                
                clientLogger.dev('Update requests sent to service worker');
              } catch (err) {
                const errorMsg = err instanceof Error ? err.message : String(err);
                clientLogger.error('Error sending update message', { error: errorMsg });
                // Non blocchiamo il processo, continuiamo comunque
              }
            } else {
              clientLogger.dev('No service worker waiting to update');
            }

            // Imposta un timeout di sicurezza in caso di mancata risposta
            const timeoutId = setTimeout(() => {
              clientLogger.warn('Service worker update timeout - forcing reload');
              window.location.reload();
              resolve();
            }, 3000);

            // Ascoltiamo l'evento di cambio controller
            const onControllerChange = () => {
              clearTimeout(timeoutId);
              navigator.serviceWorker.removeEventListener('controllerchange', onControllerChange);
              clientLogger.info('Service Worker updated, reloading page...');
              // Attendiamo un momento prima di ricaricare per consentire all'evento di propagarsi
              setTimeout(() => {
                window.location.reload(); // Ricarica la pagina dopo l'attivazione
                resolve();
              }, 50);
            };

            navigator.serviceWorker.addEventListener('controllerchange', onControllerChange);
          });
        };
      }).catch((error: Error) => {
        console.error('Service Worker registration error:', error);
      });

      // Verifica periodicamente gli aggiornamenti
      setInterval(() => {
        navigator.serviceWorker.ready.then(registration => {
          registration.update().catch((error: Error) => {
            console.error('Service Worker update error:', error);
          });
        });
      }, 60 * 60 * 1000); // Controlla aggiornamenti ogni ora
    }
  } catch (error) {
    console.error('Error initializing PWA:', error);
  }
}

/**
 * Forza l'aggiornamento del service worker
 */
export function updateServiceWorker(): Promise<void> {
  if (pwaState.updateSW) {
    return pwaState.updateSW();
  }
  return Promise.resolve();
}

/**
 * Verifica se l'app è stata installata come PWA
 */
export function isPwaInstalled(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches || 
         ('standalone' in window.navigator && (window.navigator as any).standalone === true);
}

/**
 * Genera banner di aggiornamento quando c'è una nuova versione disponibile
 */
export function createRefreshUI(onRefresh: () => void, onClose: () => void) {
  if (pwaState.needRefresh) {
    return {
      show: true,
      message: 'Nuova versione disponibile!',
      refreshAction: () => {
        pwaState.needRefresh = false;
        updateServiceWorker().then(() => {
          onRefresh();
        });
      },
      closeAction: () => {
        pwaState.needRefresh = false;
        onClose();
      }
    };
  }
  
  return { show: false };
}

/**
 * Gestisce il prompt di installazione PWA
 */
export class InstallPromptManager {
  private deferredPrompt: any = null;
  private promptEventCallback: ((event: Event) => void) | null = null;
  
  constructor() {
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    // Cattura l'evento beforeinstallprompt per mostrare il prompt personalizzato
    this.promptEventCallback = (e: Event) => {
      e.preventDefault();
      this.deferredPrompt = e;
    };
    
    window.addEventListener('beforeinstallprompt', this.promptEventCallback);
  }
  
  public canInstall(): boolean {
    return !!this.deferredPrompt;
  }
  
  public async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }
    
    // Mostra il prompt di installazione
    this.deferredPrompt.prompt();
    
    // Attendi la risposta dell'utente
    try {
      const choiceResult = await this.deferredPrompt.userChoice;
      
      // Pulisci il prompt dopo l'uso
      this.deferredPrompt = null;
      
      // Restituisci true se l'utente ha accettato l'installazione
      return choiceResult.outcome === 'accepted';
    } catch (error) {
      console.error('Errore durante il prompt di installazione:', error);
      this.deferredPrompt = null;
      return false;
    }
  }
  
  public destroy() {
    if (this.promptEventCallback) {
      window.removeEventListener('beforeinstallprompt', this.promptEventCallback);
      this.promptEventCallback = null;
    }
    this.deferredPrompt = null;
  }
}

// Funzioni di utilità per la gestione dei permessi
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    return 'denied';
  }
  
  if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
    return await Notification.requestPermission();
  }
  
  return Notification.permission;
}

// Funzioni di supporto per il fallback al service worker manuale
export async function checkServiceWorkerStatus(): Promise<boolean> {
  if (!('serviceWorker' in navigator)) {
    return false;
  }
  
  const registrations = await navigator.serviceWorker.getRegistrations();
  return registrations.length > 0;
}