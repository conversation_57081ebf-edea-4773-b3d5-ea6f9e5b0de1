/**
 * Client-side Logger for HACCP Tracker
 * Environment-aware logging for frontend
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogContext {
  component?: string;
  userId?: string;
  action?: string;
  [key: string]: any;
}

class ClientLogger {
  private isDevelopment: boolean;
  private minLevel: LogLevel;

  constructor() {
    this.isDevelopment = 
      window.location.hostname === 'localhost' || 
      window.location.hostname === '127.0.0.1' ||
      window.location.hostname.includes('.replit.dev') ||
      import.meta.env.DEV;
    
    this.minLevel = this.isDevelopment ? LogLevel.DEBUG : LogLevel.WARN;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLevel;
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const contextStr = context ? ` ${JSON.stringify(context)}` : '';
    return `[${timestamp}] ${levelName}: ${message}${contextStr}`;
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage(LogLevel.DEBUG, message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage(LogLevel.INFO, message, context));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage(LogLevel.WARN, message, context));
    }
  }

  error(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      console.error(this.formatMessage(LogLevel.ERROR, message, context));
    }
  }

  // Performance monitoring
  performance(operation: string, duration: number, context?: LogContext): void {
    if (this.isDevelopment) {
      this.debug(`Performance: ${operation} took ${duration}ms`, context);
    }
  }

  // Development-only features
  dev(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      this.debug(`DEV: ${message}`, context);
    }
  }

  // Cache operations
  cache(operation: string, context?: LogContext): void {
    if (this.isDevelopment) {
      this.debug(`Cache: ${operation}`, context);
    }
  }

  // Auth operations
  auth(event: string, context?: LogContext): void {
    this.info(`Auth: ${event}`, context);
  }
}

export const clientLogger = new ClientLogger();