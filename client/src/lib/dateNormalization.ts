/**
 * Normalizzazione delle date nel sistema HACCP
 * Tutti i formati di data devono essere convertiti a DD/MM/YYYY
 */

/**
 * Normalizza una data al formato standard DD/MM/YYYY
 * Supporta i formati: DD/MM/YYYY, DD-MM-YYYY, DD/MM/YY, DD-MM-YY, ISO (YYYY-MM-DDTHH:mm:ss.sssZ), YYYY-MM-DD
 */
export function normalizeDateToStandard(dateStr: string | null | undefined): string | null {
  if (!dateStr || typeof dateStr !== 'string') {
    return null;
  }

  const trimmed = dateStr.trim();
  if (!trimmed) {
    return null;
  }

  // Se è già nel formato DD/MM/YYYY corretto, restituiscilo
  if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(trimmed)) {
    return trimmed;
  }

  // Se è nel formato DD-MM-YYYY, convertilo a DD/MM/YYYY
  if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(trimmed)) {
    return trimmed.replace(/-/g, '/');
  }

  // Se è nel formato DD/MM/YY, convertilo a DD/MM/YYYY
  if (/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(trimmed)) {
    const parts = trimmed.split('/');
    const day = parts[0];
    const month = parts[1];
    let year = parseInt(parts[2], 10);
    
    // Assume che gli anni a 2 cifre < 50 siano 20XX, altrimenti 19XX
    year = year < 50 ? 2000 + year : 1900 + year;
    
    return `${day}/${month}/${year}`;
  }

  // Se è nel formato DD-MM-YY, convertilo a DD/MM/YYYY
  if (/^\d{1,2}-\d{1,2}-\d{2}$/.test(trimmed)) {
    const parts = trimmed.split('-');
    const day = parts[0];
    const month = parts[1];
    let year = parseInt(parts[2], 10);
    
    // Assume che gli anni a 2 cifre < 50 siano 20XX, altrimenti 19XX
    year = year < 50 ? 2000 + year : 1900 + year;
    
    return `${day}/${month}/${year}`;
  }

  // Se è una data ISO (YYYY-MM-DDTHH:mm:ss.sssZ), convertila
  if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(trimmed)) {
    try {
      const date = new Date(trimmed);
      if (!isNaN(date.getTime())) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      }
    } catch (error) {
      console.warn(`Errore nella conversione della data ISO: ${dateStr}`, error);
    }
  }

  // Se è una data in formato YYYY-MM-DD, convertila
  if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
    try {
      const parts = trimmed.split('-');
      const year = parts[0];
      const month = parts[1];
      const day = parts[2];
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.warn(`Errore nella conversione della data YYYY-MM-DD: ${dateStr}`, error);
    }
  }

  console.warn(`Formato data non riconosciuto: ${dateStr}`);
  return null;
}

/**
 * Valida che una data sia nel formato DD/MM/YYYY e sia una data valida
 */
export function isValidDateFormat(dateStr: string): boolean {
  if (!dateStr || !/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
    return false;
  }

  const parts = dateStr.split('/');
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);

  return isValidDate(day, month, year);
}

/**
 * Verifica che una data sia valida
 */
function isValidDate(day: number, month: number, year: number): boolean {
  if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > 2100) {
    return false;
  }
  
  // Verifica giorni per mese
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  
  // Anno bisestile
  if (month === 2 && ((year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0))) {
    return day <= 29;
  }
  
  return day <= daysInMonth[month - 1];
}

/**
 * Formatta una data per la visualizzazione (sempre DD/MM/YYYY)
 */
export function formatDateForDisplay(dateInput: string | null | undefined): string {
  const normalized = normalizeDateToStandard(dateInput);
  return normalized || 'Data non valida';
}