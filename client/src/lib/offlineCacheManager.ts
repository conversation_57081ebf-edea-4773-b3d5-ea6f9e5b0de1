import { logger } from './logger';
import { saveCachedData, getCachedData } from './offlineAPI';

// Costanti per identificare i tipi di cache
export const CACHE_TYPES = {
  PRODUCTS: 'products_cache',
  CONTAINERS: 'containers_cache',
  SUPPLIERS: 'suppliers_cache',
  DDT: 'ddt_cache',
  USERS: 'users_cache',
  ACTIVITY_LOGS: 'activity_logs_cache',
  CONTAINER_TYPES: 'container_types_cache',
  PRODUCT_TEMPLATES: 'product_templates_cache',
};

// Tempo di scadenza della cache in millisecondi
const CACHE_EXPIRY_TIMES = {
  SHORT: 5 * 60 * 1000, // 5 minuti
  MEDIUM: 30 * 60 * 1000, // 30 minuti
  LONG: 12 * 60 * 60 * 1000, // 12 ore
  PERSISTENT: 7 * 24 * 60 * 60 * 1000, // 7 giorni
};

// Interfaccia per le opzioni di cache
interface CacheOptions {
  forceRefresh?: boolean;
  expiry?: number;
  onlyIfOnline?: boolean;
}

/**
 * Funzione generica per creare, ottenere e aggiornare un file di cache
 * 
 * @param cacheKey - Chiave identificativa della cache
 * @param fetchFunction - Funzione per recuperare i dati freschi dal server
 * @param options - Opzioni per la gestione della cache
 * @returns I dati dalla cache o freschi dal server
 */
export async function createOrUpdateCache<T>(
  cacheKey: string,
  fetchFunction: () => Promise<T>,
  options: CacheOptions = {}
): Promise<T | null> {
  const { 
    forceRefresh = false, 
    expiry = CACHE_EXPIRY_TIMES.MEDIUM,
    onlyIfOnline = false
  } = options;

  // Log l'inizio dell'operazione
  logger.log(`[CacheManager] Richiesta cache per: ${cacheKey}`);

  try {
    // Se non siamo online e l'opzione onlyIfOnline è true, non aggiorniamo la cache
    if (onlyIfOnline && !navigator.onLine) {
      logger.log(`[CacheManager] Offline, non aggiorno la cache per: ${cacheKey}`);
      return getCachedData<T>(cacheKey);
    }

    // Se non è richiesto un refresh forzato, prova a recuperare dalla cache
    if (!forceRefresh) {
      const cachedData = await getCachedData<T>(cacheKey);
      if (cachedData) {
        logger.log(`[CacheManager] Dati recuperati dalla cache per: ${cacheKey}`);
        return cachedData;
      }
    }

    // Se siamo online, recupera dati freschi e aggiorna la cache
    if (navigator.onLine) {
      logger.log(`[CacheManager] Recupero dati freschi per: ${cacheKey}`);
      const freshData = await fetchFunction();
      
      // Salva i dati freschi in cache
      if (freshData) {
        logger.log(`[CacheManager] Aggiornamento cache per: ${cacheKey}`);
        await saveCachedData(cacheKey, freshData, expiry);
      }
      
      return freshData;
    } else {
      // Se offline, restituisci i dati in cache (che a questo punto sarebbero null)
      logger.log(`[CacheManager] Offline, nessun dato disponibile per: ${cacheKey}`);
      return null;
    }
  } catch (error) {
    logger.error(`[CacheManager] Errore durante l'operazione di cache per ${cacheKey}:`, error);
    
    // In caso di errore, prova a recuperare dalla cache
    const cachedData = await getCachedData<T>(cacheKey);
    if (cachedData) {
      logger.log(`[CacheManager] Recuperati dati dalla cache dopo errore per: ${cacheKey}`);
      return cachedData;
    }
    
    return null;
  }
}

/**
 * Funzione per aggiornare tutte le cache principali dell'applicazione
 * Questa funzione può essere chiamata all'avvio dell'app o dopo il login
 */
export async function updateAllCaches(apiRequest: any): Promise<void> {
  if (!navigator.onLine) {
    logger.log('[CacheManager] Offline, aggiornamento cache rimandata');
    return;
  }

  logger.log('[CacheManager] Avvio aggiornamento di tutte le cache');
  
  try {
    // Aggiorna cache prodotti
    createOrUpdateCache(
      CACHE_TYPES.PRODUCTS,
      () => apiRequest('/api/product-labels', 'GET'),
      { expiry: CACHE_EXPIRY_TIMES.LONG }
    );
    
    // Aggiorna cache contenitori
    createOrUpdateCache(
      CACHE_TYPES.CONTAINERS,
      () => apiRequest('/api/containers', 'GET'),
      { expiry: CACHE_EXPIRY_TIMES.MEDIUM }
    );
    
    // Aggiorna cache fornitori
    createOrUpdateCache(
      CACHE_TYPES.SUPPLIERS,
      () => apiRequest('/api/suppliers', 'GET'),
      { expiry: CACHE_EXPIRY_TIMES.LONG }
    );
    
    // Aggiorna cache tipi di contenitore
    createOrUpdateCache(
      CACHE_TYPES.CONTAINER_TYPES,
      () => apiRequest('/api/container-types', 'GET'),
      { expiry: CACHE_EXPIRY_TIMES.PERSISTENT }
    );
    
    // Aggiorna cache template prodotti
    createOrUpdateCache(
      CACHE_TYPES.PRODUCT_TEMPLATES,
      () => apiRequest('/api/product-templates', 'GET'),
      { expiry: CACHE_EXPIRY_TIMES.PERSISTENT }
    );
    
    logger.log('[CacheManager] Aggiornamento di tutte le cache completato');
  } catch (error) {
    logger.error('[CacheManager] Errore durante l\'aggiornamento delle cache:', error);
  }
}

/**
 * Funzione helper per recuperare dati dalla cache con fallback su richiesta API
 * 
 * @param cacheKey - Chiave della cache
 * @param apiEndpoint - Endpoint API per il recupero dati freschi
 * @param apiRequest - Funzione di richiesta API con supporto offline
 * @param options - Opzioni per la gestione della cache
 */
export async function getDataWithOfflineSupport<T>(
  cacheKey: string,
  apiEndpoint: string,
  apiRequest: any,
  options: CacheOptions = {}
): Promise<T | null> {
  return createOrUpdateCache<T>(
    cacheKey,
    () => apiRequest(apiEndpoint, 'GET'),
    options
  );
}

/**
 * Funzione per pianificare aggiornamenti periodici della cache
 * 
 * @param apiRequest - Funzione di richiesta API con supporto offline
 * @param intervalMinutes - Intervallo di aggiornamento in minuti
 */
export function schedulePeriodicCacheUpdates(apiRequest: any, intervalMinutes: number = 30): () => void {
  logger.log(`[CacheManager] Pianificazione aggiornamenti cache ogni ${intervalMinutes} minuti`);
  
  // Esegui primo aggiornamento all'avvio
  setTimeout(() => {
    if (navigator.onLine) {
      updateAllCaches(apiRequest);
    }
  }, 5000); // Ritardo di 5 secondi per l'aggiornamento iniziale
  
  // Pianifica aggiornamenti periodici
  const intervalId = setInterval(() => {
    if (navigator.onLine) {
      logger.log('[CacheManager] Esecuzione aggiornamento periodico cache');
      updateAllCaches(apiRequest);
    }
  }, intervalMinutes * 60 * 1000);
  
  // Restituisci una funzione per cancellare l'intervallo se necessario
  return () => {
    logger.log('[CacheManager] Cancellazione pianificazione aggiornamenti cache');
    clearInterval(intervalId);
  };
}