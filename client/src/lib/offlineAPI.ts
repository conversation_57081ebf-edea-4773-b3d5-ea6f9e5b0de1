import { apiRequest, queryClient } from "./queryClient";
import { logger } from "./logger";

// Nome del database IndexedDB per le operazioni offline - ottimizzato con versione incrementata
const OFFLINE_DB_NAME = 'haccp-offline-storage';
const OFFLINE_DB_VERSION = 2; // Incrementata la versione per includere nuove ottimizzazioni
const PENDING_STORE_NAME = 'pending-operations';
const CACHED_DATA_STORE = 'cached-data';
const METADATA_STORE = 'storage-metadata'; // Nuovo store per metadati e gestione ottimizzata

// Modulo per il logging
const LOG_MODULE = 'OfflineAPI';

/**
 * Genera un ID univoco utilizzando UUID v4 per garantire unicità assoluta
 * Fallback su crypto.getRandomValues() per compatibilità cross-browser
 */
function generateUniqueId(): string {
  // Prova a usare crypto.randomUUID() se disponibile (moderno)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // Fallback per browser meno recenti usando crypto.getRandomValues()
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    // Implementazione UUID v4 RFC 4122 compliant
    const bytes = new Uint8Array(16);
    crypto.getRandomValues(bytes);
    
    // Set version (4) e variant bits secondo RFC 4122
    bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
    bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10
    
    // Converte in stringa UUID formato standard
    const hex = Array.from(bytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    return [
      hex.substring(0, 8),
      hex.substring(8, 12),
      hex.substring(12, 16),
      hex.substring(16, 20),
      hex.substring(20, 32)
    ].join('-');
  }
  
  // Fallback finale per ambienti molto limitati (meno sicuro ma ancora ragionevole)
  // Combina timestamp con randomness per ridurre probabilità di collisione
  const timestamp = Date.now().toString(36);
  const random1 = Math.random().toString(36).substring(2, 15);
  const random2 = Math.random().toString(36).substring(2, 15);
  return `${timestamp}-${random1}-${random2}`;
}

// Costanti per la gestione della cache
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000; // 5 minuti in milliseconds
const MAX_CACHE_ITEMS = 500; // Limita il numero di elementi in cache
const CACHE_CLEANUP_INTERVAL = 10 * 60 * 1000; // Pulizia ogni 10 minuti
const MEMORY_CACHE_SIZE = 100; // Massimo numero di elementi in memoria per controllo di RAM

// Cache in memoria per le operazioni più frequenti
const memoryCache = new Map<string, {data: any, timestamp: number, expiry: number}>();

// Stato globale per gestire le connessioni al database
let dbInstance: IDBDatabase | null = null;
let dbConnectingPromise: Promise<IDBDatabase> | null = null;
let lastCleanupTime = Date.now();

/**
 * Funzione di pulizia automatica della cache che rimuove i dati scaduti
 * per mantenere alte prestazioni del database
 */
export const cleanupCache = async (): Promise<void> => {
  // Verifica se è passato abbastanza tempo dall'ultima pulizia
  const now = Date.now();
  if (now - lastCleanupTime < CACHE_CLEANUP_INTERVAL) {
    return; // Non eseguire la pulizia troppo frequentemente
  }
  lastCleanupTime = now;
  
  try {
    // Pulisci la memoria cache scaduta
    for (const [key, entry] of memoryCache.entries()) {
      if (entry.expiry < now) {
        memoryCache.delete(key);
      }
    }
    
    // Pulisci il database
    const db = await initOfflineDB();
    
    return new Promise((resolve, reject) => {
      const tx = db.transaction(CACHED_DATA_STORE, 'readwrite');
      const store = tx.objectStore(CACHED_DATA_STORE);
      const expiryIndex = store.index('expiry');
      
      // Trova tutti gli elementi scaduti
      const range = IDBKeyRange.upperBound(now);
      const request = expiryIndex.openCursor(range);
      
      let deleteCount = 0;
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          // Elimina l'elemento scaduto
          store.delete(cursor.primaryKey);
          deleteCount++;
          cursor.continue();
        }
      };
      
      tx.oncomplete = () => {
        if (deleteCount > 0) {
          console.log(`Cache cleanup: rimossi ${deleteCount} elementi scaduti`);
        }
        db.close();
        resolve();
      };
      
      tx.onerror = () => {
        console.error('Errore durante la pulizia della cache:', tx.error);
        db.close();
        reject(tx.error);
      };
    });
  } catch (error) {
    console.error('Errore durante la pulizia della cache:', error);
  }
};

// Interfaccia per un'operazione in attesa
interface PendingOperation {
  id?: number;
  url: string;
  method: string;
  body?: any;
  headers?: HeadersInit;
  timestamp: number;
  cacheName?: string;
}

// Interfaccia per i dati in cache
interface CachedData {
  key: string;
  data: any;
  timestamp: number;
  expiry?: number; // Tempo di scadenza in millisecondi
}

/**
 * Inizializza il database IndexedDB per le operazioni offline
 */
export const initOfflineDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    if (!('indexedDB' in window)) {
      reject(new Error('IndexedDB non supportato in questo browser'));
      return;
    }
    
    const request = indexedDB.open(OFFLINE_DB_NAME, OFFLINE_DB_VERSION);
    
    request.onerror = (event) => {
      console.error(`Errore nell'apertura del database offline: ${(event.target as any).error}`);
      reject((event.target as any).error);
    };
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      const oldVersion = event.oldVersion;
      
      // Aggiornamento incrementale del database per ottimizzare le prestazioni
      if (oldVersion < 1) {
        // Crea object store per operazioni in attesa
        const pendingStore = db.createObjectStore(PENDING_STORE_NAME, { keyPath: 'id', autoIncrement: true });
        pendingStore.createIndex('timestamp', 'timestamp', { unique: false });
        console.log('Object store per operazioni offline creato');
        
        // Crea object store per dati in cache
        const cacheStore = db.createObjectStore(CACHED_DATA_STORE, { keyPath: 'key' });
        cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
        console.log('Object store per dati in cache creato');
      }
      
      // Nuove ottimizzazioni nella versione 2
      if (oldVersion < 2) {
        // Aggiungiamo indici per migliorare le prestazioni
        const transaction = event.target.transaction;
        
        // Ottimizziamo lo store delle operazioni pendenti
        if (transaction) {
          const pendingStore = transaction.objectStore(PENDING_STORE_NAME);
          
          // Verifica se l'indice esiste già prima di crearlo
          if (!pendingStore.indexNames.contains('url_method')) {
            pendingStore.createIndex('url_method', ['url', 'method'], { unique: false });
            console.log('Indice composito url_method aggiunto per ricerche più veloci');
          }
          
          // Ottimizziamo lo store della cache
          if (transaction.objectStore(CACHED_DATA_STORE) && 
              !transaction.objectStore(CACHED_DATA_STORE).indexNames.contains('expiry')) {
            transaction.objectStore(CACHED_DATA_STORE).createIndex('expiry', 'expiry', { unique: false });
            console.log('Indice expiry aggiunto per ottimizzare la pulizia della cache');
          }
          
          // Creiamo lo store per i metadati se non esiste
          if (!db.objectStoreNames.contains(METADATA_STORE)) {
            const metaStore = db.createObjectStore(METADATA_STORE, { keyPath: 'key' });
            metaStore.add({
              key: 'lastCleanup',
              timestamp: Date.now(),
              version: OFFLINE_DB_VERSION
            });
            console.log('Store per metadati creato per ottimizzare la gestione della cache');
          }
        }
      }
    };
    
    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      console.log('Database offline inizializzato con successo');
      resolve(db);
    };
  });
};

/**
 * Salva un'operazione in attesa nel database offline
 */
export const savePendingOperation = async (operation: PendingOperation): Promise<number> => {
  const db = await initOfflineDB();
  
  return new Promise((resolve, reject) => {
    const tx = db.transaction(PENDING_STORE_NAME, 'readwrite');
    const store = tx.objectStore(PENDING_STORE_NAME);
    
    const request = store.add(operation);
    
    request.onsuccess = () => {
      resolve(request.result as number);
      console.log(`Operazione salvata per esecuzione offline con ID: ${request.result}`);
    };
    
    request.onerror = () => {
      reject(request.error);
      console.error(`Errore nel salvataggio dell'operazione offline: ${request.error}`);
    };
    
    tx.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Recupera tutte le operazioni in attesa dal database offline
 */
export const getPendingOperations = async (): Promise<PendingOperation[]> => {
  const db = await initOfflineDB();
  
  return new Promise((resolve, reject) => {
    const tx = db.transaction(PENDING_STORE_NAME, 'readonly');
    const store = tx.objectStore(PENDING_STORE_NAME);
    
    const request = store.getAll();
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    tx.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Elimina un'operazione in attesa dal database offline
 */
export const deletePendingOperation = async (id: number): Promise<void> => {
  const db = await initOfflineDB();
  
  return new Promise((resolve, reject) => {
    const tx = db.transaction(PENDING_STORE_NAME, 'readwrite');
    const store = tx.objectStore(PENDING_STORE_NAME);
    
    const request = store.delete(id);
    
    request.onsuccess = () => {
      resolve();
      console.log(`Operazione con ID ${id} eliminata dal database offline`);
    };
    
    request.onerror = () => {
      reject(request.error);
      console.error(`Errore nell'eliminazione dell'operazione con ID ${id}: ${request.error}`);
    };
    
    tx.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Salva dati in cache nel database offline
 */
export const saveCachedData = async (key: string, data: any, expiry?: number): Promise<void> => {
  const db = await initOfflineDB();
  
  return new Promise((resolve, reject) => {
    const tx = db.transaction(CACHED_DATA_STORE, 'readwrite');
    const store = tx.objectStore(CACHED_DATA_STORE);
    
    const cachedData: CachedData = {
      key,
      data,
      timestamp: Date.now(),
      expiry: expiry ? Date.now() + expiry : undefined
    };
    
    const request = store.put(cachedData);
    
    request.onsuccess = () => {
      resolve();
      console.log(`Dati salvati in cache con chiave: ${key}`);
    };
    
    request.onerror = () => {
      reject(request.error);
      console.error(`Errore nel salvataggio dei dati in cache: ${request.error}`);
    };
    
    tx.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Recupera dati dalla cache offline
 * @returns I dati in cache o null se non trovati o scaduti
 */
export const getCachedData = async <T>(key: string): Promise<T | null> => {
  const db = await initOfflineDB();
  
  return new Promise((resolve, reject) => {
    const tx = db.transaction(CACHED_DATA_STORE, 'readonly');
    const store = tx.objectStore(CACHED_DATA_STORE);
    
    const request = store.get(key);
    
    request.onsuccess = () => {
      const cachedData = request.result as CachedData | undefined;
      
      if (!cachedData) {
        resolve(null);
        return;
      }
      
      // Controlla se i dati sono scaduti
      if (cachedData.expiry && Date.now() > cachedData.expiry) {
        console.log(`Dati in cache per la chiave ${key} scaduti`);
        resolve(null);
        return;
      }
      
      console.log(`Dati recuperati dalla cache per la chiave: ${key}`);
      resolve(cachedData.data as T);
    };
    
    request.onerror = () => {
      reject(request.error);
      console.error(`Errore nel recupero dei dati dalla cache: ${request.error}`);
    };
    
    tx.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Registra la sincronizzazione in background se supportata
 */
export const registerBackgroundSync = () => {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    navigator.serviceWorker.ready
      .then(registration => {
        // Registra la sincronizzazione in background
        // Verifica se sync è supportato
        if ('sync' in registration) {
          // TypeScript non riconosce il tipo sync, ma è disponibile nei browser moderni
          return (registration as any).sync.register('haccp-tracker-sync-queue');
        }
        return Promise.resolve();
      })
      .then(() => {
        console.log('Background Sync registrato con successo');
      })
      .catch(err => {
        console.error(`Errore nella registrazione del Background Sync: ${err}`);
      });
  } else {
    console.warn('Background Sync non supportato in questo browser');
  }
};

/**
 * Processa tutte le operazioni in attesa
 */
export const processPendingOperations = async (): Promise<void> => {
  if (!navigator.onLine) {
    console.log('Offline, non è possibile processare le operazioni in attesa');
    return;
  }
  
  try {
    const operations = await getPendingOperations();
    
    if (operations.length === 0) {
      console.log('Nessuna operazione in attesa da processare');
      return;
    }
    
    console.log(`Elaborazione di ${operations.length} operazioni in attesa`);
    
    for (const op of operations) {
      try {
        console.log(`Processando operazione #${op.id}: ${op.method} ${op.url}`);
        
        // Esegui la richiesta
        const response = await fetch(op.url, {
          method: op.method,
          headers: op.headers || {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          body: op.body ? JSON.stringify(op.body) : undefined,
          credentials: 'include'
        });
        
        console.log(`Risposta operazione #${op.id}: status=${response.status}, ok=${response.ok}`);
        
        if (response.ok) {
          // Se la richiesta ha successo, rimuovi l'operazione dal database
          await deletePendingOperation(op.id!);
          console.log(`Operazione #${op.id} completata e rimossa dalla coda`);
          
          // Invalida le query correlate per aggiornare l'interfaccia utente
          try {
            await queryClient.invalidateQueries();
            console.log('Cache dati invalidata per aggiornamento UI');
          } catch (error) {
            console.warn('Impossibile invalidare le query cache:', error);
          }
        } else {
          const errorText = await response.text();
          console.error(`Errore nell'elaborazione dell'operazione #${op.id}: ${response.status} - ${errorText}`);
        }
      } catch (error) {
        console.error(`Errore durante l'elaborazione dell'operazione #${op.id}:`, error);
      }
    }
  } catch (error) {
    console.error('Errore durante il recupero delle operazioni in attesa:', error);
  }
};

/**
 * API wrapper che gestisce le richieste offline
 * @param url URL della richiesta
 * @param method Metodo HTTP
 * @param data Dati da inviare (per POST, PUT, PATCH)
 * @param options Opzioni aggiuntive
 * @returns Promise con i dati della risposta
 */
export async function offlineApiRequest<T>(
  url: string,
  method: string = "GET",
  data?: any,
  options: {
    cacheKey?: string;
    cacheExpiry?: number;
    syncWhenOnline?: boolean;
  } = {}
): Promise<T> {
  const requestId = generateUniqueId();
  logger.log(`[${LOG_MODULE}][${requestId}] Richiesta: ${method} ${url}`);

  try {
    // Garantiamo che il database sia inizializzato
    await initOfflineDB().catch(e => {
      logger.error(`[${LOG_MODULE}][${requestId}] Errore inizializzazione DB: ${e instanceof Error ? e.message : String(e)}`);
    });
    
    const cacheKey = options.cacheKey || `${method}-${url}`;
    const cacheExpiry = options.cacheExpiry || DEFAULT_CACHE_EXPIRY;
    
    if (navigator.onLine) {
      try {
        logger.log(`[${LOG_MODULE}][${requestId}] Esecuzione online: ${method} ${url}`);
        const response = await apiRequest<T>(method, url, data);
        
        if (method === "GET" && cacheKey) {
          logger.log(`[${LOG_MODULE}][${requestId}] Salvataggio in cache: ${cacheKey}`);
          await saveCachedData(cacheKey, response, cacheExpiry);
        }
        
        return response;
      } catch (error) {
        logger.error(`[${LOG_MODULE}][${requestId}] Errore richiesta online: ${error instanceof Error ? error.message : String(error)}`);
        
        // Gestione speciale per errori di parsing JSON
        if (error instanceof Error && error.message.includes("Unexpected token")) {
          logger.error(`${LOG_MODULE}`, `[${requestId}] Errore critico`);
          
          // Tenta di usare dati dalla cache se disponibili
          if (method === "GET" && cacheKey) {
            const cachedData = await getCachedData<T>(cacheKey);
            if (cachedData) {
              logger.log(`[${LOG_MODULE}][${requestId}] Usando cache per ${url} a causa di errore di parsing`);
              return cachedData;
            }
          }
          
          // Se non ci sono dati in cache, crea un valore fallback appropriato per evitare errori a cascata
          if (url.includes("/api/product-templates")) {
            return [] as unknown as T; // Array vuoto come fallback per modelli di prodotto
          }
        } else if (method === "GET" && cacheKey) {
          const cachedData = await getCachedData<T>(cacheKey);
          if (cachedData) {
            logger.log(`[${LOG_MODULE}][${requestId}] Usando cache per ${url}`);
            return cachedData;
          }
        }
        
        throw error;
      }
    } else {
      logger.info(LOG_MODULE, `[${requestId}] Modalità offline: ${method} ${url}`);
      
      if (method === "GET" && cacheKey) {
        const cachedData = await getCachedData<T>(cacheKey);
        if (cachedData) {
          logger.info(LOG_MODULE, `[${requestId}] Usando cache offline: ${url}`);
          return cachedData;
        }
      }
      
      // Per richieste di modifica, salva per sincronizzazione futura
      const pendingOperation: PendingOperation = {
        url,
        method,
        body: data,
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        timestamp: Date.now(),
        cacheName: cacheKey
      };
      
      await savePendingOperation(pendingOperation);
      registerBackgroundSync();
      
      return {
        success: true,
        offlinePending: true,
        message: "Operazione salvata per sincronizzazione",
        timestamp: new Date().toISOString(),
        data: data || {}
      } as unknown as T;
    }
  } catch (finalError) {
    logger.error(LOG_MODULE, `[${requestId}] Errore critico`, {
      error: finalError instanceof Error ? finalError.message : String(finalError),
      url, method
    });
    throw finalError;
  }
}

/**
 * Configura gli event listener per la sincronizzazione
 */
export const setupOfflineSync = () => {
  // Registra i listener per gli eventi online/offline
  window.addEventListener('online', () => {
    console.log('Tornato online, inizia sincronizzazione...');
    processPendingOperations();
  });
  
  // Utilizziamo setTimeout per ritardare l'inizializzazione non critica
  // Questo permette al resto dell'app di caricarsi più velocemente
  setTimeout(() => {
    // Inizializza il database e registra la sincronizzazione in background
    initOfflineDB()
      .then(() => {
        if (navigator.onLine) {
          // Se siamo online, processa subito le operazioni pendenti
          processPendingOperations();
        }
        
        // Registra la sincronizzazione in background
        registerBackgroundSync();
      })
      .catch(error => {
        console.error(`Errore nell'inizializzazione del database offline: ${error}`);
      });
  }, 2000); // Ritardo di 2 secondi per permettere all'app di caricarsi prima
};