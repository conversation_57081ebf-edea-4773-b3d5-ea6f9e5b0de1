/**
 * Ottimizzatore Service Worker per ridurre conflitti in sviluppo
 * Gestisce cleanup automatico e prevenzione cache conflicts
 */

import { logger } from './logger';

interface ServiceWorkerState {
  registered: boolean;
  active: boolean;
  waiting: boolean;
  controller: boolean;
  registration?: ServiceWorkerRegistration;
}

class ServiceWorkerOptimizer {
  private state: ServiceWorkerState = {
    registered: false,
    active: false,
    waiting: false,
    controller: false
  };

  private isDevelopment = import.meta.env.DEV;
  private cleanupInterval?: NodeJS.Timeout;

  /**
   * Inizializza l'ottimizzatore service worker
   */
  async initialize(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      logger.log('[SW] Service Worker non supportato');
      return;
    }

    if (this.isDevelopment) {
      await this.developmentCleanup();
      this.startDevelopmentMonitoring();
    } else {
      await this.productionSetup();
    }

    this.monitorState();
  }

  /**
   * Cleanup per ambiente di sviluppo
   */
  private async developmentCleanup(): Promise<void> {
    logger.log('[SW] Modalità sviluppo: cleanup service workers');

    try {
      // Unregister tutti i service workers esistenti
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      for (const registration of registrations) {
        logger.log('[SW] Rimozione service worker esistente');
        await registration.unregister();
      }

      // Pulisci tutte le cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            logger.log(`[SW] Rimozione cache: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );
      }

      this.state.registered = false;
      this.state.active = false;
      this.state.waiting = false;
      this.state.controller = false;

      logger.log('[SW] ✅ Cleanup sviluppo completato');
    } catch (error) {
      logger.log('[SW] ⚠️ Errore durante cleanup:', error);
    }
  }

  /**
   * Setup per produzione
   */
  private async productionSetup(): Promise<void> {
    try {
      // Registra service worker solo in produzione
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'imports'
      });

      this.state.registration = registration;
      this.state.registered = true;

      logger.log('[SW] Service worker registrato per produzione');

      // Configura listeners per updates
      registration.addEventListener('updatefound', () => {
        this.handleServiceWorkerUpdate(registration);
      });

      // Controlla immediatamente per updates
      await registration.update();

    } catch (error) {
      logger.log('[SW] ⚠️ Errore registrazione service worker:', error);
    }
  }

  /**
   * Monitoraggio per sviluppo - previene registrazioni accidentali
   */
  private startDevelopmentMonitoring(): void {
    this.cleanupInterval = setInterval(async () => {
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      if (registrations.length > 0) {
        logger.log('[SW] Rilevati service workers in sviluppo - rimozione automatica');
        await this.developmentCleanup();
      }
    }, 30000); // Controlla ogni 30 secondi
  }

  /**
   * Gestisce aggiornamenti del service worker
   */
  private handleServiceWorkerUpdate(registration: ServiceWorkerRegistration): void {
    const newWorker = registration.installing;
    if (!newWorker) return;

    this.state.waiting = true;
    logger.log('[SW] Nuovo service worker disponibile');

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed') {
        if (navigator.serviceWorker.controller) {
          // Notifica utente che un update è disponibile
          this.notifyUpdateAvailable();
        } else {
          // Service worker installato per la prima volta
          logger.log('[SW] Service worker installato');
          this.state.active = true;
          this.state.waiting = false;
        }
      }
    });
  }

  /**
   * Notifica utente di update disponibile
   */
  private notifyUpdateAvailable(): void {
    logger.log('[SW] 🔄 Aggiornamento applicazione disponibile');
    
    // Dispatch evento personalizzato per la UI
    window.dispatchEvent(new CustomEvent('sw-update-available', {
      detail: { registration: this.state.registration }
    }));
  }

  /**
   * Applica update del service worker
   */
  async applyUpdate(): Promise<void> {
    if (!this.state.registration?.waiting) {
      logger.log('[SW] Nessun update in attesa');
      return;
    }

    try {
      // Invia messaggio al service worker per skip waiting
      this.state.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      
      // Reload pagina dopo activation
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        logger.log('[SW] Service worker aggiornato - ricaricamento pagina');
        window.location.reload();
      });

    } catch (error) {
      logger.log('[SW] ⚠️ Errore applicazione update:', error);
    }
  }

  /**
   * Monitora stato service worker
   */
  private monitorState(): void {
    if (!navigator.serviceWorker) return;

    navigator.serviceWorker.addEventListener('controllerchange', () => {
      this.state.controller = !!navigator.serviceWorker.controller;
      logger.log(`[SW] Controller change: ${this.state.controller}`);
    });

    navigator.serviceWorker.ready.then(registration => {
      this.state.active = !!registration.active;
      logger.log(`[SW] Service worker ready: ${this.state.active}`);
    });
  }

  /**
   * Forza rimozione service worker (debug)
   */
  async forceUnregister(): Promise<void> {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      await Promise.all(
        registrations.map(registration => registration.unregister())
      );

      // Pulisci anche le cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      logger.log('[SW] ✅ Rimozione forzata completata');
      
      // Reset stato
      this.state = {
        registered: false,
        active: false,
        waiting: false,
        controller: false
      };

    } catch (error) {
      logger.log('[SW] ⚠️ Errore rimozione forzata:', error);
    }
  }

  /**
   * Ottieni stato attuale
   */
  getState(): ServiceWorkerState {
    return { ...this.state };
  }

  /**
   * Ottieni statistiche cache
   */
  async getCacheStats(): Promise<{
    caches: Array<{ name: string; size: number; keys: number }>;
    totalSize: number;
  }> {
    if (!('caches' in window)) {
      return { caches: [], totalSize: 0 };
    }

    try {
      const cacheNames = await caches.keys();
      const cacheInfos = [];
      let totalSize = 0;

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        
        // Stima dimensione cache
        let cacheSize = 0;
        for (const request of keys.slice(0, 10)) { // Campiona solo i primi 10
          const response = await cache.match(request);
          if (response) {
            const blob = await response.blob();
            cacheSize += blob.size;
          }
        }
        
        // Estrapola dimensione totale
        if (keys.length > 10) {
          cacheSize = (cacheSize / 10) * keys.length;
        }

        cacheInfos.push({
          name: cacheName,
          size: cacheSize,
          keys: keys.length
        });

        totalSize += cacheSize;
      }

      return { caches: cacheInfos, totalSize };
    } catch (error) {
      logger.log('[SW] ⚠️ Errore calcolo statistiche cache:', error);
      return { caches: [], totalSize: 0 };
    }
  }

  /**
   * Pulisci cache selettivamente
   */
  async cleanupCache(olderThanHours: number = 24): Promise<number> {
    if (!('caches' in window)) return 0;

    const cutoff = Date.now() - (olderThanHours * 60 * 60 * 1000);
    let deletedEntries = 0;

    try {
      const cacheNames = await caches.keys();
      
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        
        for (const request of keys) {
          const response = await cache.match(request);
          if (response) {
            const dateHeader = response.headers.get('date');
            if (dateHeader) {
              const responseDate = new Date(dateHeader).getTime();
              if (responseDate < cutoff) {
                await cache.delete(request);
                deletedEntries++;
              }
            }
          }
        }
      }

      logger.log(`[SW] 🧹 Cache cleanup: ${deletedEntries} elementi rimossi`);
      return deletedEntries;
    } catch (error) {
      logger.log('[SW] ⚠️ Errore cleanup cache:', error);
      return 0;
    }
  }

  /**
   * Cleanup quando si chiude l'app
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.isDevelopment) {
      // In sviluppo, cleanup finale
      void this.developmentCleanup();
    }
  }
}

// Istanza globale
export const serviceWorkerOptimizer = new ServiceWorkerOptimizer();

// Auto-inizializzazione
if (typeof window !== 'undefined') {
  void serviceWorkerOptimizer.initialize();

  // Cleanup automatico quando si chiude la pagina
  window.addEventListener('beforeunload', () => {
    serviceWorkerOptimizer.destroy();
  });

  // Listener per controlli manuali
  window.addEventListener('sw-force-cleanup', () => {
    void serviceWorkerOptimizer.forceUnregister();
  });

  logger.log('[SW] Ottimizzatore Service Worker inizializzato');
}

export default serviceWorkerOptimizer;