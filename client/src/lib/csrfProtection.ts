/**
 * CLIENT-SIDE CSRF PROTECTION SYSTEM
 * Addresses the missing CSRF protection vulnerability
 */

import { apiRequest } from './queryClient';

interface CSRFTokenResponse {
  csrfToken: string;
}

class CSRFProtectionManager {
  private static token: string | null = null;
  private static tokenExpiry: Date | null = null;
  private static refreshPromise: Promise<string> | null = null;
  
  /**
   * Get the current CSRF token, refreshing if necessary
   */
  static async getToken(): Promise<string> {
    // Check if we have a valid token
    if (this.token && this.tokenExpiry && this.tokenExpiry > new Date()) {
      return this.token;
    }
    
    // If a refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    
    // Start a new token refresh
    this.refreshPromise = this.refreshToken();
    
    try {
      const token = await this.refreshPromise;
      return token;
    } finally {
      this.refreshPromise = null;
    }
  }
  
  /**
   * Refresh the CSRF token from the server
   */
  private static async refreshToken(): Promise<string> {
    try {
      const response = await fetch('/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include', // Include session cookies
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to get CSRF token: ${response.status} ${response.statusText}`);
      }
      
      const data: CSRFTokenResponse = await response.json();
      
      this.token = data.csrfToken;
      // Set expiry to 23 hours (tokens are valid for 24 hours)
      this.tokenExpiry = new Date(Date.now() + 23 * 60 * 60 * 1000);
      
      console.log('[CSRF] Token refreshed successfully');
      return this.token;
      
    } catch (error) {
      console.error('[CSRF] Failed to refresh token:', error);
      throw error;
    }
  }
  
  /**
   * Clear the stored token (useful for logout)
   */
  static clearToken(): void {
    this.token = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
    console.log('[CSRF] Token cleared');
  }
  
  /**
   * Add CSRF token to request headers
   */
  static async addTokenToHeaders(headers: HeadersInit = {}): Promise<HeadersInit> {
    try {
      const token = await this.getToken();
      return {
        ...headers,
        'X-CSRF-Token': token
      };
    } catch (error) {
      console.error('[CSRF] Failed to add token to headers:', error);
      // Return original headers if token fetch fails
      return headers;
    }
  }
  
  /**
   * Enhanced fetch wrapper with automatic CSRF protection
   */
  static async secureRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const method = options.method?.toUpperCase() || 'GET';
    
    // Only add CSRF token for state-changing methods
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      try {
        const headers = await this.addTokenToHeaders(options.headers);
        options.headers = headers;
      } catch (error) {
        console.error('[CSRF] Failed to add CSRF token to request:', error);
        // Continue with request - server will reject if CSRF is required
      }
    }
    
    // Ensure credentials are included for session-based auth
    options.credentials = options.credentials || 'include';
    
    try {
      const response = await fetch(url, options);
      
      // Handle CSRF token errors
      if (response.status === 403) {
        const errorData = await response.clone().json().catch(() => ({}));
        if (errorData.code === 'INVALID_CSRF_TOKEN' || errorData.code === 'MISSING_CSRF_TOKEN') {
          console.warn('[CSRF] Token invalid, refreshing and retrying...');
          
          // Clear the invalid token
          this.clearToken();
          
          // Retry the request with a fresh token
          if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
            const newHeaders = await this.addTokenToHeaders(options.headers);
            options.headers = newHeaders;
            
            return fetch(url, options);
          }
        }
      }
      
      return response;
    } catch (error) {
      console.error('[CSRF] Request failed:', error);
      throw error;
    }
  }
}

/**
 * Enhanced apiRequest wrapper with CSRF protection
 */
export const secureApiRequest = async (url: string, options: RequestInit = {}) => {
  return CSRFProtectionManager.secureRequest(url, options);
};

/**
 * Hook for React components to get CSRF token
 */
export const useCSRFToken = () => {
  const getToken = () => CSRFProtectionManager.getToken();
  const clearToken = () => CSRFProtectionManager.clearToken();
  
  return { getToken, clearToken };
};

/**
 * Form helper to add CSRF token to form data
 */
export const addCSRFToFormData = async (formData: FormData): Promise<FormData> => {
  try {
    const token = await CSRFProtectionManager.getToken();
    formData.append('_csrf', token);
    return formData;
  } catch (error) {
    console.error('[CSRF] Failed to add token to form data:', error);
    return formData;
  }
};

/**
 * Object helper to add CSRF token to request body
 */
export const addCSRFToRequestBody = async (body: any): Promise<any> => {
  try {
    const token = await CSRFProtectionManager.getToken();
    return {
      ...body,
      _csrf: token
    };
  } catch (error) {
    console.error('[CSRF] Failed to add token to request body:', error);
    return body;
  }
};

// Export the main manager for advanced usage
export { CSRFProtectionManager };

// Initialize CSRF protection on module load
if (typeof window !== 'undefined') {
  // Pre-fetch CSRF token for immediate use
  CSRFProtectionManager.getToken().catch(error => {
    console.warn('[CSRF] Failed to pre-fetch token on initialization:', error);
  });
  
  // Clear token on page unload to prevent stale tokens
  window.addEventListener('beforeunload', () => {
    CSRFProtectionManager.clearToken();
  });
}