import { useState, useEffect } from "react";
import { useLocation, useSearch } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { BottomNavigation } from "@/components/layout/bottom-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Package, Loader2, AlertCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ProductLabel, Container } from "@shared/schema";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface ContainerProductPayload {
  containerId: number;
  productLabelId: number;
}

export default function ProductLabelSelectionPage() {
  const [, setLocation] = useLocation();
  const search = useSearch();
  const params = new URLSearchParams(search);
  const containerId = params.get("containerId");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Fetch container details
  const {
    data: container,
    isLoading: isContainerLoading,
    error: containerError,
  } = useQuery<Container>({ 
    queryKey: containerId ? [`/api/containers/${containerId}`] : [], 
    enabled: !!containerId 
  });

  // Fetch all product labels
  const {
    data: products,
    isLoading: isProductsLoading,
    error: productsError,
  } = useQuery<ProductLabel[]>({ 
    queryKey: ["/api/product-labels"],
  });

  // Mutation to add product to container
  const addProductMutation = useMutation({
    mutationFn: async (data: ContainerProductPayload) => {
      const response = await apiRequest(
        "/api/container-products",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }
      );
      return response;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      if (containerId) {
        queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}/products`] });
      }
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      
      toast({
        title: "Prodotto aggiunto",
        description: "Il prodotto è stato aggiunto al container con successo.",
      });

      // Return to container details
      if (containerId) {
        setLocation(`/container/${containerId}`);
      } else {
        setLocation("/containers");
      }
    },
    onError: (error: any) => {
      console.error("Error adding product to container:", error);
      toast({
        title: "Errore",
        description: error.message || "Si è verificato un errore durante l'aggiunta del prodotto al container.",
        variant: "destructive",
      });
    },
  });

  const handleAddProduct = (productLabelId: number) => {
    if (!containerId) {
      toast({
        title: "Errore",
        description: "ID container non valido",
        variant: "destructive",
      });
      return;
    }

    addProductMutation.mutate({
      containerId: parseInt(containerId),
      productLabelId,
    });
  };

  const filteredProducts = () => {
    if (!products) return [];
    
    let filtered = [...products];
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product => 
        product.productName.toLowerCase().includes(query) ||
        product.batchNumber.toLowerCase().includes(query)
      );
    }
    
    // Filter by tab selection
    if (activeTab === "recent") {
      filtered = filtered.sort((a, b) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }).slice(0, 10); // Get 10 most recent
    }
    
    return filtered;
  };

  if (isContainerLoading || isProductsLoading) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Caricamento..." showBack backPath={containerId ? `/container/${containerId}` : "/containers"} />
        <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-10 w-10 animate-spin text-indigo-600" />
          </div>
        </main>
        <BottomNavigation activeItem="containers" />
      </div>
    );
  }

  if (containerError || !container) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Errore" showBack backPath="/containers" />
        <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle>Container non trovato</AlertTitle>
            <AlertDescription>
              Non è stato possibile trovare il container richiesto. Torna indietro e riprova.
            </AlertDescription>
          </Alert>
          <Button 
            className="w-full mt-4 h-12 rounded-xl text-white bg-black border-2 border-indigo-500/70 shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(99,102,241,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu"
            onClick={() => setLocation("/containers")}
          >
            Torna ai Container
          </Button>
        </main>
        <BottomNavigation activeItem="containers" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#f5f5f7]">
      <Header 
        title={`Aggiungi prodotto a ${container.name}`} 
        showBack 
        backPath={`/container/${containerId}`} 
      />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="py-6 max-w-md mx-auto">
          <div className="mb-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Cerca prodotto per nome o lotto..."
              className="pl-10 h-12 rounded-xl"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4 h-14 rounded-xl bg-white shadow-md p-1">
              <TabsTrigger 
                value="all" 
                className="rounded-lg h-full data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-[0_2px_8px_rgba(79,70,229,0.35)]"
              >
                <Package className="mr-2 h-5 w-5" />
                Tutti i prodotti
              </TabsTrigger>
              <TabsTrigger 
                value="recent" 
                className="rounded-lg h-full data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-[0_2px_8px_rgba(79,70,229,0.35)]"
              >
                <Package className="mr-2 h-5 w-5" />
                Recenti
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              <ProductList 
                products={filteredProducts()} 
                onAddProduct={handleAddProduct} 
              />
            </TabsContent>

            <TabsContent value="recent" className="mt-4">
              <ProductList 
                products={filteredProducts()} 
                onAddProduct={handleAddProduct} 
              />
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <BottomNavigation activeItem="containers" />
    </div>
  );
}

function ProductList({ products, onAddProduct }: { 
  products: ProductLabel[], 
  onAddProduct: (id: number) => void 
}) {
  if (products.length === 0) {
    return (
      <Card className="bg-white p-4 mb-4">
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Nessun prodotto trovato</h3>
          <p className="text-gray-500 text-sm">
            Non ci sono prodotti disponibili da aggiungere al container.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {products.map((product) => (
        <Card 
          key={product.id} 
          className="bg-white p-4 cursor-pointer hover:shadow-md transition-all duration-200"
          onClick={() => onAddProduct(product.id)}
        >
          <CardContent className="p-0">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">{product.productName}</h3>
                <div className="flex flex-col mt-1 text-sm">
                  <span className="text-gray-500">
                    <strong>Lotto:</strong> {product.batchNumber}
                  </span>
                  <span className="text-gray-500">
                    <strong>Scadenza:</strong> {product.expiryDate}
                  </span>
                </div>
              </div>
              <Button 
                variant="ghost" 
                className="h-10 w-10 rounded-full p-2 text-indigo-600 hover:bg-indigo-50"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddProduct(product.id);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
