import { useState } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SupplierCard } from "@/components/suppliers/supplier-card";
import { AddSupplierDialog } from "@/components/suppliers/add-supplier-dialog";
import { EditSupplierDialog } from "@/components/suppliers/edit-supplier-dialog";
import { SearchIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Supplier } from "@shared/schema";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useSafeAsyncState } from "@/hooks/useSafeAsyncState";

export default function Suppliers() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useSafeAsyncState("");
  const [selectedSupplier, setSelectedSupplier] = useSafeAsyncState<Supplier | null>(null);
  const [showDetails, setShowDetails] = useSafeAsyncState(false);
  const { toast } = useToast();

  const { data: suppliers, isLoading: isSuppliersLoading } = useQuery<Supplier[]>({
    queryKey: ["/api/suppliers"],
  });

  const handleViewDetails = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setShowDetails(true);
  };

  const [isAddSupplierOpen, setIsAddSupplierOpen] = useSafeAsyncState(false);
  const [isEditSupplierOpen, setIsEditSupplierOpen] = useSafeAsyncState(false);
  
  const handleAddSupplier = () => {
    setIsAddSupplierOpen(true);
  };
  
  const handleEditSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsEditSupplierOpen(true);
  };

  const filteredSuppliers = suppliers
    ? suppliers.filter((supplier) =>
        supplier.companyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        supplier.vatNumber.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  return (
    <div className="flex flex-col h-screen">
      <Header title="Suppliers" showBack backPath="/" />

      <main className="flex-1 pt-24 pb-32 px-4 overflow-y-auto">
        <div className="py-6">
          <div className="mb-6">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search suppliers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10"
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <SearchIcon className="h-5 w-5" />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {isSuppliersLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="h-32 bg-gray-200 animate-pulse rounded-lg"
                ></div>
              ))
            ) : filteredSuppliers.length > 0 ? (
              filteredSuppliers.map((supplier) => (
                <SupplierCard
                  key={supplier.id}
                  supplier={supplier}
                  onViewDetails={handleViewDetails}
                  onEditSupplier={handleEditSupplier}
                />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No suppliers found
              </div>
            )}
          </div>

          <Button
            className="gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground hover:bg-primary/90 px-4 py-2 fixed bottom-20 right-6 w-14 h-14 rounded-full flex items-center justify-center shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] active:translate-y-[1px] active:scale-95 transition-all duration-150 transform-gpu touch-manipulation bg-blue-500 border-2 border-blue-400 z-50 pt-[8px] pb-[8px] mt-[59px] mb-[59px]"
            onClick={handleAddSupplier}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-white drop-shadow-md"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              style={{ filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.3))', transform: 'translateZ(5px) scale(1.6)' }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2.5}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </Button>
        </div>
      </main>

      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Supplier Details</DialogTitle>
          </DialogHeader>
          {selectedSupplier && (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Company Name</h4>
                <p className="text-base">{selectedSupplier.companyName}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">P.IVA</h4>
                <p className="text-base">{selectedSupplier.vatNumber}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Address</h4>
                <p className="text-base">{selectedSupplier.address}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setShowDetails(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialogo per aggiungere un fornitore */}
      <AddSupplierDialog 
        open={isAddSupplierOpen} 
        onOpenChange={setIsAddSupplierOpen} 
      />
      
      {/* Dialogo per modificare un fornitore */}
      <EditSupplierDialog 
        open={isEditSupplierOpen} 
        onOpenChange={setIsEditSupplierOpen}
        supplier={selectedSupplier}
      />

      <Footer activeItem="suppliers" />
    </div>
  );
}
