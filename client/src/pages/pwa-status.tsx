import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { RefreshCw, Info, Database, Wifi, WifiOff, Trash2 } from "lucide-react";
import { pwaState, isPwaInstalled, updateServiceWorker } from "@/lib/pwaManager";
import { useToast } from "@/hooks/use-toast";

// Implementazione semplice di Code component
const Code = ({ children }: { children: React.ReactNode }) => (
  <div className="rounded-md bg-muted p-4 overflow-x-auto font-mono text-sm">
    {children}
  </div>
);

interface CacheInfo {
  name: string;
  size: number;
  items: number;
}

export default function PWAStatusPage() {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [installed, setInstalled] = useState<boolean>(false);
  const [caches, setCaches] = useState<CacheInfo[]>([]);
  const [swVersion, setSwVersion] = useState<string>("");
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const { toast } = useToast();

  // Controlla lo stato online/offline
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Controlla se l'app è installata
  useEffect(() => {
    setInstalled(isPwaInstalled());
  }, []);

  // Ottieni informazioni sulla cache
  const refreshCacheInfo = async () => {
    setRefreshing(true);
    try {
      if ('caches' in window) {
        const cacheNames = await window.caches.keys();
        const cacheInfos: CacheInfo[] = [];

        for (const name of cacheNames) {
          const cache = await window.caches.open(name);
          const keys = await cache.keys();
          const size = await estimateCacheSize(cache);

          cacheInfos.push({
            name,
            size,
            items: keys.length
          });
        }

        setCaches(cacheInfos);
      }

      // Recupera la versione del service worker se disponibile
      if (pwaState.registration) {
        // Tenta di ottenere la versione dal Service Worker
        if (navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'GET_VERSION'
          });
        }
      }
    } catch (error) {
      console.error('Errore nel recupero delle informazioni sulla cache:', error);
      toast({
        title: "Errore",
        description: "Impossibile recuperare le informazioni sulla cache",
        variant: "destructive"
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Stima la dimensione della cache
  const estimateCacheSize = async (cache: Cache): Promise<number> => {
    try {
      const keys = await cache.keys();
      let totalSize = 0;

      for (const request of keys) {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      }

      return totalSize;
    } catch (error) {
      console.error('Errore nel calcolo della dimensione della cache:', error);
      return 0;
    }
  };

  // Formatta dimensione in KB, MB, ecc.
  const formatSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    else return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };

  // Pulisci una cache specifica
  const clearCache = async (cacheName: string) => {
    try {
      await window.caches.delete(cacheName);
      toast({
        title: "Cache eliminata",
        description: `La cache ${cacheName} è stata eliminata con successo`
      });
      refreshCacheInfo();
    } catch (error) {
      console.error('Errore nella pulizia della cache:', error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare la cache",
        variant: "destructive"
      });
    }
  };

  // Pulisci tutte le cache
  const clearAllCaches = async () => {
    try {
      const cacheNames = await window.caches.keys();
      
      for (const name of cacheNames) {
        await window.caches.delete(name);
      }
      
      toast({
        title: "Cache pulite",
        description: "Tutte le cache sono state eliminate con successo"
      });
      refreshCacheInfo();
    } catch (error) {
      console.error('Errore nella pulizia di tutte le cache:', error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare tutte le cache",
        variant: "destructive"
      });
    }
  };

  // Aggiorna l'app
  const handleUpdateApp = async () => {
    try {
      await updateServiceWorker();
      toast({
        title: "Aggiornamento in corso",
        description: "L'app verrà ricaricata con la nuova versione"
      });
    } catch (error) {
      console.error('Errore nell\'aggiornamento dell\'app:', error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare l'app",
        variant: "destructive"
      });
    }
  };

  // Effetto per caricare le informazioni all'avvio
  useEffect(() => {
    refreshCacheInfo();

    // Ascolta i messaggi dal service worker
    const messageHandler = (event: MessageEvent) => {
      if (event.data && event.data.type === 'VERSION_INFO') {
        setSwVersion(event.data.version || 'Sconosciuta');
      }
    };

    navigator.serviceWorker.addEventListener('message', messageHandler);

    return () => {
      navigator.serviceWorker.removeEventListener('message', messageHandler);
    };
  }, []);

  return (
    <div className="container py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Stato Progressive Web App</h1>
      
      <div className="grid gap-4 mb-8 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Connessione</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {isOnline ? (
                <><Wifi className="h-5 w-5 text-green-500" /> <span>Online</span></>
              ) : (
                <><WifiOff className="h-5 w-5 text-amber-500" /> <span>Offline</span></>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Installazione</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {installed ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Installata</Badge>
              ) : (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">Browser</Badge>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Service Worker</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {pwaState.registration ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Attivo</Badge>
              ) : (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">Non attivo</Badge>
              )}
              {swVersion && <span className="text-xs text-muted-foreground">{swVersion}</span>}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="cache">
        <TabsList className="mb-4">
          <TabsTrigger value="cache">Cache</TabsTrigger>
          <TabsTrigger value="info">Informazioni</TabsTrigger>
        </TabsList>
        
        <TabsContent value="cache">
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>Gestione cache</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={refreshCacheInfo}
                  disabled={refreshing}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  Aggiorna
                </Button>
              </CardTitle>
              <CardDescription>
                Cache del service worker e dati memorizzati localmente
              </CardDescription>
            </CardHeader>
            <CardContent>
              {caches.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Database className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p>Nessuna cache trovata</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {caches.map((cache) => (
                    <div key={cache.name} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-sm">{cache.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {cache.items} elementi • {formatSize(cache.size)}
                          </p>
                        </div>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => clearCache(cache.name)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={clearAllCaches}
                disabled={caches.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Pulisci tutte le cache
              </Button>
              
              <Button 
                variant="default"
                onClick={handleUpdateApp}
                disabled={!pwaState.updateSW}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Aggiorna app
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="info">
          <Card>
            <CardHeader>
              <CardTitle>Informazioni PWA</CardTitle>
              <CardDescription>
                Dettagli tecnici sulla Progressive Web App
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-sm mb-2">Service Worker</h3>
                <div className="rounded-md bg-muted p-4">
                  <Code>
                    {pwaState.registration ? (
                      <pre className="text-xs">{`URL: ${pwaState.registration.scope}
Stato: ${pwaState.registration.active ? 'Attivo' : 'Non attivo'}
Aggiornamento disponibile: ${pwaState.needRefresh ? 'Sì' : 'No'}
Pronto per offline: ${pwaState.offlineReady ? 'Sì' : 'No'}`}
                      </pre>
                    ) : (
                      <pre className="text-xs">Nessun service worker registrato</pre>
                    )}
                  </Code>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-sm mb-2">Manifest</h3>
                <div className="rounded-md bg-muted p-4">
                  <Code>
                    <pre className="text-xs">{`Installabile: ${installed ? 'Installata' : 'Installabile'}
Display Mode: ${window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser'}
Orientation: portrait`}
                    </pre>
                  </Code>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-sm mb-2">Funzionalità</h3>
                <div className="rounded-md bg-muted p-4">
                  <Code>
                    <pre className="text-xs">{`Offline Storage: ${typeof window.indexedDB !== 'undefined' ? 'Supportato' : 'Non supportato'}
Background Sync: ${typeof (window as any).SyncManager !== 'undefined' ? 'Supportato' : 'Non supportato'}
Push API: ${typeof (window as any).PushManager !== 'undefined' ? 'Supportato' : 'Non supportato'}
Notifications: ${typeof window.Notification !== 'undefined' ? 'Supportato' : 'Non supportato'}
Permission: ${typeof window.Notification !== 'undefined' ? Notification.permission : 'N/A'}`}
                    </pre>
                  </Code>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex items-center text-sm text-muted-foreground">
                <Info className="h-4 w-4 mr-2" />
                <span>Queste informazioni sono utili per la diagnosi di problemi tecnici</span>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}