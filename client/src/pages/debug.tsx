import React, { useEffect, useState } from 'react';
import { Link } from 'wouter';
import { Loader2 } from 'lucide-react';

export default function DebugPage() {
  const [directProducts, setDirectProducts] = useState<any[]>([]);
  const [validProducts, setValidProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDebugData() {
      try {
        setLoading(true);
        
        // Otteniamo i dati grezzi dal nuovo endpoint di debug
        const debugResponse = await fetch('/api/debug/products');
        if (!debugResponse.ok) {
          throw new Error(`Errore API Debug: ${debugResponse.status} ${debugResponse.statusText}`);
        }
        const debugData = await debugResponse.json();
        
        // Otteniamo anche i dati dall'endpoint normale dei prodotti validi per confronto
        const validResponse = await fetch('/api/product-labels/valid');
        if (!validResponse.ok) {
          throw new Error(`Errore API Valid: ${validResponse.status} ${validResponse.statusText}`);
        }
        const validData = await validResponse.json();
        
        console.log('Debug: Dati grezzi dal database:', debugData);
        console.log('Debug: Prodotti validi dall\'API:', validData);
        
        setDirectProducts(debugData.products || []);
        setValidProducts(validData || []);
        setError(null);
      } catch (err) {
        console.error('Errore durante il fetch dei dati di debug:', err);
        setError(`${err}`);
      } finally {
        setLoading(false);
      }
    }

    fetchDebugData();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Debug Prodotti</h1>
      <Link href="/" className="text-blue-500 hover:underline mb-4 inline-block">
        &larr; Torna alla homepage
      </Link>
      
      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Caricamento dati diagnostici...</span>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p className="font-bold">Errore!</p>
          <p>{error}</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Riassunto dei dati */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-2">Riassunto Diagnostico</h2>
            <ul className="list-disc pl-5 space-y-1">
              <li><strong>Prodotti totali nel database:</strong> {directProducts.length}</li>
              <li><strong>Prodotti validi dall'API:</strong> {validProducts.length}</li>
              <li><strong>Differenza:</strong> {directProducts.length - validProducts.length} prodotti</li>
            </ul>
          </div>
          
          {/* Test del componente Select diretto */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-2">Test Selezione Diretta (Bypass ReactQuery)</h2>
            
            {directProducts.length === 0 ? (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                Nessun prodotto trovato direttamente nel database
              </div>
            ) : (
              <div className="relative">
                <select
                  className="w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-base shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 appearance-none pr-10"
                >
                  <option value="">Seleziona un prodotto (Dati diretti dal DB)</option>
                  {directProducts.map((product) => (
                    <option key={`direct-${product.id}`} value={product.id.toString()}>
                      {product.productName} - Lotto: {product.batchNumber || 'N/D'} (ID: {product.id})
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            )}
          </div>
          
          {/* Test del componente Select valido */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-2">Test Selezione Prodotti Validi</h2>
            
            {validProducts.length === 0 ? (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                Nessun prodotto valido dall'API
              </div>
            ) : (
              <div className="relative">
                <select
                  className="w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-base shadow-sm focus:border-purple-500 focus:ring-1 focus:ring-purple-500 appearance-none pr-10"
                >
                  <option value="">Seleziona un prodotto (Solo validi dall'API)</option>
                  {validProducts.map((product) => (
                    <option key={`valid-${product.id}`} value={product.id.toString()}>
                      {product.productName} - Lotto: {product.batchNumber || 'N/D'} (ID: {product.id})
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            )}
          </div>
          
          {/* Tabella dettagli prodotti */}
          <div>
            <h2 className="text-xl font-semibold mb-2">
              Dati Prodotti Completi ({directProducts.length})
            </h2>
            
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-300">
                <thead>
                  <tr>
                    <th className="border border-gray-300 px-4 py-2">ID</th>
                    <th className="border border-gray-300 px-4 py-2">Nome</th>
                    <th className="border border-gray-300 px-4 py-2">DDT ID</th>
                    <th className="border border-gray-300 px-4 py-2">Lotto</th>
                    <th className="border border-gray-300 px-4 py-2">Scadenza</th>
                    <th className="border border-gray-300 px-4 py-2">In API Valid?</th>
                    <th className="border border-gray-300 px-4 py-2">Dati Grezzi</th>
                  </tr>
                </thead>
                <tbody>
                  {directProducts.map((product) => {
                    // Verifichiamo se questo prodotto è presente anche nella lista dei validi
                    const isInValidApi = validProducts.some(p => p.id === product.id);
                    
                    return (
                      <tr key={product.id} className={isInValidApi ? "" : "bg-red-50"}>
                        <td className="border border-gray-300 px-4 py-2">{product.id}</td>
                        <td className="border border-gray-300 px-4 py-2">{product.productName || 'N/D'}</td>
                        <td className="border border-gray-300 px-4 py-2">{product.ddtId || 'N/D'}</td>
                        <td className="border border-gray-300 px-4 py-2">{product.batchNumber || 'N/D'}</td>
                        <td className="border border-gray-300 px-4 py-2">{product.expiryDate || 'N/D'}</td>
                        <td className={`border border-gray-300 px-4 py-2 ${isInValidApi ? "text-green-600" : "text-red-600 font-bold"}`}>
                          {isInValidApi ? "✓ Si" : "✗ No"}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                            {JSON.stringify(product, null, 2)}
                          </pre>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}