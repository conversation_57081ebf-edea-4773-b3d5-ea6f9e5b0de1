import { useState } from "react";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Container } from "@/components/ui/container";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Plus, Filter } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { ContainerTypeCard } from "@/components/container-types/container-type-card";
import { CreateContainerTypeDialog } from "@/components/container-types/create-container-type-dialog";
import { EditContainerTypeDialog } from "@/components/container-types/edit-container-type-dialog";
import { DeleteContainerTypeDialog } from "@/components/container-types/delete-container-type-dialog";
import { ContainerType } from "@/types";
import { useAuth } from "@/context/auth-context";
import { useLocation } from "wouter";

export default function ContainerTypes() {
  const [, setLocation] = useLocation();
  const { isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Tab state
  const [activeTab, setActiveTab] = useState<string>("active");
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContainerType, setSelectedContainerType] = useState<ContainerType | null>(null);

  // Fetch container types
  const {
    data: containerTypes = [],
    isLoading,
    isError,
    error,
  } = useQuery<ContainerType[]>({
    queryKey: ["/api/container-types"],
    queryFn: async () => {
      try {
        console.log("Fetching container types...");
        const response = await fetch("/api/container-types", {
          credentials: "include",
          headers: {
            'Accept': 'application/json'
          }
        });
        
        console.log("Response status:", response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", response.status, errorText);
          throw new Error(`Errore ${response.status}: ${errorText || "Errore durante il recupero dei tipi di contenitore"}`);
        }
        
        const data = await response.json();
        console.log("Container types retrieved:", data);
        return data;
      } catch (err) {
        console.error("Error fetching container types:", err);
        throw err;
      }
    },
  });

  // Create container type mutation
  const createContainerTypeMutation = useMutation({
    mutationFn: async (data: { value: string; label: string; description?: string }) => {
      try {
        const response = await fetch("/api/container-types", {
          method: "POST",
          credentials: "include",
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(data)
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          try {
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || "Errore durante la creazione del tipo di contenitore");
          } catch {
            throw new Error(errorText || "Errore durante la creazione del tipo di contenitore");
          }
        }
        
        return await response.json();
      } catch (err) {
        console.error("Error creating container type:", err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/container-types"] });
      toast({ description: "Tipo di contenitore creato con successo" });
      setIsCreateDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        description: error.message,
      });
    },
  });

  // Update container type mutation
  const updateContainerTypeMutation = useMutation({
    mutationFn: async (data: { id: number; value: string; label: string; description?: string, isActive: boolean }) => {
      const { id, ...updateData } = data;
      return await apiRequest<any>(`/api/container-types/${id}`, {
        method: "PUT"
      }, updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/container-types"] });
      toast({ description: "Tipo di contenitore aggiornato con successo" });
      setIsEditDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        description: error.message,
      });
    },
  });

  // Toggle container type active state mutation
  const toggleContainerTypeActiveMutation = useMutation({
    mutationFn: async ({ id, isActive, value, label, description }: { 
      id: number; 
      isActive: boolean;
      value: string;
      label: string;
      description?: string;
    }) => {
      return await apiRequest<any>(`/api/container-types/${id}`, "PUT", { isActive, value, label, description });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/container-types"] });
      toast({ description: "Stato del tipo di contenitore modificato con successo" });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        description: error.message,
      });
    },
  });

  const handleEditContainerType = (containerType: ContainerType) => {
    setSelectedContainerType(containerType);
    setIsEditDialogOpen(true);
  };

  const handleDeleteContainerType = (containerType: ContainerType) => {
    setSelectedContainerType(containerType);
    setIsDeleteDialogOpen(true);
  };

  const handleToggleActive = (id: number, isActive: boolean) => {
    // Trova il containerType corrispondente all'id
    const containerType = containerTypes.find(ct => ct.id === id);
    if (containerType) {
      // Invia tutti i dati necessari, non solo isActive
      toggleContainerTypeActiveMutation.mutate({ 
        id, 
        isActive,
        value: containerType.value,
        label: containerType.label,
        description: containerType.description
      });
    }
  };



  return (
    <div className="min-h-screen bg-[#f5f5f7] pb-24">
      <Header title="Gestione Contenitori" showBack backPath="/settings" />
      
      <div className="px-4 py-4 pt-32">
        <Container className="mb-4 p-4">
          <Button 
            onClick={() => setIsCreateDialogOpen(true)}
            className="w-full justify-center bg-black hover:bg-black/90 text-white mb-0"  
          >
            <Plus className="w-4 h-4 mr-2" />
            Nuovo Tipo di Contenitore
          </Button>
        </Container>

        {isLoading ? (
          <Container className="p-6">
            <div className="text-center py-4">Caricamento...</div>
          </Container>
        ) : isError ? (
          <Container className="p-6">
            <div className="text-center py-4 text-red-500">
              <div>Si è verificato un errore nel recupero dei tipi di contenitore.</div>
              {error && (
                <div className="mt-2 text-sm p-2 bg-red-50 rounded-md mx-4">
                  {error.toString()}
                </div>
              )}
            </div>
          </Container>
        ) : containerTypes.length === 0 ? (
          <Container className="p-6">
            <div className="text-center py-4 text-gray-500">
              Nessun tipo di contenitore trovato. Crea il primo tipo!
            </div>
          </Container>
        ) : (
          <Container className="p-4 mb-4">
            <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="active" className="flex items-center justify-center">
                  <span>Attivi</span>
                  <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">
                    {containerTypes.filter(ct => ct.isActive).length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="inactive" className="flex items-center justify-center">
                  <span>Disattivati</span>
                  <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-100">
                    {containerTypes.filter(ct => !ct.isActive).length}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="active" className="mt-0">
                <Container className="p-0 overflow-hidden">
                  {containerTypes.filter(ct => ct.isActive).length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      Nessun tipo di contenitore attivo trovato.
                    </div>
                  ) : (
                    <div className="space-y-0 divide-y">
                      {containerTypes
                        .filter(containerType => containerType.isActive)
                        .map((containerType: ContainerType) => (
                          <ContainerTypeCard
                            key={containerType.id}
                            containerType={containerType}
                            onEdit={() => handleEditContainerType(containerType)}
                            onDelete={() => handleDeleteContainerType(containerType)}
                            onToggleActive={handleToggleActive}
                          />
                        ))}
                    </div>
                  )}
                </Container>
              </TabsContent>

              <TabsContent value="inactive" className="mt-0">
                <Container className="p-0 overflow-hidden">
                  {containerTypes.filter(ct => !ct.isActive).length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      Nessun tipo di contenitore disattivato trovato.
                    </div>
                  ) : (
                    <div className="space-y-0 divide-y">
                      {containerTypes
                        .filter(containerType => !containerType.isActive)
                        .map((containerType: ContainerType) => (
                          <ContainerTypeCard
                            key={containerType.id}
                            containerType={containerType}
                            onEdit={() => handleEditContainerType(containerType)}
                            onDelete={() => handleDeleteContainerType(containerType)}
                            onToggleActive={handleToggleActive}
                          />
                        ))}
                    </div>
                  )}
                </Container>
              </TabsContent>
            </Tabs>
          </Container>
        )}
      </div>

      {/* Create Dialog */}
      <CreateContainerTypeDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSave={(data: { value: string; label: string; description?: string }) => createContainerTypeMutation.mutate(data)}
      />

      {/* Edit Dialog */}
      {selectedContainerType && (
        <EditContainerTypeDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          containerType={selectedContainerType}
          onSave={(data: { value: string; label: string; description?: string; isActive: boolean }) => updateContainerTypeMutation.mutate({ id: selectedContainerType.id, ...data })}
        />
      )}

      {/* Delete Dialog (actually just toggles active state) */}
      {selectedContainerType && (
        <DeleteContainerTypeDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          containerType={selectedContainerType}
          onConfirm={() => {
            toggleContainerTypeActiveMutation.mutate({ 
              id: selectedContainerType.id, 
              isActive: !selectedContainerType.isActive,
              value: selectedContainerType.value,
              label: selectedContainerType.label,
              description: selectedContainerType.description
            });
            setIsDeleteDialogOpen(false);
          }}
        />
      )}

      <Footer activeItem="home" />
    </div>
  );
}
