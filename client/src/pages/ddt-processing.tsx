import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircleIcon, CheckCircleIcon } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Supplier, DDT } from "@shared/schema";
import { normalizeDateToStandard } from '@/lib/dateNormalization';
import type { DDTFormData } from "../types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Schema di validazione del form
const ddtFormSchema = z.object({
  companyName: z.string().min(1, "Nome azienda obbligatorio"),
  vatNumber: z.string().min(1, "Partita IVA obbligatoria"),
  address: z.string().min(1, "Indirizzo obbligatorio"),
  number: z.string().min(1, "Numero DDT obbligatorio"),
  date: z.string().min(1, "Data obbligatoria"),
});

export default function DDTProcessing() {
  const [, setLocation] = useLocation();
  const [imageData, setImageData] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);
  const [selectedSupplierId, setSelectedSupplierId] = useState<number | null>(null);
  const [matchingSupplier, setMatchingSupplier] = useState<Supplier | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<DDTFormData>({
    resolver: zodResolver(ddtFormSchema),
    defaultValues: {
      companyName: "",
      vatNumber: "",
      address: "",
      number: "",
      date: "",
    },
  });

  // Get suppliers list
  const { data: suppliers, isLoading: isSuppliersLoading } = useQuery<Supplier[]>({
    queryKey: ["/api/suppliers"],
    queryFn: async () => {
      const response = await fetch("/api/suppliers", {
        credentials: "include",
        headers: { 'Accept': 'application/json' }
      });
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      return response.json();
    }
  });

  // Process DDT with Claude AI
  const processDDT = async (imageBase64: string) => {
    try {
      console.log("Sending OCR request for DDT processing");
      const data = await apiRequest(
        "/api/ocr/process-ddt", 
        "POST",
        { imageData: imageBase64 }
      );
      
      return data;
    } catch (error) {
      console.error("DDT processing error:", error);
      toast({
        title: "Elaborazione Fallita",
        description: "Impossibile estrarre informazioni dal documento. Riprova.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Save DDT mutation
  const saveDDTMutation = useMutation({
    mutationFn: async (formData: DDTFormData) => {
      // Prepara i dati base
      const data: any = { 
        ...formData,
        image: imageData  // Inviamo l'immagine come campo 'image'
      };
      
      // Aggiungi supplierId solo se non è null
      if (selectedSupplierId !== null) {
        data.supplierId = selectedSupplierId;
      }
      
      console.log("Dati DDT da inviare:", { ...data, image: data.image ? data.image.substring(0, 100) + '...' : null });
      return await apiRequest("/api/ddt", "POST", data);
    },
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/ddt"] });
      toast({
        title: "DDT Salvato",
        description: "Le informazioni del documento di trasporto sono state salvate con successo.",
      });
      // Store DDT info in session storage
      // IMPORTANTE: Salviamo sia l'ID del database che il numero del DDT
      sessionStorage.setItem("currentDDTId", response.id.toString());
      sessionStorage.setItem("currentDDTNumber", form.getValues().number);
      sessionStorage.setItem("currentDDTCompany", form.getValues().companyName);
      
      // Clean up captured images from session storage and local storage
      sessionStorage.removeItem("capturedDDTImage");
      localStorage.removeItem("capturedDDTImages");
      localStorage.removeItem("ddtProcessingPhotos");
      
      // Redirect to QR code generation page
      setLocation("/ddt-qrcode");
    },
    onError: (error: any) => {
      console.error("Error saving DDT:", error);
      
      // Check if it's a duplicate DDT error
      if (error?.response?.status === 409) {
        clearPhotoStorage(); // Clean up photos on duplicate detection
        toast({
          title: "DDT Duplicato",
          description: error.response.data?.details || "Questo DDT è già presente nel sistema. Verifica i dati o consulta l'archivio DDT esistenti.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Salvataggio Fallito",
          description: "Impossibile salvare le informazioni del documento di trasporto. Riprova.",
          variant: "destructive",
        });
      }
    },
  });

  // Function to clear all photo storage
  const clearPhotoStorage = () => {
    sessionStorage.removeItem("capturedDDTImage");
    localStorage.removeItem("capturedDDTImages");
    localStorage.removeItem("ddtProcessingPhotos");
    sessionStorage.removeItem("ddtData");
  };

  useEffect(() => {
    // Get the captured image from session storage
    let storedImage = sessionStorage.getItem("capturedDDTImage");
    
    // Prova anche il nuovo formato di archiviazione
    if (!storedImage) {
      const ddtData = sessionStorage.getItem("ddtData");
      if (ddtData) {
        try {
          const parsedData = JSON.parse(ddtData);
          storedImage = parsedData.image;
        } catch (e) {
          console.error("Errore nel parsing dei dati DDT:", e);
        }
      }
    }
    
    if (storedImage) {
      setImageData(storedImage);
      
      // Process the image with Claude
      processDDT(storedImage)
        .then((result: any) => {
          console.log("DDT API response:", result);
          
          // Funzione per normalizzare la partita IVA (formato IT + 11 numeri senza spazio)
          const normalizeVatNumber = (vatStr: string): string => {
            if (!vatStr) return "";
            
            // Rimuovi eventuali spazi e altri caratteri non alfanumerici
            vatStr = vatStr.trim().replace(/[^a-zA-Z0-9]/g, '');
            
            // Verifica se inizia già con due lettere (codice paese)
            if (/^[a-zA-Z]{2}\d+$/.test(vatStr)) {
              // Se le due lettere iniziali non sono "IT", lasciamole così come sono
              const countryCode = vatStr.substring(0, 2).toUpperCase();
              const numbers = vatStr.substring(2);
              return countryCode + numbers;
            }
            
            // Se è solo numerico, aggiungi "IT" all'inizio
            if (/^\d+$/.test(vatStr)) {
              return "IT" + vatStr;
            }
            
            return vatStr; // Formato non riconosciuto, ritorna il valore originale
          };
          

          
          // Adatta i campi al formato atteso dal frontend
          // Assicuriamoci che tutti i valori siano stringhe, mai null
          const companyName = String(result?.fornitore || result?.rag_soc_ddt || result?.ragione_sociale || "");
          const rawVatNumber = String(result?.partita_iva_fornitore || result?.partita_iva_ddt || result?.partita_iva || "");
          const vatNumber = normalizeVatNumber(rawVatNumber);
          const address = String(result?.indirizzo_fornitore || result?.indirizzo_ddt || result?.indirizzo || "");
          const number = String(result?.numero_documento || result?.numero_ddt || result?.numero || "");
          const date = normalizeDateToStandard(String(result?.data_documento || result?.data_ddt || result?.data || "")) || "";
          
          form.reset({
            companyName,
            vatNumber,
            address,
            number,
            date,
          });
          
          // Try to find a matching supplier
          if (companyName && suppliers) {
            const match = suppliers.find(
              (s) => s.companyName.toLowerCase() === companyName.toLowerCase() ||
                     s.vatNumber === vatNumber
            );
            
            if (match) {
              setSelectedSupplierId(parseInt(match.id.toString()));
              setMatchingSupplier(match);
            }
          }
          
          setIsProcessing(false);
        })
        .catch(() => {
          setIsProcessing(false);
        });
    } else {
      toast({
        title: "Nessuna Immagine Trovata",
        description: "Cattura prima un'immagine del documento.",
        variant: "destructive",
      });
      setLocation("/incoming-goods");
    }
  }, [suppliers]);

  const onSubmit = (data: DDTFormData) => {
    saveDDTMutation.mutate(data);
  };

  const handleRetry = () => {
    sessionStorage.removeItem("capturedDDTImage");
    setLocation("/incoming-goods");
  };

  return (
    <div className="flex flex-col h-screen">
      <Header
        title="Elaborazione DDT"
        showBack
        backPath="/incoming-goods"
        showNotification={false}
        showUserMenu={false}
      />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="py-6">
          {isProcessing ? (
            <div className="mb-6">
              <div className="flex flex-col items-center justify-center p-8">
                <div className="rounded-full bg-primary-50 p-3 mb-4">
                  <svg
                    className="h-8 w-8 text-primary-600 animate-pulse"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  Elaborazione Documento
                </h2>
                <p className="text-sm text-gray-500 text-center">
                  La nostra IA sta estraendo informazioni dal tuo documento
                </p>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div className="bg-primary-600 h-2.5 rounded-full w-3/4 animate-pulse"></div>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <Card className="bg-white mb-6">
                  <CardContent className="p-4 space-y-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      Informazioni Estratte
                    </h3>

                    <FormField
                      control={form.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome Fornitore</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="vatNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Partita IVA</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Indirizzo</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Numero DDT</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data DDT</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {!isSuppliersLoading && suppliers && suppliers.length > 0 && (
                      <div>
                        <Label htmlFor="supplierId">
                          Seleziona Fornitore (se non abbinato automaticamente)
                        </Label>
                        <Select
                          value={selectedSupplierId?.toString() || ""}
                          onValueChange={(value) => setSelectedSupplierId(parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Seleziona un fornitore" />
                          </SelectTrigger>
                          <SelectContent>
                            {suppliers.map((supplier) => (
                              <SelectItem
                                key={supplier.id}
                                value={supplier.id.toString()}
                              >
                                {supplier.companyName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {matchingSupplier && (
                  <Alert className="bg-green-50 border border-green-100 mb-6">
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    <AlertTitle className="text-sm font-medium text-green-800">
                      Fornitore Trovato
                    </AlertTitle>
                    <AlertDescription className="text-xs text-green-700 mt-1">
                      Questo fornitore esiste nel tuo registro.
                    </AlertDescription>
                  </Alert>
                )}

                {!matchingSupplier && !selectedSupplierId && (
                  <Alert className="bg-amber-50 border border-amber-100 mb-6">
                    <AlertCircleIcon className="h-5 w-5 text-amber-500" />
                    <AlertTitle className="text-sm font-medium text-amber-800">
                      Nuovo Fornitore
                    </AlertTitle>
                    <AlertDescription className="text-xs text-amber-700 mt-1">
                      Questo fornitore verrà aggiunto al tuo registro.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-between space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleRetry}
                  >
                    Riprova
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      clearPhotoStorage();
                      setLocation("/");
                    }}
                  >
                    Annulla
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    variant="default"
                    disabled={saveDDTMutation.isPending}
                  >
                    {saveDDTMutation.isPending ? "Salvando..." : "Avanti"}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </main>
    </div>
  );
}
