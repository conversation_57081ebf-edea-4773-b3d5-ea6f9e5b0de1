import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw, Server, Smartphone, Database, Clock, CheckCircle, XCircle, AlertCircle, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ServiceStatus {
  name: string;
  status: 'active' | 'inactive' | 'conditional' | 'not_implemented';
  description: string;
  frequency: string;
}

interface SystemStatus {
  timestamp: string;
  uptime: number;
  nodeVersion: string;
  platform: string;
  pwaServices: Record<string, ServiceStatus>;
  backendServices: Record<string, ServiceStatus>;
  automatedServices: Record<string, ServiceStatus>;
  automaticFilters: Record<string, ServiceStatus>;
  monitoring: Record<string, ServiceStatus>;
}

interface SystemSummary {
  totalServices: number;
  activeServices: number;
  notImplementedServices: number;
  categoriesBreakdown: Record<string, { total: number; active: number }>;
  recommendations: string[];
}

export default function SystemStatus() {
  const { toast } = useToast();
  const [refreshing, setRefreshing] = useState(false);

  const { data: systemStatus, isLoading, refetch } = useQuery<{ success: boolean; systemStatus: SystemStatus }>({
    queryKey: ["/api/system/status"],
    enabled: true,
  });

  const { data: summary } = useQuery<{ success: boolean; summary: SystemSummary }>({
    queryKey: ["/api/system/services-summary"],
    enabled: true,
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
      toast({
        title: "Stato aggiornato",
        description: "Lo stato dei servizi è stato aggiornato con successo.",
      });
    } catch (error) {
      toast({
        title: "Errore di aggiornamento",
        description: "Non è stato possibile aggiornare lo stato dei servizi.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'conditional':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'not_implemented':
        return <Info className="h-4 w-4 text-gray-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Attivo</Badge>;
      case 'conditional':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Condizionale</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Inattivo</Badge>;
      case 'not_implemented':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Non Implementato</Badge>;
      default:
        return <Badge variant="secondary">Sconosciuto</Badge>;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}g ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const renderServiceCategory = (title: string, icon: React.ReactNode, services: Record<string, ServiceStatus>) => {
    const serviceArray = Object.entries(services);
    const activeCount = serviceArray.filter(([_, service]) => service.status === 'active').length;
    
    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            {title}
            <Badge variant="outline" className="ml-auto">
              {activeCount}/{serviceArray.length} attivi
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {serviceArray.map(([key, service]) => (
            <div key={key} className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  {getStatusIcon(service.status)}
                  <h4 className="font-medium text-sm">{service.name}</h4>
                  {getStatusBadge(service.status)}
                </div>
                <p className="text-xs text-gray-600 mb-1">{service.description}</p>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>{service.frequency}</span>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <GradientBackground>
        <div className="flex flex-col h-screen">
          <Header title="Stato Servizi" showBack backUrl="/settings" />
          <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
            <div className="py-6 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Caricamento stato servizi...</p>
            </div>
          </main>
          <Footer />
        </div>
      </GradientBackground>
    );
  }

  const status = systemStatus?.systemStatus;
  const summaryData = summary?.summary;

  return (
    <GradientBackground>
      <div className="flex flex-col h-screen">
        <Header title="Stato Servizi" showBack backUrl="/settings" />
        
        <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
          <div className="py-6 space-y-6">
            {/* Header con informazioni sistema */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Server className="h-5 w-5" />
                    Informazioni Sistema
                  </CardTitle>
                  <Button
                    onClick={handleRefresh}
                    disabled={refreshing}
                    size="sm"
                    variant="outline"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Aggiorna
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Uptime</p>
                  <p className="font-medium">{status ? formatUptime(status.uptime) : 'N/A'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Node.js</p>
                  <p className="font-medium">{status?.nodeVersion || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Piattaforma</p>
                  <p className="font-medium">{status?.platform || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Ultimo aggiornamento</p>
                  <p className="font-medium">
                    {status ? new Date(status.timestamp).toLocaleTimeString('it-IT') : 'N/A'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Riepilogo rapido */}
            {summaryData && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Riepilogo Servizi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{summaryData.activeServices}</div>
                      <div className="text-xs text-gray-600">Attivi</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600">{summaryData.notImplementedServices}</div>
                      <div className="text-xs text-gray-600">Da Implementare</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{summaryData.totalServices}</div>
                      <div className="text-xs text-gray-600">Totali</div>
                    </div>
                  </div>
                  
                  {summaryData.recommendations.length > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm font-medium text-blue-900 mb-2">Raccomandazioni:</p>
                      <ul className="text-xs text-blue-800 space-y-1">
                        {summaryData.recommendations.map((rec, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-blue-600">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Servizi PWA */}
            {status?.pwaServices && renderServiceCategory(
              "Servizi PWA Frontend", 
              <Smartphone className="h-5 w-5" />, 
              status.pwaServices
            )}

            {/* Servizi Backend */}
            {status?.backendServices && renderServiceCategory(
              "Servizi Backend", 
              <Database className="h-5 w-5" />, 
              status.backendServices
            )}

            {/* Servizi Automatizzati */}
            {status?.automatedServices && renderServiceCategory(
              "Servizi Automatizzati", 
              <RefreshCw className="h-5 w-5" />, 
              status.automatedServices
            )}

            {/* Filtri Automatici */}
            {status?.automaticFilters && renderServiceCategory(
              "Filtri Automatici", 
              <CheckCircle className="h-5 w-5" />, 
              status.automaticFilters
            )}

            {/* Monitoraggio */}
            {status?.monitoring && renderServiceCategory(
              "Monitoraggio e Log", 
              <Info className="h-5 w-5" />, 
              status.monitoring
            )}
          </div>
        </main>
        
        <Footer />
      </div>
    </GradientBackground>
  );
}