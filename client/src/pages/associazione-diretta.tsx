import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle, ArrowLeft } from 'lucide-react';
import { useLocation } from 'wouter';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { Header } from '@/components/layout/header';
import { GradientBackground } from '@/components/layout/gradient-background';
import { SimpleProductSelector } from '@/components/ui/simple-product-selector';
import { SimpleContainerSelector } from '@/components/ui/simple-container-selector';
import { useQueryClient } from '@tanstack/react-query';

// Enum per le fasi dell'associazione diretta
enum SimpleAssociationPhase {
  PRODUCT = 'product',
  CONTAINER = 'container',
  CONFIRMATION = 'confirmation',
  COMPLETED = 'completed'
}

export default function AssociazioneDirettaPage() {
  const [simplePhase, setSimplePhase] = useState<SimpleAssociationPhase>(SimpleAssociationPhase.PRODUCT);
  const [selectedProduct, setSelectedProduct] = useState<{ id: number, name: string } | null>(null);
  const [selectedContainer, setSelectedContainer] = useState<{ id: number, name: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();

  // Gestori per l'associazione semplificata
  const handleProductSelect = (productId: number, productName: string) => {
    console.log(`Prodotto selezionato: ${productName} (${productId})`);
    setSelectedProduct({ id: productId, name: productName });
    setSimplePhase(SimpleAssociationPhase.CONTAINER);
    
    toast({
      title: 'Prodotto selezionato',
      description: `Hai scelto: ${productName}`,
    });
  };
  
  const handleContainerSelect = (containerId: number, containerName: string) => {
    console.log(`Contenitore selezionato: ${containerName} (${containerId})`);
    setSelectedContainer({ id: containerId, name: containerName });
    setSimplePhase(SimpleAssociationPhase.CONFIRMATION);
    
    toast({
      title: 'Contenitore selezionato',
      description: `Hai scelto: ${containerName}`,
    });
  };
  
  const completeAssociation = async () => {
    if (!selectedProduct || !selectedContainer) {
      toast({
        title: 'Errore',
        description: 'Mancano i dati necessari per l\'associazione',
        variant: 'destructive'
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/containers/associate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          containerId: selectedContainer.id,
          productLabelId: selectedProduct.id
        }),
      });
      
      if (!response.ok) {
        throw new Error('Errore durante l\'associazione');
      }
      
      // Successo
      toast({
        title: 'Associazione completata',
        description: `${selectedProduct.name} è stato associato a ${selectedContainer.name}`,
      });
      
      // Invalida la cache dei dati relativi ai contenitori
      queryClient.invalidateQueries({ queryKey: ['/api/containers'] });
      
      // Passa alla fase completata
      setSimplePhase(SimpleAssociationPhase.COMPLETED);
    } catch (error) {
      console.error('Errore durante l\'associazione:', error);
      toast({
        title: 'Errore',
        description: 'Si è verificato un errore durante l\'associazione',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const resetProcess = () => {
    setSelectedProduct(null);
    setSelectedContainer(null);
    setSimplePhase(SimpleAssociationPhase.PRODUCT);
  };
  
  const goToHomePage = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen flex flex-col">
      
      <Header title="Associazione Diretta" />
      
      <main className="flex-1 container mx-auto p-4 max-w-3xl">
        <Card className="p-6 shadow-lg mb-20">
          {/* Header con pulsante di ritorno */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Associazione Semplificata</h2>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={goToHomePage}
              className="flex items-center gap-1"
            >
              <ArrowLeft size={16} />
              <span>Torna alla Home</span>
            </Button>
          </div>
          
          {/* Indicatore fasi */}
          <div className="flex mb-6">
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  simplePhase === SimpleAssociationPhase.PRODUCT || simplePhase === SimpleAssociationPhase.CONTAINER || 
                  simplePhase === SimpleAssociationPhase.CONFIRMATION || simplePhase === SimpleAssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {simplePhase === SimpleAssociationPhase.CONTAINER || simplePhase === SimpleAssociationPhase.CONFIRMATION || 
                 simplePhase === SimpleAssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  1
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  simplePhase === SimpleAssociationPhase.PRODUCT ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Prodotto
              </span>
            </div>
            
            <div className="flex-1 flex items-center">
              <div 
                className={`h-0.5 w-full ${
                  simplePhase === SimpleAssociationPhase.CONTAINER || simplePhase === SimpleAssociationPhase.CONFIRMATION || 
                  simplePhase === SimpleAssociationPhase.COMPLETED
                    ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            </div>
            
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  simplePhase === SimpleAssociationPhase.CONTAINER || simplePhase === SimpleAssociationPhase.CONFIRMATION || 
                  simplePhase === SimpleAssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {simplePhase === SimpleAssociationPhase.CONFIRMATION || simplePhase === SimpleAssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  2
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  simplePhase === SimpleAssociationPhase.CONTAINER ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Contenitore
              </span>
            </div>
            
            <div className="flex-1 flex items-center">
              <div 
                className={`h-0.5 w-full ${
                  simplePhase === SimpleAssociationPhase.CONFIRMATION || simplePhase === SimpleAssociationPhase.COMPLETED
                    ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            </div>
            
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  simplePhase === SimpleAssociationPhase.CONFIRMATION || simplePhase === SimpleAssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {simplePhase === SimpleAssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  3
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  simplePhase === SimpleAssociationPhase.CONFIRMATION ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Conferma
              </span>
            </div>
          </div>
          
          {/* Contenuto principale in base alla fase */}
          <div className="mt-8">
            {simplePhase === SimpleAssociationPhase.PRODUCT && (
              <div>
                <h3 className="text-lg font-medium mb-4">Seleziona un prodotto</h3>
                <SimpleProductSelector onSelect={handleProductSelect} />
              </div>
            )}
            
            {simplePhase === SimpleAssociationPhase.CONTAINER && (
              <div>
                <h3 className="text-lg font-medium mb-2">Prodotto selezionato:</h3>
                <p className="mb-4 p-2 bg-blue-50 rounded-md">{selectedProduct?.name}</p>
                
                <h3 className="text-lg font-medium mb-4">Seleziona un contenitore</h3>
                <SimpleContainerSelector 
                  onSelect={handleContainerSelect}
                  productId={selectedProduct?.id}
                />
                
                <div className="mt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setSimplePhase(SimpleAssociationPhase.PRODUCT)}
                  >
                    Torna indietro
                  </Button>
                </div>
              </div>
            )}
            
            {simplePhase === SimpleAssociationPhase.CONFIRMATION && (
              <div>
                <h3 className="text-lg font-medium mb-4">Conferma associazione</h3>
                
                <div className="mb-6">
                  <div className="p-4 bg-blue-50 rounded-md mb-2">
                    <div className="font-medium">Prodotto:</div>
                    <div>{selectedProduct?.name}</div>
                  </div>
                  
                  <div className="p-4 bg-blue-50 rounded-md">
                    <div className="font-medium">Contenitore:</div>
                    <div>{selectedContainer?.name}</div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    variant="default" 
                    onClick={completeAssociation}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Associazione in corso...' : 'Conferma Associazione'}
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={() => setSimplePhase(SimpleAssociationPhase.CONTAINER)}
                    disabled={isSubmitting}
                  >
                    Torna indietro
                  </Button>
                </div>
              </div>
            )}
            
            {simplePhase === SimpleAssociationPhase.COMPLETED && (
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                
                <h3 className="text-xl font-bold mb-2">Associazione completata!</h3>
                <p className="mb-6 text-gray-600">
                  Il prodotto è stato associato correttamente al contenitore.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                  <Button onClick={resetProcess}>
                    Nuova associazione
                  </Button>
                  
                  <Button variant="outline" onClick={goToHomePage}>
                    Torna alla Home
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      </main>
      
      <BottomNavigation activeItem="qrscan" />
    </div>
  );
}