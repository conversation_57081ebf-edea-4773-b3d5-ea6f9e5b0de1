import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/auth-context";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Container } from "@/components/ui/container";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { LoaderCircle, User as UserIcon, Check, RefreshCw, Trash2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { PWAInstaller } from "@/components/ui/pwa-installer";

const profileSchema = z.object({
  username: z.string().min(3, "Il nome utente deve contenere almeno 3 caratteri"),
  email: z.string().email("Inserisci un indirizzo email valido").optional().or(z.literal('')),
  newPassword: z.string().optional(),
  confirmPassword: z.string().optional(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Le password non corrispondono",
  path: ["confirmPassword"],
}).refine(data => !data.newPassword || data.newPassword.length >= 6, {
  message: "La nuova password deve contenere almeno 6 caratteri",
  path: ["newPassword"],
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function Profile() {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  
  // Utilizziamo any temporaneamente per aggirare problemi di compatibilità dei tipi
  const form = useForm<any>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      username: user?.username || "",
      email: (user as any)?.email || "", // Asserzione di tipo per evitare errori
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    
    try {
      // Prepara i dati per l'API
      const updateData: {
        username: string;
        email?: string;
        newPassword?: string;
      } = {
        username: data.username,
        email: data.email,
      };
      
      // Aggiunge la nuova password solo se è stata fornita
      if (data.newPassword) {
        updateData.newPassword = data.newPassword;
      }

      // Usa fetch direttamente perché stiamo ancora implementando l'API
      const response = await fetch("/api/user/profile", {
        method: "PATCH",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        credentials: "include",
        body: JSON.stringify(updateData)
      });
      
      if (response.ok) {
        toast({
          title: "Profilo aggiornato",
          description: "Le modifiche al profilo sono state salvate con successo.",
          variant: "default",
        });
        
        // Aggiorna i dati utente nella sessione
        refreshUser();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Errore durante l'aggiornamento del profilo");
      }
    } catch (error) {
      console.error("Profile update error:", error);
      toast({
        title: "Errore",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante l'aggiornamento del profilo",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const clearCacheAndUpdate = async () => {
    setIsClearing(true);
    
    try {
      // 1. Cancellazione di tutte le cache dell'app
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // 2. Cancellazione localStorage e sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // 3. Cancellazione IndexedDB (se presente)
      if ('indexedDB' in window) {
        try {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => {
              if (db.name) {
                return new Promise<void>((resolve, reject) => {
                  const deleteReq = indexedDB.deleteDatabase(db.name!);
                  deleteReq.onsuccess = () => resolve();
                  deleteReq.onerror = () => reject(deleteReq.error);
                });
              }
              return Promise.resolve();
            })
          );
        } catch (error) {
          console.warn('Errore durante la cancellazione IndexedDB:', error);
        }
      }

      // 4. Aggiornamento forzato del service worker
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.ready;
          await registration.update();
          
          if (registration.waiting) {
            registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          }
          
          // Disregistra e re-registra il service worker per un aggiornamento completo
          const registrations = await navigator.serviceWorker.getRegistrations();
          for (const reg of registrations) {
            await reg.unregister();
          }
          
          // Re-registra il service worker
          await navigator.serviceWorker.register('/service-worker.js', {
            updateViaCache: 'none'
          });
        } catch (error) {
          console.warn('Errore durante l\'aggiornamento del service worker:', error);
        }
      }

      // 5. Messaggio di successo
      toast({
        title: "Aggiornamento completato",
        description: "Cache cancellate e PWA aggiornata con successo. L'app verrà ricaricata.",
        variant: "default",
      });

      // 6. Ricarica l'app dopo un breve delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('Errore durante la cancellazione cache:', error);
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante la cancellazione delle cache.",
        variant: "destructive",
      });
    } finally {
      setIsClearing(false);
    }
  };

  if (!user) {
    return (
      <div className="flex flex-col h-screen">
        <Header title="Profilo" showBack backPath="/" />
        <Container className="flex-1 pt-24 pb-16 px-4">
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Caricamento in corso...</p>
          </div>
        </Container>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <Header title="Profilo" showBack backPath="/" />
      
      <main className="flex-1 pt-24 pb-20 px-4 overflow-y-auto bg-[#f5f5f7]">
        <div className="max-w-md mx-auto py-4 space-y-6 mb-8">
          <Card className="border-0 shadow-md bg-white">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-center mb-4">
                <div className="h-20 w-20 rounded-full bg-gradient-to-br from-gray-700 to-black flex items-center justify-center">
                  <UserIcon className="h-10 w-10 text-white" />
                </div>
              </div>
              <CardTitle className="text-xl font-semibold text-center">{user.username}</CardTitle>
              <CardDescription className="text-center">
                {user.isAdmin ? "Amministratore" : user.role === "manager" ? "Manager" : "Utente Standard"}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium">Nome utente</FormLabel>
                        <FormControl>
                          <Input {...field} className="border border-gray-300 rounded-lg" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium">Email</FormLabel>
                        <FormControl>
                          <Input {...field} type="email" className="border border-gray-300 rounded-lg" />
                        </FormControl>
                        <FormDescription className="text-xs text-gray-500">
                          Opzionale - Inserisci la tua email per le notifiche
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="border-t border-gray-200 pt-5">
                    <h3 className="text-sm font-medium mb-3">Cambia password</h3>
                    
                    <FormField
                      control={form.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">Nuova password</FormLabel>
                          <FormControl>
                            <Input 
                              type="password" 
                              {...field} 
                              className="border border-gray-300 rounded-lg"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Lascia vuoto per mantenere la password attuale
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">Conferma nuova password</FormLabel>
                          <FormControl>
                            <Input 
                              type="password" 
                              {...field} 
                              className="border border-gray-300 rounded-lg"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <CardFooter className="px-0 pt-4">
                    <Button 
                      type="submit" 
                      disabled={isSubmitting}
                      className="w-full rounded-lg py-5 bg-gradient-to-r from-gray-700 to-black hover:from-gray-800 hover:to-gray-900 text-white font-medium"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <LoaderCircle className="animate-spin h-4 w-4 mr-2" />
                          Salvataggio in corso...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Check className="h-4 w-4 mr-2" />
                          Salva modifiche
                        </div>
                      )}
                    </Button>
                  </CardFooter>
                </form>
              </Form>
            </CardContent>
          </Card>

          {/* Componente PWA Installer (solo per admin) */}
          {user?.isAdmin && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold mb-3 px-1">Gestione App</h2>
              <PWAInstaller />
            </div>
          )}

          {/* Sezione Cancellazione Cache e Aggiornamento (solo per admin) */}
          {user?.isAdmin && (
            <Card className="border-0 shadow-md bg-white">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center">
                  <RefreshCw className="h-5 w-5 mr-2 text-blue-600" />
                  Aggiornamento e Cache
                </CardTitle>
                <CardDescription>
                  Cancella tutte le cache dell'app e forza l'aggiornamento della PWA
                </CardDescription>
              </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start space-x-3">
                    <Trash2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-900 mb-1">Cosa viene cancellato:</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Tutte le cache del browser</li>
                        <li>• Dati memorizzati localmente</li>
                        <li>• Cache del service worker</li>
                        <li>• Database locale dell'app</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <Button 
                  onClick={clearCacheAndUpdate}
                  disabled={isClearing}
                  className="w-full rounded-lg py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium"
                >
                  {isClearing ? (
                    <div className="flex items-center">
                      <LoaderCircle className="animate-spin h-4 w-4 mr-2" />
                      Aggiornamento in corso...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Cancella cache e aggiorna PWA
                    </div>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
