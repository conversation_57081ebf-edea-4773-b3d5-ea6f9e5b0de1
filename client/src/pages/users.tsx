import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { UserCard } from "@/components/users/user-card";
import { CreateUserDialog } from "@/components/users/create-user-dialog";
import { EditUserDialog } from "@/components/users/edit-user-dialog";
import { DeleteUserDialog } from "@/components/users/delete-user-dialog";
import { SearchIcon } from "lucide-react";
import { User, UserRole } from "@shared/schema";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/auth-context";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function Users() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editUser, setEditUser] = useState<User | null>(null);
  const [deleteUser, setDeleteUser] = useState<User | null>(null);
  const [impersonateUser, setImpersonateUser] = useState<User | null>(null);
  const [showImpersonateConfirm, setShowImpersonateConfirm] = useState<boolean>(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user: currentUser, setUser } = useAuth();

  // Controlliamo se l'utente è un amministratore o un manager
  useEffect(() => {
    if (currentUser && !currentUser.isAdmin && currentUser.role !== UserRole.MANAGER) {
      setLocation("/");
    }
  }, [currentUser, setLocation]);

  // Verifichiamo se l'utente corrente è un admin (non solo manager)
  const isCurrentUserAdmin = currentUser?.isAdmin || currentUser?.role === UserRole.ADMIN;
  
  const { data: users, isLoading: isUsersLoading } = useQuery<User[]>({
    queryKey: ["/api/users"],
    queryFn: async () => {
      const response = await apiRequest<User[]>("/api/users");
      return response;
    },
    // Abilitiamo la query se l'utente è admin o manager
    enabled: !!currentUser && (currentUser.isAdmin || currentUser.role === UserRole.MANAGER),
  });

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (userData: any) => {
      const response = await apiRequest<User>("POST", "/api/users", userData);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users"] });
      toast({
        title: "Utente creato",
        description: "Il nuovo utente è stato creato con successo.",
      });
    },
    onError: (error) => {
      console.error("Error creating user:", error);
      toast({
        title: "Errore creazione utente",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante la creazione dell'utente.",
        variant: "destructive",
      });
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async (userData: any) => {
      // Debug log per vedere cosa stiamo inviando al server
      console.log("Updating user with data:", userData);
      
      // Utilizziamo il nuovo formato di apiRequest
      const response = await apiRequest<User>(
        "PATCH",
        `/api/users/${userData.id}`, 
        userData
      );
      
      return response;
    },
    onSuccess: (updatedUser) => {
      // Debug log per il risultato dell'aggiornamento
      console.log("User updated successfully:", updatedUser);
      // Invalidiamo la cache per ottenere dati aggiornati
      queryClient.invalidateQueries({ queryKey: ["/api/users"] });
      toast({
        title: "Utente aggiornato",
        description: "Le informazioni dell'utente sono state aggiornate con successo.",
      });
    },
    onError: (error) => {
      console.error("Errore aggiornamento utente:", error);
      toast({
        title: "Errore aggiornamento",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante l'aggiornamento dell'utente.",
        variant: "destructive",
      });
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await apiRequest<void>("DELETE", `/api/users/${userId}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users"] });
      toast({
        title: "Utente eliminato",
        description: "L'utente è stato eliminato con successo.",
      });
    },
    onError: (error) => {
      console.error("Errore eliminazione utente:", error);
      toast({
        title: "Errore eliminazione",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante l'eliminazione dell'utente.",
        variant: "destructive",
      });
    },
  });

  // Toggle admin status
  const toggleAdminMutation = useMutation({
    mutationFn: async ({ userId, isAdmin }: { userId: string; isAdmin: boolean }) => {
      console.log(`Cambio stato admin per utente ${userId} a ${isAdmin}`);
      return await apiRequest<User>("PATCH", `/api/users/${userId}`, { isAdmin });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users"] });
      toast({
        title: "Stato admin aggiornato",
        description: "Lo stato di amministratore dell'utente è stato aggiornato.",
      });
    },
    onError: (error) => {
      console.error("Errore cambio stato admin:", error);
      toast({
        title: "Errore aggiornamento",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante l'aggiornamento del ruolo.",
        variant: "destructive",
      });
    },
  });
  
  // Mutation semplificata per impersonare un utente - soluzione alternativa che evita problemi di rendering
  const impersonateUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      console.log(`Avvio impersonificazione dell'utente con ID ${userId}...`);
      
      // Invece di usare un'API che modifica la sessione, generiamo un token URL one-time
      // e redirectiamo direttamente
      const impersonationData = {
        userId,
        timestamp: Date.now()
      };
      
      // Salva l'informazione di impersonificazione per la verifica
      localStorage.setItem('user_impersonation', JSON.stringify({
        userId: userId,
        isImpersonating: true,
        timestamp: Date.now()
      }));
      
      // Usa window.location per un redirect vero che aggiornerà completamente la sessione
      window.location.href = `/api/users/direct-impersonate/${userId}?timestamp=${Date.now()}`;
      
      // Ritorna un risultato fittizio per completare la mutazione correttamente
      return {
        success: true,
        message: "Avvio impersonificazione in corso...",
        redirecting: true
      };
    },
    onSuccess: (data) => {
      console.log("Impersonificazione avviata:", data);
      
      // Non facciamo nulla qui perché il redirect è già gestito direttamente
      // nella funzione mutationFn
      
      // Mostra un messaggio di avvio impersonificazione
      toast({
        title: "Avvio impersonificazione...",
        description: "Stai per impersonare un altro utente. Verrai reindirizzato automaticamente.",
      });
    },
    onError: (error) => {
      console.error("Errore durante l'impersonificazione:", error);
      toast({
        title: "Errore impersonificazione",
        description: error instanceof Error ? error.message : "Si è verificato un errore durante l'impersonificazione dell'utente.",
        variant: "destructive",
      });
    }
  });

  // Handle user creation
  const handleCreateUser = (userData: { username: string; email: string; password: string; isAdmin: boolean; role: string }) => {
    createUserMutation.mutate(userData);
  };

  // Handle user update
  const handleUpdateUser = (userData: Partial<User>) => {
    console.log("Updating user with data:", userData);
    updateUserMutation.mutate(userData);
  };

  // Handle user deletion
  const handleDeleteUser = () => {
    if (deleteUser) {
      deleteUserMutation.mutate(deleteUser.id);
      setDeleteUser(null);
    }
  };

  // Handle opening edit dialog
  const handleEdit = (user: User) => {
    console.log("Opening edit dialog with user:", JSON.stringify(user));
    // Imposta immediatamente l'utente per l'editing senza chiamate API aggiuntive
    setEditUser(user);
  };

  // Handle opening delete dialog
  const handleDelete = (userId: string) => {
    const user = users?.find((u) => u.id === userId);
    if (user) {
      setDeleteUser(user);
    }
  };

  // Handle toggling admin status
  const handleToggleAdmin = (userId: string, isAdmin: boolean) => {
    toggleAdminMutation.mutate({ userId, isAdmin });
  };

  // Filter users based on search query and role
  const filteredUsers = users
    ? users.filter((user) => {
        // I manager non possono vedere gli utenti admin
        if (!isCurrentUserAdmin && (user.isAdmin || user.role === UserRole.ADMIN)) {
          return false;
        }
        
        return (
          user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      })
    : [];

  return (
    <div className="flex flex-col h-screen">
      <Header title="Gestione Utenti" showBack backPath="/settings" />

      <main className="flex-1 overflow-y-scroll bg-[#f5f5f7] pt-24 pb-16 px-4" style={{height: 'calc(100vh - 96px)', overflowY: 'scroll', WebkitOverflowScrolling: 'touch'}}>
        <div className="max-w-3xl mx-auto py-6">
          <div className="mb-6">
            <div className="relative">
              <Input
                type="text"
                placeholder="Cerca utenti..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10"
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <SearchIcon className="h-5 w-5" />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {isUsersLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="h-20 bg-gray-200 animate-pulse rounded-lg"
                ></div>
              ))
            ) : filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <UserCard
                  key={user.id}
                  user={user}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onImpersonate={isCurrentUserAdmin ? (userId: string) => {
                    const target = users?.find((u) => u.id === userId);
                    if (target) {
                      setShowImpersonateConfirm(true);
                      setImpersonateUser(target);
                    }
                  } : undefined}
                  isCurrentUserAdmin={isCurrentUserAdmin}
                  currentUser={currentUser as any}
                />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                Nessun utente trovato
              </div>
            )}
          </div>

          {isCurrentUserAdmin && (
            <Button
              className="fixed bottom-36 right-4 w-14 h-14 rounded-full flex items-center justify-center shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(59,130,246,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu bg-blue-500 border-2 border-blue-400"
              onClick={() => setShowCreateDialog(true)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-white drop-shadow-md"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                style={{ filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.3))', transform: 'translateZ(5px) scale(1.6)' }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2.5}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </Button>
          )}
        </div>
      </main>

      <Footer />

      {/* Dialogs */}
      <CreateUserDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSave={handleCreateUser}
      />

      <EditUserDialog
        user={editUser}
        open={!!editUser}
        onOpenChange={(open) => !open && setEditUser(null)}
        onSave={handleUpdateUser}
      />

      <DeleteUserDialog
        open={!!deleteUser}
        onOpenChange={(open) => !open && setDeleteUser(null)}
        onConfirm={handleDeleteUser}
        username={deleteUser?.username || ""}
      />
      
      {/* Dialogo conferma impersonificazione */}
      <AlertDialog open={showImpersonateConfirm} onOpenChange={setShowImpersonateConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Conferma impersonificazione</AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler impersonare l'utente <strong>{impersonateUser?.username}</strong>?
              <br /><br />
              Durante l'impersonificazione, avrai le stesse autorizzazioni e limitazioni dell'utente selezionato.
              Ricordati di tornare al tuo account amministratore al termine dell'operazione.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            <AlertDialogAction
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => {
                if (impersonateUser) {
                  impersonateUserMutation.mutate(impersonateUser.id);
                }
                setShowImpersonateConfirm(false);
              }}
            >
              Impersona utente
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
