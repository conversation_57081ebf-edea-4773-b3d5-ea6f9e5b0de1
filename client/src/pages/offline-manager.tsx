import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Settings, 
  AlertTriangle,
  Info,
  ArrowLeft
} from "lucide-react";
import { useLocation } from "wouter";
import { useOfflineApi } from "@/hooks/use-offline-api";
import { SyncStatus } from "@/components/ui/sync-status";
import { useToast } from "@/hooks/use-toast";

export default function OfflineManager() {
  const { isOnline, pendingOperations } = useOfflineApi();
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLocation("/settings")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Impostazioni
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-slate-900">Sistema Cache</h1>
              <p className="text-slate-600">Gestione del sistema di cache offline</p>
            </div>
          </div>
          <SyncStatus showBadge={true} showText={true} />
        </div>

        {/* Status Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isOnline ? (
                <Wifi className="h-5 w-5 text-green-500" />
              ) : (
                <WifiOff className="h-5 w-5 text-amber-500" />
              )}
              Stato Connessione
            </CardTitle>
            <CardDescription>
              Stato attuale della connessione di rete
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <Badge variant={isOnline ? "default" : "secondary"}>
                  {isOnline ? "Online" : "Offline"}
                </Badge>
                {pendingOperations > 0 && (
                  <Badge variant="outline" className="ml-2">
                    {pendingOperations} operazioni in coda
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cache Disabled Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Sistema di cache disabilitato</strong><br />
            Il sistema di caching è stato completamente disabilitato per massimizzare le performance. 
            Tutti i dati vengono recuperati direttamente dal server ad ogni richiesta.
          </AlertDescription>
        </Alert>

        {/* Performance Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configurazione Performance
            </CardTitle>
            <CardDescription>
              Impostazioni correnti del sistema
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-slate-50 rounded-lg">
                <h4 className="font-medium text-slate-900">React Query Cache</h4>
                <p className="text-sm text-slate-600">Disabilitato (cacheTime: 0)</p>
              </div>
              <div className="p-4 bg-slate-50 rounded-lg">
                <h4 className="font-medium text-slate-900">Offline Storage</h4>
                <p className="text-sm text-slate-600">Disabilitato</p>
              </div>
              <div className="p-4 bg-slate-50 rounded-lg">
                <h4 className="font-medium text-slate-900">PWA Cache</h4>
                <p className="text-sm text-slate-600">Disabilitato</p>
              </div>
              <div className="p-4 bg-slate-50 rounded-lg">
                <h4 className="font-medium text-slate-900">Service Worker</h4>
                <p className="text-sm text-slate-600">Solo per notifiche</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Azioni Rapide</CardTitle>
            <CardDescription>
              Operazioni di manutenzione del sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Ricarica App
              </Button>
              <Button
                variant="outline"
                onClick={() => setLocation("/settings")}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Impostazioni Generali
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}