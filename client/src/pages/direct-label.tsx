import { useState, useRef, useEffect } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Camera } from "@/components/ui/camera";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Loader2 } from "lucide-react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { SelectDDTDialog } from "@/components/ddt/select-ddt-dialog";
import { generateUniqueId } from "@/lib/utils";

// Form validation schema
const labelFormSchema = z.object({
  productName: z.string().min(1, "Product name is required"),
  expiryDate: z.string().min(1, "Expiry date is required"),
  batchNumber: z.string().min(1, "Batch number is required"),
  storageInstructions: z.string().optional(),
  notes: z.string().optional(),
});

type LabelFormData = z.infer<typeof labelFormSchema>;

export default function DirectLabel() {
  const [, setLocation] = useLocation();
  const [showCamera, setShowCamera] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Cleanup camera when component unmounts or user navigates away
  useEffect(() => {
    return () => {
      // Force close camera if it's open when component unmounts
      if (showCamera) {
        setShowCamera(false);
      }
    };
  }, []);

  // Close camera when user navigates away from page
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (showCamera) {
        setShowCamera(false);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [showCamera]);

  const form = useForm<LabelFormData>({
    resolver: zodResolver(labelFormSchema),
    defaultValues: {
      productName: "",
      expiryDate: "",
      batchNumber: "",
      storageInstructions: "",
      notes: "",
    },
  });

  const handleCapture = async (imageData: string) => {
    try {
      setShowCamera(false);
      setPreviewImage(imageData);
      await processImage(imageData);
    } catch (error) {
      console.error("Error processing captured image:", error);
      toast({
        title: "Processing Failed",
        description: "Could not process the captured image.",
        variant: "destructive",
      });
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        console.log("📁 DIRECT-LABEL: File upload started...");
        setIsUploading(true);
        const reader = new FileReader();
        reader.onload = async (event) => {
          if (event.target?.result) {
            const imageData = event.target.result as string;
            console.log("📁 DIRECT-LABEL: File loaded, calling processImage...");
            setPreviewImage(imageData);
            await processImage(imageData);
          }
        };
        reader.readAsDataURL(file);
      } catch (error) {
        console.error("Error processing uploaded file:", error);
        toast({
          title: "Processing Failed",
          description: "Could not process the uploaded file.",
          variant: "destructive",
        });
      } finally {
        setIsUploading(false);
      }
    }
  };

  const processImage = async (imageData: string) => {
    const callStack = new Error().stack;
    console.log("🔄 DIRECT-LABEL: processImage called from:", callStack?.split('\n')[2]?.trim());
    
    try {
      setIsProcessing(true);
      toast({
        title: "Processing Image",
        description: "Please wait while we extract information...",
      });

      console.log("🔄 DIRECT-LABEL: Starting image processing...");
      const response = await apiRequest(
        "/api/direct-upload/process-label", 
        "POST",
        { imageData }
      );
      console.log("✅ DIRECT-LABEL: Processing completed successfully");
      
      console.log("Label API direct upload response:", response);
      
      if (response) {
        const result = response as any; // Tipiamo come any per accedere alle proprietà
        form.reset({
          productName: result.productName || result.product_name || "",
          expiryDate: result.expiryDate || result.expiry_date || "",
          batchNumber: result.batchNumber || result.batch_number || "",
          storageInstructions: result.storageInstructions || result.storage_instructions || "",
          notes: result.notes || "",
        });
        
        toast({
          title: "Processing Complete",
          description: "Label information extracted successfully.",
        });
      }
    } catch (error) {
      console.error("Direct label API processing error:", error);
      toast({
        title: "Processing Failed",
        description: "Failed to extract information from the label. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Stato per la selezione DDT
  const [showDDTSelector, setShowDDTSelector] = useState(false);
  const [selectedDDT, setSelectedDDT] = useState<any>(null);
  const [qrCodeData, setQrCodeData] = useState<string>("");

  // Funzione per gestire la selezione DDT
  const handleDDTSelected = (ddt: any) => {
    setSelectedDDT(ddt);
    toast({
      title: "DDT Selezionato",
      description: `DDT ${ddt.number} selezionato con successo.`,
    });
  };

  // Genera un QR code unico per questa etichetta
  const generateQRCode = () => {
    if (!qrCodeData) {
      // Genera un ID univoco per il QR code se non esiste
      const qrId = generateUniqueId("L");
      setQrCodeData(qrId);
      return qrId;
    }
    return qrCodeData;
  };

  // Salva l'etichetta con API
  const saveLabel = async (data: LabelFormData, ddtId: number) => {
    try {
      console.log("💾 DIRECT-LABEL: Starting save label process...");
      // Genera un QR code se non esiste
      const qrCode = generateQRCode();
      
      // Prepara i dati per l'invio
      const labelData = {
        ...data,
        ddtId,
        image: previewImage,
        qrCode
      };
      
      console.log("💾 DIRECT-LABEL: Saving label with DDT ID:", ddtId);
      
      // Invia all'API
      const response = await apiRequest(
        "/api/product-labels",
        "POST",
        labelData
      );
      
      // Pulizia completa al termine del task
      sessionStorage.removeItem("tempLabelData");
      sessionStorage.removeItem("currentDDTId");
      sessionStorage.removeItem("currentDDTNumber");
      console.log("Task completato - DDT associazione rimossa per nuovo task");
      
      toast({
        title: "Etichetta Salvata",
        description: "Informazioni dell'etichetta salvate con successo.",
      });
      
      // Resetta lo stato della modale
      setShowDDTSelector(false);
      setSelectedDDT(null);
      
      // Reindirizza alla pagina principale dopo un breve ritardo
      setTimeout(() => {
        setLocation("/");
      }, 500);
      
      return response;
    } catch (error) {
      console.error("Error saving label:", error);
      toast({
        title: "Errore Salvataggio",
        description: "Impossibile salvare l'etichetta. Riprova.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const onSubmit = (data: LabelFormData) => {
    // Verifica se abbiamo già un DDT selezionato o se dobbiamo aprire il selettore
    if (selectedDDT) {
      // Se abbiamo già un DDT, salva direttamente
      saveLabel(data, selectedDDT.id);
    } else {
      // Altrimenti, mostra il selettore DDT
      setShowDDTSelector(true);
      
      // Memorizziamo temporaneamente i dati del form
      sessionStorage.setItem("tempLabelData", JSON.stringify(data));
    }
  };

  return (
    <GradientBackground>
      <Header 
        title="Scan Etichetta (Direct)" 
        showBack 
        backPath="/product-label" 
      />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        {!previewImage && !isProcessing && !isUploading ? (
          <div className="flex flex-col items-center justify-center h-full max-w-md mx-auto gap-6">
            <Card className="w-full p-6 text-center">
              <CardContent className="p-0">
                <h2 className="text-xl font-bold mb-4">Scan Product Label</h2>
                <p className="text-gray-500 mb-6">
                  Take a photo of the product label or upload an image to extract information.
                </p>
                <div className="flex flex-col gap-4">
                  <Button 
                    className="w-full py-6 bg-gradient-to-r from-blue-600 to-indigo-600"
                    onClick={() => setShowCamera(true)}
                  >
                    Take Photo
                  </Button>
                  <div className="relative">
                    <Input 
                      ref={fileInputRef}
                      type="file" 
                      accept="image/*"
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                    <Button 
                      variant="outline" 
                      className="w-full py-6 border-dashed"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      Upload Image
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : isProcessing || isUploading ? (
          <div className="flex flex-col items-center justify-center h-full max-w-md mx-auto">
            <Card className="w-full p-6 text-center">
              <CardContent className="p-0">
                <div className="flex flex-col items-center">
                  <Loader2 className="h-10 w-10 text-indigo-600 animate-spin mb-4" />
                  <h2 className="text-xl font-semibold mb-2">
                    {isProcessing ? "Processing Image" : "Uploading"}
                  </h2>
                  <p className="text-gray-500">
                    Please wait while we extract information from your image...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="max-w-md mx-auto">
              <Card className="mb-6">
                <CardContent className="p-6 space-y-4">
                  <div className="w-full aspect-square max-h-60 overflow-hidden rounded-lg mb-4">
                    {previewImage && (
                      <img
                        src={previewImage}
                        alt="Label Preview"
                        className="object-contain w-full h-full"
                      />
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="batchNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Batch Number</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="expiryDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Expiry Date</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="DD/MM/YYYY" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="storageInstructions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Storage Instructions</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Storage conditions, temperature requirements, etc."
                            className="resize-none"
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Additional notes about this product..."
                            className="resize-none"
                            rows={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    setPreviewImage(null);
                    form.reset();
                  }}
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600"
                  disabled={form.formState.isSubmitting}
                >
                  {form.formState.isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span>Salvando...</span>
                    </div>
                  ) : (
                    "Save Label"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </main>

      {showCamera && (
        <Camera
          aspectRatio="vertical"
          onCapture={handleCapture}
          onClose={() => setShowCamera(false)}
          enableStabilization={true}
          cameraMode="label"
        />
      )}

      <Footer activeItem="goods" />
      
      {/* Dialog di selezione DDT */}
      <SelectDDTDialog 
        open={showDDTSelector}
        onOpenChange={(open) => {
          setShowDDTSelector(open);
          // Se l'utente chiude la dialog senza selezionare, ripuliamo
          if (!open && !selectedDDT) {
            sessionStorage.removeItem("tempLabelData");
          }
        }}
        onSelectDDT={(ddt) => {
          console.log("🎯 DIRECT-LABEL: onSelectDDT called with DDT:", ddt.id);
          // Verifica se ci sono dati temporanei da salvare
          const tempData = sessionStorage.getItem("tempLabelData");
          console.log("🎯 DIRECT-LABEL: Found temp data:", !!tempData);
          if (tempData) {
            try {
              const parsedData = JSON.parse(tempData);
              console.log("🎯 DIRECT-LABEL: Calling saveLabel with temp data...");
              // Salva direttamente senza chiamare handleDDTSelected per evitare doppio processamento
              saveLabel(parsedData, ddt.id);
            } catch (e) {
              console.error("Errore nel parsing dei dati temporanei:", e);
              toast({
                title: "Errore",
                description: "Errore nel salvataggio dei dati. Riprova.",
                variant: "destructive",
              });
            }
          } else {
            console.log("🎯 DIRECT-LABEL: No temp data, calling handleDDTSelected...");
            // Se non ci sono dati temporanei, seleziona semplicemente il DDT
            handleDDTSelected(ddt);
          }
        }}
      />
    </GradientBackground>
  );
}