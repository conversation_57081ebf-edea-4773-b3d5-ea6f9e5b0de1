import React from 'react';
import { <PERSON> } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Header } from '@/components/layout/header';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { GradientBackground } from '@/components/layout/gradient-background';

export default function TestPage() {
  return (
    <GradientBackground>
      <Header title="Pagina di Test" />
      
      <main className="flex-1 pt-24 pb-20 px-4 overflow-y-auto">
        <div className="max-w-md mx-auto">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Questa è una pagina di test</h2>
            <p className="text-gray-600 mb-6">
              Se stai vedendo questo messaggio, significa che la pagina è stata caricata correttamente.
            </p>
            
            <Link href="/">
              <Button className="w-full">Torna alla home</Button>
            </Link>
          </Card>
        </div>
      </main>
      
      <BottomNavigation activeItem="home" />
    </GradientBackground>
  );
}console.log('TEST UPDATE')
