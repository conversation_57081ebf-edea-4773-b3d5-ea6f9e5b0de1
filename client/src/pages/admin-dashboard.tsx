import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Building2, 
  Plus, 
  Users, 
  Activity, 
  TrendingUp,
  Database,
  CheckCircle2,
  AlertCircle,
  Search,
  MoreHorizontal,
  Edit2,
  Trash2,
  Eye,
  Edit3,
  Pause,
  Play,
  ExternalLink,
  LogIn,
  UserCheck,
  Shield
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

// Tipi per i dati dell'admin dashboard
interface DashboardStats {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
}

interface Tenant {
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  vatNumber?: string;
  email?: string;
  phone?: string;
  userCount: number;
  maxUsers: number;
  createdAt: string;
}

export default function AdminDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
  const [newTenant, setNewTenant] = useState({
    name: '',
    code: '',
    type: 'restaurant',
    email: '',
    phone: '',
    vatNumber: '',
    maxUsers: 50,
  });
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Query per le statistiche del dashboard
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['admin-dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/dashboard/stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      return response.json() as Promise<DashboardStats>;
    },
  });

  // Query per i tenant
  const { data: tenants, isLoading: tenantsLoading } = useQuery({
    queryKey: ['admin-tenants', searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      
      const response = await fetch(`/api/admin/tenants?${params}`);
      if (!response.ok) throw new Error('Failed to fetch tenants');
      return response.json() as Promise<Tenant[]>;
    },
  });

  // Mutation per creare un nuovo tenant
  const createMutation = useMutation({
    mutationFn: async (tenantData: typeof newTenant) => {
      const response = await fetch('/api/admin/tenants', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tenantData),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create tenant');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tenants'] });
      queryClient.invalidateQueries({ queryKey: ['admin-dashboard-stats'] });
      setShowCreateModal(false);
      setNewTenant({
        name: '',
        code: '',
        type: 'restaurant',
        email: '',
        phone: '',
        vatNumber: '',
        maxUsers: 50,
      });
      toast({
        title: 'Tenant creato',
        description: 'Il nuovo tenant è stato creato con successo.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Errore',
        description: error.message || 'Impossibile creare il tenant.',
        variant: 'destructive',
      });
    },
  });

  // Mutation per eliminare un tenant
  const deleteMutation = useMutation({
    mutationFn: async (tenantId: string) => {
      const response = await fetch(`/api/admin/tenants/${tenantId}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete tenant');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tenants'] });
      queryClient.invalidateQueries({ queryKey: ['admin-dashboard-stats'] });
      setSelectedTenant(null);
      toast({
        title: 'Tenant eliminato',
        description: 'Il tenant è stato eliminato con successo.',
      });
    },
    onError: () => {
      toast({
        title: 'Errore',
        description: 'Impossibile eliminare il tenant.',
        variant: 'destructive',
      });
    },
  });

  // Mutation per modificare un tenant
  const updateMutation = useMutation({
    mutationFn: async (tenantData: { id: string; name: string; type: string; email: string; phone: string; vatNumber: string; maxUsers: number; status: string }) => {
      const response = await fetch(`/api/admin/tenants/${tenantData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tenantData),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update tenant');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tenants'] });
      queryClient.invalidateQueries({ queryKey: ['admin-dashboard-stats'] });
      setShowEditModal(false);
      setEditingTenant(null);
      setSelectedTenant(null);
      toast({
        title: 'Tenant aggiornato',
        description: 'Le modifiche sono state salvate con successo.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Errore',
        description: error.message || 'Impossibile aggiornare il tenant.',
        variant: 'destructive',
      });
    },
  });

  // Mutation per sospendere/riattivare un tenant
  const toggleStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      const response = await fetch(`/api/admin/tenants/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update tenant status');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tenants'] });
      queryClient.invalidateQueries({ queryKey: ['admin-dashboard-stats'] });
      setSelectedTenant(null);
      toast({
        title: 'Stato aggiornato',
        description: 'Lo stato del tenant è stato modificato con successo.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Errore',
        description: error.message || 'Impossibile modificare lo stato del tenant.',
        variant: 'destructive',
      });
    },
  });

  const handleCreateTenant = () => {
    if (!newTenant.name || !newTenant.code) {
      toast({
        title: 'Errore',
        description: 'Nome e codice sono obbligatori.',
        variant: 'destructive',
      });
      return;
    }
    createMutation.mutate(newTenant);
  };

  const handleEditTenant = (tenant: Tenant) => {
    setEditingTenant(tenant);
    setShowEditModal(true);
  };

  const handleUpdateTenant = () => {
    if (!editingTenant) return;
    
    if (!editingTenant.name) {
      toast({
        title: 'Errore',
        description: 'Il nome è obbligatorio.',
        variant: 'destructive',
      });
      return;
    }
    
    updateMutation.mutate({
      id: editingTenant.id,
      name: editingTenant.name,
      type: editingTenant.type,
      email: editingTenant.email || '',
      phone: editingTenant.phone || '',
      vatNumber: editingTenant.vatNumber || '',
      maxUsers: editingTenant.maxUsers,
      status: editingTenant.status,
    });
  };

  const handleToggleStatus = (tenant: Tenant) => {
    const newStatus = tenant.status === 'active' ? 'suspended' : 'active';
    const action = newStatus === 'suspended' ? 'sospendere' : 'riattivare';
    
    if (confirm(`Sei sicuro di voler ${action} il tenant "${tenant.name}"?`)) {
      toggleStatusMutation.mutate({ id: tenant.id, status: newStatus });
    }
  };

  const handleDeleteTenant = (tenant: Tenant) => {
    if (confirm(`Sei sicuro di voler eliminare il tenant "${tenant.name}"? Questa azione non può essere annullata.`)) {
      deleteMutation.mutate(tenant.id);
    }
  };

  // Nuova funzione per aprire il frontend del tenant in nuovo tab con admin già loggato
  const handleOpenTenantFrontend = async (tenant: Tenant) => {
    try {
      // Prima verifichiamo che il tenant sia attivo
      if (tenant.status !== 'active') {
        toast({
          title: 'Tenant non attivo',
          description: 'Il tenant deve essere attivo per accedere al frontend.',
          variant: 'destructive',
        });
        return;
      }

      // Per ora usiamo un approccio semplificato con parametri URL
      // In futuro si potrà implementare un sistema di token più sicuro
      const tenantUrl = new URL(window.location.origin);
      tenantUrl.searchParams.set('tenant', tenant.code);
      tenantUrl.searchParams.set('adminAccess', 'true');
      tenantUrl.searchParams.set('timestamp', Date.now().toString());
      
      window.open(tenantUrl.toString(), '_blank');
      
      toast({
        title: 'Frontend aperto',
        description: `Aperto frontend di ${tenant.name} in nuova scheda.`,
      });

    } catch (error) {
      console.error('Errore apertura frontend tenant:', error);
      toast({
        title: 'Errore',
        description: 'Impossibile aprire il frontend del tenant.',
        variant: 'destructive',
      });
    }
  };

  // Funzione per accedere direttamente come admin al tenant (stesso tab)
  const handleDirectTenantAccess = async (tenant: Tenant) => {
    try {
      if (tenant.status !== 'active') {
        toast({
          title: 'Tenant non attivo',
          description: 'Il tenant deve essere attivo per accedere.',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Accesso in corso...',
        description: `Accedendo a ${tenant.name} come amministratore.`,
      });

      // Redirect diretto con parametri per l'accesso admin
      window.location.href = `/?tenant=${tenant.code}&adminAccess=true`;

    } catch (error) {
      console.error('Errore accesso diretto tenant:', error);
      toast({
        title: 'Errore',
        description: 'Impossibile accedere al tenant.',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: 'Attivo' },
      inactive: { variant: 'secondary' as const, label: 'Inattivo' },
      suspended: { variant: 'destructive' as const, label: 'Sospeso' },
      trial: { variant: 'outline' as const, label: 'Prova' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.inactive;
    
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  if (statsLoading || tenantsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Tenant Totali',
      value: stats?.totalTenants || 0,
      icon: Building2,
      description: `${stats?.activeTenants || 0} attivi`,
      color: 'text-blue-600',
    },
    {
      title: 'Utenti Totali',
      value: stats?.totalUsers || 0,
      icon: Users,
      description: `${stats?.activeUsers || 0} attivi`,
      color: 'text-green-600',
    },
    {
      title: 'Attività Sistema',
      value: '98.5%',
      icon: Activity,
      description: 'Uptime ultimo mese',
      color: 'text-purple-600',
    },
    {
      title: 'Crescita',
      value: '+12%',
      icon: TrendingUp,
      description: 'Nuovi tenant questo mese',
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="admin-dashboard-content container mx-auto p-4 md:p-6 space-y-4 md:space-y-6">
      {/* Header - Responsive */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Dashboard Amministrativa</h1>
          <p className="text-muted-foreground mt-1 md:mt-2 text-sm md:text-base">
            Gestione del sistema multi-tenant HACCP Tracker
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => window.location.href = '/security-admin'} className="w-full sm:w-auto">
            <Shield className="h-4 w-4 mr-2" />
            Sicurezza
          </Button>
          <Button onClick={() => setShowCreateModal(true)} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Nuovo Tenant
          </Button>
        </div>
      </div>

      {/* Stats Cards - Mobile Optimized */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 md:p-6">
                <CardTitle className="text-xs md:text-sm font-medium leading-tight">{card.title}</CardTitle>
                <Icon className={`h-3 w-3 md:h-4 md:w-4 ${card.color} flex-shrink-0`} />
              </CardHeader>
              <CardContent className="p-3 md:p-6 pt-0">
                <div className="text-lg md:text-2xl font-bold">{card.value}</div>
                <p className="text-xs text-muted-foreground leading-tight">{card.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Gestione Tenant</CardTitle>
          <CardDescription>
            Visualizza e gestisci tutti i tenant del sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cerca per nome, codice o email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select className="px-4 py-2 border rounded-lg sm:min-w-[160px]">
              <option value="">Tutti gli stati</option>
              <option value="active">Attivi</option>
              <option value="inactive">Inattivi</option>
              <option value="suspended">Sospesi</option>
              <option value="trial">In prova</option>
            </select>
          </div>

          {/* Tenants - Mobile Cards, Desktop Table */}
          <div className="block md:hidden space-y-3">
            {/* Cards for Mobile */}
            {tenants?.map((tenant) => (
              <Card key={tenant.id} className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <Building2 className="h-8 w-8 text-muted-foreground mr-3 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-sm">{tenant.name}</div>
                      <div className="text-xs text-muted-foreground">{tenant.code}</div>
                    </div>
                  </div>
                  {getStatusBadge(tenant.status)}
                </div>
                
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <div className="text-xs text-muted-foreground">Tipo</div>
                    <div className="text-sm capitalize">{tenant.type.replace('_', ' ')}</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground">Creato</div>
                    <div className="text-sm">{new Date(tenant.createdAt).toLocaleDateString('it-IT')}</div>
                  </div>
                </div>

                <div className="mb-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-muted-foreground">Utenti</span>
                    <span className="text-xs font-medium">{tenant.userCount} / {tenant.maxUsers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${Math.min((tenant.userCount / tenant.maxUsers) * 100, 100)}%` }}
                    />
                  </div>
                </div>

                <div className="flex flex-wrap gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleOpenTenantFrontend(tenant)}
                    className="flex-1 text-xs px-2 py-1 h-8"
                    title="Apri frontend in nuovo tab"
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Apri
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDirectTenantAccess(tenant)}
                    className="flex-1 text-xs px-2 py-1 h-8"
                    title="Accedi come admin"
                  >
                    <LogIn className="h-3 w-3 mr-1" />
                    Accedi
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditTenant(tenant)}
                    className="flex-1 text-xs px-2 py-1 h-8"
                    title="Modifica tenant"
                  >
                    <Edit2 className="h-3 w-3 mr-1" />
                    Modifica
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleStatus(tenant)}
                    className="flex-1 text-xs px-2 py-1 h-8"
                    title={tenant.status === 'active' ? 'Sospendi' : 'Riattiva'}
                  >
                    {tenant.status === 'active' ? <Pause className="h-3 w-3 mr-1" /> : <Play className="h-3 w-3 mr-1" />}
                    {tenant.status === 'active' ? 'Sospendi' : 'Riattiva'}
                  </Button>
                  {tenant.code !== 'default' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteTenant(tenant)}
                      className="text-red-600 hover:text-red-700 text-xs px-2 py-1 h-8"
                      title="Elimina tenant"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </Card>
            ))}
          </div>

          {/* Table for Desktop */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Tenant</th>
                  <th className="text-left p-4 font-medium">Tipo</th>
                  <th className="text-left p-4 font-medium">Stato</th>
                  <th className="text-left p-4 font-medium">Utenti</th>
                  <th className="text-left p-4 font-medium">Creato</th>
                  <th className="text-right p-4 font-medium">Azioni</th>
                </tr>
              </thead>
              <tbody>
                {tenants?.map((tenant) => (
                  <tr key={tenant.id} className="border-b hover:bg-muted/50">
                    <td className="p-4">
                      <div className="flex items-center">
                        <Building2 className="h-8 w-8 text-muted-foreground mr-3" />
                        <div>
                          <div className="font-medium">{tenant.name}</div>
                          <div className="text-sm text-muted-foreground">{tenant.code}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="capitalize">{tenant.type.replace('_', ' ')}</span>
                    </td>
                    <td className="p-4">
                      {getStatusBadge(tenant.status)}
                    </td>
                    <td className="p-4">
                      <div>{tenant.userCount} / {tenant.maxUsers}</div>
                      <div className="w-full bg-muted rounded-full h-2 mt-1">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${Math.min((tenant.userCount / tenant.maxUsers) * 100, 100)}%` }}
                        />
                      </div>
                    </td>
                    <td className="p-4 text-sm text-muted-foreground">
                      {new Date(tenant.createdAt).toLocaleDateString('it-IT')}
                    </td>
                    <td className="p-4">
                      <div className="flex justify-end space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedTenant(tenant)}
                          title="Visualizza dettagli"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenTenantFrontend(tenant)}
                          disabled={tenant.status !== 'active'}
                          className="text-blue-600 hover:text-blue-700"
                          title="Apri in nuovo tab come admin"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDirectTenantAccess(tenant)}
                          disabled={tenant.status !== 'active'}
                          className="text-green-600 hover:text-green-700"
                          title="Accedi come admin"
                        >
                          <UserCheck className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditTenant(tenant)}
                          title="Modifica tenant"
                        >
                          <Edit3 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(tenant)}
                          disabled={toggleStatusMutation.isPending}
                          className={tenant.status === 'active' ? 'text-yellow-600 hover:text-yellow-700' : 'text-green-600 hover:text-green-700'}
                          title={tenant.status === 'active' ? 'Sospendi tenant' : 'Attiva tenant'}
                        >
                          {tenant.status === 'active' ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteTenant(tenant)}
                          disabled={tenant.code === 'default'}
                          className="text-red-600 hover:text-red-700"
                          title="Elimina tenant"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {!tenants?.length && (
            <div className="text-center py-12">
              <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-medium">Nessun tenant trovato</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                Inizia creando il tuo primo tenant.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Status - Mobile Optimized */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Stato del Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Database</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">API Gateway</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Servizi AI</span>
              <span className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Operativo
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informazioni Sistema</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">Versione</dt>
                <dd className="mt-1 text-sm">1.2.17 Multi-Tenant</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">Uptime</dt>
                <dd className="mt-1 text-sm">15 giorni, 3 ore</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">Memoria</dt>
                <dd className="mt-1 text-sm">2.3 GB / 8 GB</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">Storage</dt>
                <dd className="mt-1 text-sm">45 GB / 100 GB</dd>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Create Tenant Modal - Mobile Responsive */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex md:items-center md:justify-center md:p-4">
          <div className="w-full h-full md:w-auto md:h-auto md:max-w-md md:rounded-lg overflow-hidden flex flex-col">
            <Card className="w-full h-full md:h-auto flex flex-col">
              <CardHeader className="flex-shrink-0">
                <div className="flex justify-between items-start">
                  <CardTitle>Crea Nuovo Tenant</CardTitle>
                  <Button
                    variant="ghost"
                    onClick={() => setShowCreateModal(false)}
                  >
                    ×
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-4 pb-20 md:pb-4">
                <div>
                  <label className="text-sm font-medium">Nome *</label>
                  <Input
                    value={newTenant.name}
                    onChange={(e) => setNewTenant({ ...newTenant, name: e.target.value })}
                    placeholder="Nome del ristorante/azienda"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Codice *</label>
                  <Input
                    value={newTenant.code}
                    onChange={(e) => setNewTenant({ ...newTenant, code: e.target.value })}
                    placeholder="Codice identificativo univoco"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Tipo</label>
                  <select 
                    value={newTenant.type}
                    onChange={(e) => setNewTenant({ ...newTenant, type: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                  >
                    <option value="restaurant">Ristorante</option>
                    <option value="catering">Catering</option>
                    <option value="food_truck">Food Truck</option>
                    <option value="bakery">Panificio</option>
                    <option value="pizzeria">Pizzeria</option>
                  </select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    type="email"
                    value={newTenant.email}
                    onChange={(e) => setNewTenant({ ...newTenant, email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Telefono</label>
                  <Input
                    value={newTenant.phone}
                    onChange={(e) => setNewTenant({ ...newTenant, phone: e.target.value })}
                    placeholder="+39 xxx xxx xxxx"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Partita IVA</label>
                  <Input
                    value={newTenant.vatNumber}
                    onChange={(e) => setNewTenant({ ...newTenant, vatNumber: e.target.value })}
                    placeholder="*************"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Massimo Utenti</label>
                  <Input
                    type="number"
                    value={newTenant.maxUsers}
                    onChange={(e) => setNewTenant({ ...newTenant, maxUsers: parseInt(e.target.value) || 50 })}
                    min="1"
                    max="1000"
                  />
                </div>
              
                <div className="flex flex-col sm:flex-row justify-end gap-2 pt-4 sticky bottom-0 bg-white md:bg-transparent md:static">
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateModal(false)}
                    className="w-full sm:w-auto"
                  >
                    Annulla
                  </Button>
                  <Button
                    onClick={handleCreateTenant}
                    disabled={createMutation.isPending}
                    className="w-full sm:w-auto"
                  >
                    {createMutation.isPending ? 'Creazione...' : 'Crea Tenant'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Edit Tenant Modal */}
      {showEditModal && editingTenant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex md:items-center md:justify-center md:p-4">
          <div className="w-full h-full md:w-auto md:h-auto md:max-w-lg md:rounded-lg overflow-hidden flex flex-col">
            <Card className="w-full h-full md:h-auto flex flex-col">
              <CardHeader className="flex-shrink-0">
                <div className="flex justify-between items-start">
                  <CardTitle>Modifica Tenant</CardTitle>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      setShowEditModal(false);
                      setEditingTenant(null);
                    }}
                  >
                    ×
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-4 pb-20 md:pb-4">
              <div>
                <label className="text-sm font-medium">Nome *</label>
                <Input
                  value={editingTenant.name}
                  onChange={(e) => setEditingTenant({ ...editingTenant, name: e.target.value })}
                  placeholder="Nome del ristorante/azienda"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Codice</label>
                <Input
                  value={editingTenant.code}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground mt-1">Il codice non può essere modificato</p>
              </div>
              
              <div>
                <label className="text-sm font-medium">Tipo</label>
                <select 
                  value={editingTenant.type}
                  onChange={(e) => setEditingTenant({ ...editingTenant, type: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="restaurant">Ristorante</option>
                  <option value="catering">Catering</option>
                  <option value="food_truck">Food Truck</option>
                  <option value="bakery">Panificio</option>
                  <option value="pizzeria">Pizzeria</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  value={editingTenant.email || ''}
                  onChange={(e) => setEditingTenant({ ...editingTenant, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Telefono</label>
                <Input
                  value={editingTenant.phone || ''}
                  onChange={(e) => setEditingTenant({ ...editingTenant, phone: e.target.value })}
                  placeholder="+39 xxx xxx xxxx"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Partita IVA</label>
                <Input
                  value={editingTenant.vatNumber || ''}
                  onChange={(e) => setEditingTenant({ ...editingTenant, vatNumber: e.target.value })}
                  placeholder="*************"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Massimo Utenti</label>
                <Input
                  type="number"
                  value={editingTenant.maxUsers}
                  onChange={(e) => setEditingTenant({ ...editingTenant, maxUsers: parseInt(e.target.value) || 50 })}
                  min="1"
                  max="1000"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Stato</label>
                <select 
                  value={editingTenant.status}
                  onChange={(e) => setEditingTenant({ ...editingTenant, status: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="active">Attivo</option>
                  <option value="suspended">Sospeso</option>
                  <option value="inactive">Inattivo</option>
                  <option value="trial">In prova</option>
                </select>
              </div>
              
              </CardContent>
              <div className="flex-shrink-0 p-6 pt-0 md:pt-4 border-t md:border-t-0 bg-white">
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowEditModal(false);
                      setEditingTenant(null);
                    }}
                  >
                    Annulla
                  </Button>
                  <Button
                    onClick={handleUpdateTenant}
                    disabled={updateMutation.isPending}
                  >
                    {updateMutation.isPending ? 'Salvataggio...' : 'Salva Modifiche'}
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* Tenant Details Modal */}
      {selectedTenant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex md:items-center md:justify-center md:p-4">
          <div className="w-full h-full md:w-auto md:h-auto md:max-w-2xl md:rounded-lg overflow-hidden flex flex-col">
            <Card className="w-full h-full md:h-auto flex flex-col">
              <CardHeader className="flex-shrink-0">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedTenant.name}</CardTitle>
                    <CardDescription>Codice: {selectedTenant.code}</CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedTenant(null)}
                  >
                    ×
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-4 pb-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Stato</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedTenant.status)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Tipo</label>
                  <p className="mt-1 text-sm capitalize">
                    {selectedTenant.type.replace('_', ' ')}
                  </p>
                </div>
              </div>
              
              {selectedTenant.email && (
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <p className="mt-1 text-sm">{selectedTenant.email}</p>
                </div>
              )}
              
              {selectedTenant.phone && (
                <div>
                  <label className="text-sm font-medium">Telefono</label>
                  <p className="mt-1 text-sm">{selectedTenant.phone}</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Utenti</label>
                  <p className="mt-1 text-sm">
                    {selectedTenant.userCount} / {selectedTenant.maxUsers}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Creato</label>
                  <p className="mt-1 text-sm">
                    {new Date(selectedTenant.createdAt).toLocaleDateString('it-IT')}
                  </p>
                </div>
              </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}