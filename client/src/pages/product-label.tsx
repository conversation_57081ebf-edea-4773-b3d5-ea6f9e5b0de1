import { useState, useEffect } from "react";
import { useLocation, useSearch, Link } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Camera } from "@/components/ui/camera";
import { <PERSON>ert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircleIcon, CameraIcon, XIcon, CheckIcon, PlusIcon } from "lucide-react";
import { motion } from "framer-motion";

export default function ProductLabel() {
  const [, setLocation] = useLocation();
  const [showCamera, setShowCamera] = useState(false);
  const [capturedImages, setCapturedImages] = useState<string[]>([]);
  const [maxPhotos] = useState(3);
  const search = useSearch();
  const params = new URLSearchParams(search);
  const urlDdtId = params.get("ddtId");
  const [currentDDTId, setCurrentDDTId] = useState<string | null>(null);

  // Load existing images when component mounts
  useEffect(() => {
    const existingImages = sessionStorage.getItem("capturedLabelImages");
    if (existingImages) {
      try {
        const parsedImages = JSON.parse(existingImages);
        setCapturedImages(parsedImages);
      } catch (e) {
        console.error("Error parsing existing images:", e);
      }
    } else {
      // Check for single image (backward compatibility)
      const singleImage = sessionStorage.getItem("capturedLabelImage");
      if (singleImage) {
        setCapturedImages([singleImage]);
      }
    }
  }, []);
  
  // Cleanup camera when component unmounts or user navigates away
  useEffect(() => {
    return () => {
      // Force close camera if it's open when component unmounts
      if (showCamera) {
        setShowCamera(false);
      }
    };
  }, []);

  // Close camera when user navigates away from page
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (showCamera) {
        setShowCamera(false);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [showCamera]);
  
  useEffect(() => {
    // Prioritize URL parameter over sessionStorage
    if (urlDdtId) {
      sessionStorage.setItem("currentDDTId", urlDdtId);
      setCurrentDDTId(urlDdtId);
    } else {
      const storedDdtId = sessionStorage.getItem("currentDDTId");
      setCurrentDDTId(storedDdtId);
    }
  }, [urlDdtId]);
  
  // Per debugging, vediamo i valori in sessionStorage
  useEffect(() => {
    console.log("DDT ID in sessionStorage:", sessionStorage.getItem("currentDDTId"));
    console.log("DDT Number in sessionStorage:", sessionStorage.getItem("currentDDTNumber"));
  }, []);

  const compressImage = (imageData: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        const maxSize = 1200;
        if (width > maxSize || height > maxSize) {
          if (width > height) {
            height = Math.round(height * (maxSize / width));
            width = maxSize;
          } else {
            width = Math.round(width * (maxSize / height));
            height = maxSize;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);
          const compressedImage = canvas.toDataURL('image/jpeg', 0.7);
          resolve(compressedImage);
        } else {
          reject(new Error("Impossibile creare il contesto del canvas"));
        }
      };
      img.onerror = () => reject(new Error("Errore nel caricamento dell'immagine"));
      img.src = imageData;
    });
  };

  const handleCapture = async (imageData: string) => {
    setShowCamera(false);
    try {
      const compressedImage = await compressImage(imageData);
      const newImages = [...capturedImages, compressedImage];
      setCapturedImages(newImages);
    } catch (err) {
      alert("Errore nel salvataggio dell'immagine. Riprova.");
      console.error("Errore nel salvataggio dell'immagine:", err);
    }
  };

  const removeImage = (index: number) => {
    setCapturedImages(capturedImages.filter((_, i) => i !== index));
  };

  const processAllImages = () => {
    if (capturedImages.length === 0) return;
    
    try {
      // Salva tutte le immagini nel sessionStorage
      sessionStorage.setItem("capturedLabelImages", JSON.stringify(capturedImages));
      setLocation("/label-processing");
    } catch (err) {
      alert("Errore nel salvataggio delle immagini. Prova con immagini più piccole.");
      console.error("Errore nel salvataggio delle immagini:", err);
    }
  };

  if (!currentDDTId) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Errore" showBack backPath="/" />
        <main className="flex-1 pt-20 pb-20 px-6 overflow-y-auto">
          <div className="py-6 max-w-md mx-auto">
            <Alert variant="destructive">
              <AlertCircleIcon className="h-5 w-5" />
              <AlertTitle className="text-sm font-medium">Errore</AlertTitle>
              <AlertDescription className="text-xs mt-1">
                Nessun DDT attivo trovato. Devi prima registrare un documento di trasporto.
              </AlertDescription>
            </Alert>
            <Button
              className="w-full mt-6 bg-indigo-600 text-white hover:bg-indigo-700"
              onClick={() => setLocation("/incoming-goods")}
            >
              Vai a Ingresso Merci
            </Button>
          </div>
        </main>
        <Footer activeItem="goods" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#f5f5f7]">
      <Header 
        title="Etichetta Prodotto" 
        showBack 
        backPath={urlDdtId ? `/ddt-details/${urlDdtId}` : "/incoming-goods"} 
      />

      <main className="flex-1 pt-20 pb-32 px-6 overflow-y-auto">
        <div className="w-full max-w-md mx-auto space-y-6">
          

          
          <Alert className="bg-amber-50 border border-amber-100 rounded-lg mb-6">
            <AlertCircleIcon className="h-5 w-5 text-amber-500" />
            <AlertTitle className="text-sm font-medium text-amber-800">
              Associato al DDT {urlDdtId ? (
                <Link href={`/ddt-details/${urlDdtId}`} className="underline hover:text-amber-900">
                  n. {currentDDTId}
                </Link>
              ) : (
                <>n. {currentDDTId}</>
              )}
            </AlertTitle>
            <AlertDescription className="text-xs text-amber-700 mt-1">
              Puoi scattare fino a {maxPhotos} foto dell'etichetta per una migliore estrazione dati.
            </AlertDescription>
          </Alert>

          {/* Captured Images Preview */}
          {capturedImages.length > 0 && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl p-4 shadow-md border border-gray-100 mb-6"
            >
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                Foto Acquisite ({capturedImages.length}/{maxPhotos})
              </h3>
              <div className="grid grid-cols-3 gap-3 mb-4">
                {capturedImages.map((image, index) => (
                  <div key={index} className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
                    <img 
                      src={image} 
                      alt={`Foto ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <XIcon className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
              <Button
                onClick={processAllImages}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckIcon className="w-4 h-4 mr-2" />
                Elabora Tutte le Foto
              </Button>
            </motion.div>
          )}

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`w-full rounded-2xl bg-white p-5 shadow-md border border-gray-100 overflow-hidden transition-all duration-200 ${
              capturedImages.length >= maxPhotos 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:bg-gray-50 cursor-pointer'
            }`}
            onClick={() => {
              if (capturedImages.length < maxPhotos) {
                setShowCamera(true);
              }
            }}
          >
            <div className="flex items-center space-x-5">
              <div className="flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3">
                {capturedImages.length >= maxPhotos ? (
                  <CheckIcon className="h-8 w-8 text-green-600" />
                ) : (
                  <CameraIcon className="h-8 w-8 text-indigo-600" />
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900">
                  {capturedImages.length >= maxPhotos ? 'LIMITE FOTO RAGGIUNTO' : 'FOTO ETICHETTA'}
                </h3>
                <p className="text-sm text-gray-500 mt-0.5">
                  {capturedImages.length >= maxPhotos 
                    ? 'Hai raggiunto il limite di foto' 
                    : `Scatta foto ${capturedImages.length + 1} di ${maxPhotos}`
                  }
                </p>
              </div>
              <div className="flex-shrink-0 text-gray-400">
                {capturedImages.length >= maxPhotos ? (
                  <CheckIcon className="w-6 h-6 text-green-600" />
                ) : (
                  <PlusIcon className="w-6 h-6" />
                )}
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="relative w-full rounded-2xl bg-white p-5 shadow-md border border-gray-100 overflow-hidden hover:bg-gray-50 transition-all duration-200 mb-8"
          >
            <input
              type="file"
              accept="image/*"
              multiple
              disabled={capturedImages.length >= maxPhotos}
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
              onChange={async (e) => {
                const files = Array.from(e.target.files || []);
                if (files.length === 0) return;
                
                const remainingSlots = maxPhotos - capturedImages.length;
                const filesToProcess = files.slice(0, remainingSlots);
                
                try {
                  const compressedImages = await Promise.all(
                    filesToProcess.map(file => {
                      return new Promise<string>((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = async (event) => {
                          if (event.target?.result) {
                            try {
                              const compressed = await compressImage(event.target.result as string);
                              resolve(compressed);
                            } catch (err) {
                              reject(err);
                            }
                          } else {
                            reject(new Error("Impossibile leggere il file"));
                          }
                        };
                        reader.onerror = () => reject(new Error("Errore nella lettura del file"));
                        reader.readAsDataURL(file);
                      });
                    })
                  );
                  
                  setCapturedImages([...capturedImages, ...compressedImages]);
                } catch (err) {
                  alert("Errore nel salvataggio delle immagini. Riprova.");
                  console.error("Errore nel salvataggio delle immagini:", err);
                }
                
                // Reset input
                e.target.value = '';
              }}
            />
            <div className="flex items-center space-x-5">
              <div className="flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3">
                <svg className="h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900">CARICA IMMAGINE</h3>
                <p className="text-sm text-gray-500 mt-0.5">Seleziona un'immagine dalla galleria</p>
              </div>
              <div className="flex-shrink-0 text-gray-400">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      {showCamera && (
        <Camera
          aspectRatio="square"
          onCapture={handleCapture}
          onClose={() => setShowCamera(false)}
          cameraMode="label"
        />
      )}
      
      <Footer activeItem="goods" />
    </div>
  );
}
