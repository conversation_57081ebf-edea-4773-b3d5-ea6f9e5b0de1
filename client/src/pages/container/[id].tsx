import { useState, useEffect, useRef } from "react";
import html2canvas from "html2canvas";
import { useLocation, useRoute } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { apiRequest, queryInvalidationManager } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { offlineApiRequest } from "@/lib/offlineAPI";
import { Container, ProductLabel } from "@/types";
import { QRCode, QRCodeType, generateQRCodeFilename } from "@/components/ui/qr-code";
import { generateContainerQRValue } from "@/lib/utils";
import { isExpired } from "@/lib/dateUtils"; // Importiamo la funzione isExpired
import { debouncedLog } from "@/lib/debounceUtils";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle, 
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Package, 
  BoxIcon, 
  QrCode, 
  PackageCheck, 
  Clock,
  Archive,
  RotateCcw,
  Loader2,
  Edit,
  Download as DownloadIcon,
  Printer as PrinterIcon,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { RetireProductDialog } from "@/components/retire-product-dialog";

export default function ContainerDetails() {
  const [, params] = useRoute("/container/:id");
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  // Usa l'URL per mantenere lo stato del tab
  const [searchParams, setSearchParams] = useLocation();
  const tabFromURL = new URLSearchParams(searchParams.split('?')[1] || '').get('tab');
  const [activeTab, setActiveTab] = useState(tabFromURL || "details");
  
  // Aggiorna sia lo stato locale che l'URL quando cambia il tab
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Aggiorna l'URL per mantenere il tab anche dopo il refresh
    const url = `/container/${params?.id}?tab=${value}`;
    setSearchParams(url, { replace: true });
  };
  
  // Assicura che il tab rimanga sullo stesso valore anche dopo gli aggiornamenti dei dati
  useEffect(() => {
    if (activeTab && tabFromURL && activeTab !== tabFromURL) {
      // Il tab nell'URL ha la precedenza
      setActiveTab(tabFromURL);
    }
  }, [tabFromURL]);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrLabel, setQrLabel] = useState("");
  const qrCodeRef = useRef<HTMLDivElement>(null);
  const [productToRemove, setProductToRemove] = useState<number | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  
  // Stati per il ritiro prodotti
  const [productToRetire, setProductToRetire] = useState<{ id: number; name: string } | null>(null);
  const [isRetireDialogOpen, setIsRetireDialogOpen] = useState(false);
  
  // Stati per la gestione della capacità massima del contenitore
  const [isCapacityDialogOpen, setIsCapacityDialogOpen] = useState(false);
  const [newCapacity, setNewCapacity] = useState<number>(5);
  
  // Get container ID from URL params
  const containerId = params?.id ? parseInt(params.id) : 0;
  
  // Chiave per localStorage
  const qrLabelStorageKey = `container_${containerId}_qr_label`;
  
  // Carica il nome personalizzato dal localStorage quando il componente si monta
  useEffect(() => {
    if (containerId) {
      const savedLabel = localStorage.getItem(qrLabelStorageKey);
      if (savedLabel) {
        setQrLabel(savedLabel);
      }
    }
  }, [containerId, qrLabelStorageKey]);
  
  // Salva il nome personalizzato nel localStorage quando cambia
  const handleQRLabelChange = (value: string) => {
    setQrLabel(value);
    if (containerId) {
      localStorage.setItem(qrLabelStorageKey, value);
    }
  };

  // Fetch container details
  const {
    data: container,
    isLoading: isContainerLoading,
    error: containerError,
  } = useQuery<Container>({
    queryKey: [`/api/containers/${containerId}`],
    enabled: !!containerId,
  });

  // Fetch container products
  const {
    data: containerProducts,
    isLoading: isProductsLoading,
    error: productsError,
  } = useQuery<ProductLabel[]>({
    queryKey: [`/api/containers/${containerId}/products`],
    enabled: !!containerId,
  });
  
  // Fetch all available products that are not yet in this container
  const { 
    data: availableProducts,
    isLoading: isAvailableProductsLoading 
  } = useQuery<ProductLabel[]>({
    queryKey: ['/api/product-labels'],
    enabled: !!containerId,
  });

  // Archive/Unarchive container mutation con supporto offline
  const toggleArchiveMutation = useMutation({
    mutationFn: async ({ containerId, archive }: { containerId: number; archive: boolean }) => {
      try {
        return await apiRequest(
          "PATCH",
          `/api/containers/${containerId}/archive`,
          { isArchived: archive }
        );
      } catch (error) {
        console.log("Errore connessione al database, provo con modalità offline", error);
        // Fallback alla modalità offline se la richiesta fallisce
        return await offlineApiRequest(
          `/api/containers/${containerId}/archive`,
          "PATCH",
          { isArchived: archive },
          {
            syncWhenOnline: true
          }
        );
      }
    },
    onMutate: async ({ containerId, archive }) => {
      // Cancella eventuali query in corso
      await queryClient.cancelQueries({ queryKey: [`/api/containers/${containerId}`] });
      await queryClient.cancelQueries({ queryKey: ["/api/containers"] });
      
      // Salva lo stato attuale per un eventuale rollback
      const previousContainer = queryClient.getQueryData<Container>([`/api/containers/${containerId}`]);
      const previousContainers = queryClient.getQueryData<Container[]>(["/api/containers"]);
      
      // Ottimisticamente aggiorna la UI
      if (previousContainer) {
        queryClient.setQueryData<Container>([`/api/containers/${containerId}`], {
          ...previousContainer,
          isArchived: archive
        });
      }
      
      if (previousContainers) {
        queryClient.setQueryData<Container[]>(["/api/containers"], 
          previousContainers.map(c => c.id === containerId ? {...c, isArchived: archive} : c)
        );
      }
      
      return { previousContainer, previousContainers };
    },
    onSuccess: (data, variables) => {
      // Se l'operazione è stata salvata offline
      if (data && 'offlinePending' in data) {
        toast({
          title: variables.archive ? "Contenitore verrà archiviato" : "Contenitore verrà ripristinato",
          description: "L'operazione sarà completata quando tornerai online.",
        });
      } else {
        // Forza refresh completo delle query dei contenitori
        queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
        queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
        queryClient.removeQueries({ queryKey: ["/api/containers"] });
        
        setTimeout(() => {
          queryClient.refetchQueries({ queryKey: ["/api/containers"] });
        }, 100);
        
        toast({
          title: variables.archive ? "Contenitore Archiviato" : "Contenitore Ripristinato",
          description: variables.archive
            ? "Il contenitore è stato archiviato con successo."
            : "Il contenitore è stato ripristinato e reso attivo.",
        });
      }
    },
    onError: (error, variables, context) => {
      console.error("Error toggling archive status:", error);
      
      // Ripristina lo stato precedente in caso di errore
      if (context) {
        if (context.previousContainer) {
          queryClient.setQueryData([`/api/containers/${containerId}`], context.previousContainer);
        }
        if (context.previousContainers) {
          queryClient.setQueryData(["/api/containers"], context.previousContainers);
        }
      }
      
      toast({
        title: "Azione fallita",
        description: "Impossibile aggiornare lo stato del contenitore.",
        variant: "destructive",
      });
    },
  });

  // Handle archiving/unarchiving container
  const handleToggleArchive = () => {
    if (container) {
      toggleArchiveMutation.mutate({
        containerId: container.id,
        archive: !container.isArchived
      });
    }
  };
  
  // Mutation for adding a product to the container
  const addProductMutation = useMutation({
    mutationFn: async (productId: number) => {
      return await apiRequest(
        "POST", 
        "/api/container-products", 
        {
          containerId: containerId,
          productLabelId: productId
        }
      );
    },
    onMutate: async (productId) => {
      setIsAddingProduct(true);
      
      // Cancella le query in corso per evitare race condition
      await queryClient.cancelQueries({ queryKey: [`/api/containers/${containerId}`] });
      await queryClient.cancelQueries({ queryKey: [`/api/containers/${containerId}/products`] });
      
      // Salva i dati precedenti
      const previousContainer = queryClient.getQueryData<Container>([`/api/containers/${containerId}`]);
      const previousProducts = queryClient.getQueryData<ProductLabel[]>([`/api/containers/${containerId}/products`]);
      
      // Ottimisticamente aggiorna i dati locali
      if (previousContainer) {
        queryClient.setQueryData<Container>([`/api/containers/${containerId}`], {
          ...previousContainer,
          currentItems: previousContainer.currentItems + 1
        });
      }
      
      if (previousProducts && availableProducts) {
        // Trova il prodotto da aggiungere
        const productToAdd = availableProducts.find(p => p.id === productId);
        
        if (productToAdd) {
          queryClient.setQueryData<ProductLabel[]>(
            [`/api/containers/${containerId}/products`],
            [...previousProducts, productToAdd]
          );
        }
      }
      
      return { previousContainer, previousProducts };
    },
    onSuccess: (_, productId) => {
      // Rimuovo log ridondante per migliorare performance
      
      // Mostra messaggio di successo
      const productName = availableProducts?.find(p => p.id === productId)?.productName || "Prodotto";
      toast({
        title: "Prodotto aggiunto",
        description: `${productName} è stato aggiunto con successo al contenitore.`,
      });
      
      // Resetta lo stato
      setSelectedProductId("");
      setIsAddingProduct(false);
      
      // Forza refresh completo delle query dei contenitori
      queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}/products`] });
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      queryClient.removeQueries({ queryKey: ["/api/containers"] });
      
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["/api/containers"] });
      }, 100);
    },
    onError: (error, _, context) => {
      console.error("Error adding product:", error);
      
      // Ripristina i dati precedenti in caso di errore
      if (context) {
        if (context.previousContainer) {
          queryClient.setQueryData([`/api/containers/${containerId}`], context.previousContainer);
        }
        if (context.previousProducts) {
          queryClient.setQueryData([`/api/containers/${containerId}/products`], context.previousProducts);
        }
      }
      
      toast({
        title: "Errore",
        description: "Impossibile aggiungere il prodotto. Riprova più tardi.",
        variant: "destructive",
      });
      
      // Resetta lo stato
      setIsAddingProduct(false);
    },
  });

  // Mutation for updating container capacity
  const updateCapacityMutation = useMutation({
    mutationFn: async (maxItems: number) => {
      try {
        return await apiRequest(
          "PATCH",
          `/api/containers/${containerId}/capacity`,
          { maxItems }
        );
      } catch (error) {
        console.log("Errore connessione al database, impossibile aggiornare capacità", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      // Forza refresh immediato e completo delle query dei contenitori
      queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      
      // Rimuove completamente i dati dalla cache per forzare un nuovo fetch
      queryClient.removeQueries({ queryKey: ["/api/containers"] });
      
      // Forza refetch immediato per assicurare dati aggiornati
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["/api/containers"] });
      }, 100);
      
      toast({
        title: "Capacità aggiornata",
        description: `La capacità massima del contenitore è stata impostata a ${data.maxItems} prodotti`
      });
      
      setIsCapacityDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.response?.data?.message || "Impossibile aggiornare la capacità del contenitore",
        variant: "destructive"
      });
    }
  });
  
  // Mutation for removing a product from a container
  const removeProductMutation = useMutation({
    mutationFn: async (productId: number) => {
      // Prima dobbiamo ottenere l'associazione container-prodotto
      const containerProductsResponse = await apiRequest<any[]>(
        "GET",
        `/api/container-products/container/${containerId}`
      );
      
      // Troviamo l'associazione specifica con questo prodotto
      const association = containerProductsResponse.find(
        cp => cp.productLabelId === productId
      );
      
      if (!association) {
        throw new Error("Associazione tra prodotto e container non trovata");
      }
      
      // Ora possiamo eliminare l'associazione usando il suo ID
      return await apiRequest(
        "DELETE",
        `/api/container-products/${association.id}`
      );
    },
    onSuccess: () => {
      // Forza refresh completo delle query dei contenitori
      queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/containers/${containerId}/products`] });
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      queryClient.removeQueries({ queryKey: ["/api/containers"] });
      
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["/api/containers"] });
      }, 100);
      
      toast({
        title: "Prodotto rimosso",
        description: "Il prodotto è stato rimosso con successo dal contenitore.",
      });
      
      setProductToRemove(null);
      setIsRemoveDialogOpen(false);
    },
    onError: (error) => {
      console.error("Error removing product:", error);
      
      toast({
        title: "Errore",
        description: "Impossibile rimuovere il prodotto. Riprova più tardi.",
        variant: "destructive",
      });
      
      // Reset state
      setProductToRemove(null);
      setIsRemoveDialogOpen(false);
    },
  });

  // Handle removing a product
  const handleRemoveProduct = (productId: number) => {
    setProductToRemove(productId);
    setIsRemoveDialogOpen(true);
  };
  
  // Confirm product removal
  const confirmRemoveProduct = () => {
    if (productToRemove !== null) {
      removeProductMutation.mutate(productToRemove);
    }
  };

  const getUsageStatus = () => {
    if (!container) return { color: "", text: "" };
    
    if (container.currentItems === 0) {
      return { color: "blue", text: "Vuoto" };
    }
    if (container.currentItems === container.maxItems) {
      return { color: "red", text: "Pieno" };
    }
    return { 
      color: "green", 
      text: `${container.currentItems}/${container.maxItems} Prodotti` 
    };
  };
  
  // Handle adding a product from the selector
  const handleAddProduct = () => {
    if (!selectedProductId) return;
    
    const productId = parseInt(selectedProductId, 10);
    if (isNaN(productId)) return;
    
    // Check if container is full
    if (container && container.currentItems >= container.maxItems) {
      toast({
        title: "Container pieno",
        description: "Il container ha raggiunto la capacità massima",
        variant: "destructive"
      });
      return;
    }
    
    // Check if product is already in the container
    const alreadyInContainer = containerProducts?.some(p => p.id === productId);
    if (alreadyInContainer) {
      toast({
        title: "Prodotto già presente",
        description: "Questo prodotto è già stato aggiunto a questo container",
        variant: "default"
      });
      return;
    }
    
    // Add the product
    addProductMutation.mutate(productId);
  };
  
  // Get filtered products (exclude those already in container and expired products)
  const getFilteredProducts = () => {
    if (!availableProducts || !containerProducts) return [];
    
    // Create a set of product IDs already in the contenitore for faster lookup
    const containerProductIds = new Set(containerProducts.map(p => p.id));
    
    // Filter out products already in the contenitore and expired products
    const filtered = availableProducts.filter(p => {
      // Verifica se il prodotto è già nel contenitore
      if (containerProductIds.has(p.id)) return false;
      
      // Verifica se il prodotto è scaduto
      const isProductExpired = isExpired(p.expiryDate);
      if (isProductExpired) {
        // Se è scaduto, non lo mostriamo nella lista dei prodotti selezionabili
        return false;
      }
      
      // Se il prodotto non è né nel contenitore né scaduto, lo mostriamo
      return true;
    });
    
    // Ordina per data di creazione (più recenti prima)
    return filtered.sort((a, b) => {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateB.getTime() - dateA.getTime();
    });
  };
  
  // Debounced logging per evitare log multipli ridondanti
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (containerProducts && process.env.NODE_ENV === 'development') {
        // Log solo in development e solo se realmente necessario
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [containerProducts?.length]);

  const status = getUsageStatus();
  const filteredProducts = getFilteredProducts();

  // Handle QR code download utilizzando la nuova utility generateAndDownloadQRCode
  const handleDownloadQRCode = async () => {
    try {
      // Mostra un messaggio di lavorazione in corso
      toast({
        title: "Generazione in corso...",
        description: "Sto creando l'immagine del QR code",
      });
      
      // Importa la funzione generateAndDownloadQRCode dalla nuova utility
      const { generateAndDownloadQRCode } = await import('@/lib/qr-utils');
      
      // Usa il nome del contenitore per il file
      const nameToUse = qrLabel || container.name || 'container';
      
      // Crea un nome file leggibile e sicuro (senza estensione)
      const fileName = `${nameToUse.replace(/\s+/g, '_')}_${format(new Date(), 'ddMMyy')}`;
      
      // Usa il valore QR generato dalla funzione standardizzata
      const qrValue = generateContainerQRValue(container);
      
      // Utilizza la nuova funzione ottimizzata per generare e scaricare il QR code
      // Questa funzione garantisce il formato corretto con "CONTENITORE" e nome ben visibili
      const success = await generateAndDownloadQRCode(
        nameToUse, // nome da mostrare sotto il QR code
        fileName,  // nome del file
        qrValue    // valore QR (determina che verrà aggiunto "CONTENITORE")
      );
      
      if (success) {
        toast({
          title: "QR Code Pronto",
          description: "L'immagine è stata generata e scaricata correttamente",
        });
      } else {
        toast({
          title: "Errore",
          description: "Non è stato possibile generare il QR code",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Errore durante il download del QR code:", error);
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante il salvataggio",
        variant: "destructive"
      });
    }
  };
  
  // Handle QR code printing utilizzando la funzione utils (versione migliorata)
  const handlePrintQRCode = async () => {
    // Passiamo un valore diretto per il QR code con il formato standardizzato
    const qrValue = generateContainerQRValue(container);
    const nameToUse = qrLabel || container.name;
    
    try {
      // Mostra un messaggio di lavorazione in corso
      toast({
        title: "Preparazione stampa...",
        description: "Sto preparando il QR code per la stampa",
      });
      
      // Importiamo la funzione direttamente per evitare problemi di circolarità
      const { printQRCode } = await import('@/lib/utils');
      
      console.log("Avvio stampa QR code con:", {
        id: 'container-qrcode-svg',
        title: nameToUse,
        subtitle: container.type,
        qrValue
      });
      
      const result = await printQRCode(
        undefined, // Non usiamo l'ID dell'elemento SVG ma passiamo direttamente il valore
        nameToUse,
        container.type,
        qrValue
      );
      
      if (!result) {
        toast({
          title: "Errore di Stampa",
          description: "Impossibile stampare il QR code. Verifica che i popup siano abilitati.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Stampa inviata",
          description: "Il QR code è stato inviato alla stampa",
        });
      }
    } catch (error) {
      console.error("Errore durante la stampa del QR code:", error);
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante la stampa",
        variant: "destructive"
      });
    }
  };
  

  if (isContainerLoading) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Dettagli Container" showBack backPath="/containers" />
        <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-10 w-10 animate-spin text-white" />
          </div>
        </main>
        <Footer activeItem="containers" />
      </div>
    );
  }

  if (containerError || !container) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Errore" showBack backPath="/containers" />
        <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
          <Card className="mt-4">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-bold text-red-600 mb-2">Container non trovato</h2>
              <p className="text-gray-500 mb-4">Non è stato possibile trovare il container richiesto</p>
              <Button 
                className="h-12 rounded-xl text-white bg-black border-2 border-indigo-500/70 shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(99,102,241,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu"
                onClick={() => setLocation("/containers")}
              >
                Torna ai Container
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer activeItem="containers" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#f5f5f7]">
      <Header 
        title={container.name} 
        showBack 
        backPath="/containers" 
      />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="py-6 max-w-md mx-auto">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4 h-14 rounded-xl bg-white shadow-md p-1">
              <TabsTrigger 
                value="details" 
                className="rounded-lg h-full data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-[0_2px_8px_rgba(79,70,229,0.35)]"
              >
                <BoxIcon className="mr-2 h-5 w-5" />
                Dettagli
              </TabsTrigger>
              <TabsTrigger 
                value="products" 
                className="rounded-lg h-full data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-[0_2px_8px_rgba(79,70,229,0.35)]"
              >
                <Package className="mr-2 h-5 w-5" />
                Prodotti
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-4">
              <Card className="bg-white shadow-md border border-gray-100">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BoxIcon className="mr-2 h-5 w-5" />
                    {container.name}
                    {container.isArchived && (
                      <Badge variant="outline" className="ml-2 bg-gray-100 text-gray-500">
                        Archiviato
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription>
                    {container.type} container
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-2 text-sm">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 rounded-lg bg-blue-50 border border-blue-100">
                      <div className="flex items-center">
                        <PackageCheck className="h-5 w-5 text-blue-500 mr-2" />
                        <span className="text-blue-700">Capacità</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant="outline" 
                          className={`
                            ${status.color === "red" ? "bg-red-100 text-red-800" : ""}
                            ${status.color === "blue" ? "bg-blue-100 text-blue-800" : ""}
                            ${status.color === "green" ? "bg-green-100 text-green-800" : ""}
                          `}
                        >
                          {status.text}
                        </Badge>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-7 px-2 rounded-full hover:bg-blue-100"
                          onClick={() => {
                            setNewCapacity(container.maxItems);
                            setIsCapacityDialogOpen(true);
                          }}
                        >
                          <Edit className="h-3.5 w-3.5 text-blue-600" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex justify-between items-center p-3 rounded-lg bg-purple-50 border border-purple-100">
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 text-purple-500 mr-2" />
                        <span className="text-purple-700">Creato il</span>
                      </div>
                      <span className="text-purple-800 font-medium">
                        {container.createdAt && !isNaN(new Date(container.createdAt).getTime())
                          ? new Date(container.createdAt).toLocaleDateString()
                          : 'Data non disponibile'}
                      </span>
                    </div>

                    <div 
                      className="my-5 w-full rounded-2xl bg-white p-5 shadow-md border border-gray-100 overflow-hidden hover:bg-gray-50 transition-all duration-200 cursor-pointer"
                      onClick={() => setShowQRCode(true)}
                    >
                      <div className="flex items-center space-x-5">
                        <div className="flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3">
                          <QrCode className="h-8 w-8 text-indigo-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-base font-medium text-gray-900 whitespace-nowrap">VISUALIZZA QR CODE</h3>
                          <p className="text-sm text-gray-500 mt-0.5">Genera e stampa il QR Code del contenitore</p>
                        </div>
                        <div className="flex-shrink-0 text-gray-400">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <div 
                    className="w-full rounded-2xl bg-white p-5 shadow-md border border-gray-100 overflow-hidden hover:bg-gray-50 transition-all duration-200 cursor-pointer"
                    onClick={handleToggleArchive}
                  >
                    <div className="flex items-center space-x-5">
                      <div className="flex-shrink-0 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-3">
                        {container.isArchived ? (
                          <RotateCcw className="h-8 w-8 text-green-600" />
                        ) : (
                          <Archive className="h-8 w-8 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-base font-medium text-gray-900 whitespace-nowrap">
                          {container.isArchived ? "RIPRISTINA CONTAINER" : "ARCHIVIA CONTAINER"}
                        </h3>
                        <p className="text-sm text-gray-500 mt-0.5">
                          {container.isArchived ? "Rendi nuovamente attivo il container" : "Rimuovi il container dalla lista attivi"}
                        </p>
                      </div>
                      <div className="flex-shrink-0 text-gray-400">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="products" className="mt-4">
              <Card className="bg-white shadow-md border border-gray-100">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Package className="mr-2 h-5 w-5" />
                    Prodotti nel Container
                  </CardTitle>
                  <CardDescription>
                    {isProductsLoading ? "Caricamento prodotti..." : 
                      containerProducts && containerProducts.length > 0 
                        ? `${containerProducts.length} prodotti presenti` 
                        : "Nessun prodotto presente"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Selettore a tendina per aggiungere prodotti */}
                  {container && container.currentItems < container.maxItems && (
                    <div className="mb-6 p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                      <div className="flex flex-col space-y-2">
                        <label className="text-sm font-medium text-indigo-700 flex items-center">
                          <Plus className="w-4 h-4 mr-1" />
                          Aggiungi un prodotto a questo container
                        </label>
                        <div className="text-xs text-indigo-600 flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1 inline" />
                          <span>I prodotti scaduti non possono essere aggiunti per motivi di sicurezza</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="relative flex-1">
                            <select
                              className="w-full rounded-l-md border border-indigo-300 bg-white px-4 py-2 text-base shadow-sm focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 appearance-none pr-10"
                              value={selectedProductId}
                              onChange={(e) => setSelectedProductId(e.target.value)}
                              disabled={isAddingProduct || isAvailableProductsLoading}
                            >
                              <option value="">Seleziona un prodotto</option>
                              {filteredProducts && filteredProducts.map((product) => (
                                <option key={product.id} value={product.id.toString()}>
                                  {product.productName} - Scade: {product.expiryDate || 'N/D'}
                                </option>
                              ))}
                              {filteredProducts && filteredProducts.length === 0 && !isAvailableProductsLoading && (
                                <option value="" disabled>Nessun prodotto disponibile</option>
                              )}
                            </select>
                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                              </svg>
                            </div>
                          </div>
                          <Button 
                            className="rounded-r-md bg-indigo-600 hover:bg-indigo-800 text-white px-4 py-2 h-10 flex items-center"
                            onClick={handleAddProduct}
                            disabled={!selectedProductId || isAddingProduct || isAvailableProductsLoading}
                          >
                            {isAddingProduct ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <CheckCircle className="h-4 w-4 mr-1" />
                            )}
                            Aggiungi
                          </Button>
                        </div>
                        {filteredProducts && filteredProducts.length === 0 && !isAvailableProductsLoading && (
                          <p className="text-xs text-indigo-600 mt-1">Tutti i prodotti disponibili sono già in questo contenitore o non ci sono prodotti disponibili.</p>
                        )}
                        {isAvailableProductsLoading && (
                          <p className="text-xs text-indigo-600 mt-1 flex items-center">
                            <Loader2 className="animate-spin h-3 w-3 mr-1" />
                            Caricamento prodotti disponibili...
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {isProductsLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                    </div>
                  ) : containerProducts && containerProducts.length > 0 ? (
                    <div className="space-y-3">
                      {containerProducts.map((product) => {
                        // Verifica se il prodotto è scaduto
                        const productExpired = isExpired(product.expiryDate);
                        
                        return (
                          <div 
                            key={product.id} 
                            className={`w-full rounded-lg bg-white p-3 shadow-sm hover:shadow-md transition-all duration-200 ${
                              productExpired ? 'border-red-500 border-2' : 'border border-gray-200'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div 
                                className="flex-1 cursor-pointer" 
                                onClick={() => setLocation(`/product-details/${product.id}`)}
                              >
                                <h3 className="font-medium text-gray-900 mb-1">{product.productName}</h3>
                                <div className="flex justify-between text-sm">
                                  <div className="flex items-center gap-1">
                                    <span className={productExpired ? 'text-red-600 font-semibold' : 'text-gray-500'}>
                                      Scade: {product.expiryDate || "N/D"}
                                    </span>
                                    {productExpired && (
                                      <Badge variant="destructive" className="ml-1 text-xs py-0">
                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                        Scaduto
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="flex space-x-2 items-center">
                                <Button 
                                  variant="ghost" 
                                  size="icon"
                                  className="text-gray-500 hover:text-orange-500 hover:bg-orange-50"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setProductToRetire({ id: product.id, name: product.productName });
                                    setIsRetireDialogOpen(true);
                                  }}
                                  title="Ritira prodotto"
                                >
                                  <Archive className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon"
                                  className="text-gray-500 hover:text-red-500 hover:bg-red-50"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRemoveProduct(product.id);
                                  }}
                                  title="Rimuovi prodotto"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                                <div className="flex-shrink-0 text-gray-400">
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-gray-500">
                      <Package className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                      <p>Non ci sono prodotti in questo container</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <Footer activeItem="containers" />
      
      {/* Confirmation Dialog for Product Removal */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              Conferma rimozione
            </AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler rimuovere questo prodotto dal container?
              <br />
              Questa azione non è reversibile.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-500 hover:bg-red-600"
              onClick={confirmRemoveProduct}
            >
              Rimuovi
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Button
        className="fixed bottom-36 right-4 w-14 h-14 rounded-full flex items-center justify-center shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(59,130,246,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu bg-blue-500 border-2 border-blue-400"
        onClick={() => setLocation("/new-container")}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-white drop-shadow-md"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          style={{ filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.3))', transform: 'translateZ(5px) scale(1.6)' }}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2.5}
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
      </Button>

      {showQRCode && (
        <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-1">
              QR Code {container.name}
            </h3>
            <p className="text-sm text-gray-500 mb-1">Scansiona questo codice per accedere rapidamente alle informazioni</p>
            <p className="text-xs text-purple-600 italic mb-4">Questo QR code è associato permanentemente a questo contenitore e non può essere modificato.</p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Nome da applicare (opzionale):</label>
              <input 
                type="text" 
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500/70"
                placeholder="Inserisci un nome per il QR code"
                value={qrLabel}
                onChange={(e) => handleQRLabelChange(e.target.value)}
              />
            </div>
            
            <div ref={qrCodeRef} className="bg-white p-4 rounded-lg flex flex-col items-center justify-center" style={{ width: '210px', margin: '0 auto' }}>
              {/* Utilizziamo sempre la funzione generateContainerQRValue per garantire consistenza */}
              <QRCode 
                value={generateContainerQRValue(container)}
                size={189} /* 5cm at 96dpi is approximately 189px */
                ecLevel="H"
                id="container-qrcode-svg"
                qrCodeType={QRCodeType.CONTAINER}
                itemName={qrLabel || container.name}
              />
              <div className="w-full text-center mt-2 overflow-hidden" style={{ minHeight: '24px', paddingTop: '3px' }}>
                <p className="font-mono uppercase font-bold truncate" style={{ fontSize: '24px', letterSpacing: '-0.05em' }}>{qrLabel || container.name}</p>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-2">
              <Button 
                variant="outline" 
                onClick={() => setShowQRCode(false)}
              >
                Chiudi
              </Button>
              <Button 
                variant="outline"
                className="flex items-center bg-blue-50 hover:bg-blue-100 border-blue-200"
                onClick={() => handleDownloadQRCode()}
              >
                <DownloadIcon className="w-4 h-4 mr-2 text-blue-600" />
                Scarica PNG
              </Button>
              <Button 
                className="h-10 flex items-center rounded-xl text-white bg-black border-2 border-blue-500/70 shadow-[0_4px_10px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_6px_15px_-3px_rgba(59,130,246,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_3px_10px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu"
                onClick={() => handlePrintQRCode()}
              >
                <PrinterIcon className="w-4 h-4 mr-2" />
                Stampa
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Dialog per modificare la capacità massima */}
      <Dialog open={isCapacityDialogOpen} onOpenChange={setIsCapacityDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Modifica capacità massima</DialogTitle>
            <DialogDescription>
              Specifica il numero massimo di prodotti che questo contenitore può ospitare.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="capacity">Capacità massima prodotti</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                max="100"
                value={newCapacity}
                onChange={(e) => setNewCapacity(parseInt(e.target.value) || 1)}
                className="h-11"
              />
              {container && container.currentItems > 0 && (
                <p className="text-sm text-gray-500">
                  Attualmente ci sono {container.currentItems} prodotti in questo contenitore.
                  La nuova capacità deve essere almeno pari a questo valore.
                </p>
              )}
            </div>
          </div>
          
          <DialogFooter className="flex justify-between sm:justify-between">
            <Button
              variant="outline"
              onClick={() => setIsCapacityDialogOpen(false)}
            >
              Annulla
            </Button>
            <Button
              onClick={() => updateCapacityMutation.mutate(newCapacity)}
              disabled={updateCapacityMutation.isPending || (container && newCapacity < container.currentItems)}
              className="bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              {updateCapacityMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvataggio...
                </>
              ) : "Salva capacità"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog per il ritiro prodotti */}
      {productToRetire && (
        <RetireProductDialog
          open={isRetireDialogOpen}
          onOpenChange={(open) => {
            setIsRetireDialogOpen(open);
            if (!open) {
              setProductToRetire(null);
            }
          }}
          productId={productToRetire.id}
          productName={productToRetire.name}
        />
      )}
    </div>
  );
}
