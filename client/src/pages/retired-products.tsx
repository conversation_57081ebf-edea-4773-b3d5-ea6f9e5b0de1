import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertTriangle, Search, Calendar, User, FileText, RefreshCw } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";

interface RetiredProduct {
  id: number;
  productName: string;
  expiryDate: string;
  batchNumber: string;
  storageInstructions: string;
  notes?: string;
  isRetired: boolean;
  retiredAt: string;
  retiredBy: number;
  retiredReason: string;
  createdAt: string;
}

export default function RetiredProductsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [startY, setStartY] = useState(0);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: retiredProducts, isLoading, error, refetch } = useQuery<RetiredProduct[]>({
    queryKey: ["/api/retired-products"],
  });

  // Pull-to-refresh functionality
  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (window.scrollY === 0 && startY > 0) {
      const currentY = e.touches[0].clientY;
      const distance = Math.max(0, currentY - startY);
      setPullDistance(Math.min(distance, 100));
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true);
      try {
        // Invalida e ricarica i dati
        await queryClient.invalidateQueries({ queryKey: ["/api/retired-products"] });
        await refetch();
        toast({
          title: "Aggiornamento completato",
          description: "I prodotti ritirati sono stati aggiornati",
        });
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Errore",
          description: "Impossibile aggiornare i dati",
        });
      } finally {
        setIsRefreshing(false);
      }
    }
    setPullDistance(0);
    setStartY(0);
  };

  const handleRefreshClick = async () => {
    setIsRefreshing(true);
    try {
      await queryClient.invalidateQueries({ queryKey: ["/api/retired-products"] });
      await refetch();
      toast({
        title: "Aggiornamento completato",
        description: "I prodotti ritirati sono stati aggiornati",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Errore",
        description: "Impossibile aggiornare i dati",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const filteredProducts = retiredProducts?.filter(product =>
    product.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.batchNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.retiredReason.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMMM yyyy 'alle' HH:mm", { locale: it });
    } catch {
      return "Data non valida";
    }
  };

  if (error) {
    return (
      <GradientBackground>
        <div className="min-h-screen flex flex-col">
          <Header 
            title="Prodotti Ritirati" 
            showBack 
            showNotification={false}
            showUserMenu={false}
          />
          <main className="flex-1 pt-24 pb-32 px-4">
            <Card className="border-red-200">
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 text-red-600">
                  <AlertTriangle className="h-5 w-5" />
                  <span>Errore nel caricamento dei prodotti ritirati</span>
                </div>
              </CardContent>
            </Card>
          </main>
          <Footer />
        </div>
      </GradientBackground>
    );
  }

  return (
    <GradientBackground>
      <div className="min-h-screen flex flex-col">
        <Header 
          title="Prodotti Ritirati" 
          showBack 
          showNotification={false}
          showUserMenu={false}
        />
        
        <main 
          className="flex-1 pt-24 pb-32 px-4 space-y-6"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            transform: `translateY(${pullDistance * 0.5}px)`,
            transition: pullDistance === 0 ? 'transform 0.3s ease-out' : 'none'
          }}
        >
      {/* Indicatore pull-to-refresh */}
      {(pullDistance > 0 || isRefreshing) && (
        <div 
          className="fixed top-0 left-0 right-0 flex items-center justify-center bg-primary/10 backdrop-blur-sm z-50 transition-all duration-300"
          style={{
            height: `${Math.min(pullDistance, 80)}px`,
            opacity: pullDistance > 20 ? 1 : pullDistance / 20
          }}
        >
          <div className="flex items-center gap-2 text-primary">
            <RefreshCw 
              className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`}
              style={{
                transform: `rotate(${pullDistance * 2}deg)`
              }}
            />
            <span className="text-sm font-medium">
              {isRefreshing ? 'Aggiornamento...' : pullDistance > 60 ? 'Rilascia per aggiornare' : 'Trascina per aggiornare'}
            </span>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Prodotti Ritirati</h1>
            <p className="text-muted-foreground">
              Visualizza e gestisci l'archivio dei prodotti ritirati dal sistema
            </p>
          </div>
          <Button 
            onClick={handleRefreshClick}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Aggiornamento...' : 'Aggiorna'}
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca per nome prodotto, lotto o motivo..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <AlertTriangle className="h-4 w-4" />
            <span>Archivio mantenuto per 2 anni</span>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? "Nessun risultato trovato" : "Nessun prodotto ritirato"}
              </h3>
              <p className="text-gray-500">
                {searchTerm 
                  ? "Prova a modificare i termini di ricerca"
                  : "Non ci sono prodotti ritirati al momento"
                }
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {filteredProducts.length} prodotto{filteredProducts.length !== 1 ? "i" : ""} 
              {searchTerm && " trovato"}{filteredProducts.length > 1 && searchTerm && "i"}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg leading-tight">
                        {product.productName}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Lotto: {product.batchNumber}
                      </CardDescription>
                    </div>
                    <Badge variant="destructive" className="ml-2">
                      Ritirato
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Scadenza:</span>
                      <span>{product.expiryDate}</span>
                    </div>
                    
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="text-muted-foreground">Motivo:</span>
                        <p className="mt-1 text-sm">{product.retiredReason}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <User className="h-3 w-3" />
                      <span>Ritirato il {formatDate(product.retiredAt)}</span>
                    </div>
                    
                    {product.notes && (
                      <div className="flex items-start gap-2 pt-2 border-t">
                        <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="text-muted-foreground text-xs">Note:</span>
                          <p className="text-xs mt-1">{product.notes}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
        </main>
        
        <Footer />
      </div>
    </GradientBackground>
  );
}