/**
 * Performance Monitor Dashboard
 * Dashboard per visualizzare metriche performance in tempo reale
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { RefreshCw, TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';
import { clientPerformanceMonitor } from '@/lib/performance-monitor-client';
import { useQuery } from '@tanstack/react-query';

interface PerformanceStats {
  totalSessions: number;
  timeRange: string;
  fcp: {
    average: number;
    p95: number;
    target: number;
    status: 'good' | 'needs-improvement' | 'poor';
  };
  lcp: {
    average: number;
    target: number;
    status: 'good' | 'needs-improvement' | 'poor';
  };
  pageLoad: {
    average: number;
    target: number;
  };
  performance: {
    good: number;
    needsImprovement: number;
    poor: number;
    goodPercentage: number;
  };
  browsers: Record<string, number>;
  connections: Record<string, number>;
}

interface CacheStats {
  totalEntries: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  averageSize: number;
}

interface DashboardData {
  stats: PerformanceStats | null;
  cacheStats: CacheStats;
  optimizerStats: {
    averageFCP: number;
    averageCacheHit: number;
    improvementSuggestions: string[];
  };
  recommendations: string[];
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'good':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'needs-improvement':
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    case 'poor':
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    default:
      return <RefreshCw className="h-4 w-4 text-gray-600" />;
  }
}

function getStatusColor(status: string) {
  switch (status) {
    case 'good':
      return 'bg-green-100 text-green-800';
    case 'needs-improvement':
      return 'bg-yellow-100 text-yellow-800';
    case 'poor':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

export default function PerformanceMonitor() {
  const [clientStats, setClientStats] = useState(clientPerformanceMonitor.getPerformanceStats());

  const { data: dashboardData, isLoading, refetch } = useQuery<DashboardData>({
    queryKey: ['/api/performance/dashboard'],
    refetchInterval: 30000, // Refresh ogni 30 secondi
  });

  useEffect(() => {
    // Aggiorna statistiche client ogni 5 secondi
    const interval = setInterval(() => {
      setClientStats(clientPerformanceMonitor.getPerformanceStats());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (isLoading && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const stats = dashboardData?.stats;
  const cacheStats = dashboardData?.cacheStats;
  const recommendations = dashboardData?.recommendations || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Monitor</h1>
          <p className="text-muted-foreground">
            Real-time performance metrics and optimization insights
          </p>
        </div>
        <Button onClick={() => refetch()} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Client Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client FCP</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientStats.fcp}ms</div>
            <p className="text-xs text-muted-foreground">First Contentful Paint</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client LCP</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientStats.lcp}ms</div>
            <p className="text-xs text-muted-foreground">Largest Contentful Paint</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client CLS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientStats.cls}</div>
            <p className="text-xs text-muted-foreground">Cumulative Layout Shift</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client FID</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientStats.fid}ms</div>
            <p className="text-xs text-muted-foreground">First Input Delay</p>
          </CardContent>
        </Card>
      </div>

      {/* Server Performance Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(stats.fcp.status)}
                First Contentful Paint
              </CardTitle>
              <CardDescription>
                Average across {stats.totalSessions} sessions ({stats.timeRange})
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Average</span>
                  <span className="font-mono">{stats.fcp.average}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>95th Percentile</span>
                  <span className="font-mono">{stats.fcp.p95}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Target</span>
                  <span className="font-mono">{'<'}{stats.fcp.target}ms</span>
                </div>
                <Badge className={getStatusColor(stats.fcp.status)}>
                  {stats.fcp.status.replace('-', ' ')}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(stats.lcp.status)}
                Largest Contentful Paint
              </CardTitle>
              <CardDescription>
                Average page load performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Average</span>
                  <span className="font-mono">{stats.lcp.average}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Target</span>
                  <span className="font-mono">{'<'}{stats.lcp.target}ms</span>
                </div>
                <Badge className={getStatusColor(stats.lcp.status)}>
                  {stats.lcp.status.replace('-', ' ')}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Session Performance</CardTitle>
              <CardDescription>
                Distribution of session quality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-green-600">Good</span>
                  <span>{stats.performance.good} ({stats.performance.goodPercentage}%)</span>
                </div>
                <Progress value={stats.performance.goodPercentage} className="h-2" />
                
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="text-center">
                    <div className="text-green-600 font-bold">{stats.performance.good}</div>
                    <div className="text-xs">Good</div>
                  </div>
                  <div className="text-center">
                    <div className="text-yellow-600 font-bold">{stats.performance.needsImprovement}</div>
                    <div className="text-xs">Needs Work</div>
                  </div>
                  <div className="text-center">
                    <div className="text-red-600 font-bold">{stats.performance.poor}</div>
                    <div className="text-xs">Poor</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Cache Performance */}
      {cacheStats && (
        <Card>
          <CardHeader>
            <CardTitle>Cache Performance</CardTitle>
            <CardDescription>
              Session caching and data management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-2xl font-bold">{cacheStats.hitRate}%</div>
                <p className="text-xs text-muted-foreground">Hit Rate</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheStats.totalEntries}</div>
                <p className="text-xs text-muted-foreground">Cached Entries</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheStats.totalHits}</div>
                <p className="text-xs text-muted-foreground">Cache Hits</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheStats.totalMisses}</div>
                <p className="text-xs text-muted-foreground">Cache Misses</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Slow Resources */}
      {clientStats.slowResources.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Slow Resources</CardTitle>
            <CardDescription>
              Resources taking more than 1 second to load
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {clientStats.slowResources.map((resource, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                  <div className="flex-1 truncate">
                    <span className="text-sm font-mono">
                      {resource.name.replace(/^.*\//, '').substring(0, 50)}
                    </span>
                    <Badge variant="outline" className="ml-2 text-xs">
                      {resource.type}
                    </Badge>
                  </div>
                  <div className="text-sm font-mono text-red-600">
                    {Math.round(resource.duration)}ms
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Performance Recommendations
            </CardTitle>
            <CardDescription>
              Suggested optimizations based on current metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {recommendations.map((rec, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{rec}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Client Recommendations */}
      {clientStats.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Client-Side Recommendations</CardTitle>
            <CardDescription>
              Real-time optimization suggestions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {clientStats.recommendations.map((rec, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{rec}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Browser/Connection Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Browser Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.browsers).map(([browser, count]) => (
                  <div key={browser} className="flex justify-between">
                    <span>{browser}</span>
                    <span className="font-mono">{count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Connection Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.connections).map(([conn, count]) => (
                  <div key={conn} className="flex justify-between">
                    <span className="capitalize">{conn}</span>
                    <span className="font-mono">{count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}