import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Download, RefreshCw, Trash2 } from "lucide-react";
import { logger } from '@/lib/logger';
import { pwaState, updateServiceWorker } from '@/lib/pwaManager';
import { useToast } from '@/hooks/use-toast';

/**
 * Pagina di diagnostica per la risoluzione dei problemi in produzione
 */
const DiagnosticsPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("logs");
  const [logs, setLogs] = useState<any[]>([]);
  const [swInfo, setSwInfo] = useState<any>({ version: "N/A", status: "N/A", caches: [] });
  const [refreshing, setRefreshing] = useState(false);

  // Carica i log all'avvio
  useEffect(() => {
    refreshLogs();
    getServiceWorkerInfo();
  }, []);

  const refreshLogs = () => {
    setLogs(logger.getLogs());
  };

  const clearLogs = () => {
    logger.clearLogs();
    refreshLogs();
    toast({
      title: "Log cancellati",
      description: "Tutti i log sono stati cancellati"
    });
  };

  const downloadLogs = () => {
    try {
      const logData = logger.exportLogs();
      const blob = new Blob([logData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `app-logs-${new Date().toISOString()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        title: "Errore",
        description: "Impossibile scaricare i log",
        variant: "destructive"
      });
    }
  };

  const getServiceWorkerInfo = async () => {
    setRefreshing(true);
    try {
      // Informazioni sul service worker
      const swInfo: any = {
        version: "N/A",
        status: "Non registrato",
        caches: []
      };

      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        if (registrations.length > 0) {
          const reg = registrations[0];
          swInfo.status = reg.active ? "Attivo" : (reg.installing ? "In installazione" : (reg.waiting ? "In attesa" : "Sconosciuto"));
          
          // Verifica se il service worker è in attesa di aggiornamento
          if (reg.waiting) {
            swInfo.hasUpdate = true;
          }
        }
      }

      // Informazioni sulle cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        swInfo.caches = cacheNames;
        
        // Info aggiuntive sulla cache
        const cacheInfo = await Promise.all(cacheNames.map(async (name) => {
          const cache = await caches.open(name);
          const keys = await cache.keys();
          return { name, count: keys.length };
        }));
        
        swInfo.cacheDetails = cacheInfo;
      }
      
      setSwInfo(swInfo);
    } catch (error) {
      console.error("Errore nel recupero delle informazioni sul service worker:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleForceUpdate = async () => {
    try {
      await updateServiceWorker();
      toast({
        title: "Aggiornamento in corso",
        description: "L'app verrà ricaricata con la nuova versione."
      });
    } catch (error) {
      toast({
        title: "Errore di aggiornamento",
        description: "Non è stato possibile aggiornare l'app. Riprova più tardi.",
        variant: "destructive"
      });
    }
  };

  const clearCache = async () => {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        toast({
          title: "Cache cancellata",
          description: "Tutte le cache sono state eliminate. Ricarica la pagina per applicare le modifiche."
        });
        getServiceWorkerInfo();
      }
    } catch (error) {
      toast({
        title: "Errore",
        description: "Impossibile cancellare la cache",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container px-4 py-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Diagnostica App</h1>
      <p className="text-gray-600 mb-6">Questa pagina fornisce strumenti per la diagnostica e risoluzione dei problemi</p>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="logs">Log Applicazione</TabsTrigger>
          <TabsTrigger value="pwa">Stato PWA</TabsTrigger>
        </TabsList>
        
        {/* Tab dei Log */}
        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Log dell'applicazione</CardTitle>
              <CardDescription>
                Visualizza i log generati dall'applicazione
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 mb-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={refreshLogs}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="h-4 w-4" /> Aggiorna
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={clearLogs}
                  className="flex items-center gap-1"
                >
                  <Trash2 className="h-4 w-4" /> Cancella
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={downloadLogs}
                  className="flex items-center gap-1 ml-auto"
                >
                  <Download className="h-4 w-4" /> Esporta
                </Button>
              </div>
              
              <ScrollArea className="h-96 border rounded-md p-4 bg-gray-50">
                {logs.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    Nessun log disponibile
                  </div>
                ) : (
                  <div className="space-y-2">
                    {logs.map((log, index) => (
                      <div key={index} className={`p-2 rounded-md text-sm ${
                        log.level === 'error' ? 'bg-red-100' : 
                        log.level === 'warn' ? 'bg-yellow-100' : 
                        log.level === 'info' ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <div className="flex justify-between">
                          <span className="font-semibold uppercase">{log.level}</span>
                          <span className="text-xs text-gray-500">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="font-mono">{log.message}</div>
                        {log.data && (
                          <pre className="text-xs mt-1 overflow-x-auto">
                            {typeof log.data === 'object' 
                              ? JSON.stringify(log.data, null, 2)
                              : String(log.data)
                            }
                          </pre>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Tab dello stato PWA */}
        <TabsContent value="pwa" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Stato della Progressive Web App</CardTitle>
              <CardDescription>
                Gestisci le impostazioni e lo stato della PWA
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Service Worker</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={getServiceWorkerInfo}
                  disabled={refreshing}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} /> 
                  Aggiorna info
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-sm text-gray-500">Stato</div>
                  <div className="font-medium">{swInfo.status}</div>
                </div>
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-sm text-gray-500">Versione</div>
                  <div className="font-medium">{swInfo.version}</div>
                </div>
              </div>
              
              {swInfo.hasUpdate && (
                <div className="bg-yellow-100 p-4 rounded-md mb-4">
                  <p className="text-sm mb-2">È disponibile un aggiornamento del service worker.</p>
                  <Button onClick={handleForceUpdate} size="sm">
                    Aggiorna ora
                  </Button>
                </div>
              )}
              
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Cache</h3>
                {swInfo.caches.length === 0 ? (
                  <div className="text-gray-500 text-sm">Nessuna cache trovata</div>
                ) : (
                  <div className="space-y-2">
                    {swInfo.cacheDetails?.map((cache: any, index: number) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-md">
                        <div className="flex justify-between items-center">
                          <div className="font-mono text-sm">{cache.name}</div>
                          <div className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                            {cache.count} elementi
                          </div>
                        </div>
                      </div>
                    ))}
                    <Button 
                      variant="destructive" 
                      size="sm" 
                      onClick={clearCache}
                      className="mt-2"
                    >
                      Cancella tutte le cache
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Stato Connessione</h3>
                <div className={`p-3 rounded-md ${navigator.onLine ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="font-medium">
                    {navigator.onLine ? 'Online' : 'Offline'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DiagnosticsPage;