import { useState, useEffect, useRef } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DDT, Container, ProductLabel, Supplier } from "@/types";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { SearchIcon, Truck, Package, Factory, Calendar, RefreshCw, AlertTriangle } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { offlineApiRequest } from "@/lib/offlineAPI";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { isExpired } from "@/lib/dateUtils";

export default function Search() {
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [filteredResults, setFilteredResults] = useState<{
    ddts: DDT[];
    products: ProductLabel[];
    containers: Container[];
    suppliers: Supplier[];
  }>({
    ddts: [],
    products: [],
    containers: [],
    suppliers: [],
  });
  
  // Stato e riferimenti per pull-to-refresh
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const startYRef = useRef(0);
  const contentRef = useRef<HTMLDivElement>(null);
  const pullThreshold = 80; // Distanza di trascinamento per attivare il refresh
  
  // Gestori degli eventi touch per pull-to-refresh
  const handleTouchStart = (e: React.TouchEvent) => {
    // Salva solo se siamo in cima alla pagina
    if (window.scrollY <= 0) {
      startYRef.current = e.touches[0].clientY;
      setIsPulling(true);
    }
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isPulling) return;
    
    const currentY = e.touches[0].clientY;
    const diff = currentY - startYRef.current;
    
    // Attiva solo se si sta trascinando verso il basso e siamo in cima
    if (diff > 0 && window.scrollY <= 0) {
      // Resistenza: più trascini, più diventa difficile
      const resistance = 0.4;
      const newDistance = Math.min(diff * resistance, 150);
      setPullDistance(newDistance);
      
      // Previeni lo scroll normale se stiamo facendo il pull-to-refresh
      if (diff > 10) {
        e.preventDefault();
      }
    }
  };
  
  const handleTouchEnd = () => {
    if (!isPulling) return;
    
    // Se ha trascinato abbastanza, attiva il refresh
    if (pullDistance >= pullThreshold) {
      refreshAllData();
    }
    
    // Reset dello stato
    setIsPulling(false);
    setPullDistance(0);
  };
  
  // Navigation functions
  const viewDDTDetails = (ddt: DDT) => {
    console.log("View DDT details", ddt);
    navigate(`/ddt-details/${ddt.id}`);
  };

  const viewProductDetails = (product: ProductLabel) => {
    console.log("View Product details", product);
    navigate(`/product-details/${product.id}`);
  };

  const viewContainerDetails = (container: Container) => {
    console.log("View Container details", container);
    navigate(`/container/${container.id}`);
  };

  const viewSupplierDetails = (supplier: Supplier) => {
    console.log("View Supplier details", supplier);
    navigate(`/suppliers#${supplier.id}`);
  };

  // Fetch data with optimized caching and offline support
  const [offlineDDTs, setOfflineDDTs] = useState<DDT[]>([]);
  const [offlineProducts, setOfflineProducts] = useState<ProductLabel[]>([]);
  const [offlineContainers, setOfflineContainers] = useState<Container[]>([]);
  const [offlineSuppliers, setOfflineSuppliers] = useState<Supplier[]>([]);

  // Carica dati offline all'avvio dell'applicazione
  useEffect(() => {
    loadOfflineData();
  }, []);

  // Aggiungiamo queryClient per ricaricare tutti i dati contemporaneamente
  const queryClient = useQueryClient();
  
  // Stato per il refresh
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Queries ottimizzate con supporto offline
  const { data: ddts, isLoading: isLoadingDDTs, refetch: refetchDDTs } = useQuery<DDT[]>({
    queryKey: ["/api/ddt"],
    queryFn: async () => {
      const response = await fetch("/api/ddt", {
        credentials: "include",
        headers: { 'Accept': 'application/json' }
      });
      if (!response.ok) throw new Error("Failed to fetch DDTs");
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minuti di cache
    refetchOnWindowFocus: false,
    placeholderData: offlineDDTs
  });

  const { data: products, isLoading: isLoadingProducts, refetch: refetchProducts } = useQuery<ProductLabel[]>({
    queryKey: ["/api/product-labels"],
    queryFn: async () => {
      const response = await fetch('/api/product-labels');
      if (!response.ok) throw new Error('Network response was not ok');
      return response.json();
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    placeholderData: offlineProducts,
    select: (data) => {
      // Rimuovi duplicati basati sull'ID
      if (!data || !Array.isArray(data)) return [];
      const uniqueProductsMap = new Map<number, ProductLabel>();
      data.forEach((product) => {
        if (product && product.id && !uniqueProductsMap.has(product.id)) {
          uniqueProductsMap.set(product.id, product);
        }
      });
      return Array.from(uniqueProductsMap.values());
    }
  });

  const { data: containers, isLoading: isLoadingContainers, refetch: refetchContainers } = useQuery<Container[]>({
    queryKey: ["/api/containers"],
    queryFn: async () => {
      const response = await fetch("/api/containers", {
        credentials: "include",
        headers: { 'Accept': 'application/json' }
      });
      if (!response.ok) throw new Error("Failed to fetch containers");
      return response.json();
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    placeholderData: offlineContainers
  });

  const { data: suppliers, isLoading: isLoadingSuppliers, refetch: refetchSuppliers } = useQuery<Supplier[]>({
    queryKey: ["/api/suppliers"],
    queryFn: async () => {
      const response = await fetch("/api/suppliers", {
        credentials: "include",
        headers: { 'Accept': 'application/json' }
      });
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      return response.json();
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    placeholderData: offlineSuppliers
  });

  const isLoading = isLoadingDDTs || isLoadingProducts || isLoadingContainers || isLoadingSuppliers || isRefreshing;
  
  // Funzione che carica i dati in memoria locale
  const loadOfflineData = async () => {
    try {
      // Per forzare il refresh, invalidare la cache prima di richiedere nuovi dati
      queryClient.invalidateQueries({ queryKey: ["/api/ddt"] });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      queryClient.invalidateQueries({ queryKey: ["/api/suppliers"] });
      
      // Pre-carica dati offline per uso in caso di errore di rete
      const ddtData = await offlineApiRequest<DDT[]>("/api/ddt", "GET", undefined, {
        cacheKey: "/api/ddt",
        cacheExpiry: 24 * 60 * 60 * 1000
      });
      if (ddtData) setOfflineDDTs(ddtData);
      
      const productData = await offlineApiRequest<ProductLabel[]>("/api/product-labels", "GET", undefined, {
        cacheKey: "/api/product-labels",
        cacheExpiry: 24 * 60 * 60 * 1000
      });
      if (productData) setOfflineProducts(productData);
      
      const containerData = await offlineApiRequest<Container[]>("/api/containers", "GET", undefined, {
        cacheKey: "/api/containers",
        cacheExpiry: 24 * 60 * 60 * 1000
      });
      if (containerData) setOfflineContainers(containerData);
      
      const supplierData = await offlineApiRequest<Supplier[]>("/api/suppliers", "GET", undefined, {
        cacheKey: "/api/suppliers",
        cacheExpiry: 24 * 60 * 60 * 1000
      });
      if (supplierData) setOfflineSuppliers(supplierData);
    } catch (error) {
      console.log("Errore nel precaricamento dati offline:", error);
    }
  };
  
  // Funzione per ricaricare tutti i dati
  const refreshAllData = async () => {
    setIsRefreshing(true);
    console.log("Aggiornamento dati in corso...");
    
    try {
      await Promise.all([
        refetchDDTs(),
        refetchProducts(),
        refetchContainers(),
        refetchSuppliers()
      ]);
      
      // Forza anche il refresh della cache offline
      await loadOfflineData();
      
      // Riapplica i filtri dopo il refresh
      handleSearch();
      
      toast({
        title: "Dati aggiornati",
        description: "I dati sono stati aggiornati correttamente",
        duration: 3000
      });
    } catch (error) {
      console.error("Errore durante l'aggiornamento dei dati:", error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare i dati. Riprova più tardi.",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Search function
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      // Empty search query, show all results (escludendo i container archiviati, prodotti ritirati e scaduti)
      setFilteredResults({
        ddts: ddts || [],
        products: (products || [])
          .filter(product => !((product as any).isRetired || false) && !isExpired(product.expiryDate))
          .sort((a, b) => {
            // Ordina per data di creazione (più recenti prima)
            const dateA = new Date(a.createdAt || 0);
            const dateB = new Date(b.createdAt || 0);
            return dateB.getTime() - dateA.getTime();
          }),
        containers: (containers || []).filter(container => !container.isArchived),
        suppliers: suppliers || [],
      });
      return;
    }

    const query = searchQuery.toLowerCase().trim();

    const filteredDDTs = ddts
      ? ddts.filter(
          (ddt) =>
            ddt.number.toLowerCase().includes(query) ||
            ddt.companyName.toLowerCase().includes(query) ||
            ddt.vatNumber.toLowerCase().includes(query)
        )
      : [];

    const filteredProducts = products
      ? products.filter(
          (product) =>
            (product.productName.toLowerCase().includes(query) ||
            product.batchNumber.toLowerCase().includes(query)) &&
            !((product as any).isRetired || false) && // Escludi i prodotti ritirati
            !isExpired(product.expiryDate) // Escludi i prodotti scaduti
        ).sort((a, b) => {
          // Ordina per data di creazione (più recenti prima)
          const dateA = new Date(a.createdAt || 0);
          const dateB = new Date(b.createdAt || 0);
          return dateB.getTime() - dateA.getTime();
        })
      : [];

    const filteredContainers = containers
      ? containers.filter(
          (container) => 
            // Filtra per query di ricerca e mostra solo contenitori non archiviati
            ((container.name.toLowerCase().includes(query) ||
            container.type.toLowerCase().includes(query)) &&
            !container.isArchived)
        )
      : [];

    const filteredSuppliers = suppliers
      ? suppliers.filter(
          (supplier) =>
            supplier.companyName.toLowerCase().includes(query) ||
            supplier.vatNumber.toLowerCase().includes(query)
        )
      : [];

    setFilteredResults({
      ddts: filteredDDTs,
      products: filteredProducts,
      containers: filteredContainers,
      suppliers: filteredSuppliers,
    });
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle search input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Auto-search when typing
  useEffect(() => {
    const timer = setTimeout(() => {
      handleSearch();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, ddts, products, containers, suppliers]);

  // Determine counts and remove duplicates for products
  const ddtCount = filteredResults.ddts.length;
  
  // Rimuovi duplicati per il conteggio dei prodotti
  const uniqueProductIds = new Set();
  filteredResults.products.forEach(product => uniqueProductIds.add(product.id));
  const productCount = uniqueProductIds.size;
  
  const containerCount = filteredResults.containers.length;
  const supplierCount = filteredResults.suppliers.length;
  const totalCount = ddtCount + productCount + containerCount + supplierCount;

  return (
    <GradientBackground>
      <div className="flex flex-col h-screen">
        <Header title="Ricerca" showBack={false} />

      <main 
        ref={contentRef}
        className="flex-1 pt-24 pb-16 overflow-y-auto relative"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Indicatore di pull-to-refresh */}
        {isPulling && (
          <div 
            className="absolute top-0 left-0 right-0 flex justify-center items-center transition-transform duration-200 z-10 bg-gray-100 rounded-b-lg shadow-sm"
            style={{ 
              height: `${pullDistance}px`,
              transform: `translateY(${isPulling ? '0' : '-100%'})`,
              opacity: Math.min(pullDistance / pullThreshold, 1)
            }}
          >
            <div className="flex items-center">
              <RefreshCw 
                size={20} 
                className={`mr-2 text-primary ${isRefreshing ? 'animate-spin' : (pullDistance >= pullThreshold ? 'text-green-500' : '')}`} 
              />
              <span className="text-sm font-medium">
                {isRefreshing 
                  ? "Aggiornamento in corso..." 
                  : pullDistance >= pullThreshold 
                    ? "Rilascia per aggiornare" 
                    : "Trascina per aggiornare"
                }
              </span>
            </div>
          </div>
        )}
        
        <div className="px-4 py-6 pb-20">
          {/* Search bar */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                type="text"
                placeholder="Cerca DDT, prodotti, contenitori..."
                className="pl-10 pr-4 py-2"
                value={searchQuery}
                onChange={handleInputChange}
              />
            </div>
            <Button onClick={handleSearch}>
              Cerca
            </Button>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="all" className="text-xs">
                Tutti ({totalCount})
              </TabsTrigger>
              <TabsTrigger value="ddts" className="text-xs">
                DDT ({ddtCount})
              </TabsTrigger>
              <TabsTrigger value="products" className="text-xs">
                Etichette ({productCount})
              </TabsTrigger>
              <TabsTrigger value="containers" className="text-xs">
                Box ({containerCount})
              </TabsTrigger>
            </TabsList>

            {/* All results tab */}
            <TabsContent value="all">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : totalCount > 0 ? (
                <div className="space-y-6">
                  {ddtCount > 0 && (
                    <div>
                      <h3 className="font-semibold text-sm mb-2 text-gray-700 flex items-center">
                        <Truck className="mr-2" size={16} />
                        Documenti di Trasporto ({ddtCount})
                      </h3>
                      <div className="space-y-2">
                        {filteredResults.ddts.slice(0, 3).map((ddt) => (
                          <Card key={ddt.id} className="bg-white shadow-sm hover:shadow transition-shadow">
                            <CardContent className="p-3">
                              <div 
                                className="flex justify-between items-center cursor-pointer" 
                                onClick={() => viewDDTDetails(ddt)}
                              >
                                <div>
                                  <p className="font-semibold text-sm">{ddt.companyName}</p>
                                  <p className="text-xs text-gray-500">DDT #{ddt.number}</p>
                                </div>
                                <p className="text-xs text-gray-500">{formatDate(ddt.date)}</p>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                        {ddtCount > 3 && (
                          <p 
                            className="text-xs text-center text-primary cursor-pointer hover:underline"
                            onClick={() => setActiveTab('ddts')}
                          >
                            + altri {ddtCount - 3} risultati
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {productCount > 0 && (
                    <div>
                      <h3 className="font-semibold text-sm mb-2 text-gray-700 flex items-center">
                        <Package className="mr-2" size={16} />
                        Etichette ({productCount})
                      </h3>
                      <div className="space-y-2">
                        {(() => {
                          // Rimuovi i duplicati qui, prima del rendering
                          const uniqueProductIds = new Set();
                          const uniqueProducts = filteredResults.products.filter(product => {
                            if (uniqueProductIds.has(product.id)) {
                              return false; // Salta questo prodotto perché è un duplicato
                            }
                            uniqueProductIds.add(product.id);
                            return true; // Mantieni questo prodotto perché è unico
                          });
                          
                          return uniqueProducts.slice(0, 3).map((product) => {
                            const productExpired = isExpired(product.expiryDate);
                            
                            return (
                              <Card 
                                key={product.id} 
                                className={`bg-white shadow-sm hover:shadow transition-shadow ${
                                  productExpired ? 'border-red-500 border-2' : ''
                                }`}
                              >
                                <CardContent className="p-3">
                                  <div 
                                    className="flex justify-between items-center cursor-pointer"
                                    onClick={() => viewProductDetails(product)}
                                  >
                                    <div>
                                      <p className="font-semibold text-sm">{product.productName}</p>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <span className={`text-xs ${productExpired ? 'text-red-600 font-semibold' : 'text-gray-500'}`}>
                                        {formatDate(product.expiryDate)}
                                      </span>
                                      {productExpired && (
                                        <Badge variant="destructive" className="ml-1 text-xs py-0">
                                          <AlertTriangle className="h-3 w-3 mr-1" />
                                          Scaduto
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            );
                          })
                        })()}
                        {productCount > 3 && (
                          <p 
                            className="text-xs text-center text-primary cursor-pointer hover:underline"
                            onClick={() => setActiveTab('products')}
                          >
                            + altri {productCount - 3} risultati
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {containerCount > 0 && (
                    <div>
                      <h3 className="font-semibold text-sm mb-2 text-gray-700 flex items-center">
                        <Package className="mr-2" size={16} />
                        Box ({containerCount})
                      </h3>
                      <div className="space-y-2">
                        {filteredResults.containers.slice(0, 3).map((container) => (
                          <Card key={container.id} className="bg-white shadow-sm hover:shadow transition-shadow">
                            <CardContent className="p-3">
                              <div 
                                className="flex justify-between items-center cursor-pointer"
                                onClick={() => viewContainerDetails(container)}
                              >
                                <div>
                                  <p className="font-semibold text-sm">{container.name}</p>
                                  <p className="text-xs text-gray-500">Tipo: {container.type}</p>
                                </div>
                                <p className="text-xs text-gray-500">
                                  {container.currentItems === 0 ? "Zero" : `${container.currentItems} prodotti`}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                        {containerCount > 3 && (
                          <p 
                            className="text-xs text-center text-primary cursor-pointer hover:underline"
                            onClick={() => setActiveTab('containers')}
                          >
                            + altri {containerCount - 3} risultati
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {supplierCount > 0 && (
                    <div>
                      <h3 className="font-semibold text-sm mb-2 text-gray-700 flex items-center">
                        <Factory className="mr-2" size={16} />
                        Fornitori ({supplierCount})
                      </h3>
                      <div className="space-y-2">
                        {filteredResults.suppliers.slice(0, 3).map((supplier) => (
                          <Card key={supplier.id} className="bg-white shadow-sm hover:shadow transition-shadow">
                            <CardContent className="p-3">
                              <div 
                                className="flex justify-between items-center cursor-pointer"
                                onClick={() => viewSupplierDetails(supplier)}
                              >
                                <div>
                                  <p className="font-semibold text-sm">{supplier.companyName}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                        {supplierCount > 3 && (
                          <p 
                            className="text-xs text-center text-primary cursor-pointer hover:underline"
                            onClick={() => navigate('/suppliers')}
                          >
                            + altri {supplierCount - 3} risultati
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8">
                  <SearchIcon className="mx-auto h-10 w-10 text-gray-400" />
                  <p className="mt-2 text-gray-500">
                    Nessun risultato trovato per "{searchQuery}"
                  </p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <SearchIcon className="mx-auto h-10 w-10 text-gray-400" />
                  <p className="mt-2 text-gray-500">
                    Digita per iniziare la ricerca
                  </p>
                </div>
              )}
            </TabsContent>

            {/* DDTs tab */}
            <TabsContent value="ddts">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : ddtCount > 0 ? (
                <div className="space-y-2">
                  {filteredResults.ddts.map((ddt) => (
                    <Card key={ddt.id} className="bg-white shadow-sm hover:shadow transition-shadow">
                      <CardContent className="p-3">
                        <div 
                          className="flex flex-col cursor-pointer"
                          onClick={() => viewDDTDetails(ddt)}
                        >
                          <div className="flex justify-between items-center">
                            <p className="font-semibold">{ddt.companyName}</p>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              <p className="text-xs text-gray-500">{formatDate(ddt.date)}</p>
                            </div>
                          </div>
                          <div className="flex justify-between mt-1">
                            <p className="text-xs text-gray-500">P.IVA: {ddt.vatNumber}</p>
                            <p className="text-xs font-medium">DDT #{ddt.number}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun DDT trovato</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Digita per cercare DDT</p>
                </div>
              )}
            </TabsContent>

            {/* Products tab */}
            <TabsContent value="products">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : productCount > 0 ? (
                <div className="space-y-2">
                  {(() => {
                    // Rimuovi i duplicati qui, prima del rendering
                    const uniqueProductIds = new Set();
                    const uniqueProducts = filteredResults.products.filter(product => {
                      if (uniqueProductIds.has(product.id)) {
                        return false; // Salta questo prodotto perché è un duplicato
                      }
                      uniqueProductIds.add(product.id);
                      return true; // Mantieni questo prodotto perché è unico
                    });
                    
                    return uniqueProducts.map((product) => {
                      const productExpired = isExpired(product.expiryDate);
                      
                      return (
                        <Card 
                          key={product.id} 
                          className={`bg-white shadow-sm hover:shadow transition-shadow ${
                            productExpired ? 'border-red-500 border-2' : ''
                          }`}
                        >
                          <CardContent className="p-3">
                            <div 
                              className="flex flex-col cursor-pointer"
                              onClick={() => viewProductDetails(product)}
                            >
                              <div className="flex justify-between items-center">
                                <p className="font-semibold">{product.productName}</p>
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  <span className={`text-xs ${productExpired ? 'text-red-600 font-semibold' : 'text-gray-500'}`}>
                                    Scad: {formatDate(product.expiryDate)}
                                  </span>
                                  {productExpired && (
                                    <Badge variant="destructive" className="ml-1 text-xs py-0">
                                      <AlertTriangle className="h-3 w-3 mr-1" />
                                      Scaduto
                                    </Badge>
                                  )}
                                </div>
                              </div>

                            </div>
                          </CardContent>
                        </Card>
                      );
                    })
                  })()}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun prodotto trovato</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Digita per cercare prodotti</p>
                </div>
              )}
            </TabsContent>

            {/* Containers tab */}
            <TabsContent value="containers">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : containerCount > 0 ? (
                <div className="space-y-2">
                  {filteredResults.containers.map((container) => (
                    <Card key={container.id} className="bg-white shadow-sm hover:shadow transition-shadow">
                      <CardContent className="p-3">
                        <div 
                          className="flex flex-col cursor-pointer"
                          onClick={() => viewContainerDetails(container)}
                        >
                          <div className="flex justify-between items-center">
                            <p className="font-semibold">{container.name}</p>
                            <p className="text-xs px-2 py-1 rounded-full bg-gray-100">
                              {container.type}
                            </p>
                          </div>
                          <div className="flex justify-between mt-1">
                            <p className="text-xs text-gray-500">
                              {container.isArchived ? "Archiviato" : "Attivo"}
                            </p>
                            <p className="text-xs">
                              {container.currentItems === 0 ? "Zero" : `${container.currentItems} prodotti`}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun contenitore trovato</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Digita per cercare contenitori</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </main>



        <Footer activeItem="search" />
      </div>
    </GradientBackground>
  );
}
