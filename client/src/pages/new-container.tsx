import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { QRCodeView } from "@/components/ui/qr-code";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { ContainerType, Container } from "@/types";
import { ContainerFormData } from "@/types/index";
import { useMutation, useQuery, useQueryClient, QueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { generateUniqueId, generateContainerQRValue } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { offlineApiRequest } from "@/lib/offlineAPI";

// Form validation schema
const containerFormSchema = z.object({
  name: z.string().min(1, "Il nome del contenitore è obbligatorio"),
  typeId: z.coerce.number().int().positive("Seleziona un tipo di contenitore"),
  type: z.string().optional(),
  maxItems: z.coerce.number().int().min(1, "Il numero massimo di articoli deve essere almeno 1"),
});

// Fallback container types in case the API fails
const fallbackContainerTypes: ContainerType[] = [
  { id: 1, value: "steel", label: "Acciaio", isActive: true, createdAt: new Date().toISOString() },
  { id: 2, value: "pet", label: "PET", isActive: true, createdAt: new Date().toISOString() },
  { id: 3, value: "paper", label: "Carta", isActive: true, createdAt: new Date().toISOString() },
  { id: 4, value: "glass", label: "Vetro", isActive: true, createdAt: new Date().toISOString() },
];

export default function NewContainer() {
  const [, setLocation] = useLocation();
  // Non utilizziamo più il qrCodeData nella form perché ora il server genererà 
  // il QR code corretto automaticamente dopo aver creato il record nel DB
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch container types from API
  const { data: containerTypes, isLoading: isLoadingTypes, error: typesError } = useQuery<ContainerType[]>({
    queryKey: ["/api/container-types"],
    retry: 1,
    staleTime: 60000,
    refetchOnWindowFocus: false
  });
  
  // Utilizziamo useEffect per gestire onSuccess e onError
  useEffect(() => {
    if (containerTypes) {
      console.log("Container types loaded:", containerTypes);
    }
  }, [containerTypes]);
  
  useEffect(() => {
    if (typesError) {
      console.error("Error fetching container types:", typesError);
      toast({
        title: "Errore",
        description: "Impossibile caricare i tipi di container. Verranno utilizzati i tipi predefiniti.",
        variant: "destructive",
      });
    }
  }, [typesError, toast]);
  
  // Use fallback if API fails or while loading
  const availableContainerTypes = containerTypes?.filter((type) => type.isActive === true) || fallbackContainerTypes;

  // Dichiarazione del form qui

  const form = useForm<ContainerFormData>({
    resolver: zodResolver(containerFormSchema),
    defaultValues: {
      name: "",
      type: "",
      typeId: undefined,
      maxItems: 5,
    },
  });

  // Create container mutation con supporto offline
  const createContainerMutation = useMutation({
    mutationFn: async (formData: ContainerFormData) => {
      // Non inviamo più qrCodeData, il server genererà il QR code corretto
      const data = { 
        ...formData
      };
      
      try {
        // Prima prova a usare l'API tradizionale
        const response = await apiRequest("POST", "/api/containers", data);
        return response;
      } catch (error) {
        console.log("Errore connessione al database, fallback a modalità offline", error);
        
        // In caso di errore, usa l'API offline
        return await offlineApiRequest("POST", "/api/containers", data, {
          syncWhenOnline: true
        });
      }
    },
    onSuccess: (data) => {
      // Verifica se l'operazione è stata salvata per la sincronizzazione offline
      if (data && 'offlinePending' in data) {
        toast({
          title: "Container salvato localmente",
          description: "Il container sarà creato quando tornerai online.",
        });
        // Anche in modalità offline, torniamo alla lista container
        setLocation("/containers");
      } else {
        // Operazione completata con successo online
        
        // Aggiorna immediatamente la cache con i nuovi dati
        queryClient.setQueryData(["/api/containers"], (oldData: Container[] | undefined) => {
          if (oldData && data) {
            // Aggiungi il nuovo container ai dati esistenti
            return [...oldData, data];
          }
          return oldData;
        });
        
        // Invalida e refetch per assicurarsi che i dati siano sincronizzati
        queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
        queryClient.refetchQueries({ queryKey: ["/api/containers"] });
        
        toast({
          title: "Container creato",
          description: "Il nuovo container è stato creato con successo.",
        });
        setLocation("/containers");
      }
    },
    onError: (error) => {
      console.error("Error creating container:", error);
      toast({
        title: "Creazione fallita",
        description: "Impossibile creare il container. Riprova più tardi.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: any) => {
    createContainerMutation.mutate(data);
  };

  const handleCancel = () => {
    setLocation("/containers");
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-gray-200 via-gray-400 to-gray-500 relative overflow-hidden">
      {/* Effetto glow circolare intensificato e centrato - ripreso dalla pagina home */}
      <div className="absolute w-[600px] h-[600px] rounded-full bg-white/70 blur-[80px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute w-[350px] h-[350px] rounded-full bg-white/80 blur-[100px] top-[25%] left-[25%]"></div>
      <div className="absolute w-[400px] h-[400px] rounded-full bg-white/60 blur-[120px] bottom-[15%] right-[15%]"></div>
      <div className="absolute w-[200px] h-[200px] rounded-full bg-gray-600/70 blur-[50px] top-[60%] right-[30%]"></div>
      
      <Header title="Nuovo Container" showBack backPath="/containers" />

      <main className="flex-1 pt-24 pb-32 px-4 overflow-y-auto relative z-10">
        <div className="py-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <Card className="bg-white mb-6 shadow-lg border border-gray-200">
                <CardContent className="p-4 space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Dettagli Container
                  </h3>

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome Container</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., PREP-004" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="typeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tipo di Container</FormLabel>
                        {isLoadingTypes ? (
                          <div className="flex items-center space-x-2 h-10 px-4 py-2 border rounded-md">
                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">Caricamento tipi...</span>
                          </div>
                        ) : (
                          <Select
                            onValueChange={(value) => {
                              field.onChange(parseInt(value));
                              // Imposta anche il campo 'type' per retrocompatibilità
                              const selectedType = availableContainerTypes.find((t: ContainerType) => t.id === parseInt(value));
                              if (selectedType) {
                                form.setValue("type", selectedType.value);
                              }
                            }}
                            value={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Seleziona un tipo" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableContainerTypes.map((type: ContainerType) => (
                                <SelectItem key={type.id} value={type.id?.toString()}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card className="bg-white mb-6 shadow-lg border border-gray-200">
                <CardContent className="p-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Generazione QR Code
                  </h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Il QR code verrà associato permanentemente a questo contenitore e non potrà essere modificato successivamente.
                  </p>

                  <QRCodeView
                    code={`container:preview:${form.watch("name")?.replace(/\s+/g, '_') || "NEW-CONTAINER"}`}
                    label={form.watch("name") || "NEW-CONTAINER"}
                    showDownload={false}
                    showPrint={false}

                    itemName={form.watch("name") || "NEW-CONTAINER"}
                  />
                  <p className="text-xs text-center text-gray-400 mt-2 italic">
                    Anteprima - Il QR code definitivo verrà generato al momento del salvataggio
                  </p>
                </CardContent>
              </Card>

              <div className="flex justify-between space-x-2 mt-6">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1 h-16 rounded-xl text-white bg-black border-2 border-blue-500/70 shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(59,130,246,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu"
                  onClick={handleCancel}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 6L6 18M6 6l12 12"></path>
                  </svg>
                  <span className="text-base font-bold tracking-wide uppercase">Annulla</span>
                </Button>
                <Button
                  type="submit"
                  className="flex-1 h-16 rounded-xl text-white bg-black border-2 border-green-500/70 shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(34,197,94,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu"
                  disabled={createContainerMutation.isPending}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 6L9 17l-5-5"></path>
                  </svg>
                  <span className="text-base font-bold tracking-wide uppercase">
                    {createContainerMutation.isPending ? "Salvataggio..." : "Salva"}
                  </span>
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </main>
      <Footer activeItem="containers" />
    </div>
  );
}
