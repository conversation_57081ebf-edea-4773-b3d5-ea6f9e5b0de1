import { useState } from "react";
import { useLocation } from "wouter";
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ContainerCard } from "@/components/containers/container-card";
import { SearchIcon, Printer, Download, QrCode } from "lucide-react";
import { Container } from "@/types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { offlineApiRequest } from "@/lib/offlineAPI";
import { QRCode, QRCodeViewProps } from "@/components/ui/qr-code";
import { generateContainerQRValue, printQRCode, downloadQRCode } from "@/lib/utils";
import { useSafeAsyncState } from "@/hooks/useSafeAsyncState";

export default function Containers() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useSafeAsyncState("");
  const [showArchived, setShowArchived] = useSafeAsyncState(false);
  const [selectedContainer, setSelectedContainer] = useSafeAsyncState<Container | null>(null);
  const [showQRCode, setShowQRCode] = useSafeAsyncState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: containers, isLoading: isContainersLoading } = useQuery<Container[]>({
    queryKey: ["/api/containers"],
    staleTime: 0, // Permette aggiornamenti immediati per la lista contenitori
    refetchOnWindowFocus: true, // Aggiorna quando l'utente torna alla pagina
    queryFn: async () => {
      try {
        // Ottiene gli oggetti dalla rete con gestione ottimizzata
        console.log("Tentativo di caricamento containers dalla rete");
        const response = await fetch(`/api/containers?v=${Date.now()}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include' // Aggiunto per assicurare che i cookie di autenticazione vengano inviati
        });
        
        // Log dettagliato dello stato della risposta
        console.log(`Risposta API containers: status=${response.status}, ok=${response.ok}`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Errore API containers ${response.status}: ${errorText}`);
          throw new Error(`Errore di rete: ${response.status} - ${errorText}`);
        }
        
        const data = await response.json();
        console.log(`Containers ricevuti: ${JSON.stringify(data).substring(0, 200)}...`);
        
        // Verifica che i dati siano un array
        if (!Array.isArray(data)) {
          console.error('I dati dei containers ricevuti non sono un array:', data);
          throw new Error('Formato dati non valido');
        }
        
        // Archivia i dati nella cache locale per il fallback
        try {
          localStorage.setItem('containers-cache', JSON.stringify({
            timestamp: Date.now(),
            data: data
          }));
          console.log(`Salvati ${data.length} containers nella cache locale`);
        } catch (storageError) {
          console.warn('Impossibile salvare la cache dei containers:', storageError);
        }
        
        return data;
      } catch (error) {
        console.error('Errore nel recupero containers:', error);
        
        // Prova a caricare dalla cache locale
        try {
          const cachedData = localStorage.getItem('containers-cache');
          if (cachedData) {
            const parsed = JSON.parse(cachedData);
            if (Array.isArray(parsed.data)) {
              console.log(`Recuperati ${parsed.data.length} containers dalla cache locale (salvata ${new Date(parsed.timestamp).toLocaleString()})`);
              return parsed.data;
            } else {
              console.error('I dati nella cache non sono un array:', parsed.data);
            }
          }
        } catch (cacheError) {
          console.error('Errore nel recupero dalla cache containers:', cacheError);
        }
        
        // Se tutto fallisce ritorna un array vuoto
        return [];
      }
    },
    gcTime: 5 * 60 * 1000, // 5 minuti
    retry: 3,
    retryDelay: 1000,
  });

  const archiveContainerMutation = useMutation({
    mutationFn: async (containerId: number) => {
      try {
        // Prima tenta di usare l'API normale
        const response = await apiRequest<Container>(`/api/containers/${containerId}/archive`, {
          method: "PATCH"
        }, { isArchived: true });
        
        return response;
      } catch (error) {
        console.log("Errore connessione al database, fallback a modalità offline", error);
        
        // Se fallisce, usa l'API offline che salverà l'operazione per la sincronizzazione futura
        return await offlineApiRequest<Container>(
          `/api/containers/${containerId}/archive`,
          "PATCH",
          { isArchived: true },
          { syncWhenOnline: true }
        );
      }
    },
    // Ottimisticamente aggiorna la cache immediatamente per mostrare i risultati senza attendere il refetch
    onMutate: async (containerId) => {
      // Annulla qualsiasi query in sospeso per evitare che sostituisca l'aggiornamento che stiamo facendo
      await queryClient.cancelQueries({ queryKey: ["/api/containers"] });
      
      // Salva lo stato precedente per il rollback in caso di errore
      const previousContainers = queryClient.getQueryData<Container[]>(["/api/containers"]);
      
      // Aggiorna direttamente la cache con il nuovo stato
      queryClient.setQueryData<Container[]>(["/api/containers"], (old) => {
        if (!old) return old;
        return old.map(container => 
          container.id === containerId 
            ? { ...container, isArchived: true } 
            : container
        );
      });
      
      // Ritorna l'oggetto di contesto con i dati precedenti
      return { previousContainers };
    },
    onSuccess: (data, containerId) => {
      // Verifica se l'operazione è stata salvata per la sincronizzazione offline
      if (data && 'offlinePending' in data) {
        toast({
          title: "Container verrà archiviato",
          description: "L'operazione sarà completata quando tornerai online.",
        });
      } else {
        // Aggiornamento immediato avvenuto con successo
        queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
        toast({
          title: "Container archiviato",
          description: "Il container è stato archiviato con successo.",
        });
      }
    },
    onError: (error, containerId, context) => {
      console.error("Error archiving container:", error);
      // Rollback alla versione precedente in caso di errore
      if (context?.previousContainers) {
        queryClient.setQueryData(["/api/containers"], context.previousContainers);
      }
      toast({
        title: "Archiviazione fallita",
        description: "Impossibile archiviare il container. Riprova più tardi.",
        variant: "destructive",
      });
    },
  });

  const unarchiveContainerMutation = useMutation({
    mutationFn: async (containerId: number) => {
      try {
        // Prima tenta di usare l'API normale
        const response = await apiRequest<Container>(`/api/containers/${containerId}/archive`, {
          method: "PATCH"
        }, { isArchived: false });
        
        return response;
      } catch (error) {
        console.log("Errore connessione al database, fallback a modalità offline", error);
        
        // Se fallisce, usa l'API offline che salverà l'operazione per la sincronizzazione futura
        return await offlineApiRequest<Container>(
          `/api/containers/${containerId}/archive`,
          "PATCH",
          { isArchived: false },
          { syncWhenOnline: true }
        );
      }
    },
    // Ottimisticamente aggiorna la cache immediatamente per mostrare i risultati senza attendere il refetch
    onMutate: async (containerId) => {
      // Annulla qualsiasi query in sospeso per evitare che sostituisca l'aggiornamento che stiamo facendo
      await queryClient.cancelQueries({ queryKey: ["/api/containers"] });
      
      // Salva lo stato precedente per il rollback in caso di errore
      const previousContainers = queryClient.getQueryData<Container[]>(["/api/containers"]);
      
      // Aggiorna direttamente la cache con il nuovo stato
      queryClient.setQueryData<Container[]>(["/api/containers"], (old) => {
        if (!old) return old;
        return old.map(container => 
          container.id === containerId 
            ? { ...container, isArchived: false } 
            : container
        );
      });
      
      // Ritorna l'oggetto di contesto con i dati precedenti
      return { previousContainers };
    },
    onSuccess: (data, containerId) => {
      // Verifica se l'operazione è stata salvata per la sincronizzazione offline
      if (data && 'offlinePending' in data) {
        toast({
          title: "Container verrà ripristinato",
          description: "L'operazione sarà completata quando tornerai online.",
        });
      } else {
        // Aggiornamento immediato avvenuto con successo
        queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
        toast({
          title: "Container ripristinato",
          description: "Il container è stato ripristinato con successo.",
        });
      }
    },
    onError: (error, containerId, context) => {
      console.error("Error unarchiving container:", error);
      // Rollback alla versione precedente in caso di errore
      if (context?.previousContainers) {
        queryClient.setQueryData(["/api/containers"], context.previousContainers);
      }
      toast({
        title: "Ripristino Fallito",
        description: "Impossibile ripristinare il container. Riprova.",
        variant: "destructive",
      });
    },
  });

  const handleNewContainer = () => {
    setLocation("/new-container");
  };

  const handleViewDetails = (container: Container) => {
    setSelectedContainer(container);
    // Naviga alla pagina dei dettagli del container
    setLocation(`/container/${container.id}`);
  };

  const handleArchive = (container: Container) => {
    archiveContainerMutation.mutate(container.id);
  };

  const handleUnarchive = (container: Container) => {
    unarchiveContainerMutation.mutate(container.id);
  };

  const handlePrintQR = (container: Container) => {
    setSelectedContainer(container);
    setShowQRCode(true);
  };

  const filteredContainers = containers
    ? containers.filter((container: Container) => {
        const matchesSearch = container.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
        const matchesArchiveStatus = showArchived
          ? container.isArchived
          : !container.isArchived;
        return matchesSearch && matchesArchiveStatus;
      })
    : [];

  return (
    <GradientBackground>
      <div className="flex flex-col h-screen">
        <Header title="Containers" showBack backPath="/" />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="py-6">
          <div className="mb-6">
            <div className="relative">
              <Input
                type="text"
                placeholder="Cerca contenitori..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10"
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <SearchIcon className="h-5 w-5" />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800">
              {showArchived ? "Contenitori Archiviati" : "Contenitori Attivi"}
            </h2>
            <Button
              variant="link"
              className="text-sm font-medium text-primary-700"
              onClick={() => setShowArchived(!showArchived)}
            >
              {showArchived ? "Mostra Attivi" : "Mostra Archiviati"}
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4 mb-8">
            {isContainersLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="h-24 bg-gray-200 animate-pulse rounded-lg"
                ></div>
              ))
            ) : filteredContainers.length > 0 ? (
              filteredContainers.map((container) => (
                <ContainerCard
                  key={container.id}
                  container={container}
                  onViewDetails={handleViewDetails}
                  onArchive={handleArchive}
                  onUnarchive={handleUnarchive}
                  onPrintQR={handlePrintQR}
                  isArchived={container.isArchived}
                />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                {showArchived
                  ? "Nessun contenitore archiviato trovato"
                  : "Nessun contenitore attivo trovato"}
              </div>
            )}
          </div>

          {!showArchived && (
            <>
              <Button
                className="fixed bottom-36 right-4 w-14 h-14 rounded-full flex items-center justify-center shadow-[0_8px_20px_-3px_rgba(0,0,0,0.5)] hover:shadow-[0_12px_25px_-3px_rgba(59,130,246,0.3)] hover:translate-y-[-2px] active:translate-y-[1px] active:shadow-[0_5px_15px_-3px_rgba(0,0,0,0.4)] transition-all duration-300 transform-gpu bg-blue-500 border-2 border-blue-400"
                onClick={handleNewContainer}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-white drop-shadow-md"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ filter: 'drop-shadow(0 2px 3px rgba(0,0,0,0.3))', transform: 'translateZ(5px) scale(1.6)' }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </Button>

            </>
          )}
        </div>
      </main>

      <Footer activeItem="containers" />

      {showQRCode && selectedContainer && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-4 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Container QR Code
            </h3>
            <QRCode
              value={generateContainerQRValue(selectedContainer)}
              size={250}
              id={`container-qrcode-${selectedContainer.id}`}
            />
            <div className="mt-4 flex justify-between items-center">
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => 
                    printQRCode(
                      `container-qrcode-${selectedContainer.id}`, 
                      selectedContainer.name, 
                      `Container ID: ${selectedContainer.id}`, 
                      generateContainerQRValue(selectedContainer)
                    )
                  }
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Stampa
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => 
                    downloadQRCode(
                      `container-qrcode-${selectedContainer.id}`, 
                      selectedContainer.name, 
                      `Container_${selectedContainer.id}`
                    )
                  }
                >
                  <Download className="h-4 w-4 mr-2" />
                  Scarica
                </Button>
              </div>
              <Button onClick={() => setShowQRCode(false)}>Chiudi</Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </GradientBackground>
  );
}
