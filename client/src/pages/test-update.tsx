import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Header } from '@/components/layout/header';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { useLocation } from 'wouter';

// Questa pagina è progettata per testare se gli aggiornamenti al codice funzionano
export default function TestUpdatePage() {
  const [currentTime, setCurrentTime] = useState<string>('');
  const [version, setVersion] = useState<string>('v1.0.0');
  const [, navigate] = useLocation();

  useEffect(() => {
    // Aggiorniamo l'orario corrente ogni secondo per vedere se i componenti si aggiornano
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header title="Test Aggiornamenti" />
      
      <main className="flex-1 container mx-auto p-4 max-w-3xl">
        <Card className="p-6 shadow-lg mb-4">
          <h2 className="text-2xl font-bold mb-4">Test Aggiornamenti Frontend</h2>
          
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <p className="font-medium">Versione corrente: <span className="text-blue-600">{version}</span></p>
            <p className="text-sm text-gray-500">Questo valore deve cambiare se modifichi il codice sorgente</p>
          </div>
          
          <div className="mb-6">
            <p className="font-medium">Orario corrente:</p>
            <p className="text-xl font-mono text-center bg-gray-100 p-2 rounded-md">{currentTime}</p>
            <p className="text-sm text-gray-500 mt-1">Se questo orario si aggiorna, il componente è montato correttamente</p>
          </div>
          
          <div className="mt-8 space-y-3">
            <p className="font-medium">Nuove funzionalità:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Associazione diretta prodotto-contenitore</li>
              <li>Interfaccia semplificata a passi</li>
              <li>Accesso rapido dalla Home</li>
            </ul>
          </div>
          
          <div className="flex justify-between mt-8">
            <Button variant="outline" onClick={() => navigate('/')}>
              Torna alla Home
            </Button>
            
            <Button onClick={() => navigate('/associazione-diretta')}>
              Vai ad Associazione Diretta
            </Button>
          </div>
        </Card>
      </main>
      
      <BottomNavigation />
    </div>
  );
}