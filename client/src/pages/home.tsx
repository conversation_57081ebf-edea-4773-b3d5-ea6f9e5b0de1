import { useState } from "react";
import { useLocation } from "wouter";
import { useSPANavigation } from "@/lib/spa-navigation";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { GradientBackground } from "@/components/layout/gradient-background";
import { Truck, BoxIcon, QrCode, CheckCircle, AlertTriangle, Clock, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";

export default function Home() {
  const { navigate } = useSPANavigation();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [startY, setStartY] = useState(0);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Query per l'ultima attività
  const { data: lastActivity, refetch: refetchActivities } = useQuery({
    queryKey: ['/api/activity-logs'],
    queryFn: async () => {
      const response = await fetch('/api/activity-logs');
      if (!response.ok) throw new Error('Network response was not ok');
      return response.json();
    },
    select: (data: any[]) => data?.[0] // Prende il primo elemento (più recente)
  });

  // Query per il prodotto con scadenza più vicina
  const { data: expiringProduct, refetch: refetchProducts } = useQuery({
    queryKey: ['/api/product-labels'],
    queryFn: async () => {
      const response = await fetch('/api/product-labels');
      if (!response.ok) throw new Error('Network response was not ok');
      return response.json();
    },
    select: (data: any[]) => {
      if (!data || data.length === 0) return null;
      
      const today = new Date();
      const productsWithDates = data
        .filter(product => product.expiryDate)
        .map(product => {
          const { safeParseDateToDate } = require('../../shared/safe-date-utils');
          const expiryDate = safeParseDateToDate(product.expiryDate);
          // Se il parsing fallisce, usa una data futura per non includere il prodotto
          const parsedExpiryDate = expiryDate || new Date(2099, 11, 31);
          return { ...product, parsedExpiryDate };
        })
        .filter(product => product.parsedExpiryDate >= today) // Solo prodotti non ancora scaduti
        .sort((a, b) => a.parsedExpiryDate.getTime() - b.parsedExpiryDate.getTime());

      return productsWithDates[0] || null;
    }
  });

  // Pull-to-refresh functionality
  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (window.scrollY === 0 && startY > 0) {
      const currentY = e.touches[0].clientY;
      const distance = Math.max(0, currentY - startY);
      setPullDistance(Math.min(distance, 100));
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await queryClient.invalidateQueries({ queryKey: ["/api/activity-logs"] });
        await queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });
        await refetchActivities();
        await refetchProducts();
        toast({
          title: "Aggiornamento completato",
          description: "I dati della dashboard sono stati aggiornati",
        });
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Errore",
          description: "Impossibile aggiornare i dati",
        });
      } finally {
        setIsRefreshing(false);
      }
    }
    setPullDistance(0);
    setStartY(0);
  };

  const handleRefreshClick = async () => {
    setIsRefreshing(true);
    try {
      await queryClient.invalidateQueries({ queryKey: ["/api/activity-logs"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });
      await refetchActivities();
      await refetchProducts();
      toast({
        title: "Aggiornamento completato",
        description: "I dati della dashboard sono stati aggiornati",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Errore",
        description: "Impossibile aggiornare i dati",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Prepara i dati per le attività recenti
  const recentActivities = [];

  // Prima card: ultima attività
  if (lastActivity) {
    const activityDate = new Date(lastActivity.timestamp);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const activityDay = new Date(activityDate.getFullYear(), activityDate.getMonth(), activityDate.getDate());
    
    const daysDiff = Math.floor((today.getTime() - activityDay.getTime()) / (1000 * 60 * 60 * 24));
    
    let timeLabel;
    if (daysDiff === 0) {
      timeLabel = "Oggi";
    } else if (daysDiff === 1) {
      timeLabel = "Ieri";
    } else {
      timeLabel = `${daysDiff} giorni fa`;
    }
    
    // Funzione per tradurre le azioni in italiano
    const getReadableAction = (action: string): string => {
      const actionTranslations: { [key: string]: string } = {
        'add_product_to_container': 'Prodotto Aggiunto al Contenitore',
        'create_container': 'Contenitore Creato',
        'scan_product': 'Prodotto Scansionato',
        'scan_ddt': 'DDT Scansionato',
        'create_product_label': 'Etichetta Prodotto Creata',
        'retire_product': 'Prodotto Ritirato',
        'user_login': 'Accesso Utente',
        'user_logout': 'Uscita Utente',
        'create_supplier': 'Fornitore Creato',
        'edit_supplier': 'Fornitore Modificato',
        'delete_supplier': 'Fornitore Eliminato',
        'update_user': 'Utente Aggiornato',
        'login': 'Accesso Utente',
        'process_label_ocr': 'Etichetta Elaborata con OCR'
      };

      return actionTranslations[action] || action;
    };
    
    const activityTime = format(activityDate, 'HH:mm', { locale: it });
    
    recentActivities.push({
      id: 1,
      title: getReadableAction(lastActivity.action),
      status: "completed",
      time: activityTime,
      timeLabel: timeLabel,
      icon: CheckCircle,
      iconColor: "text-green-500"
    });
  }

  // Seconda card: prodotto in scadenza
  if (expiringProduct) {
    const daysUntilExpiry = Math.ceil((expiringProduct.parsedExpiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    const timeLabel = daysUntilExpiry === 0 ? "Oggi" : daysUntilExpiry === 1 ? "Domani" : `${daysUntilExpiry} giorni`;
    
    // Funzione per rendere più leggibile il nome del prodotto
    const getReadableProductName = (productName: string): string => {
      // Mappatura di termini tecnici a nomi più leggibili
      const productTranslations: { [key: string]: string } = {
        'TENERINA SENZA GLUTINE': 'Tenerina Senza Glutine',
        'TENERINI DI VITELLO': 'Tenerini di Vitello',
        'MORTADELLA DI CINGHIALE': 'Mortadella di Cinghiale',
        'MORTADELLA "OPERA" DA 6 KG': 'Mortadella Opera da 6kg',
        'SALAME ROSA': 'Salame Rosa',
      };

      // Se c'è una traduzione specifica, usala
      if (productTranslations[productName.toUpperCase()]) {
        return productTranslations[productName.toUpperCase()];
      }

      // Altrimenti applica la trasformazione generale
      return productName
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
        .replace(/\bDi\b/g, 'di')
        .replace(/\bDa\b/g, 'da')
        .replace(/\bE\b/g, 'e')
        .replace(/\bIl\b/g, 'il')
        .replace(/\bLa\b/g, 'la')
        .replace(/\bDel\b/g, 'del')
        .replace(/\bDella\b/g, 'della')
        .replace(/\bSenza\b/g, 'senza')
        .replace(/\bCon\b/g, 'con')
        .replace(/\bAl\b/g, 'al')
        .replace(/\bAlla\b/g, 'alla');
    };
    
    recentActivities.push({
      id: 2,
      title: `Scadenza Prodotto: ${getReadableProductName(expiringProduct.productName)}`,
      status: "warning",
      time: `Scade ${expiringProduct.expiryDate}`,
      timeLabel: "Vedi",
      icon: AlertTriangle,
      iconColor: daysUntilExpiry <= 1 ? "text-red-500" : "text-amber-500",
      productId: expiringProduct.id // Aggiungo l'ID del prodotto
    });
  }

  return (
    <div 
      className="relative min-h-screen"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: `translateY(${pullDistance * 0.5}px)`,
        transition: pullDistance === 0 ? 'transform 0.3s ease-out' : 'none'
      }}
    >
      <GradientBackground>
        {/* Indicatore pull-to-refresh */}
        {(pullDistance > 0 || isRefreshing) && (
          <div 
            className="fixed top-0 left-0 right-0 flex items-center justify-center bg-primary/10 backdrop-blur-sm z-50 transition-all duration-300"
            style={{
              height: `${Math.min(pullDistance, 80)}px`,
              opacity: pullDistance > 20 ? 1 : pullDistance / 20
            }}
          >
            <div className="flex items-center gap-2 text-primary">
              <RefreshCw 
                className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`}
                style={{
                  transform: `rotate(${pullDistance * 2}deg)`
                }}
              />
              <span className="text-sm font-medium">
                {isRefreshing ? 'Aggiornamento...' : pullDistance > 60 ? 'Rilascia per aggiornare' : 'Trascina per aggiornare'}
              </span>
            </div>
          </div>
        )}

        {/* Overlay glow bianco che aumenta verso l'alto */}
        <div className="absolute top-0 left-0 right-0 h-[120px] bg-gradient-to-t from-transparent via-white/20 to-white/80 pointer-events-none z-30"></div>
        
        <Header title="Home" />

        <main className="flex-1 px-6 overflow-y-auto relative z-10 md:pt-20 md:pb-20">
          {/* Container per centratura dinamica su mobile */}
          <div className="min-h-[calc(100vh-60px)] md:min-h-0 flex flex-col justify-center md:justify-start md:pt-0">
            <div className="w-full max-w-md md:max-w-2xl mx-auto space-y-6 home-content">
          <motion.div 
            initial={{ opacity: 1, y: 0 }}
            whileTap={{ scale: 0.97 }}
            className="w-full rounded-2xl bg-white/90 backdrop-blur-sm p-5 shadow-lg border border-gray-200 overflow-hidden hover:bg-white/95 transition-all duration-200 cursor-pointer spa-transition"
            onClick={(e) => {
              // Feedback aptico immediato
              if ('vibrate' in navigator) {
                navigator.vibrate([5]);
              }
              navigate("/incoming-goods")(e);
            }}
          >
            <div className="flex items-center space-x-5">
              <div className="flex-shrink-0 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl p-3 shadow-inner">
                <Truck className="h-8 w-8 text-indigo-700" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-[16px]">INGRESSO MERCI</h3>
                <p className="text-sm text-gray-600 mt-0.5">Fotografa il DDT e le etichette</p>
              </div>
              <div className="flex-shrink-0 text-gray-400">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 1, y: 0 }}
            whileTap={{ scale: 0.97 }}
            className="w-full rounded-2xl bg-white/90 backdrop-blur-sm p-5 shadow-lg border border-gray-200 overflow-hidden hover:bg-white/95 transition-all duration-200 cursor-pointer spa-transition"
            onClick={(e) => {
              if ('vibrate' in navigator) {
                navigator.vibrate([5]);
              }
              navigate("/containers")(e);
            }}
          >
            <div className="flex items-center space-x-5">
              <div className="flex-shrink-0 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl p-3 shadow-inner">
                <BoxIcon className="h-8 w-8 text-indigo-700" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-[16px]">CONTENITORI</h3>
                <p className="text-sm text-gray-600 mt-0.5">Gestisci contenitori e prodotti</p>
              </div>
              <div className="flex-shrink-0 text-gray-400">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 1, y: 0 }}
            whileTap={{ scale: 0.97 }}
            className="w-full rounded-2xl bg-white/90 backdrop-blur-sm p-5 shadow-lg border border-gray-200 overflow-hidden hover:bg-white/95 transition-all duration-200 cursor-pointer spa-transition"
            onClick={(e) => {
              if ('vibrate' in navigator) {
                navigator.vibrate([5]);
              }
              navigate("/qr-scanner")(e);
            }}
          >
            <div className="flex items-center space-x-5">
              <div className="flex-shrink-0 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl p-3 shadow-inner">
                <QrCode className="h-8 w-8 text-indigo-700" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-[16px]">ASSOCIA PRODOTTI</h3>
                <p className="text-sm text-gray-600 mt-0.5">Collega prodotti ai contenitori</p>
              </div>
              <div className="flex-shrink-0 text-gray-400">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </motion.div>



            </div>
          </div>
        </main>

        <Footer activeItem="home" />
      </GradientBackground>
    </div>
  );
}