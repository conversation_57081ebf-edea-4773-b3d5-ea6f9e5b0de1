import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { clientLogger } from "@/lib/clientLogger";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/context/auth-context";
import { LogOutIcon, SaveIcon, TrashIcon, RefreshCwIcon, ServerIcon, DatabaseIcon, AlertTriangleIcon, DownloadIcon, Cog, WifiOffIcon, HardDriveIcon, CloudIcon, RotateCcwIcon, ShieldIcon, LoaderCircle, Trash2 } from "lucide-react";
import { APP_VERSION } from "@/lib/appUpdateManager";
import { pwaUpdateManager } from "@/lib/pwaUpdateManager";
import { FeedbackList } from "@/components/feedback/feedback-list";

export default function Settings() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user, logout } = useAuth();

  // APP SETTINGS
  const [showScanner, setShowScanner] = useState<boolean>(true);
  const [vibrationFeedback, setVibrationFeedback] = useState<boolean>(true);
  const [soundFeedback, setSoundFeedback] = useState<boolean>(true);
  const [confirmScans, setConfirmScans] = useState<boolean>(true);
  const [lightFeedback, setLightFeedback] = useState<boolean>(true);

  // PWA SETTINGS
  const [offlineMode, setOfflineMode] = useState<boolean>(true);
  const [dataPersistence, setDataPersistence] = useState<boolean>(true);
  const [autoSync, setAutoSync] = useState<boolean>(true);
  const [cacheManagement, setCacheManagement] = useState<boolean>(true);
  const [backgroundSync, setBackgroundSync] = useState<boolean>(false);
  const [settingsSaved, setSettingsSaved] = useState<boolean>(false);
  const [confirmReset, setConfirmReset] = useState<boolean>(false);
  const [isLoadingInitialSettings, setIsLoadingInitialSettings] = useState<boolean>(true);
  const [logRetentionYears, setLogRetentionYears] = useState<number>(2);
  
  // State per il dialog di reset database
  const [showResetDbDialog, setShowResetDbDialog] = useState(false);
  const [resetDbConfirmText, setResetDbConfirmText] = useState("");
  
  // State per la cancellazione cache
  const [isClearing, setIsClearing] = useState(false);

  // CAMERA SETTINGS
  const [cameraFacingMode, setCameraFacingMode] = useState<string>("environment");
  const [cameraResolution, setCameraResolution] = useState<string>("hd");
  const [confirmLogCleanup, setConfirmLogCleanup] = useState<boolean>(false);

  // Mutation per eseguire il reset del database
  const { mutate: resetDatabase, isPending: isResettingDatabase } = useMutation({
    mutationFn: async () => {
      return await apiRequest<{success: boolean, message: string, details: string}>("POST", "/api/system/reset-database");
    },
    onSuccess: () => {
      toast({
        title: "Database ripristinato",
        description: "Il database è stato ripristinato con successo",
      });
      setShowResetDbDialog(false);
      setResetDbConfirmText("");
      // Aggiorna tutti i dati dopo il reset e forza il refresh della pagina
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile ripristinare il database: " + (error as Error).message,
        variant: "destructive",
      });
      setShowResetDbDialog(false);
      setResetDbConfirmText("");
    },
  });

  // Cleanup old logs mutation
  const { mutate: cleanupOldLogs, isPending: isCleaningLogs } = useMutation({
    mutationFn: async (years: number) => {
      return await apiRequest<{success: boolean, count: number}>("POST", "/api/system/cleanup-logs", { years });
    },
    onSuccess: (data) => {
      toast({
        title: "Log eliminati",
        description: `${data.count} log più vecchi di ${logRetentionYears} ${logRetentionYears === 1 ? 'anno' : 'anni'} sono stati eliminati.`,
      });
      setConfirmLogCleanup(false);
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile eliminare i log: " + (error as Error).message,
        variant: "destructive",
      });
      setConfirmLogCleanup(false);
    },
  });

  // Claude models query
  const { data: claudeModels, isLoading: isLoadingModels, error: modelsError } = useQuery({
    queryKey: ["/api/claude/models"],
    queryFn: () => apiRequest("GET", "/api/claude/models"),
    enabled: !!user?.isAdmin,
  });

  // User settings query
  const { data: userSettings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ["/api/users/settings"],
    queryFn: () => apiRequest("GET", "/api/users/settings"),
    enabled: !!user,
  });

  // Gemini models query
  const { data: geminiModels, isLoading: isLoadingGeminiModels, error: geminiModelsError } = useQuery({
    queryKey: ["/api/gemini/models"],
    queryFn: () => apiRequest("GET", "/api/gemini/models"),
    enabled: !!user?.isAdmin,
  });

  // Global settings query (solo per admin)
  const { data: globalSettings, isLoading: isLoadingGlobalSettings } = useQuery({
    queryKey: ["/api/system/global-settings"],
    queryFn: () => apiRequest("GET", "/api/system/global-settings"),
    enabled: !!user?.isAdmin,
  });

  // Selected model state
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedGeminiModel, setSelectedGeminiModel] = useState<string>("gemini-1.5-pro");
  const [selectedAiProvider, setSelectedAiProvider] = useState<string>("claude");

  // Global settings state (solo per admin)
  const [globalDefaultAiProvider, setGlobalDefaultAiProvider] = useState<string>("claude");
  const [globalDefaultClaudeModel, setGlobalDefaultClaudeModel] = useState<string>("claude-3-5-sonnet-20241022");
  const [globalDefaultGeminiModel, setGlobalDefaultGeminiModel] = useState<string>("gemini-1.5-pro");
  const [globalPwaSettings, setGlobalPwaSettings] = useState({
    pwaOfflineMode: true,
    pwaDataPersistence: true,
    pwaAutoSync: true,
    pwaCacheManagement: true,
    pwaBackgroundSync: false,
    pwaPushNotifications: false
  });

  // Update model selection when data is loaded
  useEffect(() => {
    if (claudeModels && typeof claudeModels === 'object' && 'currentModel' in claudeModels) {
      setSelectedModel((claudeModels as any).currentModel || (claudeModels as any).defaultModel);
    }
  }, [claudeModels]);

  // Update all settings when data is loaded from server
  useEffect(() => {
    if (userSettings && typeof userSettings === 'object') {
      clientLogger.debug('UserSettings loaded:', { hasSettings: true });
      const settings = userSettings as any;
      clientLogger.debug('Gemini model from DB:', { model: settings.geminiModel });
      
      // Update AI settings
      setSelectedAiProvider(settings.aiProvider || "claude");
      setSelectedGeminiModel(settings.geminiModel || "gemini-1.5-pro");
      setSelectedModel(settings.claudeModel || "claude-3-5-sonnet-20241022");
      
      // Update camera settings
      setCameraFacingMode(settings.cameraFacingMode || "environment");
      setCameraResolution(settings.cameraResolution || "fullhd");
      
      // Update app settings
      setShowScanner(settings.showScanner ?? false);
      setVibrationFeedback(settings.vibrationFeedback ?? true);
      setSoundFeedback(settings.soundFeedback ?? true);
      setConfirmScans(settings.confirmScans ?? false);
      setLightFeedback(settings.lightFeedback ?? true);
      
      // Update PWA settings
      setOfflineMode(settings.offlineMode ?? true);
      setDataPersistence(settings.dataPersistence ?? true);
      setAutoSync(settings.autoSync ?? true);
      setCacheManagement(settings.cacheManagement ?? true);
      setBackgroundSync(settings.backgroundSync ?? false);
      
      // Mark initial loading as complete
      setIsLoadingInitialSettings(false);
    }
  }, [userSettings]);

  // Update global settings when data is loaded
  useEffect(() => {
    if (globalSettings && typeof globalSettings === 'object') {
      const settings = globalSettings as any;
      setGlobalDefaultAiProvider(settings.defaultAiProvider || "claude");
      setGlobalDefaultClaudeModel(settings.defaultClaudeModel || "claude-3-5-sonnet-20241022");
      setGlobalDefaultGeminiModel(settings.defaultGeminiModel || "gemini-1.5-pro");
      setGlobalPwaSettings({
        pwaOfflineMode: settings.pwaOfflineMode ?? true,
        pwaDataPersistence: settings.pwaDataPersistence ?? true,
        pwaAutoSync: settings.pwaAutoSync ?? true,
        pwaCacheManagement: settings.pwaCacheManagement ?? true,
        pwaBackgroundSync: settings.pwaBackgroundSync ?? false,
        pwaPushNotifications: settings.pwaPushNotifications ?? false
      });
    }
  }, [globalSettings]);

  // Change Claude model mutation
  const { mutate: changeClaudeModel, isPending: isChangingModel } = useMutation({
    mutationFn: async (modelId: string) => {
      return await apiRequest<{success: boolean}>("POST", "/api/claude/model", { modelId });
    },
    onSuccess: () => {
      // Invalida la cache per ricaricare i modelli aggiornati
      queryClient.invalidateQueries({ queryKey: ["/api/claude/models"] });
      toast({
        title: "Modello aggiornato",
        description: "Il modello Claude è stato aggiornato con successo.",
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile aggiornare il modello: " + (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Change AI provider mutation
  const { mutate: changeAiProvider, isPending: isChangingProvider } = useMutation({
    mutationFn: async (provider: string) => {
      return await apiRequest<{success: boolean}>("POST", "/api/users/settings", { aiProvider: provider });
    },
    onSuccess: () => {
      // Invalida la cache per ricaricare le impostazioni aggiornate
      queryClient.invalidateQueries({ queryKey: ["/api/users/settings"] });
      toast({
        title: "Provider AI aggiornato",
        description: `Il provider AI è stato cambiato a ${selectedAiProvider === 'claude' ? 'Claude' : 'Gemini'}.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile aggiornare il provider: " + (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Change Gemini model mutation
  const { mutate: changeGeminiModel, isPending: isChangingGeminiModel } = useMutation({
    mutationFn: async (modelId: string) => {
      return await apiRequest<{success: boolean}>("POST", "/api/users/settings", { geminiModel: modelId });
    },
    onSuccess: (data, modelId) => {
      // Aggiorna immediatamente la cache con il nuovo valore
      queryClient.setQueryData(["/api/users/settings"], (oldData: any) => {
        if (oldData) {
          return { ...oldData, geminiModel: modelId };
        }
        return oldData;
      });
      
      toast({
        title: "Modello Gemini aggiornato",
        description: "Il modello Gemini è stato aggiornato con successo.",
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile aggiornare il modello Gemini: " + (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Update global settings mutation (solo per admin)
  const { mutate: updateGlobalSettings, isPending: isUpdatingGlobalSettings } = useMutation({
    mutationFn: async (settings: any) => {
      return await apiRequest<{success: boolean, settings: any, message: string}>("PATCH", "/api/system/global-settings", settings);
    },
    onSuccess: (data) => {
      // Invalida la cache per ricaricare le impostazioni aggiornate
      queryClient.invalidateQueries({ queryKey: ["/api/system/global-settings"] });
      queryClient.invalidateQueries({ queryKey: ["/api/users/settings"] }); // Anche le impostazioni utente potrebbero essere cambiate
      toast({
        title: "Impostazioni Globali Aggiornate",
        description: data.message || "Le impostazioni globali sono state aggiornate e sincronizzate con tutti gli utenti.",
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Impossibile aggiornare le impostazioni globali: " + (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Save settings to server
  const saveSettings = async () => {
    try {
      setSettingsSaved(true);
      
      const settingsUpdate = {
        showScanner,
        vibrationFeedback,
        soundFeedback,
        confirmScans,
        lightFeedback,
        cameraFacingMode,
        cameraResolution,
        // Include AI settings
        aiProvider: selectedAiProvider,
        claudeModel: selectedModel,
        geminiModel: selectedGeminiModel,
        // Include PWA settings
        offlineMode,
        dataPersistence,
        autoSync,
        cacheManagement,
        backgroundSync
      };

      clientLogger.debug("Sending settings update:", { updateKeys: Object.keys(settingsUpdate) });
      const response = await apiRequest("/api/users/settings", "POST", settingsUpdate);
      clientLogger.info("Settings saved successfully:", { success: response.success });
      
      // Update query cache
      queryClient.invalidateQueries({ queryKey: ["/api/users/settings"] });
      
      toast({
        title: "Impostazioni salvate",
        description: "Le tue preferenze sono state salvate con successo.",
      });
      
      setTimeout(() => setSettingsSaved(false), 2000);
    } catch (error) {
      setSettingsSaved(false);
      toast({
        variant: "destructive",
        title: "Errore nel salvataggio",
        description: "Impossibile salvare le impostazioni. Riprova.",
      });
      clientLogger.error("Error saving settings:", { error: error.message });
    }
  };

  // Reset settings
  const resetSettings = () => {
    if (confirmReset) {
      setShowScanner(true);
      setVibrationFeedback(true);
      setSoundFeedback(true);
      setConfirmScans(true);
      setLightFeedback(true);
      setCameraFacingMode("environment");
      setCameraResolution("hd");
      
      localStorage.removeItem("settings");
      
      toast({
        title: "Impostazioni reimpostate",
        description: "Tutte le impostazioni sono state ripristinate ai valori predefiniti.",
      });
      
      setConfirmReset(false);
    } else {
      setConfirmReset(true);
      setTimeout(() => setConfirmReset(false), 3000);
    }
  };

  // Funzione completa di cancellazione cache e aggiornamento PWA
  const clearCacheAndUpdate = async () => {
    setIsClearing(true);
    
    try {
      // 1. Cancellazione di tutte le cache dell'app
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // 2. Cancellazione localStorage e sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // 3. Cancellazione IndexedDB (se presente)
      if ('indexedDB' in window) {
        try {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => {
              if (db.name) {
                return new Promise<void>((resolve, reject) => {
                  const deleteReq = indexedDB.deleteDatabase(db.name!);
                  deleteReq.onsuccess = () => resolve();
                  deleteReq.onerror = () => reject(deleteReq.error);
                });
              }
              return Promise.resolve();
            })
          );
        } catch (error) {
          clientLogger.warn('Errore durante la cancellazione IndexedDB:', { error: error.message });
        }
      }

      // 4. Aggiornamento forzato del service worker
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.ready;
          await registration.update();
          
          if (registration.waiting) {
            registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          }
          
          // Disregistra e re-registra il service worker per un aggiornamento completo
          const registrations = await navigator.serviceWorker.getRegistrations();
          for (const reg of registrations) {
            await reg.unregister();
          }
          
          // Re-registra il service worker
          await navigator.serviceWorker.register('/service-worker.js', {
            updateViaCache: 'none'
          });
        } catch (error) {
          clientLogger.warn('Errore durante l\'aggiornamento del service worker:', { error: error.message });
        }
      }

      // 5. Messaggio di successo
      toast({
        title: "Aggiornamento completato",
        description: "Cache cancellate e PWA aggiornata con successo. L'app verrà ricaricata.",
        variant: "default",
      });

      // 6. Ricarica l'app dopo un breve delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      clientLogger.error('Errore durante la cancellazione cache:', { error: error.message });
      toast({
        title: "Errore",
        description: "Si è verificato un errore durante la cancellazione delle cache.",
        variant: "destructive",
      });
    } finally {
      setIsClearing(false);
    }
  };

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem("settings");
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      setShowScanner(parsed.showScanner ?? true);
      setVibrationFeedback(parsed.vibrationFeedback ?? true);
      setSoundFeedback(parsed.soundFeedback ?? true);
      setConfirmScans(parsed.confirmScans ?? true);
      setLightFeedback(parsed.lightFeedback ?? true);
      setCameraFacingMode(parsed.cameraFacingMode ?? "environment");
      setCameraResolution(parsed.cameraResolution ?? "hd");
    }
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-[#f5f5f7]">
      <Header title="Impostazioni" showBack backPath="/" />

      <main className="flex-1 pt-24 pb-[calc(4rem+60px)] px-4">
        <div className="max-w-xl mx-auto space-y-6">


          {/* Sezione Account */}
          <Card className="border-0 shadow-md bg-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold">Account</CardTitle>
              <CardDescription className="text-gray-600">
                Informazioni sull'account e ruolo nel sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between py-2">
                <div>
                  <p className="font-medium">{user?.username}</p>
                  <p className="text-sm text-gray-500">{user?.email || "Nessuna email"}</p>
                  <p className="text-xs text-blue-600 font-medium mt-1">
                    {user?.isAdmin ? "Amministratore" : 
                     user?.role === "manager" ? "Manager" : "Utente Standard"}
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                  onClick={() => {
                    logout();
                    setLocation("/login");
                  }}
                >
                  <LogOutIcon className="w-4 h-4 mr-2" />
                  Logout
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Sezione Aggiornamento App */}
          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <DownloadIcon className="w-5 h-5 text-orange-600" />
                <CardTitle className="text-xl font-semibold text-orange-900">Aggiornamento App</CardTitle>
              </div>
              <CardDescription className="text-orange-700">
                Gestisci gli aggiornamenti dell'applicazione
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between py-2 mb-4">
                <div>
                  <p className="font-medium text-orange-900">Versione corrente</p>
                  <p className="text-sm text-orange-700">HACCP Tracker v{APP_VERSION}</p>
                </div>
              </div>
              
              <div className="text-center">
                <Button 
                  variant="outline" 
                  className="bg-orange-600 text-white border-orange-700 hover:bg-orange-700 w-full"
                  onClick={async () => {
                    toast({
                      title: "🔄 Aggiornamento forzato",
                      description: "Forzatura aggiornamento app in corso...",
                      duration: 3000,
                    });
                    await pwaUpdateManager.forceUpdate();
                  }}
                >
                  <DownloadIcon className="w-4 h-4 mr-2" />
                  Forza Aggiornamento App
                </Button>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Usa questo pulsante se l'app non si aggiorna automaticamente
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Sezione Gestione Utenti (solo Manager e Admin) */}
          {(user?.isAdmin || user?.role === "admin" || user?.role === "manager") && (
            <Card className="border-0 shadow-md bg-gradient-to-br from-sky-50 to-blue-50 border-sky-200">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-sky-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                  <CardTitle className="text-xl font-semibold text-sky-900">Gestione Utenti</CardTitle>
                </div>
                <CardDescription className="text-sky-700">
                  {user?.role === "manager" ? 
                    "Gestisci utenti standard e manager (non amministratori)" : 
                    "Gestisci tutti gli utenti del sistema"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  variant="outline" 
                  className="bg-sky-600 text-white border-sky-700 hover:bg-sky-700 w-full justify-start group"
                  onClick={() => setLocation("/users")}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                  Gestione Utenti
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Sezione Catalogo Tipi Contenitori (per tutti) */}
          <Card className="border-0 shadow-md bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="2" y="7" width="20" height="14" rx="2" ry="2" />
                  <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16" />
                </svg>
                <CardTitle className="text-xl font-semibold text-teal-900">Catalogo Tipi Contenitori</CardTitle>
              </div>
              <CardDescription className="text-teal-700">
                Gestisci i tipi di contenitori disponibili nel sistema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                className="bg-teal-600 text-white border-teal-700 hover:bg-teal-700 w-full justify-start group"
                onClick={() => setLocation("/container-types")}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="2" y="7" width="20" height="14" rx="2" ry="2" />
                  <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16" />
                </svg>
                Tipi Contenitori
              </Button>
            </CardContent>
          </Card>

          {/* Sezione Impostazioni Globali (solo admin) */}
          {user?.isAdmin && (
            <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <ShieldIcon className="w-5 h-5 text-purple-600" />
                  <CardTitle className="text-xl font-semibold text-purple-900">Impostazioni Globali</CardTitle>
                </div>
                <CardDescription className="text-purple-700">
                  Configura le impostazioni predefinite per tutti gli utenti del sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Modelli AI Globali */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <ServerIcon className="w-4 h-4 text-purple-600" />
                    <h3 className="text-lg font-medium text-purple-900">Modelli AI Predefiniti</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    {/* Provider AI predefinito */}
                    <div className="space-y-2">
                      <Label htmlFor="global-ai-provider" className="text-sm font-medium text-purple-800">
                        Provider AI Predefinito
                      </Label>
                      <Select value={globalDefaultAiProvider} onValueChange={setGlobalDefaultAiProvider}>
                        <SelectTrigger className="bg-white border-purple-200">
                          <SelectValue placeholder="Seleziona provider..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="claude">Claude (Anthropic)</SelectItem>
                          <SelectItem value="gemini">Gemini (Google)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Modello Claude predefinito */}
                    <div className="space-y-2">
                      <Label htmlFor="global-claude-model" className="text-sm font-medium text-purple-800">
                        Modello Claude Predefinito
                      </Label>
                      <Select value={globalDefaultClaudeModel} onValueChange={setGlobalDefaultClaudeModel}>
                        <SelectTrigger className="bg-white border-purple-200">
                          <SelectValue placeholder="Seleziona modello..." />
                        </SelectTrigger>
                        <SelectContent>
                          {(claudeModels as any)?.models?.map((model: any, index: number) => (
                            <SelectItem key={`global-claude-${model.id || index}`} value={model.id}>
                              {model.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Modello Gemini predefinito */}
                    <div className="space-y-2">
                      <Label htmlFor="global-gemini-model" className="text-sm font-medium text-purple-800">
                        Modello Gemini Predefinito
                      </Label>
                      <Select value={globalDefaultGeminiModel} onValueChange={setGlobalDefaultGeminiModel}>
                        <SelectTrigger className="bg-white border-purple-200">
                          <SelectValue placeholder="Seleziona modello..." />
                        </SelectTrigger>
                        <SelectContent>
                          {(geminiModels as any)?.models?.map((model: any, index: number) => (
                            <SelectItem key={`global-gemini-${model.id || index}`} value={model.id}>
                              {model.displayName || model.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Funzionalità PWA Globali */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <CloudIcon className="w-4 h-4 text-purple-600" />
                    <h3 className="text-lg font-medium text-purple-900">Funzionalità PWA Globali</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Modalità Offline</Label>
                        <p className="text-xs text-purple-600">Abilita il funzionamento offline dell'app</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaOfflineMode}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaOfflineMode: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Persistenza Dati</Label>
                        <p className="text-xs text-purple-600">Salva i dati localmente per l'offline</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaDataPersistence}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaDataPersistence: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Sincronizzazione Automatica</Label>
                        <p className="text-xs text-purple-600">Sincronizza automaticamente quando online</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaAutoSync}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaAutoSync: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Gestione Cache</Label>
                        <p className="text-xs text-purple-600">Gestione intelligente della cache</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaCacheManagement}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaCacheManagement: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Sincronizzazione in Background</Label>
                        <p className="text-xs text-purple-600">Sincronizza i dati in background</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaBackgroundSync}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaBackgroundSync: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-purple-800">Notifiche Push</Label>
                        <p className="text-xs text-purple-600">Abilita le notifiche push</p>
                      </div>
                      <Switch
                        checked={globalPwaSettings.pwaPushNotifications}
                        onCheckedChange={(checked) => 
                          setGlobalPwaSettings(prev => ({ ...prev, pwaPushNotifications: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Link per gestione prompt AI */}
                <div className="pt-4 border-t border-purple-200">
                  <Link href="/prompts" className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors mb-4 w-full justify-center">
                    <Cog className="h-4 w-4 mr-2" />
                    Gestisci Prompt AI
                  </Link>
                  <p className="text-xs text-purple-600 mb-4 text-center">
                    Personalizza i prompt per analisi DDT ed etichette prodotti
                  </p>
                </div>

                {/* Pulsante Salva Impostazioni Globali */}
                <div className="pt-4 border-t border-purple-200">
                  <Button
                    onClick={() => {
                      const globalSettingsUpdate = {
                        defaultAiProvider: globalDefaultAiProvider,
                        defaultClaudeModel: globalDefaultClaudeModel,
                        defaultGeminiModel: globalDefaultGeminiModel,
                        ...globalPwaSettings
                      };
                      updateGlobalSettings(globalSettingsUpdate);
                    }}
                    disabled={isUpdatingGlobalSettings}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                  >
                    {isUpdatingGlobalSettings ? (
                      <>
                        <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                        Aggiornamento in corso...
                      </>
                    ) : (
                      <>
                        <SaveIcon className="w-4 h-4 mr-2" />
                        Salva Impostazioni Globali
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-purple-600 mt-2 text-center">
                    Le modifiche saranno applicate automaticamente a tutti gli utenti
                  </p>
                </div>
              </CardContent>
            </Card>
          )}



          {/* Sezione Fotocamera */}
          <Card className="border-0 shadow-md bg-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold">Fotocamera</CardTitle>
              <CardDescription className="text-gray-600">
                Configura le impostazioni della fotocamera
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="facing-mode">Fotocamera predefinita</Label>
                  <p className="text-sm text-gray-500">
                    Scegli quale fotocamera utilizzare per default
                  </p>
                </div>
                <Select value={cameraFacingMode} onValueChange={setCameraFacingMode}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Scegli..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="environment">Posteriore</SelectItem>
                    <SelectItem value="user">Frontale</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="resolution">Risoluzione</Label>
                  <p className="text-sm text-gray-500">
                    Qualità della fotocamera
                  </p>
                </div>
                <Select value={cameraResolution} onValueChange={setCameraResolution}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Scegli..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hd">HD (720p)</SelectItem>
                    <SelectItem value="fullhd">Full HD (1080p)</SelectItem>
                    <SelectItem value="4k">4K (se supportato)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Sezione PWA e Gestione Offline (solo per admin) */}
          {user?.isAdmin && (
            <Card className="border-0 shadow-md bg-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <WifiOffIcon className="h-5 w-5 text-blue-600" />
                  Funzionalità PWA
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Gestisci le funzionalità avanzate dell'applicazione web progressiva
                </CardDescription>
              </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="offline-mode" className="flex items-center gap-2">
                    <WifiOffIcon className="h-4 w-4" />
                    Modalità Offline
                  </Label>
                  <p className="text-sm text-gray-500">
                    Consenti l'utilizzo dell'app senza connessione internet
                  </p>
                </div>
                <Switch
                  id="offline-mode"
                  checked={offlineMode}
                  onCheckedChange={setOfflineMode}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="data-persistence" className="flex items-center gap-2">
                    <HardDriveIcon className="h-4 w-4" />
                    Persistenza Dati
                  </Label>
                  <p className="text-sm text-gray-500">
                    Salva i dati localmente per accesso rapido
                  </p>
                </div>
                <Switch
                  id="data-persistence"
                  checked={dataPersistence}
                  onCheckedChange={setDataPersistence}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-sync" className="flex items-center gap-2">
                    <CloudIcon className="h-4 w-4" />
                    Sincronizzazione Automatica
                  </Label>
                  <p className="text-sm text-gray-500">
                    Sincronizza automaticamente quando possibile
                  </p>
                </div>
                <Switch
                  id="auto-sync"
                  checked={autoSync}
                  onCheckedChange={setAutoSync}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="cache-management" className="flex items-center gap-2">
                    <DatabaseIcon className="h-4 w-4" />
                    Gestione Cache
                  </Label>
                  <p className="text-sm text-gray-500">
                    Gestione intelligente della cache dati
                  </p>
                </div>
                <Switch
                  id="cache-management"
                  checked={cacheManagement}
                  onCheckedChange={setCacheManagement}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="background-sync" className="flex items-center gap-2">
                    <RotateCcwIcon className="h-4 w-4" />
                    Sincronizzazione in Background
                  </Label>
                  <p className="text-sm text-gray-500">
                    Sincronizza i dati anche quando l'app è chiusa
                  </p>
                </div>
                <Switch
                  id="background-sync"
                  checked={backgroundSync}
                  onCheckedChange={setBackgroundSync}
                />
              </div>

              {/* Informazioni sulla cache */}
              <div className="pt-4 border-t">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900 flex items-center gap-2">
                    <ShieldIcon className="h-4 w-4" />
                    Informazioni Cache
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="font-medium text-gray-700">Cache Prodotti</div>
                      <div className="text-gray-500">Aggiornata automaticamente</div>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="font-medium text-gray-700">Cache Contenitori</div>
                      <div className="text-gray-500">Aggiornata automaticamente</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sezione Aggiornamento e Cache */}
              <div className="pt-4 border-t">
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-3">
                      <Trash2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="font-medium text-blue-900 mb-1">Aggiornamento completo</h4>
                        <p className="text-sm text-blue-700 mb-2">
                          Cancella tutte le cache e forza l'aggiornamento della PWA
                        </p>
                        <ul className="text-xs text-blue-600 space-y-1">
                          <li>• Cache del browser e service worker</li>
                          <li>• Dati memorizzati localmente</li>
                          <li>• Database locale dell'app</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={clearCacheAndUpdate}
                    disabled={isClearing}
                    className="w-full rounded-lg py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium"
                  >
                    {isClearing ? (
                      <div className="flex items-center">
                        <LoaderCircle className="animate-spin h-4 w-4 mr-2" />
                        Aggiornamento in corso...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <RefreshCwIcon className="h-4 w-4 mr-2" />
                        Cancella cache e aggiorna PWA
                      </div>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          )}



          {/* Sezione App */}
          <Card className="border-0 shadow-md bg-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold">App</CardTitle>
              <CardDescription className="text-gray-600">
                Personalizza il comportamento dell'applicazione
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="scanner-toggle">Scanner QR</Label>
                  <p className="text-sm text-gray-500">
                    Mostra scanner nella barra inferiore
                  </p>
                </div>
                <Switch
                  id="scanner-toggle"
                  checked={showScanner}
                  onCheckedChange={setShowScanner}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="vibration-feedback">Feedback vibrazione</Label>
                  <p className="text-sm text-gray-500">
                    Vibra alla scansione di un codice
                  </p>
                </div>
                <Switch
                  id="vibration-feedback"
                  checked={vibrationFeedback}
                  onCheckedChange={setVibrationFeedback}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sound-feedback">Feedback sonoro</Label>
                  <p className="text-sm text-gray-500">
                    Emetti suono alla scansione
                  </p>
                </div>
                <Switch
                  id="sound-feedback"
                  checked={soundFeedback}
                  onCheckedChange={setSoundFeedback}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="confirm-scans">Conferma scansioni</Label>
                  <p className="text-sm text-gray-500">
                    Richiedi conferma dopo la scansione
                  </p>
                </div>
                <Switch
                  id="confirm-scans"
                  checked={confirmScans}
                  onCheckedChange={setConfirmScans}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="light-feedback">Feedback luminosità</Label>
                  <p className="text-sm text-gray-500">
                    Mostra avvisi sul livello di luce
                  </p>
                </div>
                <Switch
                  id="light-feedback"
                  checked={lightFeedback}
                  onCheckedChange={setLightFeedback}
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Sezione Feedback Utenti - Solo per Admin */}
          {user?.isAdmin && (
            <FeedbackList />
          )}

          {/* Sezione Sistema (solo per admin) */}
          {user?.isAdmin && (
            <Card className="border-0 shadow-md bg-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-semibold">Sistema</CardTitle>
                <CardDescription className="text-gray-600">
                  Funzionalità avanzate di amministrazione
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Gestione Log Attività - solo per SuperAdmin */}
                {user?.isAdmin && user?.username === "admin" && (
                  <div className="py-2">
                    <div className="mb-4">
                      <p className="font-medium">Conservazione Log Attività</p>
                      <p className="text-sm text-gray-500">
                        Configura per quanto tempo conservare i log delle attività. Attualmente conserviamo:
                        <br />• Accessi utenti: {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                        <br />• Modifiche prodotti: {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                        <br />• Operazioni etichette: {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                        <br />• Scansioni QR: {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-4 mb-4">
                      <Label htmlFor="log-retention">Periodo di conservazione:</Label>
                      <Select 
                        value={logRetentionYears.toString()}
                        onValueChange={(value) => setLogRetentionYears(parseInt(value))}
                      >
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Scegli periodo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 anno</SelectItem>
                          <SelectItem value="2">2 anni</SelectItem>
                          <SelectItem value="3">3 anni</SelectItem>
                          <SelectItem value="5">5 anni</SelectItem>
                          <SelectItem value="7">7 anni</SelectItem>
                          <SelectItem value="10">10 anni</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <Button
                      type="button"
                      variant={confirmLogCleanup ? "destructive" : "outline"}
                      disabled={isCleaningLogs}
                      onClick={() => {
                        if (confirmLogCleanup) {
                          cleanupOldLogs(logRetentionYears);
                        } else {
                          setConfirmLogCleanup(true);
                          // Reset lo stato di conferma dopo 5 secondi
                          setTimeout(() => setConfirmLogCleanup(false), 5000);
                        }
                      }}
                    >
                      {confirmLogCleanup ? (
                        <>
                          <AlertTriangleIcon className="mr-2 h-4 w-4" />
                          Conferma eliminazione log più vecchi di {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                        </>
                      ) : (
                        <>
                          <TrashIcon className="mr-2 h-4 w-4" />
                          Elimina log più vecchi di {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'}
                        </>
                      )}
                    </Button>
                    
                    <p className="text-xs text-gray-500 mt-2">
                      I log più vecchi di {logRetentionYears} {logRetentionYears === 1 ? 'anno' : 'anni'} verranno eliminati definitivamente. Questa operazione non può essere annullata.
                    </p>
                  </div>
                )}
                
                {/* Reset Database */}
                <div className="py-2">
                  <div className="mb-4">
                    <p className="font-medium">Reset Database</p>
                    <p className="text-sm text-gray-500">
                      Ripristina completamente il database, eliminando tutti i dati.
                      <br />
                      <strong className="text-orange-600">ATTENZIONE:</strong> Questa operazione non è reversibile.
                    </p>
                  </div>
                  
                  <Button 
                    variant="outline"
                    className="bg-red-600 text-white border-red-700 hover:bg-red-700 w-full justify-start"
                    onClick={() => setShowResetDbDialog(true)}
                  >
                    <DatabaseIcon className="mr-2 h-4 w-4" />
                    Reset Database
                  </Button>
                </div>

                {/* Server Info */}
                <div className="py-2 border-t border-gray-200 mt-4 pt-4">
                  <div className="mb-2">
                    <p className="font-medium">Info Server</p>
                    <p className="text-sm text-gray-500">
                      Informazioni di sistema e diagnostica
                    </p>
                  </div>
                  
                  <Button 
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setLocation("/debug")}
                  >
                    <ServerIcon className="mr-2 h-4 w-4" />
                    Visualizza Info Diagnostiche
                  </Button>
                </div>
                
                <div className="py-2 border-t border-gray-200 mt-4 pt-4">
                  <div className="mb-2">
                    <p className="font-medium">Stato Servizi</p>
                    <p className="text-sm text-gray-500">
                      Monitora lo stato di tutti i servizi automatizzati del sistema
                    </p>
                  </div>
                  
                  <Button 
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setLocation("/system-status")}
                  >
                    <Cog className="mr-2 h-4 w-4" />
                    Visualizza Stato Servizi
                  </Button>
                </div>
                
                <div className="py-2 border-t border-gray-200 mt-4 pt-4">
                  <div className="mb-2">
                    <p className="font-medium">Log Attività</p>
                    <p className="text-sm text-gray-500">
                      Visualizza le attività recenti degli utenti
                    </p>
                  </div>
                  
                  <Button 
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setLocation("/activities")}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 8v4l3 3" />
                      <circle cx="12" cy="12" r="10" />
                    </svg>
                    Visualizza Log Attività
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bottoni di azione */}
          <div className="flex space-x-3 mb-[30px]">
            <Button 
              className="flex-1"
              onClick={saveSettings}
              disabled={settingsSaved}
            >
              {settingsSaved ? (
                "Salvato ✓"
              ) : (
                <>
                  <SaveIcon className="mr-2 h-4 w-4" />
                  Salva Impostazioni
                </>
              )}
            </Button>
            
            <Button
              variant={confirmReset ? "destructive" : "outline"}
              className="flex-1"
              onClick={resetSettings}
            >
              {confirmReset ? (
                <>
                  <AlertTriangleIcon className="mr-2 h-4 w-4" />
                  Conferma Reset
                </>
              ) : (
                <>
                  <RefreshCwIcon className="mr-2 h-4 w-4" />
                  Reset Impostazioni
                </>
              )}
            </Button>
          </div>
        </div>
      </main>

      <Footer />

      {/* Database Reset Confirmation Dialog */}
      <Dialog open={showResetDbDialog} onOpenChange={setShowResetDbDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-red-600">Conferma Reset Database</DialogTitle>
            <DialogDescription>
              Questa azione eliminerà <strong>tutti i dati</strong> dal database. L'operazione non è reversibile.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div>
              <p className="text-sm text-gray-700 mb-2">
                Per confermare, digita "RESET" nel campo sottostante:
              </p>
              <Input
                placeholder="Digita RESET per confermare"
                value={resetDbConfirmText}
                onChange={(e) => setResetDbConfirmText(e.target.value)}
                className="border-red-200 focus:border-red-400"
              />
            </div>
            
            <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
              <p className="text-amber-800 text-sm">
                ⚠️ ATTENZIONE: Questa operazione eliminerà tutti i contenitori, prodotti, fornitori, etichette e altre informazioni dal database. Gli utenti e le impostazioni di sistema verranno mantenuti.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowResetDbDialog(false);
                setResetDbConfirmText("");
              }}
            >
              Annulla
            </Button>
            <Button
              variant="destructive"
              disabled={resetDbConfirmText !== "RESET" || isResettingDatabase}
              onClick={() => resetDatabase()}
              className={isResettingDatabase ? "opacity-70" : ""}
            >
              {isResettingDatabase ? (
                <>
                  <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                  Ripristino in corso...
                </>
              ) : (
                "Ripristina Database"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}