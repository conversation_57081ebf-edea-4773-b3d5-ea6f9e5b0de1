import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { SimpleProductSelector } from '@/components/ui/simple-product-selector';
import { SimpleContainerSelector } from '@/components/ui/simple-container-selector';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckCircle, ArrowLeft } from 'lucide-react';
import { Link } from 'wouter';
import { toast } from '@/hooks/use-toast';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { Header } from '@/components/layout/header';
import { GradientBackground } from '@/components/layout/gradient-background';

// Enumeration delle fasi di associazione
enum AssociationPhase {
  PRODUCT = 'product',
  CONTAINER = 'container',
  CONFIRMATION = 'confirmation',
  COMPLETED = 'completed'
}

export default function AssociazioneSemplicePage() {
  // Stato dell'applicazione
  const [phase, setPhase] = useState<AssociationPhase>(AssociationPhase.PRODUCT);
  const [selectedProduct, setSelectedProduct] = useState<{id: number, name: string} | null>(null);
  const [selectedContainer, setSelectedContainer] = useState<{id: number, name: string} | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  useEffect(() => {
    console.log("Associazione Semplice component loaded! TEST v3");
    console.log("Current phase:", phase);
  }, [phase]);
  
  const queryClient = useQueryClient();
  
  // Gestisce la selezione di un prodotto
  const handleProductSelect = (productId: number, productName: string) => {
    console.log(`Prodotto selezionato: ${productName} (${productId})`);
    setSelectedProduct({ id: productId, name: productName });
    setPhase(AssociationPhase.CONTAINER);
    
    toast({
      title: 'Prodotto selezionato',
      description: `Hai scelto: ${productName}`,
    });
  };
  
  // Gestisce la selezione di un contenitore
  const handleContainerSelect = (containerId: number, containerName: string) => {
    console.log(`Contenitore selezionato: ${containerName} (${containerId})`);
    setSelectedContainer({ id: containerId, name: containerName });
    setPhase(AssociationPhase.CONFIRMATION);
    
    toast({
      title: 'Contenitore selezionato',
      description: `Hai scelto: ${containerName}`,
    });
  };
  
  // Completa l'associazione
  const completeAssociation = async () => {
    if (!selectedProduct || !selectedContainer) {
      toast({
        title: 'Errore',
        description: 'Mancano i dati necessari per l\'associazione',
        variant: 'destructive'
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/container-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productLabelId: selectedProduct.id,
          containerId: selectedContainer.id
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        // Se il prodotto è già associato, non è un errore vero e proprio
        if (response.status === 400 && errorData?.message?.includes("already associated")) {
          toast({
            title: 'Informazione',
            description: errorData.message || 'Il prodotto è già associato a questo contenitore.',
            variant: 'default',
          });
          setPhase(AssociationPhase.COMPLETED);
          return;
        }
        
        throw new Error(`Errore API: ${response.status}`);
      }
      
      toast({
        title: 'Successo',
        description: 'Associazione completata con successo',
      });
      
      // Invalidare le query relevanti
      queryClient.invalidateQueries({ queryKey: ['/api/container-products'] });
      queryClient.invalidateQueries({ queryKey: ['/api/containers', selectedContainer.id] });
      
      // Passaggio alla fase completata
      setPhase(AssociationPhase.COMPLETED);
    } catch (error) {
      toast({
        title: 'Errore',
        description: `Impossibile completare l'associazione: ${error instanceof Error ? error.message : 'Errore sconosciuto'}`,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Torna alla fase precedente
  const goBack = () => {
    if (phase === AssociationPhase.CONTAINER) {
      setPhase(AssociationPhase.PRODUCT);
      setSelectedProduct(null);
    } else if (phase === AssociationPhase.CONFIRMATION) {
      setPhase(AssociationPhase.CONTAINER);
      setSelectedContainer(null);
    }
  };
  
  // Ricomincia da capo
  const restart = () => {
    setPhase(AssociationPhase.PRODUCT);
    setSelectedProduct(null);
    setSelectedContainer(null);
  };
  
  // Renderizza la fase corrente
  const renderPhase = () => {
    switch (phase) {
      case AssociationPhase.PRODUCT:
        return (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Seleziona un Prodotto</h2>
            <p className="text-gray-600 mb-6">
              Scegli un prodotto dal menu a tendina per iniziare l'associazione.
            </p>
            <SimpleProductSelector onSelect={handleProductSelect} />
          </Card>
        );
        
      case AssociationPhase.CONTAINER:
        return (
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Seleziona un Contenitore</h2>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={goBack}
                className="flex items-center gap-1"
              >
                <ArrowLeft size={16} />
                <span>Indietro</span>
              </Button>
            </div>
            
            <div className="mb-6 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-700 font-medium">
                Prodotto selezionato: {selectedProduct?.name}
              </p>
              <p className="text-xs text-blue-500">
                Ora scegli un contenitore dove posizionare questo prodotto
              </p>
            </div>
            
            <SimpleContainerSelector 
              onSelect={handleContainerSelect} 
              productId={selectedProduct?.id} 
            />
          </Card>
        );
        
      case AssociationPhase.CONFIRMATION:
        return (
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Conferma Associazione</h2>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={goBack}
                disabled={isSubmitting}
                className="flex items-center gap-1"
              >
                <ArrowLeft size={16} />
                <span>Indietro</span>
              </Button>
            </div>
            
            <div className="mb-6">
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Prodotto:</h3>
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="font-medium">{selectedProduct?.name}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Contenitore:</h3>
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="font-medium">{selectedContainer?.name}</p>
                </div>
              </div>
            </div>
            
            <Button 
              onClick={completeAssociation}
              disabled={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? 'Associazione in corso...' : 'Conferma Associazione'}
            </Button>
          </Card>
        );
        
      case AssociationPhase.COMPLETED:
        return (
          <Card className="p-6">
            <div className="flex flex-col items-center text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
              <h2 className="text-xl font-semibold mb-2">Associazione Completata</h2>
              <p className="text-gray-600 mb-6">
                L'associazione è stata completata con successo!
              </p>
              
              <div className="bg-green-50 p-4 rounded-md border border-green-200 mb-6 text-left w-full">
                <h3 className="text-green-800 font-medium mb-2">Riepilogo:</h3>
                <p><span className="font-medium">Prodotto:</span> {selectedProduct?.name}</p>
                <p><span className="font-medium">Contenitore:</span> {selectedContainer?.name}</p>
              </div>
              
              <Button onClick={restart} className="w-full">
                Associa un altro prodotto
              </Button>
            </div>
          </Card>
        );
    }
  };
  
  return (
    <GradientBackground>
      <Header title="Associazione Semplificata" />
      
      <main className="flex-1 pt-24 pb-20 px-4 overflow-y-auto relative z-10">
        <div className="max-w-md mx-auto">
          {/* Indicatore fasi */}
          <div className="flex mb-6">
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  phase === AssociationPhase.PRODUCT || phase === AssociationPhase.CONTAINER || phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {phase === AssociationPhase.CONTAINER || phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  1
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  phase === AssociationPhase.PRODUCT ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Prodotto
              </span>
            </div>
            
            <div className="flex-1 flex items-center">
              <div 
                className={`h-0.5 w-full ${
                  phase === AssociationPhase.CONTAINER || phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED
                    ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            </div>
            
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  phase === AssociationPhase.CONTAINER || phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  2
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  phase === AssociationPhase.CONTAINER ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Contenitore
              </span>
            </div>
            
            <div className="flex-1 flex items-center">
              <div 
                className={`h-0.5 w-full ${
                  phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED
                    ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            </div>
            
            <div className="flex flex-col items-center">
              <div 
                className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                  phase === AssociationPhase.CONFIRMATION || phase === AssociationPhase.COMPLETED
                    ? 'bg-blue-500 text-white border-blue-500' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}
              >
                {phase === AssociationPhase.COMPLETED ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  3
                )}
              </div>
              <span 
                className={`text-xs mt-1 ${
                  phase === AssociationPhase.CONFIRMATION ? 'text-blue-500 font-medium' : 'text-gray-500'
                }`}
              >
                Conferma
              </span>
            </div>
          </div>
          
          {renderPhase()}
          
          <div className="mt-6 text-center">
            <Link href="/qr-scanner">
              <Button variant="outline">
                Torna allo Scanner QR
              </Button>
            </Link>
          </div>
        </div>
      </main>
      
      <BottomNavigation activeItem="qrscan" />
    </GradientBackground>
  );
}