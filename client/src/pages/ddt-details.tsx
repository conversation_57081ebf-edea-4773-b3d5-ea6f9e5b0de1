import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { DDT, ProductLabel } from "@shared/schema";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, FileText, QrCode, Package, X, Download, Printer, Eye } from "lucide-react";
import { formatDate, downloadQRCode } from "@/lib/utils";
import { simplePrintQR } from "@/lib/simple-print";
import { QRCode, QRCodeType, generateQRCodeFilename } from "@/components/ui/qr-code";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ImageViewerModal } from "@/components/ui/image-viewer-modal";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { useToast } from "@/hooks/use-toast";
import { LabelAssociationModal } from "@/components/ddt/label-association-modal";

export default function DDTDetails() {
  const { id } = useParams<{ id: string }>();
  const ddtId = parseInt(id);
  const { toast } = useToast();
  
  // Stato per la visualizzazione delle immagini in formato grande
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const openImageDialog = (imageSrc: string | null) => {
    setSelectedImage(imageSrc);
    setDialogOpen(true);
  };

  const { data: ddt, isLoading: isLoadingDDT } = useQuery<DDT>({
    queryKey: ["/api/ddt", ddtId],
    queryFn: async () => {
      const response = await fetch(`/api/ddt/${ddtId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch DDT");
      }
      const result = await response.json();
      console.log("View DDT details", result);
      return result;
    },
    enabled: !isNaN(ddtId)
  });

  const { data: products, isLoading: isLoadingProducts } = useQuery<ProductLabel[]>({
    queryKey: ["/api/product-labels/ddt", ddtId],
    queryFn: async () => {
      const response = await fetch(`/api/product-labels/ddt/${ddtId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch product labels");
      }
      const result = await response.json();
      console.log("Product labels retrieved for DDT:", result);
      return result;
    },
    enabled: !isNaN(ddtId)
  });

  // Funzione semplificata per stampare il QR code
  const printQRCodeSimple = (qrValue: string, title: string, subtitle: string) => {
    try {
      // Crea una nuova finestra di stampa
      const printWindow = window.open('', '_blank', 'width=400,height=400');
      if (!printWindow) {
        toast({
          title: "Errore di stampa",
          description: "Impossibile aprire la finestra di stampa. Verifica che i popup non siano bloccati.",
          variant: "destructive"
        });
        return false;
      }
      
      // Scrivi il contenuto HTML con il QR code
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Stampa QR Code</title>
            <style>
              @page {
                size: 50mm 60mm;
                margin: 0;
              }
              body {
                margin: 0;
                padding: 5mm;
                background: white;
                font-family: Arial, sans-serif;
              }
              .container {
                width: 40mm;
                height: 50mm;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              }
              .title {
                font-size: 3mm;
                font-weight: bold;
                line-height: 4mm;
                margin-top: 2mm;
                text-align: center;
                text-transform: uppercase;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <img src="data:image/png;base64,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" width="40mm" height="40mm" alt="QR code">
              <p class="title">${title}</p>
              <p class="title">${subtitle}</p>
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 500);
              };
            </script>
          </body>
        </html>
      `);
      
      printWindow.document.close();
      return true;
    } catch (error) {
      console.error("Errore durante la stampa:", error);
      toast({
        title: "Errore di stampa",
        description: "Si è verificato un errore durante la stampa del QR code.",
        variant: "destructive"
      });
      return false;
    }
  };
  
  if (isLoadingDDT || isLoadingProducts) {
    return (
      <div className="p-4 flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin w-10 h-10 border-t-2 border-blue-500 rounded-full"></div>
        <p className="mt-4 text-gray-600">Caricamento dati...</p>
      </div>
    );
  }

  if (!ddt) {
    return (
      <div className="p-4 flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-6xl mb-4">
          <X size={48} />
        </div>
        <h1 className="text-xl font-semibold mb-2">DDT non trovato</h1>
        <p className="text-gray-600 mb-4">Il DDT richiesto non esiste o è stato rimosso.</p>
        <Link to="/ddt">
          <Button variant="outline" className="flex items-center gap-2">
            <ChevronLeft size={16} />
            Torna alla lista
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <Header title={`DDT #${ddt.number}`} showBack={true} backPath="/search" />
      
      <main className="flex-1 pt-24 pb-32 overflow-y-auto px-4">
        <div className="container mx-auto max-w-5xl">
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col">
              <h1 className="text-xl font-bold">{ddt.companyName}</h1>
              <Badge className="text-sm mt-1 w-fit" variant="outline">
                {formatDate(ddt.date)}
              </Badge>
            </div>
          </div>
          
          <Tabs defaultValue="info" className="w-full">
            <TabsList className="mb-4 grid w-full grid-cols-3">
              <TabsTrigger value="info" className="text-sm whitespace-nowrap">
                <FileText size={16} className="mr-2" />
                Informazioni
              </TabsTrigger>
              <TabsTrigger value="products" className="text-sm whitespace-nowrap">
                <Package size={16} className="mr-2" />
                Prodotti ({products?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="qrcode" className="text-sm whitespace-nowrap">
                <QrCode size={16} className="mr-2" />
                QR Code
              </TabsTrigger>
            </TabsList>

            <TabsContent value="info" className="p-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Dettagli fornitore</h3>
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <p className="font-semibold text-lg">{ddt.companyName}</p>
                    <p className="text-gray-700">{ddt.vatNumber}</p>
                    <p className="text-gray-700">{ddt.address}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Dettagli documento</h3>
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Numero</span>
                      <span className="font-medium">{ddt.number}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Data</span>
                      <span className="font-medium">{formatDate(ddt.date)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Prodotti</span>
                      <span className="font-medium">{products?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {ddt.image && (
                <div className="mt-6">
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Immagine documento</h3>
                  <div className="border border-gray-200 rounded-lg p-2 bg-gray-50">
                    <img 
                      src={ddt.image} 
                      alt={`DDT #${ddt.number}`} 
                      className="w-full max-h-96 object-contain cursor-pointer"
                      onClick={() => openImageDialog(ddt.image)} 
                    />
                  </div>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="products" className="p-1">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Prodotti Associati</h3>
                <LabelAssociationModal 
                  ddtId={ddtId} 
                  ddtNumber={ddt.number}
                  onSuccess={() => {
                    // Force refresh of products data
                    window.location.reload();
                  }}
                />
              </div>
              
              {products?.length === 0 ? (
                <div className="text-center py-8">
                  <Package size={48} className="mx-auto text-gray-300 mb-2" />
                  <p className="text-gray-500">Nessun prodotto associato a questo DDT</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Usa il pulsante "Associa Etichetta" per aggiungere prodotti esistenti
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[500px] rounded-md border">
                  <div className="p-4">
                    {products?.map((product) => (
                      <div key={product.id} className="mb-6 bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                        <div className="p-4">
                          <h3 className="font-semibold text-lg mb-2">{product.productName}</h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-gray-600 text-sm">Lotto</span>
                                <span className="font-medium">{product.batchNumber || '-'}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600 text-sm">Scadenza</span>
                                <span className="font-medium">{product.expiryDate || '-'}</span>
                              </div>
                            </div>
                            
                            <div>
                              {product.storageInstructions && (
                                <div className="text-sm">
                                  <span className="text-gray-600 block mb-1">Conservazione:</span>
                                  <p className="text-gray-800">{product.storageInstructions}</p>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-end mt-4">
                            <Link href={`/product-details/${product.id}`}>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="flex items-center gap-2"
                              >
                                <Eye size={16} />
                                Dettaglio
                              </Button>
                            </Link>
                            
                            {product.qrCode && (
                              <img 
                                src={product.qrCode} 
                                alt={`QR Code per ${product.productName}`}
                                className="w-24 h-24 object-contain cursor-pointer"
                                onClick={() => openImageDialog(product.qrCode)}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </TabsContent>
            
            <TabsContent value="qrcode" className="p-1">
              <div className="flex flex-col items-center justify-center py-6">
                <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col items-center shadow-sm">
                  <QRCode 
                    value={`ddt:${ddt.id}:${ddt.number.replace(/[^a-zA-Z0-9]/g, '_')}`}
                    size={200}
                    qrCodeType={QRCodeType.DDT}
                    itemName={ddt.companyName}
                    creationDate={new Date(ddt.date)}
                    id="ddt-qrcode-svg"
                  />
                  <div className="text-center mb-3">
                    <p className="font-black text-lg uppercase tracking-wide">DDT #{ddt.number}</p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={() => {
                        // Utilizziamo la funzione di utilità per il download del QR code
                        // Passiamo un valore diretto per il QR code con il formato standardizzato
                        const qrValue = `ddt:${ddt.id}:${ddt.number.replace(/[^a-zA-Z0-9]/g, '_')}`;
                        const result = downloadQRCode(
                          'ddt-qrcode-svg', 
                          `DDT #${ddt.number}`,
                          ddt.companyName,
                          qrValue
                        );
                        
                        if (!result) {
                          toast({
                            title: "Errore di download",
                            description: "Non è stato possibile scaricare il QR code. Riprova.",
                            variant: "destructive",
                          });
                        }
                      }}
                      className="px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md shadow-sm hover:bg-blue-600 transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center gap-2"
                    >
                      <Download size={16} />
                      Scarica
                    </button>
                    <button
                      onClick={() => {
                        // Prepariamo il valore per il QR code
                        const qrValue = `ddt:${ddt.id}:${ddt.number.replace(/[^a-zA-Z0-9]/g, '_')}`;
                        console.log("Usando valore QR diretto:", qrValue);
                        
                        // Approccio semplificato: passiamo direttamente il valore alla pagina di stampa
                        // La pagina di stampa genererà il QR code lato client usando la libreria
                        const title = `DDT #${ddt.number}`;
                        const subtitle = ddt.companyName;
                        const encodedTitle = encodeURIComponent(title);
                        const encodedSubtitle = encodeURIComponent(subtitle);
                        const encodedQRValue = encodeURIComponent(qrValue);
                        
                        // Prepara l'URL con tutti i parametri necessari
                        const printURL = `/print-qr-direct.html?title=${encodedTitle}&subtitle=${encodedSubtitle}&qrvalue=${encodedQRValue}`;
                        
                        // Apre la finestra di stampa
                        const printWindow = window.open(printURL, '_blank', 'width=400,height=400');
                        
                        if (!printWindow) {
                          console.error("Impossibile aprire la finestra di stampa. Verifica che i popup non siano bloccati.");
                        }
                      }}
                      className="px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md shadow-sm hover:bg-blue-600 transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center gap-2"
                    >
                      <Printer size={16} />
                      Stampa
                    </button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
      
      <ImageViewerModal
        isOpen={dialogOpen}
        onClose={() => setDialogOpen(false)}
        imageSrc={selectedImage}
      />
    </div>
  );
}