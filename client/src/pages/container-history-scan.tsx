import React from 'react';
import { Header } from '@/components/layout/header';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { GradientBackground } from '@/components/layout/gradient-background';
import { Card, CardContent } from '@/components/ui/card';
import { ContainerHistoryScanner } from '@/components/containers/container-history-scanner';
import { Archive, History, Scan } from 'lucide-react';

export default function ContainerHistoryScanPage() {
  return (
    <GradientBackground>
      <Header title="Storico Contenitori" />
      <main className="container mx-auto py-6 px-4 flex-1 overflow-y-auto mb-16">
        <div className="max-w-lg mx-auto">
          <h1 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <Archive className="h-6 w-6 mr-2 text-blue-600" />
            Storico Contenitori
          </h1>

          <Card className="shadow-lg border-0 mb-6">
            <CardContent className="p-6">
              <div className="flex items-center mb-4 text-blue-600">
                <History className="h-5 w-5 mr-2" />
                <h2 className="text-lg font-semibold">Controlla Storico Contenitori</h2>
              </div>
              
              <p className="text-gray-600 mb-6">
                Utilizza il pulsante qui sotto per scansionare il QR code di un contenitore e visualizzare 
                la cronologia delle operazioni degli ultimi 30 giorni.
              </p>

              <ContainerHistoryScanner />
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center mb-4 text-blue-600">
                <Scan className="h-5 w-5 mr-2" />
                <h2 className="text-lg font-semibold">Istruzioni per la Scansione</h2>
              </div>
              
              <ol className="list-decimal pl-5 space-y-2 text-gray-600">
                <li>Premi il pulsante "Scansiona QR Contenitore" sopra</li>
                <li>Inquadra il QR code del contenitore con la fotocamera</li>
                <li>Attendi il riconoscimento automatico del QR code</li>
                <li>Visualizza lo storico delle operazioni degli ultimi 30 giorni</li>
              </ol>
            </CardContent>
          </Card>
        </div>
      </main>
      <BottomNavigation activeItem="containers" />
    </GradientBackground>
  );
}