import React from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { GradientBackground } from '@/components/layout/gradient-background';
import { ContainerQRScannerModal } from '@/components/containers/container-qr-scanner-modal';
import { ContainerHistory } from '@/components/containers/container-history';
import { Button } from '@/components/ui/button';
import { QrCode, X, Home } from 'lucide-react';
import { useLocation } from 'wouter';

export default function ContainerHistoryScannerPage() {
  const [isQRScannerOpen, setIsQRScannerOpen] = React.useState(true);
  const [isHistoryOpen, setIsHistoryOpen] = React.useState(false);
  const [selectedContainerId, setSelectedContainerId] = React.useState<number | null>(null);
  const [, setLocation] = useLocation();

  // Close QR Scanner Modal
  const closeQRScanner = () => {
    setIsQRScannerOpen(false);
  };

  // Open QR Scanner Modal
  const openQRScanner = () => {
    setIsQRScannerOpen(true);
  };

  // Close History Modal
  const closeHistory = () => {
    setIsHistoryOpen(false);
  };

  // Handle when a container is found from the QR scanner
  const handleContainerFound = (containerId: number) => {
    setSelectedContainerId(containerId);
    // Close the scanner modal and open the history modal
    setIsQRScannerOpen(false);
    setIsHistoryOpen(true);
  };

  // Handle cancel - return to home
  const handleCancel = () => {
    setLocation('/');
  };

  return (
    <GradientBackground>
      <Header showBack={true} backPath="/" />
      <main className="container mx-auto py-6 px-4 flex-1 overflow-y-auto mb-24 flex items-center justify-center">
        <div className="max-w-lg w-full">

          {/* QR Scanner Modal - opens automatically */}
          <ContainerQRScannerModal 
            isOpen={isQRScannerOpen}
            onClose={closeQRScanner}
            onContainerFound={handleContainerFound}
          />

          {/* Container History Modal */}
          <ContainerHistory
            isOpen={isHistoryOpen}
            onClose={closeHistory}
            containerId={selectedContainerId}
          />

          {/* If scanner is closed, show button to reopen it */}
          {!isQRScannerOpen && (
            <div className="text-center p-8 bg-white rounded-lg shadow-lg border border-gray-100">
              <QrCode className="h-20 w-20 mx-auto mb-6 text-blue-600 opacity-75" />
              <p className="text-gray-600 mb-6">
                Scanner chiuso. Premi sul pulsante qui sotto per riaprire lo scanner QR e visualizzare lo storico di un contenitore.
              </p>
              <div className="space-y-4">
                <button 
                  onClick={openQRScanner}
                  className="w-[70%] mx-auto bg-blue-600 hover:bg-blue-700 py-4 text-lg font-bold shadow-xl rounded-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] border-b-4 border-blue-800 text-white px-4 flex items-center justify-center"
                >
                  Scansiona QR Contenitore
                </button>
                
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  className="w-[70%] mx-auto py-4 text-lg font-semibold border-2 border-gray-300 hover:border-gray-400 flex items-center justify-center"
                >
                  <Home className="mr-2 h-5 w-5" />
                  Annulla
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer activeItem="qrscan" />
    </GradientBackground>
  );
}