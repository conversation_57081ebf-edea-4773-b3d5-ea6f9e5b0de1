import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Header } from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { QRCodeView } from "@/components/ui/qr-code";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { generateUniqueId } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeftIcon, ArrowRightIcon } from "lucide-react";

const qrFormSchema = z.object({
  customName: z.string().optional(),
});

type QRFormData = z.infer<typeof qrFormSchema>;

export default function DDTQRCode() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [qrData, setQrData] = useState<string>("");
  const [ddtNumber, setDdtNumber] = useState<string>("");
  const [ddtCompany, setDdtCompany] = useState<string>("");
  const [ddtDate, setDdtDate] = useState<string>("");
  
  const form = useForm<QRFormData>({
    resolver: zodResolver(qrFormSchema),
    defaultValues: {
      customName: "",
    },
  });
  
  // Funzione per formattare il nome in base alla convenzione: RAGIONESOCIALE-DDMMYY
  const formatCustomName = (company: string, dateStr: string): string => {
    // Estrai solo la prima parte del nome aziendale (es. "RG" da "RG s.r.l")
    const companyPrefix = company.split(/\s+/)[0].toUpperCase();
    
    // Formatta la data da DD/MM/YYYY a DDMMYY
    let formattedDate = "";
    if (dateStr) {
      const dateParts = dateStr.split(/[\/\-\.]/); // Supporta vari separatori: / - .
      if (dateParts.length >= 3) {
        const day = dateParts[0].padStart(2, '0');
        const month = dateParts[1].padStart(2, '0');
        // Prendi solo le ultime due cifre dell'anno se l'anno è a 4 cifre
        const year = dateParts[2].length > 2 ? dateParts[2].slice(-2) : dateParts[2];
        formattedDate = `${day}${month}${year}`;
      }
    }
    
    return formattedDate ? `${companyPrefix}-${formattedDate}` : companyPrefix;
  };
  
  // Generate QR code data when component mounts
  useEffect(() => {
    const storedDDTNumber = sessionStorage.getItem("currentDDTId");
    const storedDDTCompany = sessionStorage.getItem("currentDDTCompany");
    const storedDDTDate = sessionStorage.getItem("currentDDTDate");
    
    if (storedDDTNumber) {
      setDdtNumber(storedDDTNumber);
      setQrData(`ddt:${storedDDTNumber}`);
    } else {
      // Fallback in case DDT number is not in session storage
      const uniqueId = generateUniqueId("DDT");
      setQrData(`ddt:${uniqueId}`);
    }
    
    if (storedDDTCompany) {
      setDdtCompany(storedDDTCompany);
    }
    
    if (storedDDTDate) {
      setDdtDate(storedDDTDate);
    }
    
    // Imposta il nome personalizzato formattato
    if (storedDDTCompany && storedDDTDate) {
      const formattedName = formatCustomName(storedDDTCompany, storedDDTDate);
      form.setValue("customName", formattedName);
    } else if (storedDDTCompany) {
      form.setValue("customName", storedDDTCompany);
    }
  }, [form]);
  
  const getQrLabel = () => {
    const customName = form.watch("customName");
    if (customName && customName.trim() !== "") {
      return customName.trim().toUpperCase();
    }
    return (ddtCompany || ddtNumber || "DDT").toUpperCase();
  };
  
  const handleContinue = () => {
    toast({
      title: "QR Code Generated",
      description: "You can download or print the QR code for future reference.",
      duration: 1000,
    });
    setLocation("/product-label");
  };
  
  const handleBack = () => {
    setLocation("/ddt-processing");
  };
  
  return (
    <div className="flex flex-col h-screen">
      <Header
        title="QR Code Generation"
        showBack
        backPath="/ddt-processing"
      />

      <main className="flex-1 pt-24 pb-6 px-4 overflow-y-auto">
        <div className="py-4 max-w-md mx-auto">
          <Card className="shadow-md border-0">
            <CardHeader>
              <CardTitle className="text-center text-xl">
                DDT QR Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center mb-4 text-gray-600">
                Non necessario ai fini HACCP
              </p>
              
              <Form {...form}>
                <form className="space-y-6">
                  <FormField
                    control={form.control}
                    name="customName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., RG-070523"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="pt-2">
                    <QRCodeView
                      code={qrData}
                      label={getQrLabel()}
                      showDownload
                      showPrint
                    />
                  </div>
                  
                  <div className="flex justify-between space-x-4 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                    >
                      <ArrowLeftIcon className="w-4 h-4 mr-2" />
                      Indietro
                    </Button>
                    <Button
                      type="button"
                      onClick={handleContinue}
                      className="flex-1"
                    >
                      Avanti
                      <ArrowRightIcon className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
