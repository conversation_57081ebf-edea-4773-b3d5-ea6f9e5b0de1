import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { apiRequest } from "@/lib/queryClient";
import { Header } from "@/components/layout/header";
import { GradientBackground } from "@/components/layout/gradient-background";
import { useToast } from "@/hooks/use-toast";
import { InfoIcon, AlertTriangleIcon, FileImageIcon, CameraIcon } from "lucide-react";

export default function ApiTest() {
  const [result, setResult] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useRealImage, setUseRealImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  // Immagine di test breve che causerà un errore
  const shortMockImage = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAgEASABIAAD";
  
  // Gestisce il caricamento di un'immagine reale
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUseRealImage(true);
      toast({
        title: "Immagine caricata",
        description: `File "${file.name}" pronto per i test`,
      });
    }
  };
  
  // Legge un file come Base64
  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as string);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const testDDTProcessing = async () => {
    setIsLoading(true);
    setError(null);
    try {
      let imageData = shortMockImage;
      
      // Se l'utente ha caricato un'immagine, usala
      if (useRealImage && fileInputRef.current?.files?.length) {
        const file = fileInputRef.current.files[0];
        imageData = await readFileAsBase64(file);
      }
      
      const data = await apiRequest(
        "/api/ocr/process-ddt",
        "POST",
        { imageData }
      );
      
      setResult(JSON.stringify(data, null, 2));
      toast({
        title: "API Test Successful",
        description: "DDT processing API works correctly",
      });
    } catch (error: any) {
      console.error("API Test Error:", error);
      setResult(JSON.stringify(error, null, 2));
      setError(error?.data?.message || error?.data?.details || "Unknown error");
      toast({
        title: "API Test Failed",
        description: "There was an error with the API call",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testLabelProcessing = async () => {
    setIsLoading(true);
    setError(null);
    try {
      let imageData = shortMockImage;
      
      // Se l'utente ha caricato un'immagine, usala
      if (useRealImage && fileInputRef.current?.files?.length) {
        const file = fileInputRef.current.files[0];
        imageData = await readFileAsBase64(file);
      }
      
      const data = await apiRequest(
        "/api/ocr/process-label",
        "POST",
        { imageData }
      );
      
      setResult(JSON.stringify(data, null, 2));
      toast({
        title: "API Test Successful",
        description: "Label processing API works correctly",
      });
    } catch (error: any) {
      console.error("API Test Error:", error);
      setResult(JSON.stringify(error, null, 2));
      setError(error?.data?.message || error?.data?.details || "Unknown error");
      toast({
        title: "API Test Failed",
        description: "There was an error with the API call",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <GradientBackground>
      <Header title="API Test" showBack backPath="/" />
      
      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <Card className="max-w-xl mx-auto mb-6 bg-white/90 backdrop-blur-sm border border-gray-200">
          <CardContent className="p-4 space-y-4">
            <h2 className="text-xl font-bold">Test API Calls</h2>
            <p className="text-gray-600">Use these buttons to test if the API calls are working correctly after the fixes.</p>
            
            <div className="flex gap-4 mt-6">
              <Button 
                onClick={testDDTProcessing}
                disabled={isLoading}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
              >
                Test DDT Processing
              </Button>
              
              <Button 
                onClick={testLabelProcessing}
                disabled={isLoading}
                className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700"
              >
                Test Label Processing
              </Button>
            </div>
            
            {result && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">API Response:</h3>
                <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">{result}</pre>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </GradientBackground>
  );
}