import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Header } from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QRCodeView } from "@/components/ui/qr-code";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { ProductLabelFormData } from "@/types";
import { normalizeDateToStandard } from '@/lib/dateNormalization';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { generateUniqueId } from "@/lib/utils";
import { SelectDDTDialog } from "@/components/ddt/select-ddt-dialog";
import { Plus, ArrowRight } from "lucide-react";

// Schema di validazione del form
const labelFormSchema = z.object({
  productName: z.string().min(1, "Nome del prodotto obbligatorio"),
  expiryDate: z.string().min(1, "Data di scadenza obbligatoria"),
  batchNumber: z.string().min(1, "Numero di lotto obbligatorio"),
  storageInstructions: z.string().min(1, "Istruzioni di conservazione obbligatorie"),
  notes: z.string().optional(),
  qrCodeName: z.string().optional(),
});

export default function LabelProcessing() {
  const [, setLocation] = useLocation();
  const [imageData, setImageData] = useState<string | null>(null);
  const [multipleImages, setMultipleImages] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(true);
  const [qrCodeData, setQrCodeData] = useState<string>("");
  const [showDDTSelector, setShowDDTSelector] = useState(false);
  const [waitingForDDT, setWaitingForDDT] = useState(false);
  const [labelDataToSave, setLabelDataToSave] = useState<ProductLabelFormData | null>(null);
  const [currentDDTId, setCurrentDDTId] = useState<string | null>(
    sessionStorage.getItem("currentDDTId")
  );
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<ProductLabelFormData>({
    resolver: zodResolver(labelFormSchema),
    defaultValues: {
      productName: "",
      expiryDate: "",
      batchNumber: "",
      storageInstructions: "",
      notes: "",
      qrCodeName: "",
    },
  });

  // Process multiple labels with Claude AI
  const processMultipleLabels = async (images: string[]) => {
    try {
      const data = await apiRequest(
        "/api/ocr/process-multiple-labels", 
        "POST",
        { images: images }
      );
      
      return data;
    } catch (error) {
      console.error("Multiple labels processing error:", error);
      toast({
        title: "Elaborazione Fallita",
        description: "Impossibile estrarre informazioni dalle etichette. Riprova.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Process single label with Claude AI (backward compatibility)
  const processLabel = async (imageBase64: string) => {
    try {
      const data = await apiRequest(
        "/api/ocr/process-label", 
        "POST",
        { imageData: imageBase64 }
      );
      
      return data;
    } catch (error) {
      console.error("Label processing error:", error);
      toast({
        title: "Elaborazione Fallita",
        description: "Impossibile estrarre informazioni dall'etichetta. Riprova.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Funzione per gestire la selezione di un DDT
  const handleDDTSelected = async (ddt: any) => {
    if (ddt && ddt.id) {
      // Memorizza il DDT ID e il numero in sessionStorage per uso futuro
      sessionStorage.setItem("currentDDTId", ddt.id.toString());
      sessionStorage.setItem("currentDDTNumber", ddt.number || "");
      console.log("DDT selezionato:", ddt.id, ddt.number);
      setCurrentDDTId(ddt.id.toString());
      
      // Se avevamo dati in attesa, salva immediatamente
      if (labelDataToSave && waitingForDDT) {
        console.log("Salvataggio immediato dopo selezione DDT");
        
        const data = { 
          ...labelDataToSave,
          ddtId: ddt.id,
          image: imageData,
          qrCode: qrCodeData
        };
        
        try {
          await saveLabel(data);
          setLabelDataToSave(null);
          setWaitingForDDT(false);
          
          // Naviga automaticamente alla home dopo il salvataggio
          setTimeout(() => {
            setLocation("/");
          }, 1000);
        } catch (error) {
          console.error("Errore nel salvataggio:", error);
        }
      }
    }
    
    setShowDDTSelector(false);
  };
  
  // Funzione per salvare l'etichetta
  const saveLabel = async (data: any) => {
    try {
      console.log("Sending product label data to server:", data);
      
      const response = await apiRequest(
        "/api/product-labels",
        "POST", 
        data
      );
      
      // Invalidare tutte le query relative alle etichette e ai DDT
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels/ddt"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ddt"] });
      
      toast({
        title: "Label Saved",
        description: "Product label information has been saved successfully.",
      });
      
      return response;
    } catch (error) {
      console.error("Error saving label:", error);
      toast({
        title: "Salvataggio Fallito",
        description: "Impossibile salvare l'etichetta del prodotto. Riprova.",
        variant: "destructive",
      });
      throw error;
    }
  };
  
  // Mutation per salvare l'etichetta
  const saveLabelMutation = useMutation({
    mutationFn: async (formData: ProductLabelFormData) => {
      console.log("Running saveLabelMutation with:", formData);
      console.log("currentDDTId:", currentDDTId);
      
      // Se non c'è un DDT attivo, avvia il processo di selezione
      if (!currentDDTId) {
        console.log("Nessun DDT attivo trovato, aprendo selettore...");
        setLabelDataToSave(formData);
        setWaitingForDDT(true);
        setShowDDTSelector(true);
        // Ritorna una promessa che non si risolve mai, poiché il salvataggio
        // avverrà dopo la selezione del DDT
        return new Promise(() => {});
      }
      
      // Se abbiamo un DDT, procediamo normalmente
      let ddtId: number;
      try {
        ddtId = parseInt(currentDDTId);
        if (isNaN(ddtId)) {
          throw new Error("Invalid DDT ID");
        }
      } catch (err) {
        console.error("Error parsing DDT ID:", err);
        throw new Error("Invalid DDT ID format");
      }
      
      const data = { 
        ...formData,
        ddtId,
        image: imageData,
        qrCode: qrCodeData
      };
      
      return saveLabel(data);
    },
    onSuccess: () => {
      toast({
        title: "Etichetta Salvata",
        description: "Le informazioni dell'etichetta del prodotto sono state salvate con successo.",
      });
    },
    onError: (error) => {
      console.error("Error saving label:", error);
      toast({
        title: "Salvataggio Fallito",
        description: "Impossibile salvare l'etichetta del prodotto. Riprova.",
        variant: "destructive",
      });
    },
  });

  // Gestione automatica del salvataggio dopo selezione DDT
  useEffect(() => {
    if (currentDDTId && labelDataToSave && waitingForDDT) {
      console.log("Salvataggio automatico dopo selezione DDT");
      
      // Salva direttamente senza re-submit del form
      const data = { 
        ...labelDataToSave,
        ddtId: parseInt(currentDDTId),
        image: imageData,
        qrCode: qrCodeData
      };
      
      saveLabel(data).then(() => {
        setLabelDataToSave(null);
        setWaitingForDDT(false);
      }).catch(console.error);
    }
  }, [currentDDTId, labelDataToSave, waitingForDDT]);

  // Questo useEffect carica e processa le immagini dell'etichetta salvate nella sessione
  useEffect(() => {
    console.log("🔥 USEEFFECT ESEGUITO - Component ID:", Math.random().toString(36).slice(2, 8));
    console.log("DDT ID in sessionStorage:", sessionStorage.getItem("currentDDTId"));
    
    // Check for multiple images first (new format)
    const storedMultipleImages = sessionStorage.getItem("capturedLabelImages");
    let storedImage = sessionStorage.getItem("capturedLabelImage");
    
    // Prova anche il nuovo formato di archiviazione
    if (!storedImage && !storedMultipleImages) {
      const labelData = sessionStorage.getItem("labelData");
      if (labelData) {
        try {
          const parsedData = JSON.parse(labelData);
          storedImage = parsedData.image;
        } catch (e) {
          console.error("Errore nel parsing dei dati dell'etichetta:", e);
        }
      }
    }
    
    if (storedMultipleImages) {
      // Process multiple images
      try {
        const images = JSON.parse(storedMultipleImages);
        setMultipleImages(images);
        setImageData(images[0]); // Show first image as preview
        
        console.log(`Processing ${images.length} images with Claude`);
        
        // Process all images with Claude
        processMultipleLabels(images)
          .then((result: any) => {
            console.log("Multiple Labels API response:", result);
            
            // Adatta i campi al formato atteso dal frontend
            const productName = result?.productName || result?.e_product_name || "";
            const rawExpiryDate = result?.expiryDate || result?.e_expiry_date || "";
            const expiryDate = normalizeDateToStandard(rawExpiryDate);
            const batchNumber = result?.batchNumber || result?.e_batch_number || "";
            
            // Importante: Claude può restituire il campo come 'storageRequirements' invece di 'storageInstructions'
            const storageInstructions = 
              result?.storageInstructions || 
              result?.storageRequirements || 
              result?.e_storage_instructions || 
              "";
            
            const notes = result?.notes || "";
            
            form.reset({
              productName,
              expiryDate,
              batchNumber,
              storageInstructions,
              notes,
              qrCodeName: "",
            });
            
            // Generate QR code data
            const qrId = generateUniqueId("L");
            setQrCodeData(qrId);
            
            setIsProcessing(false);
          })
          .catch(() => {
            setIsProcessing(false);
          });
      } catch (e) {
        console.error("Errore nel parsing delle immagini multiple:", e);
        setIsProcessing(false);
      }
    } else if (storedImage) {
      // Convert single image to multi-image array for consistent handling
      const singleImageArray = [storedImage];
      setMultipleImages(singleImageArray);
      setImageData(storedImage); // Show first image as preview
      
      console.log("Converting single image to multi-photo workflow");
      
      // Process the single image with the multi-photo endpoint for consistency
      processMultipleLabels(singleImageArray)
        .then((result: any) => {
          console.log("Single Image (via Multi-photo API) response:", result);
          
          // Adatta i campi al formato atteso dal frontend
          const productName = result?.productName || result?.e_product_name || "";
          const rawExpiryDate = result?.expiryDate || result?.e_expiry_date || "";
          const expiryDate = normalizeDateToStandard(rawExpiryDate);
          const batchNumber = result?.batchNumber || result?.e_batch_number || "";
          
          // Importante: Claude può restituire il campo come 'storageRequirements' invece di 'storageInstructions'
          const storageInstructions = 
            result?.storageInstructions || 
            result?.storageRequirements || 
            result?.e_storage_instructions || 
            "";
          
          const notes = result?.notes || "";
          
          form.reset({
            productName,
            expiryDate,
            batchNumber,
            storageInstructions,
            notes,
            qrCodeName: "",
          });
          
          // Generate QR code data
          const qrId = generateUniqueId("L");
          setQrCodeData(qrId);
          
          setIsProcessing(false);
        })
        .catch(() => {
          setIsProcessing(false);
        });
    } else {
      toast({
        title: "Nessuna Immagine Trovata",
        description: "Cattura prima un'immagine dell'etichetta.",
        variant: "destructive",
      });
      setLocation("/product-label");
    }
  }, []);

  // Handler per l'invio del form
  const onSubmit = (data: ProductLabelFormData) => {
    saveLabelMutation.mutate(data);
  };

  // Handler per il pulsante "Aggiungi un altro prodotto"
  const handleAddAnother = () => {
    form.handleSubmit((data) => {
      saveLabelMutation.mutate(data, {
        onSuccess: () => {
          // Manteniamo il DDT corrente ma rimuoviamo le immagini
          sessionStorage.removeItem("capturedLabelImage");
          sessionStorage.removeItem("capturedLabelImages");
          setLocation("/product-label");
        }
      });
    })();
  };

  // Handler per il pulsante "Completa"
  const handleFinish = () => {
    form.handleSubmit((data) => {
      saveLabelMutation.mutate(data, {
        onSuccess: () => {
          // Pulizia completa al termine del task
          sessionStorage.removeItem("currentDDTId");
          sessionStorage.removeItem("currentDDTNumber");
          sessionStorage.removeItem("capturedLabelImage");
          sessionStorage.removeItem("capturedLabelImages");
          sessionStorage.removeItem("tempLabelData");
          console.log("Task completato - DDT associazione rimossa per nuovo task");
          setLocation("/");
        }
      });
    })();
  };

  // Ottenimento dell'etichetta per il QR code
  const getQrLabel = () => {
    const qrCodeName = form.watch("qrCodeName");
    const productName = form.watch("productName");
    
    if (qrCodeName) return qrCodeName.toUpperCase();
    if (productName) {
      return productName.toUpperCase();
    }
    return "PRODUCT-LABEL";
  };

  return (
    <div className="flex flex-col h-screen">
      <Header
        title="Elaborazione Etichetta"
        showBack
        backPath="/product-label"
        showNotification={false}
        showUserMenu={false}
      />
      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="py-6">
          {isProcessing ? (
            <div className="mb-6">
              <div className="flex flex-col items-center justify-center p-8">
                <div className="rounded-full bg-primary-50 p-3 mb-4">
                  <svg
                    className="h-8 w-8 text-primary-600 animate-pulse"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  Elaborazione Etichetta
                </h2>
                <p className="text-sm text-gray-500 text-center">
                  La nostra IA sta estraendo informazioni dall'etichetta del prodotto
                </p>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div className="bg-primary-600 h-2.5 rounded-full w-3/4 animate-pulse"></div>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                {/* Multi-Photo Management */}
                {(multipleImages.length > 0 || imageData) && (
                  <Card className="bg-white mb-6">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-800">
                          Foto Acquisite ({multipleImages.length || 1}/3)
                        </h3>
                        {(multipleImages.length < 3 || (!multipleImages.length && imageData)) && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Preserve current images in sessionStorage
                              if (multipleImages.length > 0) {
                                sessionStorage.setItem("capturedLabelImages", JSON.stringify(multipleImages));
                              } else if (imageData) {
                                sessionStorage.setItem("capturedLabelImages", JSON.stringify([imageData]));
                              }
                              // Clear old single image storage
                              sessionStorage.removeItem("capturedLabelImage");
                              setLocation("/product-label");
                            }}
                            className="text-indigo-600 border-indigo-600 hover:bg-indigo-50"
                          >
                            + Aggiungi Altra Foto
                          </Button>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-3 gap-3">
                        {multipleImages.length > 0 ? (
                          multipleImages.map((image, index) => (
                            <div key={index} className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
                              <img 
                                src={image} 
                                alt={`Foto ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                              <div className="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white rounded px-2 py-1 text-xs">
                                {index + 1}
                              </div>
                            </div>
                          ))
                        ) : imageData ? (
                          <div className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
                            <img 
                              src={imageData} 
                              alt="Foto etichetta"
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white rounded px-2 py-1 text-xs">
                              1
                            </div>
                          </div>
                        ) : null}
                      </div>
                      
                      {multipleImages.length < 3 && (
                        <p className="text-sm text-gray-500 mt-3">
                          Puoi aggiungere fino a {3 - multipleImages.length} foto aggiuntive per migliorare l'estrazione dati.
                        </p>
                      )}

                      {multipleImages.length > 1 && (
                        <Button
                          type="button"
                          variant="secondary"
                          size="sm"
                          onClick={() => {
                            setIsProcessing(true);
                            processMultipleLabels(multipleImages)
                              .then((result: any) => {
                                console.log("Reprocessed Multiple Labels API response:", result);
                                
                                const productName = result?.productName || result?.e_product_name || "";
                                const rawExpiryDate = result?.expiryDate || result?.e_expiry_date || "";
                                const expiryDate = normalizeDateToStandard(rawExpiryDate);
                                const batchNumber = result?.batchNumber || result?.e_batch_number || "";
                                
                                const storageInstructions = 
                                  result?.storageInstructions || 
                                  result?.storageRequirements || 
                                  result?.e_storage_instructions || 
                                  "";
                                
                                const notes = result?.notes || "";
                                
                                form.reset({
                                  productName,
                                  expiryDate,
                                  batchNumber,
                                  storageInstructions,
                                  notes,
                                  qrCodeName: form.getValues("qrCodeName") || "",
                                });
                                
                                setIsProcessing(false);
                              })
                              .catch(() => {
                                setIsProcessing(false);
                              });
                          }}
                          className="mt-3 w-full"
                        >
                          🔄 Riprocessa Tutte le Foto
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                )}

                <Card className="bg-white mb-6">
                  <CardContent className="p-4 space-y-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      Informazioni Estratte
                    </h3>

                    <FormField
                      control={form.control}
                      name="productName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome Prodotto</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="expiryDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data di Scadenza</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="batchNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Numero di Lotto</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="storageInstructions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Istruzioni di Conservazione</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Note (Opzionali)</FormLabel>
                          <FormControl>
                            <textarea
                              className="flex h-24 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              placeholder="Inserisci eventuali note aggiuntive sul prodotto..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                <Card className="bg-white mb-6">
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Generate QR Code
                    </h3>

                    <div className="mb-4">
                      <FormField
                        control={form.control}
                        name="qrCodeName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Custom Name (Optional)</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g., Salmon Delivery 12/23"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <QRCodeView
                      code={qrCodeData}
                      label={getQrLabel()}
                      showDownload
                      showPrint
                    />
                  </CardContent>
                </Card>

                <div className="flex flex-col space-y-3 mt-6">
                  <div className="flex gap-4 justify-center">
                    <button
                      type="button"
                      onClick={handleAddAnother}
                      className="font-medium bg-black text-white rounded-md shadow-md hover:bg-gray-800 transition-all hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-gray-300 flex items-center justify-center gap-2 text-[12px] pl-[6px] pr-[6px]"
                      style={{ fontSize: '16px', fontFamily: 'ui-sans-serif, system-ui, sans-serif', padding: '12px 24px', width: '131px', height: '48px' }}
                    >
                      <Plus size={20} />
                      AGGIUNGI ALTRO
                    </button>
                    <button
                      type="submit"
                      onClick={handleFinish}
                      disabled={saveLabelMutation.isPending}
                      className="font-medium bg-black text-white rounded-md shadow-md hover:bg-gray-800 transition-all hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-gray-300 flex items-center justify-center gap-2 text-[12px] pl-[18px] pr-[18px] pt-[16px] pb-[16px]"
                      style={{ fontSize: '16px', fontFamily: 'ui-sans-serif, system-ui, sans-serif', padding: '12px 24px', width: '131px', height: '48px' }}
                    >
                      <ArrowRight size={20} />
                      {saveLabelMutation.isPending ? "SALVANDO..." : "COMPLETA"}
                    </button>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full h-12 rounded-xl text-gray-700 bg-gray-100 border-2 border-gray-300 hover:bg-gray-200 hover:border-gray-400 transition-all duration-200"
                    onClick={() => setLocation('/')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M19 12H5M12 19l-7-7 7-7"></path>
                    </svg>
                    <span className="text-sm font-medium">Annulla</span>
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </main>
      {/* Dialog per la selezione del DDT quando non è stato preselezionato */}
      <SelectDDTDialog 
        open={showDDTSelector} 
        onOpenChange={setShowDDTSelector} 
        onSelectDDT={handleDDTSelected}
      />
    </div>
  );
}
