import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, CheckCircle, SaveIcon, PlusIcon, EditIcon, Trash2Icon } from "lucide-react";
import { Di<PERSON>, DialogContent, <PERSON>alogDes<PERSON>, Di<PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { promptService, AIPrompt } from "@/services/promptService";

export default function Prompts() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeCategory, setActiveCategory] = useState<"ddt" | "label" | "general">("ddt");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState<AIPrompt | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Recupera i prompt dal server
  const { data: promptsData = [], isLoading: isLoadingPrompts, isError } = useQuery({ 
    queryKey: ['prompts'], 
    queryFn: () => promptService.getAllPrompts()
  });
  
  // Assicura che prompts sia sempre un array valido
  const prompts = Array.isArray(promptsData) ? promptsData : [];

  // Mutation per creare un nuovo prompt
  const createPromptMutation = useMutation({
    mutationFn: (promptData: Omit<AIPrompt, "id">) => promptService.createPrompt(promptData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      setIsEditDialogOpen(false);
      toast({
        title: "Nuovo prompt creato",
        description: "Il prompt è stato creato con successo",
        duration: 1000,
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Si è verificato un errore nella creazione del prompt",
        duration: 3000,
      });
    }
  });

  // Mutation per aggiornare un prompt
  const updatePromptMutation = useMutation({
    mutationFn: ({ id, data }: { id: string, data: Omit<AIPrompt, "id"> }) => 
      promptService.updatePrompt(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      setIsEditDialogOpen(false);
      toast({
        title: "Prompt aggiornato",
        description: "Il prompt è stato aggiornato con successo",
        duration: 1000,
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Si è verificato un errore nell'aggiornamento del prompt",
        duration: 3000,
      });
    }
  });

  // Mutation per eliminare un prompt
  const deletePromptMutation = useMutation({
    mutationFn: (id: string) => promptService.deletePrompt(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      setIsDeleteConfirmOpen(false);
      setCurrentPrompt(null);
      toast({
        title: "Prompt eliminato",
        description: "Il prompt è stato eliminato con successo",
        duration: 1000,
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Si è verificato un errore nell'eliminazione del prompt",
        duration: 3000,
      });
    }
  });

  // Mutation per ripristinare i prompt predefiniti
  const resetDefaultPromptsMutation = useMutation({
    mutationFn: () => promptService.resetDefaultPrompts(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      toast({
        title: "Prompt predefiniti ripristinati",
        description: "I prompt predefiniti sono stati ripristinati con successo",
        duration: 1000,
      });
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Si è verificato un errore nel ripristino dei prompt predefiniti",
        duration: 3000,
      });
    }
  });

  // Gestisce il salvataggio del prompt (creazione o aggiornamento)
  const savePrompt = (prompt: AIPrompt) => {
    const { id, ...promptData } = prompt;
    if (id) {
      updatePromptMutation.mutate({ id, data: promptData });
    } else {
      createPromptMutation.mutate(promptData);
    }
  };
  
  // Gestisce l'eliminazione del prompt
  const deletePrompt = (promptId: string) => {
    deletePromptMutation.mutate(promptId);
  };
  
  // Ripristina i prompt di default
  const restoreDefaultPrompts = () => {
    resetDefaultPromptsMutation.mutate();
  };
  
  // Apre il dialog per creare un nuovo prompt
  const handleNewPrompt = () => {
    setCurrentPrompt({
      id: "",
      name: "",
      content: "",
      description: "",
      category: activeCategory,
    });
    setIsEditDialogOpen(true);
  };
  
  // Apre il dialog per modificare un prompt esistente
  const handleEditPrompt = (prompt: AIPrompt) => {
    setCurrentPrompt(prompt);
    setIsEditDialogOpen(true);
  };
  
  // Dialog di conferma eliminazione
  const handleDeleteClick = (prompt: AIPrompt) => {
    setCurrentPrompt(prompt);
    setIsDeleteConfirmOpen(true);
  };

  return (
    <div className="flex flex-col h-screen">
      <Header title="Gestione Prompt AI" showBack backPath="/settings" />

      <main className="flex-1 pt-24 pb-16 px-4 overflow-y-auto">
        <div className="max-w-md mx-auto py-4 space-y-6">
          {/* Descrizione */}
          <div className="text-center mb-6">
            <p className="text-sm text-gray-600">
              Personalizza i prompt utilizzati da Claude AI per interpretare i documenti e le etichette.
            </p>
          </div>
          
          {/* Tabs per categorie di prompt */}
          <Tabs defaultValue="ddt" className="w-full" onValueChange={(value) => setActiveCategory(value as "ddt" | "label" | "general")}>
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="ddt">DDT</TabsTrigger>
              <TabsTrigger value="label">Etichette</TabsTrigger>
              <TabsTrigger value="general">Generali</TabsTrigger>
            </TabsList>
            
            {/* Lista prompt DDT */}
            <TabsContent value="ddt" className="space-y-4">
              {prompts.filter(p => p.category === "ddt").length > 0 ? (
                prompts
                  .filter(p => p.category === "ddt")
                  .map(prompt => (
                    <PromptCard 
                      key={prompt.id} 
                      prompt={prompt} 
                      onEdit={handleEditPrompt}
                      onDelete={handleDeleteClick}
                    />
                  ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun prompt per DDT configurato</p>
                </div>
              )}
            </TabsContent>
            
            {/* Lista prompt Etichette */}
            <TabsContent value="label" className="space-y-4">
              {prompts.filter(p => p.category === "label").length > 0 ? (
                prompts
                  .filter(p => p.category === "label")
                  .map(prompt => (
                    <PromptCard 
                      key={prompt.id} 
                      prompt={prompt} 
                      onEdit={handleEditPrompt}
                      onDelete={handleDeleteClick}
                    />
                  ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun prompt per etichette configurato</p>
                </div>
              )}
            </TabsContent>
            
            {/* Lista prompt Generali */}
            <TabsContent value="general" className="space-y-4">
              {prompts.filter(p => p.category === "general").length > 0 ? (
                prompts
                  .filter(p => p.category === "general")
                  .map(prompt => (
                    <PromptCard 
                      key={prompt.id} 
                      prompt={prompt} 
                      onEdit={handleEditPrompt}
                      onDelete={handleDeleteClick}
                    />
                  ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Nessun prompt generale configurato</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
          
          {/* Pulsanti azione */}
          <div className="flex space-x-4 py-4">
            <Button 
              variant="outline"
              className="flex-1 bg-black text-white border-gray-700 hover:bg-black/80"
              onClick={restoreDefaultPrompts}
            >
              Ripristina default
            </Button>
            
            <Button 
              className="flex-1 bg-black text-white hover:bg-black/80 border border-gray-700"
              onClick={handleNewPrompt}
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Nuovo prompt
            </Button>
          </div>
        </div>
      </main>

      <Footer />
      
      {/* Dialog Modifica/Crea Prompt */}
      {currentPrompt && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>{currentPrompt.id ? "Modifica Prompt" : "Nuovo Prompt"}</DialogTitle>
              <DialogDescription>
                {currentPrompt.id ? "Modifica i dettagli del prompt esistente" : "Crea un nuovo prompt AI"}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="prompt-name">Nome</Label>
                <Input 
                  id="prompt-name"
                  placeholder="Nome prompt" 
                  value={currentPrompt.name}
                  onChange={(e) => setCurrentPrompt({...currentPrompt, name: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="prompt-description">Descrizione (opzionale)</Label>
                <Input 
                  id="prompt-description"
                  placeholder="Descrizione breve" 
                  value={currentPrompt.description || ""}
                  onChange={(e) => setCurrentPrompt({...currentPrompt, description: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="prompt-category">Categoria</Label>
                <select
                  id="prompt-category"
                  className="w-full rounded-md border border-gray-300 p-2"
                  value={currentPrompt.category}
                  onChange={(e) => setCurrentPrompt({...currentPrompt, category: e.target.value as "ddt" | "label" | "general"})}
                >
                  <option value="ddt">DDT</option>
                  <option value="label">Etichetta</option>
                  <option value="general">Generale</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="prompt-content">Contenuto Prompt</Label>
                <Textarea 
                  id="prompt-content"
                  placeholder="Inserisci il contenuto del prompt" 
                  value={currentPrompt.content}
                  onChange={(e) => setCurrentPrompt({...currentPrompt, content: e.target.value})}
                  className="min-h-32"
                />
                <p className="text-xs text-gray-500">Puoi usare {'{}'} per inserire variabili che verranno sostituite durante l'esecuzione.</p>
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="prompt-default" className="text-sm">
                  Imposta come prompt predefinito per questa categoria
                </Label>
                <Switch
                  id="prompt-default"
                  checked={currentPrompt.isDefault || false}
                  onCheckedChange={(checked) => setCurrentPrompt({...currentPrompt, isDefault: checked})}
                />
              </div>
            </div>
            
            <DialogFooter className="sm:justify-between">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
              >
                Annulla
              </Button>
              <Button 
                type="button" 
                onClick={() => savePrompt(currentPrompt)}
                disabled={!currentPrompt.name || !currentPrompt.content}
              >
                <SaveIcon className="w-4 h-4 mr-2" />
                Salva
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      
      {/* Dialog Conferma Eliminazione */}
      {currentPrompt && (
        <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Conferma eliminazione</DialogTitle>
              <DialogDescription>
                Sei sicuro di voler eliminare il prompt "{currentPrompt.name}"?
                {currentPrompt.isDefault && (
                  <Alert className="mt-4 border-amber-200 bg-amber-50">
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                    <AlertTitle>Attenzione</AlertTitle>
                    <AlertDescription>
                      Questo è un prompt predefinito. Se lo elimini, potrai ripristinarlo in seguito utilizzando l'opzione "Ripristina default".
                    </AlertDescription>
                  </Alert>
                )}
              </DialogDescription>
            </DialogHeader>
            
            <DialogFooter className="sm:justify-between">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsDeleteConfirmOpen(false)}
              >
                Annulla
              </Button>
              <Button 
                type="button" 
                variant="destructive"
                onClick={() => deletePrompt(currentPrompt.id)}
              >
                <Trash2Icon className="w-4 h-4 mr-2" />
                Elimina
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Componente Card per il prompt
function PromptCard({ prompt, onEdit, onDelete }: { 
  prompt: AIPrompt; 
  onEdit: (prompt: AIPrompt) => void;
  onDelete: (prompt: AIPrompt) => void;
}) {
  return (
    <Card className="overflow-hidden border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{prompt.name}</CardTitle>
            {prompt.description && (
              <CardDescription className="mt-1">{prompt.description}</CardDescription>
            )}
          </div>
          {prompt.isDefault && (
            <span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
              Default
            </span>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 line-clamp-2">
          {prompt.content}
        </p>
      </CardContent>
      <CardFooter className="pt-2 flex justify-end space-x-2">
        <Button 
          size="sm" 
          variant="ghost"
          onClick={() => onDelete(prompt)}
        >
          <Trash2Icon className="h-4 w-4" />
        </Button>
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => onEdit(prompt)}
        >
          <EditIcon className="h-4 w-4 mr-2" />
          Modifica
        </Button>
      </CardFooter>
    </Card>
  );
}

// I prompt predefiniti vengono ora gestiti dal server
