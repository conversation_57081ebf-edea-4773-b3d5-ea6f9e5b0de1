import { useState, useEffect } from 'react';
import { Q<PERSON><PERSON>ner, QRCodeData } from '@/components/ui/qr-scanner';
import { ContainerProductsView } from '@/components/association/container-products-view';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Archive, LinkIcon, PackageSearch, ArrowLeft, Box, Check, ChevronRight, Loader2 } from 'lucide-react';
import { useLocation } from 'wouter';
import { BottomNavigation } from '@/components/layout/bottom-navigation';
import { Header } from '@/components/layout/header';
import { GradientBackground } from '@/components/layout/gradient-background';
import { SimpleProductSelector } from '@/components/ui/simple-product-selector';
import { SimpleContainerSelector } from '@/components/ui/simple-container-selector';
import { LabelSelector } from '@/components/ui/label-selector';
import { ContainerSelector } from '@/components/ui/container-selector';
import { useQueryClient } from '@tanstack/react-query';

export default function QRScannerPage() {
  const [scannedContainerId, setScannedContainerId] = useState<number | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<{id: number, name: string} | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isScanning, setIsScanning] = useState(false); // Start with false to show selection page
  const [currentStep, setCurrentStep] = useState(1); // Step 1: Seleziona prodotto, Step 2: Scansiona Container, Step 3: Successo
  const [showValidOnly, setShowValidOnly] = useState(true); // Filtro per mostrare solo prodotti validi
  const [, navigate] = useLocation();
  
  // Stato per memorizzare il contenitore selezionato manualmente
  const [selectedContainerId, setSelectedContainerId] = useState<number | null>(null);
  const [selectedContainerName, setSelectedContainerName] = useState<string | null>(null);
  
  // New states for initial selection page
  const [selectedLabelId, setSelectedLabelId] = useState<number | null>(null);
  const [selectedContainerForAssociation, setSelectedContainerForAssociation] = useState<number | null>(null);
  const [showSelectionPage, setShowSelectionPage] = useState(true);
  
  const queryClient = useQueryClient();

  // Handler for direct association from selection page
  const handleDirectAssociation = async () => {
    if (!selectedLabelId || !selectedContainerForAssociation) {
      toast({
        title: 'Selezione incompleta',
        description: 'Seleziona sia un\'etichetta che un contenitore',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const { apiRequest } = await import('@/lib/queryClient');
      
      const requestData = {
        productLabelId: selectedLabelId,
        containerId: selectedContainerForAssociation
      };
      
      await apiRequest('/api/container-products', 'POST', requestData);
      
      toast({
        title: 'Successo',
        description: 'Etichetta associata al contenitore con successo',
      });
      
      // Invalidate cache and navigate back
      queryClient.invalidateQueries({ queryKey: ['/api/container-products'] });
      queryClient.invalidateQueries({ queryKey: ['/api/containers', selectedContainerForAssociation] });
      
      navigate('/');
    } catch (error) {
      console.error('Error in direct association:', error);
      toast({
        title: 'Errore',
        description: 'Impossibile completare l\'associazione',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handler to proceed to QR scanning
  const handleProceedToQRScanning = () => {
    setShowSelectionPage(false);
    setIsScanning(true);
  };



  // Handle QR code scanning for contenitore
  const handleContainerScan = async (data: QRCodeData) => {
    console.log("QRScanner: Chiamata onScan con contenitore:", data);
    
    // Verifica che il QR sia di tipo contenitore
    if (data.type !== 'container') {
      // Rimosso toast per evitare duplicazione con l'overlay
      return false; // Restituisci false per segnalare QR invalido
    }
    
    // Verifica che l'ID del contenitore sia valido utilizzando apiRequest
    try {
      // Importa apiRequest dinamicamente per evitare errori di ciclo
      const { apiRequest } = await import('@/lib/queryClient');
      
      // Controlla se il contenitore esiste nel database
      console.log(`Verifica esistenza contenitore ID: ${data.id}`);
      
      try {
        // Usa apiRequest che gestisce automaticamente l'autenticazione
        const containerData = await apiRequest(`/api/containers/${data.id}`, 'GET');
        console.log("Contenitore verificato:", containerData);
        
        console.log("Richiamata callback onSelect per il contenitore");
        
        // Contenitore valido, procedi
        // Se abbiamo un prodotto selezionato, dobbiamo associare
        if (selectedProduct && selectedProduct.id) {
          // Stampa per debug
          console.log(`Associazione del prodotto ${selectedProduct.id} (${selectedProduct.name}) al contenitore ${data.id} (${data.name})`);
          
          associateProductToContainer(selectedProduct.id, data.id, data.name || "Contenitore");
          return true;
        } else {
          // Altrimenti visualizziamo i contenuti del contenitore
          setScannedContainerId(data.id);
          setIsScanning(false);
          
          toast({
            title: 'Contenitore scansionato',
            description: `Visualizzazione prodotti nel contenitore ${data.name || data.id}`,
          });
          return true;
        }
      } catch (err) {
        // Se l'errore è 404, il contenitore non esiste
        if (err && typeof err === 'object' && 'message' in err && 
            typeof err.message === 'string' && err.message.includes("404:")) {
          console.log(`Contenitore con ID ${data.id} non trovato nel database`);
          return false; // Restituisci false per segnalare QR invalido
        }
        
        // Altro errore API
        console.error("Errore API:", err);
        toast({
          title: 'Errore API',
          description: `Errore nel controllo del contenitore: ${err instanceof Error ? err.message : 'Errore sconosciuto'}`,
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error("Errore generale durante la verifica del contenitore:", error);
      toast({
        title: 'Errore di rete',
        description: 'Impossibile verificare l\'esistenza del contenitore',
        variant: 'destructive',
      });
      return false; // Restituisci false per segnalare QR invalido
    }
  };
  
  // Gestisce la selezione di un prodotto
  const handleProductSelect = (productId: number, productName: string) => {
    console.log("QRScanner: Prodotto selezionato direttamente dalla dropdown:", productName);
    setSelectedProduct({ id: productId, name: productName });
    setCurrentStep(2); // Avanza allo step 2: Scansiona Contenitore
    
    toast({
      title: 'Prodotto selezionato',
      description: `Scansiona ora un contenitore per associare: ${productName}`,
    });
  };
  
  // Associa il prodotto selezionato al contenitore scansionato
  const associateProductToContainer = async (productId: number, containerId: number, containerName: string) => {
    setIsSubmitting(true);
    console.log(`=== INIZIO ASSOCIAZIONE: prodotto ${productId} a contenitore ${containerId} (${containerName}) ===`);
    
    try {
      // Importa il metodo apiRequest da @/lib/queryClient
      const { apiRequest } = await import('@/lib/queryClient');
      
      // Verifica contenitore usando apiRequest che gestisce automaticamente l'autenticazione
      const containerCheck = await apiRequest(`/api/containers/${containerId}`, 'GET');
      console.log("Contenitore verificato:", containerCheck);
      
      // Verifica prodotto usando apiRequest
      const productCheck = await apiRequest(`/api/product-labels/${productId}`, 'GET');
      console.log("Prodotto verificato:", productCheck);
      
      // Prepara i dati da inviare
      const requestData = {
        productLabelId: productId,
        containerId: containerId
      };
      
      console.log(`Invio POST /api/container-products con dati:`, JSON.stringify(requestData));
      
      // Usa apiRequest anche per la POST - gestisce automaticamente autenticazione e headers
      const response = await apiRequest('/api/container-products', 'POST', requestData);
      
      console.log("Associazione completata con successo:", response);
      
      toast({
        title: 'Successo',
        description: `Prodotto associato a ${containerName} con successo`,
      });
      
      // Invalidare le query relevanti
      console.log("Invalidazione cache per contenitori e prodotti specifici");
      queryClient.invalidateQueries({ queryKey: ['/api/container-products'] });
      queryClient.invalidateQueries({ queryKey: ['/api/containers', containerId] });
      
      // Passa allo step 3 (successo)
      setCurrentStep(3);
      
      // Reset della selezione prodotto - dopo 2 secondi torna allo step 1
      setTimeout(() => {
        setSelectedProduct(null);
        setCurrentStep(1);
      }, 2000);
    } catch (err) {
      // Se c'è un errore 400, potrebbe essere perché il prodotto è già associato
      if (err && typeof err === 'object' && 'message' in err && 
          typeof err.message === 'string' && err.message.includes("400:")) {
        
        if (err.message.includes("already associated")) {
          toast({
            title: 'Informazione',
            description: 'Il prodotto è già associato a questo contenitore.',
            variant: 'default',
          });
          
          // Per coerenza UX, completiamo comunque il flusso
          setCurrentStep(3);
          
          // Reset della selezione prodotto - dopo 2 secondi torna allo step 1
          setTimeout(() => {
            setSelectedProduct(null);
            setCurrentStep(1);
          }, 2000);
          
          return;
        }
      }
      
      // Altri errori
      console.error("Errore durante l'associazione:", err);
      toast({
        title: 'Errore',
        description: `Impossibile completare l'associazione: ${err instanceof Error ? err.message : 'Errore sconosciuto'}`,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Reset the container view
  const resetScan = () => {
    // Prima fermare qualsiasi scanner attivo rilasciando risorse della webcam
    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      if (video.srcObject) {
        const mediaStream = video.srcObject as MediaStream;
        const tracks = mediaStream.getTracks();
        tracks.forEach(track => {
          track.stop(); // Ferma esplicitamente ogni traccia
        });
        video.srcObject = null;
      }
    });
    
    setScannedContainerId(null);
    setIsScanning(true);
    setCurrentStep(1); // Ritorna allo step 1
    setSelectedProduct(null); // Reset del prodotto selezionato
  };
  
  // Componente per visualizzare gli step
  const StepIndicator = () => {
    return (
      <div className="mb-5">
        {/* Circle progress indicator */}
        <div className="flex items-center justify-center mb-3">
          <div className="flex items-center relative w-full max-w-sm">
            {/* Line track */}
            <div className="absolute w-full h-2 bg-gray-200 rounded-full"></div>
            
            {/* Completed line */}
            <div 
              className="absolute h-2 bg-blue-500 transition-all duration-300 rounded-full" 
              style={{ 
                width: currentStep === 1 ? '0%' : currentStep === 2 ? '50%' : '100%' 
              }}
            ></div>
            
            {/* Step 1 circle */}
            <div className={`relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 shadow-md
                ${currentStep >= 1 ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 bg-white text-gray-500'}`}>
              {currentStep > 1 ? <Check className="w-5 h-5" /> : "1"}
            </div>
            
            {/* Spacer */}
            <div className="flex-grow"></div>
            
            {/* Step 2 circle */}
            <div className={`relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 shadow-md
                ${currentStep >= 2 ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 bg-white text-gray-500'}`}>
              {currentStep > 2 ? <Check className="w-5 h-5" /> : "2"}
            </div>
            
            {/* Spacer */}
            <div className="flex-grow"></div>
            
            {/* Step 3 circle */}
            <div className={`relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 shadow-md
                ${currentStep >= 3 ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 bg-white text-gray-500'}`}>
              {currentStep >= 3 ? <Check className="w-5 h-5" /> : "3"}
            </div>
          </div>
        </div>
        
        {/* Step labels */}
        <div className="flex justify-between text-sm font-medium">
          <span className={`${currentStep >= 1 ? 'text-blue-600' : 'text-gray-600'} text-center w-20 ml-1`}>Prodotto</span>
          <span className={`${currentStep >= 2 ? 'text-blue-600' : 'text-gray-600'} text-center w-20`}>Container</span>
          <span className={`${currentStep >= 3 ? 'text-blue-600' : 'text-gray-600'} text-center w-20 mr-1`}>Completato</span>
        </div>
      </div>
    );
  };

  // Render selection page component
  const renderSelectionPage = () => (
    <Card className="rounded-lg border bg-card text-card-foreground p-6 mb-4 shadow-md pt-[14px] pb-[14px]">
      <h2 className="text-xl font-semibold mb-6 text-center">Associa Etichetta a Contenitore</h2>
      
      <div className="space-y-6">
        {/* Label selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Seleziona un'etichetta
          </label>
          <LabelSelector
            onSelect={setSelectedLabelId}
            currentValue={selectedLabelId || undefined}
          />
        </div>

        {/* Container selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Seleziona un contenitore
          </label>
          <ContainerSelector
            onSelect={setSelectedContainerForAssociation}
            currentValue={selectedContainerForAssociation || undefined}
          />
        </div>

        {/* Action buttons */}
        <div className="space-y-3 pt-4">
          <Button
            onClick={handleDirectAssociation}
            disabled={!selectedLabelId || !selectedContainerForAssociation || isSubmitting}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Associando...
              </>
            ) : (
              <>
                <LinkIcon className="h-4 w-4 mr-2" />
                Associa
              </>
            )}
          </Button>

          <Button
            onClick={handleProceedToQRScanning}
            variant="outline"
            className="w-full"
          >
            <PackageSearch className="h-4 w-4 mr-2" />
            QR-Code
          </Button>

          <Button
            onClick={() => navigate('/')}
            variant="ghost"
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Annulla
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <GradientBackground>
      <Header title="Scanner QR" showBack={true} />
      
      <main className="flex-1 pt-24 pb-20 px-6 overflow-y-auto relative z-10">
        <div className="w-full max-w-md mx-auto">
          {showSelectionPage ? (
            renderSelectionPage()
          ) : isScanning ? (
            <div>
              {/* Visualizzazione step sempre visibile */}
              <StepIndicator />
              
              <Card className="p-3 mb-4 shadow-md flex flex-col">
                {currentStep === 3 ? (
                  <div className="text-center py-4">
                    <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Check className="w-10 h-10 text-green-500" />
                    </div>
                    <h2 className="text-2xl font-semibold text-green-700 mb-2">Associazione Completata!</h2>
                    <p className="text-gray-600 mb-4">
                      Il prodotto è stato associato con successo al contenitore.
                    </p>
                    <p className="text-sm text-gray-500">Ritorno alla scansione tra un momento...</p>
                  </div>
                ) : selectedProduct && selectedProduct.id ? (
                  <>
                    <div className="mb-3">
                      <Button
                        variant="ghost"
                        className="flex items-center text-blue-600 p-0 mb-1"
                        onClick={() => {
                          setSelectedProduct(null);
                          setCurrentStep(1);
                        }}
                      >
                        <ArrowLeft className="w-4 h-4 mr-1" />
                        Torna ai prodotti
                      </Button>
                      
                      <div className="bg-blue-50 p-2 rounded-md border border-blue-100">
                        <h3 className="font-medium text-blue-800 text-sm">Prodotto selezionato: {selectedProduct.name}</h3>
                        <p className="text-blue-700 text-xs">Ora scegli un contenitore dove posizionarlo</p>
                      </div>
                    </div>
                    
                    <h2 className="text-lg font-semibold mb-2">Seleziona un Contenitore</h2>
                    <p className="text-gray-600 text-sm mb-2">
                      Scansiona un QR code o seleziona un contenitore dalla lista per associare il prodotto
                    </p>
                    
                    {/* Aggiunto selettore contenitore quando un prodotto è già selezionato */}
                    <div className="mb-4 bg-gray-50 p-3 rounded-md border border-gray-200">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Seleziona Contenitore dalla lista:</h3>
                      
                      {/* Stato per memorizzare il contenitore selezionato localmente */}
                      <div className="space-y-4">
                        <SimpleContainerSelector 
                          productId={selectedProduct.id}
                          onSelect={(containerId: number, containerName: string) => {
                            console.log(`Container selezionato dalla tendina: ${containerName} (ID: ${containerId})`);
                            // Inizia immediatamente l'associazione
                            associateProductToContainer(selectedProduct.id, containerId, containerName);
                          }} 
                        />
                        
                        <div className="text-center py-2 text-sm text-gray-500 italic">
                          Seleziona un contenitore dall'elenco sopra per completare l'associazione
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <h2 className="text-lg font-semibold mb-2">Scan Prodotto QR-Code</h2>
                    <p className="text-gray-600 text-sm mb-3">
                      Scansiona un QR code di un prodotto per associarlo a un contenitore
                    </p>
                  </>
                )}
                
                {currentStep !== 3 && (
                  <QRScanner
                    onScan={handleContainerScan}
                    buttonText="Scan QR Code"
                    isAutoScan={true}
                    showToggle={false}
                    showCameraInstantly={true}
                  />
                )}
              </Card>
              
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-center mb-4">
                <Button 
                  variant="outline"
                  onClick={resetScan}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Torna alla scansione
                </Button>
                
                {/* Titolo rimosso da qui */}
              </div>
              
              {scannedContainerId && (
                <>
                  {/* Titolo spostato qui, sopra il componente ContainerProductsView */}
                  <div className="mb-4 mt-2">
                    <h2 className="text-lg font-semibold text-blue-800 flex items-center">
                      <Archive className="h-5 w-5 mr-2" />
                      Contenuto Container
                    </h2>
                  </div>
                  <ContainerProductsView containerId={scannedContainerId} />
                </>
              )}
            </div>
          )}
        </div>
      </main>
      <BottomNavigation activeItem="qrscan" />
    </GradientBackground>
  );
}