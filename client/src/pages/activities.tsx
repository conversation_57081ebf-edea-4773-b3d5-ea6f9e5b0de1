import { useEffect, useState } from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Container } from '@/components/ui/container';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/context/auth-context';
import { useToast } from '@/hooks/use-toast';
import { ActivityLog, User } from '@shared/schema';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Calendar, Clock, User as UserIcon } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Skeleton } from '@/components/ui/skeleton';
import { ActivityFilters } from '@/components/activity/activity-filters';
import { ActivityFiltersSummary } from '@/components/activity/activity-filters-summary';
import { useActivityLogFilters } from '@/hooks/use-activity-log-filters';
import { useSafeAsyncState } from '@/hooks/useSafeAsyncState';

function getActionColor(action: string) {
  switch(action) {
    case 'login':
      return 'bg-emerald-100 text-emerald-800';
    case 'logout':
      return 'bg-amber-100 text-amber-800';
    case 'create_ddt':
    case 'create_product_label':
    case 'create_container':
    case 'create_supplier':
    case 'create_supplier_from_ddt':
      return 'bg-blue-100 text-blue-800';
    case 'process_ddt_ocr':
    case 'process_label_ocr':
      return 'bg-purple-100 text-purple-800';
    case 'update_profile':
    case 'update_supplier':
      return 'bg-indigo-100 text-indigo-800';
    case 'link_ddt_supplier':
      return 'bg-teal-100 text-teal-800';
    case 'add_product_to_container':
      return 'bg-green-100 text-green-800';
    case 'remove_product_from_container':
      return 'bg-red-100 text-red-800';
    case 'archive_container':
      return 'bg-slate-100 text-slate-800';
    case 'reset_default_prompts':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getActionLabel(action: string) {
  switch(action) {
    case 'login':
      return 'Accesso al sistema';
    case 'logout':
      return 'Disconnessione dal sistema';
    case 'create_ddt':
      return 'Creazione documento di trasporto';
    case 'create_product_label':
      return 'Aggiunta etichetta prodotto';
    case 'create_container':
      return 'Creazione nuovo contenitore';
    case 'create_supplier_from_ddt':
      return 'Fornitore creato automaticamente da DDT';
    case 'create_supplier':
      return 'Creazione nuovo fornitore';
    case 'update_supplier':
      return 'Aggiornamento dati fornitore';
    case 'process_ddt_ocr':
      return 'Elaborazione automatica DDT';
    case 'process_label_ocr':
      return 'Elaborazione automatica etichetta';
    case 'link_ddt_supplier':
      return 'Collegamento DDT a fornitore';
    case 'update_profile':
      return 'Aggiornamento profilo utente';
    case 'add_product_to_container':
      return 'Prodotto aggiunto al contenitore';
    case 'remove_product_from_container':
      return 'Prodotto rimosso dal contenitore';
    case 'archive_container':
      return 'Archiviazione contenitore';
    case 'reset_default_prompts':
      return 'Ripristino modelli predefiniti';
    default:
      // Traduci i separatori e rendi tutto in italiano
      const translated = action
        .replace(/_/g, ' ')
        .replace(/login/g, 'accesso')
        .replace(/logout/g, 'disconnessione')
        .replace(/create/g, 'creazione')
        .replace(/update/g, 'aggiornamento')
        .replace(/delete/g, 'eliminazione')
        .replace(/add/g, 'aggiunta')
        .replace(/remove/g, 'rimozione')
        .replace(/process/g, 'elaborazione')
        .replace(/link/g, 'collegamento')
        .replace(/archive/g, 'archiviazione')
        .replace(/container/g, 'contenitore')
        .replace(/product/g, 'prodotto')
        .replace(/supplier/g, 'fornitore')
        .replace(/profile/g, 'profilo')
        .replace(/label/g, 'etichetta')
        .replace(/\b\w/g, l => l.toUpperCase());
      return translated;
  }
}

export default function ActivitiesPage() {
  const { isAuthenticated, user } = useAuth();
  const { toast } = useToast();
  const [isAdmin, setIsAdmin] = useSafeAsyncState(false);

  const {
    filters,
    applyFilters,
    exportData,
    resetFilters,
    removeFilter,
    activities,
    isLoading
  } = useActivityLogFilters();

  // Check if user is admin
  useEffect(() => {
    if (user) {
      setIsAdmin(user.isAdmin === true);
    }
  }, [user]);

  return (
    <div className="min-h-screen flex flex-col bg-[#f5f5f7]">
      <Header title="Attività Recenti" showBack />

      <main className="flex-1 pt-24 pb-32 px-4 overflow-y-auto">
        <Container>
          <div className="mb-6 text-center md:text-left px-4 pt-4">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Attività Recenti</h1>
            <p className="text-gray-500 mt-2">
              Visualizza la cronologia delle attività dell'applicazione
            </p>
          </div>
          
          <ActivityFilters 
            onApplyFilters={applyFilters}
            onExport={exportData}
            onReset={resetFilters}
            filters={filters}
          />
          
          {Object.keys(filters).length > 0 && (
            <div className="mb-4 p-3 bg-gray-50 rounded-md mx-4">
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Filtri attivi:</span>
                
                {filters.startDate && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
                    Da: {filters.startDate}
                    <button 
                      className="h-4 w-4 p-0 ml-1 text-blue-700 hover:text-blue-900" 
                      onClick={() => removeFilter('startDate')}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}
                
                {filters.endDate && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
                    A: {filters.endDate}
                    <button
                      className="h-4 w-4 p-0 ml-1 text-blue-700 hover:text-blue-900"
                      onClick={() => removeFilter('endDate')}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}
                
                {filters.userId && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                    Utente ID: {filters.userId}
                    <button
                      className="h-4 w-4 p-0 ml-1 text-green-700 hover:text-green-900"
                      onClick={() => removeFilter('userId')}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}
                
                {filters.containerId && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1">
                    Contenitore ID: {filters.containerId}
                    <button
                      className="h-4 w-4 p-0 ml-1 text-purple-700 hover:text-purple-900"
                      onClick={() => removeFilter('containerId')}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}
                
                {filters.action && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1">
                    Azione: {getActionLabel(filters.action)}
                    <button
                      className="h-4 w-4 p-0 ml-1 text-amber-700 hover:text-amber-900"
                      onClick={() => removeFilter('action')}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}
                
                <button
                  className="ml-auto text-gray-500 hover:text-gray-700 p-1 h-auto text-xs"
                  onClick={resetFilters}
                >
                  Rimuovi tutti
                </button>
              </div>
            </div>
          )}

          <Card className="mx-4">
            <CardContent className="pt-6">
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div className="flex space-x-4 items-center" key={i}>
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-36" />
                        <Skeleton className="h-4 w-48" />
                      </div>
                      <div className="ml-auto space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-36" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : !activities || activities.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  Nessuna attività registrata.
                </div>
              ) : (
                <div className="space-y-4">
                  {activities.map((activity: ActivityLog) => (
                    <div
                      key={activity.id}
                      className="border-b border-gray-100 pb-4 last:border-0 last:pb-0"
                    >
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div className="flex items-center gap-3">
                          <UserIcon className="h-6 w-6 text-gray-400" />
                          <div>
                            <div className="text-sm md:text-base font-medium text-gray-900">
                              {activity.username}
                            </div>
                            <div className="text-sm text-gray-600">{activity.details}</div>
                          </div>
                        </div>

                        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-3">
                          <Badge className={getActionColor(activity.action)}>
                            {getActionLabel(activity.action)}
                          </Badge>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {format(
                              new Date(activity.timestamp),
                              "d MMMM yyyy",
                              { locale: it }
                            )}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {format(
                              new Date(activity.timestamp),
                              "HH:mm",
                              { locale: it }
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </Container>
      </main>

      <Footer />
    </div>
  );
}
