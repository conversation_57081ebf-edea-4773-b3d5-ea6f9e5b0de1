import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ProductLabel, Container as ContainerType, ContainerProduct } from "@shared/schema";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogClose, DialogTitle, DialogHeader, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Box, Tag, QrCode, CalendarClock, FileText, Package, Download, Printer, X, ZoomIn, ZoomOut, RotateCcw, Archive, Edit } from "lucide-react";
import { formatDate } from "@/lib/utils";
import * as QRUtils from "@/lib/qr-utils";
import { QRCode, QRCodeType, generateQRCodeFilename } from "@/components/ui/qr-code";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Container } from "@/components/ui/container";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ImageViewerModal } from "@/components/ui/image-viewer-modal";
import { RetireProductDialog } from "@/components/retire-product-dialog";

export default function ProductDetails() {
  const { id } = useParams<{ id: string }>();
  const productId = parseInt(id);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedContainer, setSelectedContainer] = useState<number | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isRetireDialogOpen, setIsRetireDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({
    productName: "",
    expiryDate: "",
    batchNumber: "",
    storageInstructions: "",
    notes: ""
  });
  const [_, setLocation] = useLocation();

  const { data: product, isLoading: isLoadingProduct } = useQuery<ProductLabel>({
    queryKey: ["/api/product-labels", productId],
    queryFn: async () => {
      const response = await fetch(`/api/product-labels/${productId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch product label");
      }
      return response.json();
    },
    enabled: !isNaN(productId)
  });

  const { data: containers, isLoading: isLoadingContainers } = useQuery<ContainerType[]>({
    queryKey: ["/api/containers"],
    queryFn: async () => {
      const response = await fetch(`/api/containers`);
      if (!response.ok) {
        throw new Error("Failed to fetch containers");
      }
      return response.json();
    }
  });

  const { data: containerProducts, isLoading: isLoadingContainerProducts } = useQuery<ContainerProduct[]>({
    queryKey: ["/api/container-products/product", productId],
    queryFn: async () => {
      const response = await fetch(`/api/container-products/product/${productId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch container products");
      }
      return response.json();
    },
    enabled: !isNaN(productId)
  });

  const addToContainerMutation = useMutation({
    mutationFn: async (data: { containerId: number; productLabelId: number }) => {
      return await apiRequest("POST", "/api/container-products", data);
    },
    onSuccess: () => {
      toast({
        title: "Prodotto aggiunto",
        description: "Il prodotto è stato aggiunto al container con successo",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/container-products/product", productId] });
      queryClient.invalidateQueries({ queryKey: ["/api/containers"] });
      setSelectedContainer(null);
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: error.message || "Si è verificato un errore durante l'aggiunta del prodotto",
        variant: "destructive",
      });
    }
  });

  const updateProductMutation = useMutation({
    mutationFn: async (data: Partial<ProductLabel>) => {
      return await apiRequest("PATCH", `/api/product-labels/${productId}`, data);
    },
    onSuccess: () => {
      toast({
        title: "Prodotto aggiornato",
        description: "Le informazioni del prodotto sono state aggiornate con successo",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels", productId] });
      queryClient.invalidateQueries({ queryKey: ["/api/product-labels"] });
      setIsEditDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: error.message || "Si è verificato un errore durante l'aggiornamento del prodotto",
        variant: "destructive",
      });
    }
  });

  const handleEditProduct = () => {
    if (!product) return;
    
    // Convert DD/MM/YYYY or DD-MM-YYYY to YYYY-MM-DD for the date input
    let formattedDate = "";
    if (product.expiryDate) {
      // Try both formats: with slashes or with dashes
      const dateParts = product.expiryDate.includes('/') 
        ? product.expiryDate.split('/') 
        : product.expiryDate.split('-');
      
      if (dateParts.length === 3) {
        // Convert from DD/MM/YYYY or DD-MM-YYYY to YYYY-MM-DD
        formattedDate = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`;
      }
    }
    
    setEditFormData({
      productName: product.productName || "",
      expiryDate: formattedDate,
      batchNumber: product.batchNumber || "",
      storageInstructions: product.storageInstructions || "",
      notes: product.notes || ""
    });
    
    setIsEditDialogOpen(true);
  };

  const handleFormChange = (field: string, value: string) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveChanges = () => {
    const formDataToSubmit = { ...editFormData };
    
    // Convert date from YYYY-MM-DD back to DD/MM/YYYY format
    if (formDataToSubmit.expiryDate) {
      const dateParts = formDataToSubmit.expiryDate.split('-');
      if (dateParts.length === 3) {
        // Convert from YYYY-MM-DD to DD/MM/YYYY
        formDataToSubmit.expiryDate = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
      }
    }
    
    updateProductMutation.mutate(formDataToSubmit);
  };

  const handleAddToContainer = (container: ContainerType) => {
    if (!product) return;
    
    // Check if container is full
    if (container.currentItems >= container.maxItems) {
      toast({
        title: "Container pieno",
        description: "Il container selezionato ha raggiunto la capacità massima",
        variant: "destructive",
      });
      return;
    }
    
    // Check if product is already in the container
    // Verifica più robusta con migliore controllo errori
    if (Array.isArray(containerProducts) && containerProducts.length > 0) {
      const alreadyInContainer = containerProducts.some(
        (cp) => cp.containerId === container.id && cp.productLabelId === product.id
      );
      
      if (alreadyInContainer) {
        toast({
          title: "Prodotto già presente",
          description: "Questo prodotto è già stato aggiunto al container selezionato",
          variant: "destructive",
        });
        return;
      }
    } else {
      // Se non abbiamo dati sui prodotti nel container, facciamo una verifica aggiuntiva
      console.log("Nessun dato disponibile sui prodotti nel container. Procediamo con l'aggiunta.");
    }
    
    addToContainerMutation.mutate({
      containerId: container.id,
      productLabelId: product.id
    });
  };

  // Handle QR Code download
  const handleQRDownload = async () => {
    if (!product) return;
    
    const qrValue = `product:${product.id}:${product.productName.replace(/\s+/g, '_')}`;
    const filename = `${product.productName.replace(/\s+/g, '_')}-${product.batchNumber}`;
    
    try {
      const success = await QRUtils.generateAndDownloadQRCode(
        product.productName,
        filename,
        qrValue
      );
      
      if (!success) {
        toast({
          title: "Errore di download",
          description: "Non è stato possibile generare il QR code per il download. Riprova.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Errore durante il download del QR code:", error);
      toast({
        title: "Errore di download",
        description: "Si è verificato un errore durante il download del QR code.",
        variant: "destructive",
      });
    }
  };
  
  // Handle QR Code print
  const handleQRPrint = async () => {
    if (!product) return;
    
    const qrValue = `product:${product.id}:${product.productName.replace(/\s+/g, '_')}`;
    const title = `${product.productName} - Lotto: ${product.batchNumber} - Scadenza: ${formatDate(product.expiryDate)}`;
    
    try {
      const success = await QRUtils.printQRCode(qrValue, title);
      
      if (!success) {
        toast({
          title: "Errore di stampa",
          description: "Non è stato possibile generare il QR code per la stampa. Riprova.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Errore durante la stampa del QR code:", error);
      toast({
        title: "Errore di stampa",
        description: "Si è verificato un errore durante la stampa del QR code.",
        variant: "destructive",
      });
    }
  };

  if (isLoadingProduct || isLoadingContainers || isLoadingContainerProducts) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Dettagli Prodotto" showBack backPath="/search" />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-500">Caricamento in corso...</p>
          </div>
        </div>
        <Footer activeItem="search" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex flex-col h-screen bg-[#f5f5f7]">
        <Header title="Dettagli Prodotto" showBack backPath="/search" />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6 bg-white rounded-2xl shadow-md border border-gray-100">
            <h1 className="text-2xl font-bold mb-4 text-gray-900">Prodotto non trovato</h1>
            <p className="text-gray-500 mb-6">Il prodotto richiesto non esiste o è stato rimosso.</p>
            <Button asChild className="bg-indigo-600 hover:bg-indigo-700 text-white">
              <Link href="/search">Torna alla Ricerca</Link>
            </Button>
          </div>
        </div>
        <Footer activeItem="search" />
      </div>
    );
  }

  // Filter out archived containers and already associated containers
  const availableContainers = containers?.filter(container => 
    !container.isArchived && 
    container.currentItems < container.maxItems &&
    !containerProducts?.some(cp => cp.containerId === container.id && cp.productLabelId === product.id)
  ) || [];

  // Get containers this product is in
  const productContainers = containerProducts
    ? containers?.filter(container => 
        containerProducts.some(cp => cp.containerId === container.id)
      ) || []
    : [];

  return (
    <div className="flex flex-col h-screen bg-[#f5f5f7]">
      <Header title="Dettagli Prodotto" showBack backPath="/search" />
      
      <main className="flex-1 pt-24 pb-32 px-6 overflow-y-auto">
        <div className="w-full max-w-md mx-auto space-y-6">        
          <Tabs defaultValue="info" className="w-full">
            <TabsList className="grid w-full grid-cols-3 rounded-xl bg-gray-200/80">
              <TabsTrigger value="info" className="rounded-lg">Informazioni</TabsTrigger>
              <TabsTrigger value="containers" className="rounded-lg">Container ({productContainers.length})</TabsTrigger>
              <TabsTrigger value="qrcode" className="rounded-lg">QR Code</TabsTrigger>
            </TabsList>

            <TabsContent value="info" className="mt-4">
              <Container variant="default" className="w-full p-5 overflow-hidden">
                <div className="p-0">
                  <div className="mb-6">
                    <h1 className="text-xl font-medium flex items-center mb-1 text-gray-900">
                      <Tag className="mr-2 h-5 w-5 text-indigo-600" />
                      {product.productName}
                    </h1>
                    <p className="text-sm text-gray-500">
                      Etichetta prodotto del {formatDate(product.createdAt)}
                    </p>
                  </div>

                  <div className="flex flex-col space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Lotto:</h3>
                        <p className="text-lg font-medium text-gray-900">{product.batchNumber}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Scadenza:</h3>
                        <p className="text-lg font-medium text-gray-900 flex items-center">
                          <CalendarClock className="h-4 w-4 mr-1 text-indigo-600" />
                          {formatDate(product.expiryDate)}
                        </p>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Conservazione:</h3>
                      <p className="text-gray-900">{product.storageInstructions}</p>
                    </div>
                    
                    {product.image && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Immagine Etichetta:</h3>
                        <div 
                          className="rounded-md overflow-hidden border border-gray-200 cursor-pointer"
                          onClick={() => {
                            setSelectedImage(product.image);
                            setDialogOpen(true);
                          }}
                        >
                          <img 
                            src={product.image} 
                            alt="Etichetta Prodotto" 
                            className="w-full object-contain max-h-80"
                          />
                          <div className="p-2 bg-gray-50 text-xs text-center text-gray-500">
                            Clicca per ingrandire
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-8 flex flex-col gap-3">
                    <Button asChild className="h-10 rounded-xl text-white bg-indigo-600 shadow-md hover:bg-indigo-700 transition-all duration-200">
                      <Link href={`/ddt-details/${product.ddtId}`}>
                        <FileText className="mr-2 h-4 w-4" />
                        Vai al DDT collegato
                      </Link>
                    </Button>
                    
                    {!product.isRetired && (
                      <>
                        <Button 
                          onClick={handleEditProduct}
                          variant="outline"
                          className="h-10 rounded-xl border-green-200 text-green-600 hover:bg-green-50 hover:border-green-300 transition-all duration-200"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Modifica Prodotto
                        </Button>
                        
                        <Button 
                          onClick={() => setIsRetireDialogOpen(true)}
                          variant="outline"
                          className="h-10 rounded-xl border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300 transition-all duration-200"
                        >
                          <Archive className="mr-2 h-4 w-4" />
                          Ritira Prodotto
                        </Button>
                      </>
                    )}
                    
                    {product.isRetired && (
                      <div className="p-4 bg-orange-50 border border-orange-200 rounded-xl">
                        <div className="flex items-center gap-2 text-orange-700">
                          <Archive className="h-4 w-4" />
                          <span className="font-medium">Prodotto Ritirato</span>
                        </div>
                        <p className="text-sm text-orange-600 mt-1">
                          Questo prodotto è stato ritirato il {formatDate(product.retiredAt!)}
                        </p>
                        {product.retiredReason && (
                          <p className="text-xs text-orange-600 mt-1">
                            Motivo: {product.retiredReason}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </Container>
            </TabsContent>

            <TabsContent value="containers" className="mt-4">
              <Container variant="default" className="w-full p-5 overflow-hidden">
                <div className="p-0">
                  <div className="flex items-center mb-4">
                    <div className="flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-2">
                      <Box className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div className="ml-3">
                      <h2 className="text-lg font-medium text-gray-900">Contenitori con questo prodotto</h2>
                      <p className="text-sm text-gray-500">
                        Contenitori dove il prodotto è conservato
                      </p>
                    </div>
                  </div>
                  
                  {productContainers.length > 0 ? (
                    <ScrollArea className="h-[300px] mt-4">
                      <div className="space-y-4">
                        {productContainers.map((container) => (
                          <div 
                            key={container.id} 
                            className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-all duration-200 cursor-pointer" 
                            onClick={() => setLocation(`/container/${container.id}`)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-medium text-gray-900">{container.name}</h3>
                                <p className="text-sm text-gray-500">Tipo: {container.type}</p>
                              </div>
                              <Badge variant={container.currentItems >= container.maxItems ? "destructive" : "outline"}>
                                {container.currentItems}/{container.maxItems} prodotti
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  ) : (
                    <div className="text-center py-8">
                      <Box className="mx-auto h-12 w-12 text-gray-300" />
                      <h3 className="mt-4 text-lg font-medium text-gray-900">Nessun container</h3>
                      <p className="mt-2 text-sm text-gray-500">
                        Questo prodotto non è attualmente associato a nessun container.
                      </p>
                    </div>
                  )}

                  <div className="mt-6 border-t border-gray-100 pt-6">
                    <h3 className="font-medium mb-3 text-gray-900">Aggiungi a Container</h3>
                    
                    {availableContainers.length > 0 ? (
                      <div className="flex space-x-2">
                        <Select 
                          value={selectedContainer?.toString() || ""} 
                          onValueChange={(value) => setSelectedContainer(parseInt(value))}
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Seleziona container" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableContainers.map((container) => (
                              <SelectItem key={container.id} value={container.id.toString()}>
                                {container.name} ({container.currentItems}/{container.maxItems})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button 
                          onClick={() => {
                            if (selectedContainer) {
                              const container = containers?.find(c => c.id === selectedContainer);
                              if (container) handleAddToContainer(container);
                            }
                          }}
                          disabled={!selectedContainer || addToContainerMutation.isPending}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Package className="mr-2 h-4 w-4" />
                          Aggiungi
                        </Button>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">
                        Non ci sono container disponibili. Crea un nuovo container o svuota quelli esistenti.
                      </p>
                    )}
                  </div>
                </div>
              </Container>
            </TabsContent>

            <TabsContent value="qrcode" className="mt-4">
              <Container variant="default" className="w-full p-5 overflow-hidden">
                <div className="p-0 flex flex-col items-center">
                  <div className="w-full flex items-center mb-4 justify-center">
                    <div className="flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-2">
                      <QrCode className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div className="ml-3 text-center">
                      <h2 className="text-lg font-medium text-gray-900">QR Code Etichetta</h2>
                      <p className="text-sm text-gray-500">
                        Scansiona per accedere rapidamente alle informazioni
                      </p>
                    </div>
                  </div>
                  
                  <div className="my-4 bg-white p-4 rounded-lg border border-gray-200">
                    <div className="flex flex-col items-center">
                      {/* QR Code */}
                      <QRCode 
                        value={`product:${product.id}:${product.productName.replace(/\s+/g, '_')}`}
                        size={200}
                        fgColor="#000000"
                        bgColor="#FFFFFF"
                        ecLevel="H"
                      />
                      
                      <div className="text-center mb-3">
                        <p className="font-black text-lg uppercase tracking-wide">{product.productName}</p>
                      </div>
                      
                      <div className="flex gap-3 justify-center">
                        <button
                          onClick={handleQRDownload}
                          className="px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md shadow-sm hover:bg-blue-600 transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center gap-2"
                        >
                          <Download size={16} />
                          Scarica
                        </button>
                        <button
                          onClick={handleQRPrint}
                          className="px-3 py-2 text-sm font-medium bg-green-500 text-white rounded-md shadow-sm hover:bg-green-600 transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-300 flex items-center justify-center gap-2"
                        >
                          <Printer size={16} />
                          Stampa
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </Container>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer activeItem="search" />

      {/* Dialog per visualizzare le immagini a dimensione piena con supporto per zoom e pinch-to-zoom */}
      <ImageViewerModal
        isOpen={dialogOpen}
        onClose={() => setDialogOpen(false)}
        imageSrc={selectedImage}
      />

      {/* Dialog per il ritiro prodotti */}
      <RetireProductDialog
        open={isRetireDialogOpen}
        onOpenChange={setIsRetireDialogOpen}
        productId={product.id}
        productName={product.productName}
      />

      {/* Dialog per la modifica prodotto */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Modifica Prodotto</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="productName">Nome Prodotto</Label>
              <Input
                id="productName"
                value={editFormData.productName}
                onChange={(e) => handleFormChange('productName', e.target.value)}
                placeholder="Nome del prodotto"
              />
            </div>
            
            <div>
              <Label htmlFor="expiryDate">Data Scadenza</Label>
              <Input
                id="expiryDate"
                type="date"
                value={editFormData.expiryDate}
                onChange={(e) => handleFormChange('expiryDate', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="batchNumber">Numero Lotto</Label>
              <Input
                id="batchNumber"
                value={editFormData.batchNumber}
                onChange={(e) => handleFormChange('batchNumber', e.target.value)}
                placeholder="Numero del lotto"
              />
            </div>
            
            <div>
              <Label htmlFor="storageInstructions">Istruzioni Conservazione</Label>
              <Textarea
                id="storageInstructions"
                value={editFormData.storageInstructions}
                onChange={(e) => handleFormChange('storageInstructions', e.target.value)}
                placeholder="Istruzioni per la conservazione"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="notes">Note</Label>
              <Textarea
                id="notes"
                value={editFormData.notes}
                onChange={(e) => handleFormChange('notes', e.target.value)}
                placeholder="Note aggiuntive"
                rows={2}
              />
            </div>
          </div>
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="flex-1"
            >
              Annulla
            </Button>
            <Button
              onClick={handleSaveChanges}
              disabled={updateProductMutation.isPending}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              {updateProductMutation.isPending ? "Salvataggio..." : "Salva Modifiche"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}