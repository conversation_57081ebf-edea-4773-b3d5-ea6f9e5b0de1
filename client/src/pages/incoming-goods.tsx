import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Camera } from "@/components/ui/camera";
import { QRScanner } from "@/components/ui/qr-scanner";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { useLocation } from "wouter";
import { Camera as CameraIcon, Upload as UploadIcon, Tag as TagIcon } from "lucide-react";
import { GradientBackground } from "@/components/layout/gradient-background";

export default function IncomingGoods() {
  const [showCamera, setShowCamera] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<"ddt" | "label">("ddt");
  const [, setLocation] = useLocation();
  
  // Funzione per correggere l'orientamento delle immagini
  const fixOrientation = (imageDataUrl: string): Promise<string> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // La modalità verticale richiede l'altezza maggiore della larghezza
        let width = img.width;
        let height = img.height;
        let shouldRotate = width > height; // Se la larghezza è maggiore dell'altezza, ruotiamo
        
        if (shouldRotate) {
          // Ruotiamo l'immagine di 90 gradi
          canvas.width = height;
          canvas.height = width;
          if (ctx) {
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate(Math.PI / 2);
            ctx.drawImage(img, -width / 2, -height / 2, width, height);
          }
        } else {
          // Nessuna rotazione necessaria
          canvas.width = width;
          canvas.height = height;
          if (ctx) {
            ctx.drawImage(img, 0, 0, width, height);
          }
        }
        
        resolve(canvas.toDataURL('image/jpeg', 0.8));
      };
      img.src = imageDataUrl;
    });
  };

  // Funzione per comprimere l'immagine solo per lo storage
  const compressImageForStorage = (dataUrl: string, maxWidth = 800, quality = 0.3): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        // Calcola le nuove dimensioni mantenendo l'aspect ratio
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          height = Math.round(height * (maxWidth / width));
          width = maxWidth;
        }
        
        // Crea un canvas per la compressione
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        // Disegna l'immagine ridimensionata sul canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error("Impossibile creare il contesto canvas"));
          return;
        }
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Converti in formato JPEG con la qualità desiderata
        resolve(canvas.toDataURL('image/jpeg', quality));
      };
      
      img.onerror = () => reject(new Error("Errore nel caricamento dell'immagine"));
      img.src = dataUrl;
    });
  };

  const handleCapture = async (imageData: string) => {
    try {
      // Correggiamo l'orientamento dell'immagine
      const fixedImage = await fixOrientation(imageData);
      
      // Creiamo una versione compressa SOLO per sessionStorage
      const compressedForStorage = await compressImageForStorage(fixedImage);
      
      if (previewType === "ddt") {
        // Per Claude mandiamo l'immagine originale correttamente orientata
        sessionStorage.setItem("capturedDDTImage", fixedImage);
        
        // Per lo storage delle informazioni aggiuntive, usiamo la versione compressa
        const ddtDataCompressed = { imageCompressed: true };
        sessionStorage.setItem("ddtData", JSON.stringify(ddtDataCompressed));
        
        setLocation("/ddt-processing");
      } else {
        // Per Claude mandiamo l'immagine originale correttamente orientata
        sessionStorage.setItem("capturedLabelImage", fixedImage);
        
        // Per lo storage delle informazioni aggiuntive, usiamo la versione compressa
        const labelDataCompressed = { imageCompressed: true };
        sessionStorage.setItem("labelData", JSON.stringify(labelDataCompressed));
        
        setLocation("/label-processing");
      }
    } catch (error) {
      console.error("Errore nell'elaborazione dell'immagine:", error);
      // Fallback: usa l'immagine originale se c'è un errore
      if (previewType === "ddt") {
        sessionStorage.setItem("capturedDDTImage", imageData);
        setLocation("/ddt-processing");
      } else {
        sessionStorage.setItem("capturedLabelImage", imageData);
        setLocation("/label-processing");
      }
    }
  };

  // Funzione per comprimere l'immagine
  const compressImage = (dataUrl: string, maxWidth = 1200, quality = 0.5): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        // Calcola le nuove dimensioni mantenendo l'aspect ratio
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          height = Math.round(height * (maxWidth / width));
          width = maxWidth;
        }
        
        // Crea un canvas per la compressione
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        // Disegna l'immagine ridimensionata sul canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error("Impossibile creare il contesto canvas"));
          return;
        }
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Converti in formato JPEG con la qualità desiderata
        resolve(canvas.toDataURL('image/jpeg', quality));
      };
      
      img.onerror = () => reject(new Error("Errore nel caricamento dell'immagine"));
      img.src = dataUrl;
    });
  };

  const handleConfirmImage = async () => {
    if (previewImage) {
      try {
        // Per Claude vogliamo usare l'immagine originale
        // Solo per i metadati JSON usiamo una versione compressa
        
        if (previewType === "ddt") {
          // Salva l'immagine originale per l'elaborazione da parte di Claude
          sessionStorage.setItem("capturedDDTImage", previewImage);
          
          // Per lo storage delle informazioni aggiuntive, comprimiamo
          const ddtDataCompressed = { imageCompressed: true };
          sessionStorage.setItem("ddtData", JSON.stringify(ddtDataCompressed));
          
          console.log("Immagine DDT salvata, dimensione (bytes):", previewImage.length);
          setLocation("/ddt-processing");
        } else {
          // Salva l'immagine originale per l'elaborazione da parte di Claude
          sessionStorage.setItem("capturedLabelImage", previewImage);
          
          // Per lo storage delle informazioni aggiuntive, comprimiamo
          const labelDataCompressed = { imageCompressed: true };
          sessionStorage.setItem("labelData", JSON.stringify(labelDataCompressed));
          
          console.log("Immagine etichetta salvata, dimensione (bytes):", previewImage.length);
          setLocation("/label-processing");
        }
      } catch (error) {
        console.error("Errore nella gestione dell'immagine:", error);
        
        // Controlla se l'errore è un problema di quota del sessionStorage
        if (error instanceof DOMException && error.name === "QuotaExceededError") {
          // Mostro messaggio specifico per problemi di quota dello storage
          if (confirm("L'immagine è troppo grande per essere salvata. Vuoi provare il caricamento diretto?")) {
            setLocation("/direct-label");
            return;
          }
        }
        
        alert("Errore nel processare l'immagine. Riprova con un'immagine più piccola o usa il caricamento diretto dalla pagina etichette.");
      }
    }
  };

  const handleRetakePhoto = () => {
    // Prima impostare la modalità della camera, poi eliminare l'anteprima
    setShowCamera(true);
    
    // Utilizziamo un timeout per assicurarci che la camera sia già inizializzata
    // quando rimuoviamo l'anteprima
    setTimeout(() => {
      setPreviewImage(null);
    }, 100);
  };

  // Stile comune per tutti i pulsanti in base al design fornito
  const buttonStyle = "w-full flex items-center justify-between py-6 px-7 bg-white/90 backdrop-blur-sm text-slate-800 rounded-2xl shadow-[0_2px_10px_rgba(0,0,0,0.08)] hover:shadow-[0_4px_15px_rgba(0,0,0,0.12)] transition-all duration-300 h-24 border border-indigo-100/80";
  const iconContainerStyle = "w-12 h-12 flex items-center justify-center bg-indigo-50 rounded-full";
  const iconStyle = { width: '24px', height: '24px', color: '#4338ca' };
  const arrowStyle = "w-5 h-5";
  const numberStyle = "text-2xl font-bold text-indigo-600 mr-2 flex items-center justify-center w-8 h-8 bg-indigo-50 rounded-full";

  return (
    <GradientBackground>
      <Header title="Ingresso Merci" showBack backPath="/" />
      <main className="flex-1 px-4 overflow-y-auto flex flex-col justify-center min-h-screen">
        <div className="px-4 max-w-md w-full mx-auto">
          <div className="flex flex-col gap-4">
            {/* Pulsante 1: Foto DDT */}
            <Button
              variant="outline"
              className={buttonStyle}
              onClick={() => {
                sessionStorage.setItem("documentType", "ddt");
                setPreviewType("ddt"); // Assicuriamoci che il tipo di preview sia correttamente impostato
                setShowCamera(true);
              }}
            >
              <div className="flex items-center">
                <div className={numberStyle}>1</div>
                <div className={iconContainerStyle}>
                  <CameraIcon style={iconStyle} />
                </div>
                <div className="ml-4">
                  <span className="font-semibold block text-center text-[22px]">DDT</span>
                </div>
              </div>
              <div className="text-slate-400">
                <svg xmlns="http://www.w3.org/2000/svg" className={arrowStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </div>
            </Button>

            {/* Pulsante 2: Foto Etichetta */}
            <Button
              variant="outline"
              className={buttonStyle}
              onClick={() => {
                sessionStorage.setItem("documentType", "label");
                setPreviewType("label"); // Assicuriamoci che il tipo di preview sia correttamente impostato
                setShowCamera(true);
              }}
            >
              <div className="flex items-center">
                <div className={numberStyle}>2</div>
                <div className={iconContainerStyle}>
                  <TagIcon style={iconStyle} />
                </div>
                <div className="ml-4">
                  <span className="font-semibold block text-center text-[22px]">ETICHETTE</span>
                </div>
              </div>
              <div className="text-slate-400">
                <svg xmlns="http://www.w3.org/2000/svg" className={arrowStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </div>
            </Button>
          </div>
        </div>
      </main>
      <Footer activeItem="goods" />
      {showCamera && (
        <Camera
          aspectRatio="vertical"
          onCapture={handleCapture}
          onClose={() => setShowCamera(false)}
          enableStabilization={false}
          cameraMode={sessionStorage.getItem("documentType") === "label" ? "label" : "ddt"}
        />
      )}
      {/* Schermata di anteprima dell'immagine */}
      {previewImage && (
        <div className="fixed inset-0 bg-gray-900 z-50 flex flex-col">
          <div className="absolute left-4 top-4 z-10">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-gray-800 rounded-full bg-gray-900/50 backdrop-blur-sm"
              onClick={() => setPreviewImage(null)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Button>
          </div>
          
          <div className="flex-1 flex items-center justify-center overflow-hidden px-2 mx-auto">
            <img 
              src={previewImage} 
              alt="Anteprima" 
              className="object-contain max-w-full max-h-full rounded-md border border-gray-800/30 my-2"
              style={{
                maxWidth: "100%",
                maxHeight: "90vh",
                width: "auto",
                height: "auto",
                objectFit: "contain",
                display: "block"
              }}
            />
          </div>
          
          <div className="py-6 px-5 pb-16 bg-[#141621]">
            <div className="flex justify-between gap-4">
              <Button 
                variant="outline"
                className="flex-1 h-14 rounded-full text-white bg-[#1E1E28] border border-[#FF5722]/50 hover:bg-[#1E1E28]/80"
                onClick={handleRetakePhoto}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-6.5 h-6.5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M9 14l-4-4 4-4"></path>
                  <path d="M5 10h11a4 4 0 1 1 0 8h-1"></path>
                </svg>
                <span className="text-lg font-medium">Rifai foto</span>
              </Button>
              
              <Button 
                variant="default"
                className="flex-1 h-14 rounded-full text-white bg-[#15803d] hover:bg-[#15803d]/90"
                onClick={handleConfirmImage}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-6.5 h-6.5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
                <span className="text-lg font-medium">Prosegui</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </GradientBackground>
  );
}
