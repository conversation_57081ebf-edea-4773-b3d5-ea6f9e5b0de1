import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/layout/header";
import { BottomNavigation } from "@/components/layout/bottom-navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { WifiOff, WifiIcon, Save, RefreshCw, Cloud, Database } from "lucide-react";
import { useOfflineApi } from "@/hooks/use-offline-api";
import { SyncStatus } from "@/components/ui/sync-status";
import { useToast } from "@/hooks/use-toast";

export default function OfflineTest() {
  const { isOnline, pendingOperations } = useOfflineApi();
  const [note, setNote] = useState<string>("");
  const [savedNotes, setSavedNotes] = useState<Array<{ id: string; content: string; timestamp: string; synced: boolean }>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toast } = useToast();

  // Funzione per salvare una nota (simulazione API)
  const saveNote = async () => {
    if (!note.trim()) {
      toast({
        title: "Nota vuota",
        description: "Inserisci del testo prima di salvare",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const noteId = `note-${Date.now()}`;
      const timestamp = new Date().toISOString();
      
      // Simulazione di chiamata API standard
      const response = await fetch("/api/notes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ id: noteId, content: note, timestamp })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      // Aggiorna l'interfaccia utente immediatamente
      setSavedNotes(prev => [
        ...prev, 
        { 
          id: noteId, 
          content: note, 
          timestamp, 
          synced: true // Always synced since we're not using offline caching
        }
      ]);
      
      setNote("");
      
      toast({
        title: isOnline ? "Nota salvata" : "Nota salvata offline",
        description: isOnline 
          ? "La tua nota è stata salvata con successo" 
          : "La nota sarà sincronizzata quando tornerai online",
        variant: "default",
      });
    } catch (error) {
      console.error("Errore nel salvataggio della nota:", error);
      toast({
        title: "Errore",
        description: "Impossibile salvare la nota",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Funzione per cancellare le note locali
  const clearNotes = () => {
    setSavedNotes([]);
    toast({
      title: "Note cancellate",
      description: "Tutte le note locali sono state cancellate",
      variant: "default"
    });
  };

  // Funzione per forzare offline/online per test
  const toggleNetworkMode = () => {
    const networkStatus = navigator.onLine;
    if (networkStatus) {
      toast({
        title: "Modalità offline",
        description: "Per testare, disabilita la connessione di rete del tuo dispositivo",
        variant: "default"
      });
    } else {
      toast({
        title: "Modalità online",
        description: "Per tornare online, riattiva la connessione di rete del tuo dispositivo",
        variant: "default"
      });
    }
  };

  return (
    <div className="min-h-screen pb-20">
      <Header title="Test Modalità Offline" showBack />
      
      <main className="container max-w-md mx-auto pt-16 px-4">
        <Card className="mb-4">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Status Connessione</CardTitle>
              <SyncStatus showText />
            </div>
            <CardDescription>
              Testa la funzionalità offline e la sincronizzazione
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              {isOnline ? (
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-600">
                  <WifiIcon size={24} />
                </div>
              ) : (
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-amber-100 text-amber-600">
                  <WifiOff size={24} />
                </div>
              )}
              <div>
                <h3 className="text-lg font-medium">
                  {isOnline ? "Online" : "Offline"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {isOnline 
                    ? "Tutte le modifiche vengono salvate in tempo reale" 
                    : "Le modifiche verranno sincronizzate quando tornerai online"
                  }
                </p>
              </div>
            </div>
            
            {pendingOperations > 0 && (
              <div className="flex items-center space-x-2 mb-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
                <Cloud className="text-blue-500" size={20} />
                <p className="text-sm text-blue-700">
                  {pendingOperations} {pendingOperations === 1 ? 'operazione in attesa' : 'operazioni in attesa'} di sincronizzazione
                </p>
              </div>
            )}
            
            <Button 
              onClick={toggleNetworkMode} 
              variant="outline" 
              className="w-full"
            >
              {isOnline ? (
                <>
                  <WifiOff className="mr-2 h-4 w-4" />
                  Simula modalità offline
                </>
              ) : (
                <>
                  <WifiIcon className="mr-2 h-4 w-4" />
                  Simula modalità online
                </>
              )}
            </Button>
          </CardContent>
        </Card>
        
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Aggiungi Nota</CardTitle>
            <CardDescription>
              Inserisci una nota che verrà sincronizzata quando sei online
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid w-full gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="note">Testo della nota</Label>
                <Textarea
                  id="note"
                  placeholder="Inserisci qui la tua nota..."
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <Button 
              onClick={saveNote} 
              disabled={isLoading} 
              className="w-full"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Salva Nota
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Note Salvate</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={clearNotes}
                disabled={savedNotes.length === 0}
              >
                Cancella
              </Button>
            </div>
            <CardDescription>
              Note salvate localmente
            </CardDescription>
          </CardHeader>
          <CardContent>
            {savedNotes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Database className="mx-auto h-10 w-10 mb-3 opacity-30" />
                <p>Nessuna nota salvata</p>
              </div>
            ) : (
              <div className="space-y-3">
                {savedNotes.map((savedNote) => (
                  <div
                    key={savedNote.id}
                    className="p-3 rounded-lg border border-gray-200 bg-gray-50"
                  >
                    <div className="flex justify-between items-start mb-1">
                      <p className="text-xs text-gray-500">
                        {new Date(savedNote.timestamp).toLocaleString()}
                      </p>
                      {!savedNote.synced && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                          <WifiOff className="h-3 w-3 mr-1" />
                          In attesa
                        </span>
                      )}
                      {savedNote.synced && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Cloud className="h-3 w-3 mr-1" />
                          Sincronizzato
                        </span>
                      )}
                    </div>
                    <p className="text-sm break-words">{savedNote.content}</p>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </main>
      
      <BottomNavigation activeItem="home" />
    </div>
  );
}