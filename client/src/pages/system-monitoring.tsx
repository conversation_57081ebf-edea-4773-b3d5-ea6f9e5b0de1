/**
 * Pagina System Monitoring per HACCP Tracker
 * 
 * @description Pagina dedicata al monitoraggio completo del sistema:
 * - Integra SystemMonitoringDashboard component
 * - Layout responsive e ottimizzato
 * - Accesso solo per amministratori
 * 
 * <AUTHOR> di Monitoring HACCP Tracker
 * @version 1.0.0 - Pagina monitoring di sistema
 * @date 2025-07-26
 */

import React from 'react';
import { SystemMonitoringDashboard } from '@/components/monitoring/system-monitoring-dashboard';
import { useAuth } from '@/context/auth-context';
import { useLocation } from 'wouter';
import { Shield } from 'lucide-react';

const SystemMonitoringPage: React.FC = () => {
  const { user } = useAuth();

  const [, setLocation] = useLocation();

  // Controlla se l'utente è amministratore
  if (!user?.isAdmin) {
    setLocation('/');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header con indicatore admin */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">
              Sistema di Monitoring Avanzato
            </span>
          </div>
          <div className="text-xs text-gray-500">
            Accesso Amministratore: {user.username}
          </div>
        </div>
      </div>

      {/* Dashboard Component */}
      <SystemMonitoringDashboard />
    </div>
  );
};

export default SystemMonitoringPage;