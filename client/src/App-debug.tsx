import { useEffect } from "react";

function DebugApp() {
  useEffect(() => {
    console.log("🚀 App Debug caricata!");
    document.title = "Debug App - HACCP Tracker";
  }, []);

  return (
    <div style={{ 
      padding: "20px", 
      backgroundColor: "#f0f0f0", 
      minHeight: "100vh",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ color: "#333", margin: "0 0 20px 0" }}>
        🔧 Debug App - HACCP Tracker
      </h1>
      
      <div style={{ 
        backgroundColor: "white", 
        padding: "15px", 
        borderRadius: "8px",
        marginBottom: "15px",
        border: "1px solid #ddd"
      }}>
        <h2 style={{ color: "#666", margin: "0 0 10px 0" }}>Stato App</h2>
        <p>✅ React sta funzionando</p>
        <p>✅ CSS sta caricando</p>
        <p>✅ App si renderizza correttamente</p>
        <p>🕐 Timestamp: {new Date().toLocaleString()}</p>
      </div>

      <div style={{ 
        backgroundColor: "white", 
        padding: "15px", 
        borderRadius: "8px",
        border: "1px solid #ddd"
      }}>
        <h2 style={{ color: "#666", margin: "0 0 10px 0" }}>Test API</h2>
        <button 
          onClick={async () => {
            try {
              const response = await fetch('/api/auth/me');
              const data = await response.json();
              alert(`API Response: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
              alert(`API Error: ${error}`);
            }
          }}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Testa API /auth/me
        </button>
      </div>
    </div>
  );
}

export default DebugApp;