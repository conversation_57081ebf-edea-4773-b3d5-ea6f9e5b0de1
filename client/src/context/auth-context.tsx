import { createContext, useEffect, useState, ReactNode, useContext } from "react";
import { AuthState, User } from "@/types";
import { UserRole } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  setUser: (user: User) => void;
  isAdmin: boolean;
  isManager: boolean;
  userRole: string | null;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: async () => {},
  refreshUser: async () => {},
  setUser: () => {},
  isAdmin: false,
  isManager: false,
  userRole: null,
});

export function AuthProvider({ children }: { children: ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true
  });
  const { toast } = useToast();

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      // Removed insecure offline authentication with localStorage
      // Authentication must always be validated server-side for security
      if (!navigator.onLine) {
        console.log("App offline - requiring server authentication");
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
        return;
      }

      // Se siamo online, procedi con la verifica normale
      try {
        // Controlla se c'è un parametro tenant nell'URL per il cambio di contesto
        const urlParams = new URLSearchParams(window.location.search);
        const tenantCode = urlParams.get('tenant');
        const adminAccess = urlParams.get('adminAccess');
        
        // Se c'è un parametro tenant, tenta di cambiare il contesto
        if (tenantCode && adminAccess === 'true') {
          console.log("Tentativo di cambio tenant via URL:", tenantCode);
          try {
            const switchResponse = await fetch(`/api/admin/switch-tenant/${tenantCode}`, {
              method: 'POST',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
              },
              credentials: 'include'
            });
            
            if (switchResponse.ok) {
              console.log("Cambio tenant via URL riuscito");
              // Rimuovi i parametri dall'URL dopo il cambio
              urlParams.delete('tenant');
              urlParams.delete('adminAccess');
              urlParams.delete('timestamp');
              const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
              window.history.replaceState({}, '', newUrl);
            } else {
              console.warn("Cambio tenant via URL fallito:", switchResponse.status);
            }
          } catch (switchError) {
            console.warn("Errore durante cambio tenant via URL:", switchError);
          }
        }
        
        console.log("Making GET request to /api/auth/me", "");
        // Utilizziamo fetch direttamente per byppassare qualsiasi cache o gestione secondaria
        const response = await fetch("/api/auth/me", {
          method: "GET",
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          credentials: 'include' // Fondamentale per inviare i cookie di sessione
        });
        
        console.log("Auth check response status:", response.status);
        
        if (response.ok) {
          const user = await response.json();
          console.log("User data received:", user ? user.username : "none");
          console.log("Full user data from API:", user);
          
          // Secure: Never store sensitive user data in localStorage
          const userData = user as User;
          
          setAuthState({
            user: userData,
            isAuthenticated: true,
            isLoading: false,
          });
        } else {
          console.log("Auth check non riuscito - status:", response.status);
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      } catch (error) {
        // Network errors require server authentication - no local fallback for security
        console.error("Auth check failed:", error);
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      const response = await apiRequest<{user: User}>("/api/auth/login", "POST", {
        username,
        password,
      });
      
      // Estrai l'oggetto user dalla risposta che è nel formato {user: {...}}
      const userData = response.user;
      
      setAuthState({
        user: userData,
        isAuthenticated: true,
        isLoading: false,
      });
      
      toast({
        title: "Login Effettuato",
        description: `Bentornato, ${userData.username}!`,
      });
    } catch (error) {
      console.error("Errore durante il login:", error);
      toast({
        title: "Login Fallito",
        description: "Nome utente o password non validi",
        variant: "destructive",
      });
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Secure server-side logout only
      await apiRequest("/api/auth/logout", "POST");
      
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
      
      toast({
        title: "Logout Effettuato",
        description: "Sei stato disconnesso con successo",
      });
    } catch (error) {
      console.error("Errore durante il logout:", error);
      // Clear local state even if server logout fails
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
      
      toast({
        title: "Logout Effettuato",
        description: "Sei stato disconnesso",
        variant: "default",
      });
    }
  };

  const refreshUser = async () => {
    try {
      // Utilizziamo il getQueryFn direttamente
      const user = await apiRequest<User | null>("/api/auth/me", "GET").catch(error => {
        if (error.message.startsWith("401:")) {
          return null;
        }
        throw error;
      });
      
      if (user) {
        // Nessuna modifica necessaria al tipo User
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error("Aggiornamento utente fallito:", error);
      // Non facciamo logout in caso di errore nel refresh, solo logging
    }
  };

  // Calcola isAdmin e isManager in base all'utente corrente
  const isAdmin = authState.user?.isAdmin ?? false;
  

  
  // Verifica il ruolo dell'utente dall'apposito campo, oppure dal campo isAdmin per retrocompatibilità
  const userRole = authState.user?.role || (isAdmin ? UserRole.ADMIN : UserRole.USER);
  const isManager = userRole === UserRole.MANAGER;

  // Funzione per impostare direttamente l'utente (utile per impersonificazione)
  const setUser = (user: User) => {
    setAuthState({
      user,
      isAuthenticated: true,
      isLoading: false,
    });
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        logout,
        refreshUser,
        setUser,
        isAdmin,
        isManager,
        userRole,
      }}
    >
      <div data-auth={authState.isAuthenticated ? "complete" : "loading"}>
        {children}
      </div>
    </AuthContext.Provider>
  );
}

// Hook personalizzato per usare il context di autenticazione
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
