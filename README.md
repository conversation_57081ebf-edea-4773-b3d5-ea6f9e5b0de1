# HACCP Tracker - Restaurant Inventory Management System

HACCP Tracker è un'applicazione web progressiva (PWA) progettata per la gestione intelligente dell'inventario di ristoranti, con particolare attenzione all'esperienza degli sviluppatori e all'efficienza operativa.

## Caratteristiche Principali

- **Gestione dei Fornitori**: Registra e gestisci i tuoi fornitori con dettagli come nome azienda, partita IVA e indirizzo.
- **Tracciamento DDT**: Importa e gestisci i documenti di trasporto con supporto per la scansione QR code.
- **Etichette Prodotto**: Crea e gestisci etichette per i prodotti alimentari con date di scadenza, numeri di lotto e istruzioni di conservazione.
- **Sistema di Contenitori**: Organizza i prodotti in contenitori con capacità definita e tipologie personalizzabili.
- **Autenticazione Utenti**: Sistema di accesso sicuro con ruoli amministratore e utente.
- **Registrazione Attività**: Tracciamento completo delle azioni utente per l'audit trail.
- **Scansione QR Code**: Lettura di codici QR per identificare rapidamente prodotti e contenitori.
- **Interfaccia Mobile-Friendly**: Design responsivo ottimizzato per l'uso su dispositivi mobili.

## Tecnologie Utilizzate

- **Frontend**: React/TypeScript con componenti modulari
- **Backend**: Express.js con API RESTful
- **Database**: PostgreSQL con ORM Drizzle
- **Stile**: Tailwind CSS per design responsivo
- **Autenticazione**: Sistema di autenticazione basato su sessioni
- **Gestione Stato**: React Query per la gestione dello stato dell'interfaccia
- **Validazione**: Zod per validazione dei dati

## Architettura del Sistema

L'applicazione è strutturata in modo modulare con:

- **Schema Dati**: Definito centralmente in `shared/schema.ts` per consistenza tra frontend e backend
- **API RESTful**: Endpoints per tutte le operazioni CRUD sui dati
- **Interfaccia Reattiva**: Aggiornamento in tempo reale dei dati visualizzati
- **Gestione Errori**: Sistema completo di gestione degli errori e validazione

## Requisiti di Sistema

- Node.js 
- PostgreSQL
- Browser moderno con supporto PWA

## Installazione

1. Clona il repository
2. Installa le dipendenze con `npm install`
3. Configura il database PostgreSQL
4. Esegui le migrazioni del database con `npm run db:push`
5. Avvia l'applicazione con `npm run dev`

## Sviluppo Futuro

- Integrazione con sistemi di fatturazione elettronica
- Dashboard di analisi per il monitoraggio dell'inventario
- Sistema di notifiche per prodotti in scadenza
- Supporto multilingua
- Modalità offline completa

## Licenza

Tutti i diritti riservati.