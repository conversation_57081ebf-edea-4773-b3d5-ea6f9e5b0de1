import type { Express, Request, Response } from "express";
import { isAuthenticated } from "../middlewares/auth";
import { storage } from "../storage";
import { insertContainerSchema } from "@shared/schema";
import { containerValidation } from "../middlewares/security";
import type { User } from "@shared/schema";
import { z } from "zod";
import { validateRouteId } from "../lib/safe-parsing";

/**
 * Rotte per la gestione dei contenitori
 */
export function registerContainerRoutes(app: Express) {
  
  // Get all containers
  app.get("/api/containers", isAuthenticated, async (req, res) => {
    try {
      const containers = await storage.getAllContainers();
      res.json(containers);
    } catch (error) {
      console.error("Error fetching containers:", error);
      res.status(500).json({ message: "Failed to fetch containers" });
    }
  });

  // Get container by ID
  app.get("/api/containers/:id", isAuthenticated, async (req, res) => {
    try {
      const containerId = validateRouteId(req.params.id, 'container');

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      res.json(container);
    } catch (error) {
      console.error("Error fetching container:", error);
      res.status(500).json({ message: "Failed to fetch container details" });
    }
  });

  // Get container products
  app.get("/api/containers/:id/products", isAuthenticated, async (req, res) => {
    try {
      const containerId = validateRouteId(req.params.id, 'container');

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      const containerProducts = await storage.getContainerProducts(containerId);
      const productLabelIds = containerProducts.map(cp => cp.productLabelId);
      const allProducts = await storage.getAllProductLabels();
      const containerProductLabels = allProducts.filter(product => 
        productLabelIds.includes(product.id)
      );

      res.json(containerProductLabels);
    } catch (error) {
      console.error("Error fetching container products:", error);
      res.status(500).json({ message: "Failed to fetch container products" });
    }
  });
  
  // Aggiorna la capacità massima di un contenitore
  app.patch("/api/containers/:id/capacity", isAuthenticated, async (req, res) => {
    try {
      const { id } = req.params;
      const containerId = validateRouteId(id, 'container');
      const { maxItems } = req.body;
      
      if (!Number.isInteger(maxItems) || maxItems < 1) {
        return res.status(400).json({ message: "ID contenitore non valido o valore massimo prodotti non valido" });
      }
      
      // Get the container to make sure it exists
      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Contenitore non trovato" });
      }
      
      // Check if the new capacity is less than the current number of items
      if (maxItems < container.currentItems) {
        return res.status(400).json({ 
          message: "La nuova capacità non può essere inferiore al numero attuale di prodotti",
          currentItems: container.currentItems
        });
      }
      
      // Update the container's maxItems
      const updatedContainer = await storage.updateContainer(containerId, { maxItems });
      
      const user = req.user as User;
      await storage.addActivityLog({
        tenantId: user.tenantId || 'default',
        userId: user.id,
        username: user.username,
        action: "update_container_capacity",
        details: `Utente ha modificato la capacità del contenitore ${container.name} da ${container.maxItems} a ${maxItems}`,
        containerId: containerId.toString(),
        timestamp: new Date()
      });
      
      res.json(updatedContainer);
    } catch (error) {
      console.error("Errore durante l'aggiornamento della capacità del contenitore:", error);
      res.status(500).json({ message: "Impossibile aggiornare la capacità del contenitore" });
    }
  });

  // Create new container
  app.post("/api/containers", isAuthenticated, containerValidation, async (req: Request, res: Response) => {
    try {
      const validatedData = insertContainerSchema.parse(req.body);
      
      const container = await storage.createContainer({
        ...validatedData,
        createdBy: (req.user as any)?.id
      });

      const user = req.user as User;
      await storage.addActivityLog({
        tenantId: user.tenantId || 'default',
        userId: user.id,
        username: user.username,
        action: "CREATE_CONTAINER",
        details: `Created container: ${container.name}`,
        containerId: container.id.toString(),
        timestamp: new Date()
      });

      res.status(201).json(container);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating container:", error);
      res.status(500).json({ message: "Error creating container" });
    }
  });

  // Archive/unarchive container
  app.patch("/api/containers/:id/archive", isAuthenticated, async (req, res) => {
    try {
      const containerId = validateRouteId(req.params.id, 'container');

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      const updatedContainer = await storage.updateContainer(containerId, {
        isArchived: !container.isArchived
      });

      const user = req.user as User;
      await storage.addActivityLog({
        tenantId: user.tenantId || 'default',
        userId: user.id,
        username: user.username,
        action: updatedContainer.isArchived ? "ARCHIVE_CONTAINER" : "UNARCHIVE_CONTAINER",
        details: `${updatedContainer.isArchived ? 'Archived' : 'Unarchived'} container: ${container.name}`,
        containerId: containerId.toString(),
        timestamp: new Date()
      });

      res.json(updatedContainer);
    } catch (error) {
      console.error("Error updating container archive status:", error);
      res.status(500).json({ message: "Failed to update container" });
    }
  });
}