import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { insertTenantSchema, tenants, users } from "@shared/schema";
import { z } from "zod";
import { db } from "../db";
import { eq, count, sql } from "drizzle-orm";
import { asyncHand<PERSON> } from "../lib/async-error-handler";

// Middleware to ensure system admin authentication
const requireSystemAdmin = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated() && (req.user as any)?.isAdmin) {
    next();
  } else {
    res.status(403).json({ message: "System admin access required" });
  }
};

export function registerTenantAdminRoutes(app: Express) {
  // Get all tenants with user counts (system admin only) - OPTIMIZED to fix N+1 queries
  app.get("/api/admin/tenants", requireSystemAdmin, asyncHandler(async (req: Request, res: Response) => {
      // Single optimized query that joins tenants with user counts
      // This replaces the N+1 query pattern with a single efficient query
      const enrichedTenants = await db
        .select({
          id: tenants.id,
          name: tenants.name,
          code: tenants.code,
          type: tenants.type,
          status: tenants.status,
          vatNumber: tenants.vatNumber,
          address: tenants.address,
          city: tenants.city,
          postalCode: tenants.postalCode,
          country: tenants.country,
          phone: tenants.phone,
          email: tenants.email,
          website: tenants.website,
          maxUsers: tenants.maxUsers,
          maxStorageGB: tenants.maxStorageGB,
          features: tenants.features,
          logo: tenants.logo,
          primaryColor: tenants.primaryColor,
          secondaryColor: tenants.secondaryColor,
          createdAt: tenants.createdAt,
          updatedAt: tenants.updatedAt,
          createdBy: tenants.createdBy,
          userCount: sql<number>`COALESCE(${count(users.id)}, 0)`.as('userCount')
        })
        .from(tenants)
        .leftJoin(users, eq(tenants.id, users.tenantId))
        .groupBy(tenants.id)
        .orderBy(tenants.createdAt);

    console.log(`📊 Performance: Fetched ${enrichedTenants.length} tenants with user counts in single query (eliminated N+1 queries)`);
    res.json(enrichedTenants);
  }));

  // Get tenant by ID
  app.get("/api/admin/tenants/:id", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const id = req.params.id;
      const tenant = await storage.getTenant(id);
      
      if (!tenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      res.json(tenant);
    } catch (error) {
      console.error("Error fetching tenant:", error);
      res.status(500).json({ message: "Error fetching tenant" });
    }
  });

  // Create new tenant
  app.post("/api/admin/tenants", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const validatedData = insertTenantSchema.parse(req.body);
      
      // Check for duplicate tenant code
      const existingTenant = await storage.getTenantByCode(validatedData.code);
      if (existingTenant) {
        return res.status(409).json({ message: "Tenant with this code already exists" });
      }

      const tenant = await storage.createTenant(validatedData);
      res.status(201).json(tenant);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating tenant:", error);
      res.status(500).json({ message: "Error creating tenant" });
    }
  });

  // Update tenant
  app.put("/api/admin/tenants/:id", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const id = req.params.id;
      const validatedData = insertTenantSchema.partial().parse(req.body);
      
      const existingTenant = await storage.getTenant(id);
      if (!existingTenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      // Check for duplicate code if it's being changed
      if (validatedData.code && validatedData.code !== existingTenant.code) {
        const duplicateTenant = await storage.getTenantByCode(validatedData.code);
        if (duplicateTenant) {
          return res.status(409).json({ message: "Tenant with this code already exists" });
        }
      }

      const updatedTenant = await storage.updateTenant(id, validatedData);
      res.json(updatedTenant);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error updating tenant:", error);
      res.status(500).json({ message: "Error updating tenant" });
    }
  });

  // Delete tenant
  app.delete("/api/admin/tenants/:id", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const id = req.params.id;
      
      const existingTenant = await storage.getTenant(id);
      if (!existingTenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      await storage.deleteTenant(id);
      res.json({ message: "Tenant deleted successfully" });
    } catch (error) {
      console.error("Error deleting tenant:", error);
      res.status(500).json({ message: "Error deleting tenant" });
    }
  });

  // Get tenant users
  app.get("/api/admin/tenants/:id/users", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const tenantId = req.params.id;
      
      const tenant = await storage.getTenant(tenantId);
      if (!tenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      const users = await storage.getAllUsers(tenantId);
      res.json(users);
    } catch (error) {
      console.error("Error fetching tenant users:", error);
      res.status(500).json({ message: "Error fetching tenant users" });
    }
  });

  // Get tenant statistics
  app.get("/api/admin/tenants/:id/stats", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const tenantId = req.params.id;
      
      const tenant = await storage.getTenant(tenantId);
      if (!tenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      const users = await storage.getAllUsers(tenantId);
      // Note: In a full implementation, you'd filter other resources by tenant too
      
      res.json({
        userCount: users.length,
        maxUsers: tenant.maxUsers,
        status: tenant.status,
        type: tenant.type,
        createdAt: tenant.createdAt
      });
    } catch (error) {
      console.error("Error fetching tenant stats:", error);
      res.status(500).json({ message: "Error fetching tenant stats" });
    }
  });

  // Get admin dashboard statistics (missing after modular refactor)
  app.get("/api/admin/dashboard/stats", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      // Get all tenants
      const allTenants = await db.select().from(tenants);
      
      // Get total users across all tenants
      const totalUsersResult = await db.select({ count: count() }).from(users);
      const totalUsers = totalUsersResult[0]?.count || 0;
      
      // Calculate other stats
      const activeTenants = allTenants.filter(t => t.status === 'active').length;
      const totalTenants = allTenants.length;
      
      res.json({
        totalTenants,
        activeTenants,
        totalUsers,
        inactiveTenants: totalTenants - activeTenants,
        systemStatus: 'operational'
      });
    } catch (error) {
      console.error("Error fetching admin dashboard stats:", error);
      res.status(500).json({ message: "Error fetching dashboard stats" });
    }
  });

  // Switch tenant context for admin (URL-based tenant switching)
  app.post("/api/admin/switch-tenant/:tenantCode", requireSystemAdmin, async (req: Request, res: Response) => {
    try {
      const tenantCode = req.params.tenantCode;
      console.log(`Admin ${(req.user as any)?.username} attempting to switch to tenant: ${tenantCode}`);
      
      // Find tenant by code
      const targetTenant = await db
        .select()
        .from(tenants)
        .where(eq(tenants.code, tenantCode))
        .limit(1);
      
      if (targetTenant.length === 0) {
        return res.status(404).json({ message: "Tenant not found" });
      }
      
      const tenant = targetTenant[0];
      
      // Set the active tenant in the session for admin context switching
      (req.session as any).activeTenantId = tenant.id;
      (req.session as any).adminTenantSwitch = true;
      
      console.log(`✅ Admin tenant switch successful: ${(req.user as any)?.username} now operating in tenant ${tenant.name} (${tenant.code})`);
      
      res.json({
        success: true,
        message: `Switched to tenant: ${tenant.name}`,
        tenant: {
          id: tenant.id,
          name: tenant.name,
          code: tenant.code
        }
      });
    } catch (error) {
      console.error("Error switching tenant:", error);
      res.status(500).json({ message: "Error switching tenant" });
    }
  });
}