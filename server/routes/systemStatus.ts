/**
 * API per controllare lo stato dei servizi automatizzati del sistema
 */

import { Router } from 'express';
import { isAuthenticated, isAdmin } from '../middlewares/auth';

export function createSystemStatusRoutes(): Router {
  const router = Router();

  // Middleware: Solo admin possono vedere lo stato del sistema
  router.use(isAuthenticated);
  router.use(isAdmin);

  /**
   * GET /api/system/status
   * Restituisce lo stato di tutti i servizi automatizzati
   */
  router.get('/status', (req, res) => {
    try {
      const systemStatus = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform,
        
        // Servizi PWA Frontend (lato client)
        pwaServices: {
          serviceWorker: {
            name: "Service Worker PWA",
            status: "active",
            description: "Gestisce cache, offline e aggiornamenti PWA",
            frequency: "Continuo (event-driven)"
          },
          backgroundSync: {
            name: "Background Sync",
            status: "active", 
            description: "Sincronizza dati quando torna la connessione",
            frequency: "Automatico quando online"
          },
          periodicSync: {
            name: "Periodic Background Sync",
            status: "conditional",
            description: "Sincronizzazione periodica (se supportata dal browser)",
            frequency: "Ogni 24 ore"
          },
          updateChecker: {
            name: "Service Worker Update Checker",
            status: "active",
            description: "Controlla aggiornamenti del service worker",
            frequency: "Ogni ora"
          },
          offlineDatabase: {
            name: "IndexedDB Offline Cache",
            status: "active",
            description: "Cache locale per funzionamento offline",
            frequency: "Continuo"
          }
        },

        // Servizi Backend (lato server)
        backendServices: {
          expressServer: {
            name: "Express.js Server",
            status: "active",
            description: "Server HTTP principale",
            frequency: "Continuo"
          },
          sessionStore: {
            name: "PostgreSQL Session Store", 
            status: "active",
            description: "Gestione sessioni utente nel database",
            frequency: "Continuo"
          },
          apiLogging: {
            name: "API Request Logging",
            status: "active",
            description: "Log delle richieste API", 
            frequency: "Per ogni richiesta"
          },
          databaseConnection: {
            name: "Database Connection Pool",
            status: "active",
            description: "Pool di connessioni al database PostgreSQL",
            frequency: "Continuo"
          }
        },

        // Servizi automatizzati non ancora implementati
        automatedServices: {
          backgroundMaintenance: {
            name: "Background Maintenance Services",
            status: "not_implemented", 
            description: "Archiviazione automatica e pulizia dati",
            frequency: "Giornaliera alle 02:00"
          },
          containerArchival: {
            name: "Container Auto-Archival",
            status: "not_implemented",
            description: "Archivia container vuoti dopo 30 giorni",
            frequency: "Giornaliera"
          },
          expiredProductsCleanup: {
            name: "Expired Products Cleanup", 
            status: "not_implemented",
            description: "Rimuove prodotti ritirati dopo 2 anni",
            frequency: "Giornaliera"
          },
          sessionCleanup: {
            name: "Session Cleanup",
            status: "not_implemented", 
            description: "Pulisce sessioni scadute",
            frequency: "Ogni ora"
          },
          autoBackup: {
            name: "Automatic Database Backup",
            status: "not_implemented",
            description: "Backup automatico del database",
            frequency: "Configurabile (default: 24 ore)"
          }
        },

        // Filtri automatici esistenti
        automaticFilters: {
          retiredProductsFilter: {
            name: "Retired Products Filter",
            status: "active",
            description: "Filtra prodotti ritirati oltre 2 anni dalla visualizzazione",
            frequency: "Ogni caricamento dati"
          },
          expiredProductsFilter: {
            name: "Expired Products Filter", 
            status: "active",
            description: "Filtra prodotti scaduti dalle liste selezionabili",
            frequency: "Tempo reale"
          },
          archivedContainersFilter: {
            name: "Archived Containers Filter",
            status: "active", 
            description: "Separa container archiviati da quelli attivi",
            frequency: "Interfaccia utente"
          }
        },

        // Monitoraggio e Log
        monitoring: {
          activityLogging: {
            name: "Activity Logger",
            status: "active",
            description: "Log dettagliato di tutte le azioni utente",
            frequency: "Per ogni azione"
          },
          errorLogging: {
            name: "Error Logging",
            status: "active", 
            description: "Log degli errori del sistema",
            frequency: "Per ogni errore"
          },
          performanceMonitoring: {
            name: "API Performance Monitoring",
            status: "active",
            description: "Monitora tempi di risposta delle API",
            frequency: "Per ogni richiesta"
          }
        }
      };

      res.json({
        success: true,
        systemStatus
      });

    } catch (error) {
      console.error('Errore nel recupero dello stato del sistema:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nel recupero dello stato del sistema'
      });
    }
  });

  /**
   * GET /api/system/services-summary
   * Riepilogo rapido dei servizi
   */
  router.get('/services-summary', (req, res) => {
    try {
      const summary = {
        totalServices: 16,
        activeServices: 11,
        notImplementedServices: 5,
        categoriesBreakdown: {
          pwaServices: { total: 5, active: 5 },
          backendServices: { total: 4, active: 4 },
          automatedServices: { total: 5, active: 0 },
          automaticFilters: { total: 3, active: 3 },
          monitoring: { total: 3, active: 3 }
        },
        recommendations: [
          "Implementare servizi di manutenzione automatica",
          "Attivare backup automatici schedulati", 
          "Configurare pulizia automatica sessioni scadute",
          "Implementare archiviazione automatica container inattivi"
        ]
      };

      res.json({
        success: true,
        summary
      });

    } catch (error) {
      console.error('Errore nel recupero del riepilogo:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nel recupero del riepilogo'
      });
    }
  });

  return router;
}