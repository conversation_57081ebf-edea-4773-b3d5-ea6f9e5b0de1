import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { insertDDTSchema } from "@shared/schema";
import { z } from "zod";
import { logActivity } from "../utils/activity-logger";
import { generateQRCode } from "../lib/qr-code";
import { validateRouteId } from "../lib/safe-parsing";

// Middleware to ensure authentication
const requireAuth = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    next();
  } else {
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerDDTRoutes(app: Express) {
  // Get all DDTs
  app.get("/api/ddts", requireAuth, async (req: Request, res: Response) => {
    try {
      const ddts = await storage.getAllDDTs();
      res.json(ddts);
    } catch (error) {
      console.error("Error fetching DDTs:", error);
      res.status(500).json({ message: "Error fetching DDTs" });
    }
  });

  // Get DDT by ID
  app.get("/api/ddts/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'DDT');

      const ddt = await storage.getDDT(id);
      if (!ddt) {
        return res.status(404).json({ message: "DDT not found" });
      }

      res.json(ddt);
    } catch (error) {
      console.error("Error fetching DDT:", error);
      res.status(500).json({ message: "Error fetching DDT" });
    }
  });

  // Create new DDT
  app.post("/api/ddts", requireAuth, async (req: Request, res: Response) => {
    try {
      const validatedData = insertDDTSchema.parse(req.body);
      
      // Check for duplicate DDT
      const existingDDT = await storage.checkDDTDuplicate(
        validatedData.companyName,
        validatedData.number,
        validatedData.date
      );
      
      if (existingDDT) {
        return res.status(409).json({ 
          message: "DDT with this company, number and date already exists" 
        });
      }

      // Generate QR code for DDT
      const qrCode = await generateQRCode(`DDT-${validatedData.number}-${validatedData.date}`);

      // Use the new transactional createDDT method
      const ddt = await storage.createDDT({
        ...validatedData,
        qrCode,
        createdBy: (req.user as any)?.id,
        tenantId: (req.user as any)?.tenantId || 'default'
      });

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'CREATE_DDT',
        `Created DDT: ${ddt.number} from ${ddt.companyName}`,
        { ddtId: ddt.id }
      );

      res.status(201).json(ddt);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating DDT:", error);
      res.status(500).json({ message: "Error creating DDT" });
    }
  });

  // Update DDT
  app.put("/api/ddts/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'DDT');

      const validatedData = insertDDTSchema.partial().parse(req.body);
      
      const existingDDT = await storage.getDDT(id);
      if (!existingDDT) {
        return res.status(404).json({ message: "DDT not found" });
      }

      // Check for duplicate if key fields are being changed
      if (validatedData.companyName || validatedData.number || validatedData.date) {
        const checkCompany = validatedData.companyName || existingDDT.companyName;
        const checkNumber = validatedData.number || existingDDT.number;
        const checkDate = validatedData.date || existingDDT.date;
        
        const duplicateDDT = await storage.checkDDTDuplicate(checkCompany, checkNumber, checkDate);
        if (duplicateDDT && duplicateDDT.id !== id.toString()) {
          return res.status(409).json({ 
            message: "DDT with this company, number and date already exists" 
          });
        }
      }

      const updatedDDT = await storage.updateDDT(id, validatedData);

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'UPDATE_DDT',
        `Updated DDT: ${updatedDDT.number} from ${updatedDDT.companyName}`,
        { ddtId: id }
      );

      res.json(updatedDDT);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error updating DDT:", error);
      res.status(500).json({ message: "Error updating DDT" });
    }
  });

  // Get products by DDT ID
  app.get("/api/ddts/:id/products", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'DDT');

      const products = await storage.getProductLabelsByDDT(id);
      res.json(products);
    } catch (error) {
      console.error("Error fetching DDT products:", error);
      res.status(500).json({ message: "Error fetching DDT products" });
    }
  });

  // Search DDTs
  app.get("/api/ddts/search/:query", requireAuth, async (req: Request, res: Response) => {
    try {
      const query = req.params.query.toLowerCase();
      const ddts = await storage.getAllDDTs();
      
      const filteredDDTs = ddts.filter(ddt =>
        ddt.companyName.toLowerCase().includes(query) ||
        ddt.number.toLowerCase().includes(query) ||
        ddt.date.includes(query) ||
        ddt.vatNumber.toLowerCase().includes(query)
      );

      res.json(filteredDDTs);
    } catch (error) {
      console.error("Error searching DDTs:", error);
      res.status(500).json({ message: "Error searching DDTs" });
    }
  });
}