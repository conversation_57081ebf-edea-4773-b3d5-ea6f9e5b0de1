import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { insertProductLabelSchema, retireProductSchema } from "@shared/schema";
import { z } from "zod";
import { productLabelValidation, validateImageData } from "../middlewares/security";
import { logActivity } from "../utils/activity-logger";
import { processProductLabel } from "../lib/claude";
import { validateRouteId } from "../lib/safe-parsing";

// Middleware to ensure authentication
const requireAuth = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    next();
  } else {
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerProductRoutes(app: Express) {
  // Get all product labels
  app.get("/api/product-labels", requireAuth, async (req: Request, res: Response) => {
    try {
      const productLabels = await storage.getAllProductLabels();
      console.log(`API /product-labels: Recuperati ${productLabels.length} prodotti totali`);
      res.json(productLabels);
    } catch (error) {
      console.error("Error fetching product labels:", error);
      res.status(500).json({ message: "Error fetching product labels" });
    }
  });

  // Get product label by ID
  app.get("/api/product-labels/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'product');

      const productLabel = await storage.getProductLabel(id);
      if (!productLabel) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json(productLabel);
    } catch (error) {
      console.error("Error fetching product label:", error);
      res.status(500).json({ message: "Error fetching product label" });
    }
  });

  // Create new product label
  app.post("/api/product-labels", requireAuth, productLabelValidation, async (req: Request, res: Response) => {
    try {
      const validatedData = insertProductLabelSchema.parse(req.body);
      
      const productLabel = await storage.createProductLabel({
        ...validatedData,
        createdBy: (req.user as any)?.id
      });

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'CREATE_PRODUCT',
        `Created product: ${productLabel.productName}`,
        { productId: productLabel.id }
      );

      res.status(201).json(productLabel);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating product label:", error);
      res.status(500).json({ message: "Error creating product label" });
    }
  });

  // Update product label
  app.put("/api/product-labels/:id", requireAuth, productLabelValidation, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'product');

      const validatedData = insertProductLabelSchema.partial().parse(req.body);
      
      const existingProduct = await storage.getProductLabel(id);
      if (!existingProduct) {
        return res.status(404).json({ message: "Product not found" });
      }

      const updatedProduct = await storage.updateProductLabel(id, validatedData);

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'UPDATE_PRODUCT',
        `Updated product: ${updatedProduct.productName}`,
        { productId: id }
      );

      res.json(updatedProduct);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error updating product label:", error);
      res.status(500).json({ message: "Error updating product label" });
    }
  });

  // Retire product
  app.post("/api/product-labels/:id/retire", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'product');

      const { reason } = retireProductSchema.parse(req.body);
      const userId = (req.user as any)?.id;

      const retiredProduct = await storage.retireProduct(id.toString(), reason, userId?.toString() || 'unknown');

      // Log activity
      await logActivity(
        userId,
        (req.user as any)?.username || 'Unknown',
        'RETIRE_PRODUCT',
        `Retired product: ${retiredProduct.productName} - Reason: ${reason}`,
        { productId: id, reason }
      );

      res.json(retiredProduct);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error retiring product:", error);
      res.status(500).json({ message: "Error retiring product" });
    }
  });

  // Get retired products
  app.get("/api/product-labels/retired", requireAuth, async (req: Request, res: Response) => {
    try {
      const retiredProducts = await storage.getRetiredProducts();
      res.json(retiredProducts);
    } catch (error) {
      console.error("Error fetching retired products:", error);
      res.status(500).json({ message: "Error fetching retired products" });
    }
  });

  // Get active products
  app.get("/api/product-labels/active", requireAuth, async (req: Request, res: Response) => {
    try {
      const activeProducts = await storage.getActiveProducts();
      res.json(activeProducts);
    } catch (error) {
      console.error("Error fetching active products:", error);
      res.status(500).json({ message: "Error fetching active products" });
    }
  });

  // Process product label with AI
  app.post("/api/product-labels/process", requireAuth, validateImageData, async (req: Request, res: Response) => {
    try {
      const { imageData } = req.body;
      
      if (!imageData) {
        return res.status(400).json({ message: "Image data is required" });
      }

      const result = await processProductLabel(imageData);
      res.json(result);
    } catch (error) {
      console.error("Error processing product label:", error);
      res.status(500).json({ message: "Error processing product label" });
    }
  });

  // Search products
  app.get("/api/product-labels/search/:query", requireAuth, async (req: Request, res: Response) => {
    try {
      const query = req.params.query.toLowerCase();
      const products = await storage.getAllProductLabels();
      
      const filteredProducts = products.filter(product =>
        product.productName.toLowerCase().includes(query) ||
        product.batchNumber.toLowerCase().includes(query) ||
        product.expiryDate.includes(query)
      );

      res.json(filteredProducts);
    } catch (error) {
      console.error("Error searching products:", error);
      res.status(500).json({ message: "Error searching products" });
    }
  });
}