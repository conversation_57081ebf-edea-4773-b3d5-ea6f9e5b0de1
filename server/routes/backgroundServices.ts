/**
 * API routes per la gestione dei servizi di background
 */

import { Router } from 'express';
import { BackgroundServices, BackgroundServiceConfig } from '../services/backgroundServices';
import { isAuthenticated, isAdmin } from '../middlewares/auth';

export function createBackgroundServicesRoutes(backgroundServices: BackgroundServices): Router {
  const router = Router();

  // Middleware: Solo admin possono gestire i servizi di background
  router.use(isAuthenticated);
  router.use(isAdmin);

  /**
   * GET /api/background-services/status
   * Ottieni lo stato dei servizi di background
   */
  router.get('/status', (req, res) => {
    try {
      const status = backgroundServices.getStatus();
      res.json({
        success: true,
        status
      });
    } catch (error) {
      console.error('Errore nel recupero dello stato dei servizi:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nel recupero dello stato'
      });
    }
  });

  /**
   * POST /api/background-services/start
   * Avvia i servizi di background
   */
  router.post('/start', (req, res) => {
    try {
      backgroundServices.start();
      res.json({
        success: true,
        message: 'Servizi di background avviati'
      });
    } catch (error) {
      console.error('Errore nell\'avvio dei servizi:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nell\'avvio dei servizi'
      });
    }
  });

  /**
   * POST /api/background-services/stop
   * Ferma i servizi di background
   */
  router.post('/stop', (req, res) => {
    try {
      backgroundServices.stop();
      res.json({
        success: true,
        message: 'Servizi di background fermati'
      });
    } catch (error) {
      console.error('Errore nell\'arresto dei servizi:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nell\'arresto dei servizi'
      });
    }
  });

  /**
   * PATCH /api/background-services/config
   * Aggiorna la configurazione dei servizi
   */
  router.patch('/config', (req, res) => {
    try {
      const configUpdates: Partial<BackgroundServiceConfig> = req.body;
      
      // Validazione basic dei valori
      const validKeys = [
        'autoArchiveContainersAfterDays',
        'autoDeleteRetiredProductsAfterDays', 
        'cleanupSessionsAfterDays',
        'autoBackupEnabled',
        'autoBackupFrequencyHours',
        'maintenanceHour'
      ];

      const filteredConfig: Partial<BackgroundServiceConfig> = {};
      
      for (const [key, value] of Object.entries(configUpdates)) {
        if (validKeys.includes(key)) {
          if (typeof value === 'number' && value >= 0) {
            (filteredConfig as any)[key] = value;
          } else if (typeof value === 'boolean' && key === 'autoBackupEnabled') {
            (filteredConfig as any)[key] = value;
          }
        }
      }

      backgroundServices.updateConfig(filteredConfig);
      
      res.json({
        success: true,
        message: 'Configurazione aggiornata',
        updatedConfig: filteredConfig
      });
    } catch (error) {
      console.error('Errore nell\'aggiornamento della configurazione:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nell\'aggiornamento della configurazione'
      });
    }
  });

  /**
   * POST /api/background-services/run-maintenance
   * Esegui manutenzione manuale
   */
  router.post('/run-maintenance', async (req, res) => {
    try {
      // Esegui la manutenzione manualmente
      await (backgroundServices as any).performDailyMaintenance();
      
      res.json({
        success: true,
        message: 'Manutenzione manuale completata'
      });
    } catch (error) {
      console.error('Errore durante la manutenzione manuale:', error);
      res.status(500).json({
        success: false,
        message: 'Errore durante la manutenzione manuale'
      });
    }
  });

  /**
   * POST /api/background-services/cleanup-sessions
   * Pulisci sessioni scadute manualmente
   */
  router.post('/cleanup-sessions', async (req, res) => {
    try {
      const deletedCount = await (backgroundServices as any).cleanupExpiredSessions();
      
      res.json({
        success: true,
        message: `Eliminate ${deletedCount} sessioni scadute`,
        deletedCount
      });
    } catch (error) {
      console.error('Errore durante la pulizia delle sessioni:', error);
      res.status(500).json({
        success: false,
        message: 'Errore durante la pulizia delle sessioni'
      });
    }
  });

  /**
   * POST /api/background-services/archive-containers
   * Archivia container inattivi manualmente
   */
  router.post('/archive-containers', async (req, res) => {
    try {
      const archivedCount = await (backgroundServices as any).autoArchiveInactiveContainers();
      
      res.json({
        success: true,
        message: `Archiviati ${archivedCount} container inattivi`,
        archivedCount
      });
    } catch (error) {
      console.error('Errore durante l\'archiviazione dei container:', error);
      res.status(500).json({
        success: false,
        message: 'Errore durante l\'archiviazione dei container'
      });
    }
  });

  /**
   * GET /api/background-services/logs
   * Ottieni i log delle attività di manutenzione
   */
  router.get('/logs', async (req, res) => {
    try {
      // Ottieni i log delle attività di sistema
      const logs = await (backgroundServices as any).storage.getActivityLogs({
        userId: null, // Log di sistema
        action: undefined // Tutti i tipi di azione
      });

      // Filtra solo i log di manutenzione
      const maintenanceLogs = logs.filter((log: any) => 
        log.username === 'system' && (
          log.action.includes('maintenance') ||
          log.action.includes('auto_archive') ||
          log.action.includes('auto_delete') ||
          log.action.includes('expired_product') ||
          log.action.includes('expiring_product')
        )
      );

      res.json({
        success: true,
        logs: maintenanceLogs.slice(0, 100) // Limita a 100 log più recenti
      });
    } catch (error) {
      console.error('Errore nel recupero dei log:', error);
      res.status(500).json({
        success: false,
        message: 'Errore nel recupero dei log'
      });
    }
  });

  return router;
}