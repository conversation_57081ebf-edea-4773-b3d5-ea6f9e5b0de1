import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { ActivityLogFilters } from "@shared/schema";
import { safeParseOptionalInt, validateRouteId, validatePaginationParams } from "../lib/safe-parsing";
import { asyncHand<PERSON> } from "../lib/async-error-handler";

// Middleware to ensure authentication
const requireAuth = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    next();
  } else {
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerActivityRoutes(app: Express) {
  // Get activity logs with optional filtering
  app.get("/api/activity-logs", requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const { startDate, endDate, userId, containerId, action, limit } = req.query;
    
    const filters: ActivityLogFilters = {};
    if (startDate) filters.startDate = startDate as string;
    if (endDate) filters.endDate = endDate as string;
    if (userId) filters.userId = safeParseOptionalInt(userId as string, 0, 1);
    if (containerId) filters.containerId = safeParseOptionalInt(containerId as string, 0, 1);
    if (action) filters.action = action as string;

    const limitNum = safeParseOptionalInt(limit as string, 50, 1, 500);
    
    const activities = await storage.getActivityLogs(filters, limitNum);
    res.json(activities);
  }));

  // Get recent activities (last 100)
  app.get("/api/activity-logs/recent", requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const activities = await storage.getActivityLogs({}, 100);
    res.json(activities);
  }));

  // Get activities by user
  app.get("/api/activity-logs/user/:userId", requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const userId = validateRouteId(req.params.userId, 'user');

    const activities = await storage.getActivityLogs({ userId }, 100);
    res.json(activities);
  }));

  // Get activities by container
  app.get("/api/activity-logs/container/:containerId", requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const containerId = validateRouteId(req.params.containerId, 'container');

    const activities = await storage.getActivityLogs({ containerId }, 100);
    res.json(activities);
  }));
}