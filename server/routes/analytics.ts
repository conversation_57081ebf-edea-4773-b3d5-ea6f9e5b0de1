/**
 * API Analytics Routes
 * Provides API usage analytics and performance metrics for the admin dashboard
 */

import { Router } from "express";
import { db } from "../db";
import { apiAnalytics } from "@shared/schema";
import { eq, desc, gte, lte, count, avg, sql } from "drizzle-orm";
import { isAuthenticated } from "../middlewares/auth";
import { validateApiRequest } from "../middlewares/security";
import { asyncHandler } from "../lib/async-error-handler";

const router = Router();

/**
 * GET /api/analytics/overview
 * Get API usage overview statistics
 */
router.get("/overview", isAuthenticated, validateApiRequest, asyncHandler(async (req, res) => {
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30d = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Get total API calls in different time ranges
    const [total24h] = await db
      .select({ count: count() })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last24h));

    const [total7d] = await db
      .select({ count: count() })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last7d));

    const [total30d] = await db
      .select({ count: count() })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last30d));

    // Get average response time
    const [avgResponseTime] = await db
      .select({ avg: avg(apiAnalytics.responseTime) })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last24h));

    // Get error rate (4xx and 5xx status codes)
    const [errorCount] = await db
      .select({ count: count() })
      .from(apiAnalytics)
      .where(
        sql`${apiAnalytics.timestamp} >= ${last24h} AND ${apiAnalytics.statusCode} >= 400`
      );

    const errorRate = total24h.count > 0 ? ((errorCount.count / total24h.count) * 100) : 0;

    // Get top endpoints
    const topEndpoints = await db
      .select({
        endpoint: apiAnalytics.endpoint,
        method: apiAnalytics.method,
        calls: count(),
        avgResponseTime: avg(apiAnalytics.responseTime)
      })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last24h))
      .groupBy(apiAnalytics.endpoint, apiAnalytics.method)
      .orderBy(desc(count()))
      .limit(10);

  res.json({
    overview: {
      calls24h: total24h.count,
      calls7d: total7d.count,
      calls30d: total30d.count,
      avgResponseTime: Math.round(Number(avgResponseTime.avg) || 0),
      errorRate: Math.round(errorRate * 100) / 100,
      errorCount: errorCount.count
    },
    topEndpoints
  });
}));

/**
 * GET /api/analytics/timeline
 * Get API usage timeline data
 */
router.get("/timeline", isAuthenticated, validateApiRequest, asyncHandler(async (req, res) => {
    const { hours = 24 } = req.query;
    const now = new Date();
    const startTime = new Date(now.getTime() - Number(hours) * 60 * 60 * 1000);

    // Get hourly API call counts
    const timeline = await db
      .select({
        hour: sql`DATE_TRUNC('hour', ${apiAnalytics.timestamp})`.as('hour'),
        calls: count(),
        avgResponseTime: avg(apiAnalytics.responseTime),
        errors: sql`SUM(CASE WHEN ${apiAnalytics.statusCode} >= 400 THEN 1 ELSE 0 END)`.as('errors')
      })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, startTime))
      .groupBy(sql`DATE_TRUNC('hour', ${apiAnalytics.timestamp})`)
      .orderBy(sql`DATE_TRUNC('hour', ${apiAnalytics.timestamp})`);

  res.json({ timeline });
}));

/**
 * GET /api/analytics/tenants
 * Get API usage by tenant
 */
router.get("/tenants", isAuthenticated, validateApiRequest, asyncHandler(async (req, res) => {
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const tenantUsage = await db
      .select({
        tenantId: apiAnalytics.tenantId,
        calls: count(),
        avgResponseTime: avg(apiAnalytics.responseTime),
        errors: sql`SUM(CASE WHEN ${apiAnalytics.statusCode} >= 400 THEN 1 ELSE 0 END)`.as('errors')
      })
      .from(apiAnalytics)
      .where(gte(apiAnalytics.timestamp, last24h))
      .groupBy(apiAnalytics.tenantId)
      .orderBy(desc(count()));

  res.json({ tenantUsage });
}));

/**
 * GET /api/analytics/errors
 * Get recent API errors for debugging
 */
router.get("/errors", isAuthenticated, validateApiRequest, async (req, res) => {
  try {
    const { limit = 50 } = req.query;

    const recentErrors = await db
      .select({
        id: apiAnalytics.id,
        endpoint: apiAnalytics.endpoint,
        method: apiAnalytics.method,
        statusCode: apiAnalytics.statusCode,
        responseTime: apiAnalytics.responseTime,
        timestamp: apiAnalytics.timestamp,
        errorMessage: apiAnalytics.errorMessage,
        userAgent: apiAnalytics.userAgent,
        ipAddress: apiAnalytics.ipAddress,
        tenantId: apiAnalytics.tenantId,
        userId: apiAnalytics.userId
      })
      .from(apiAnalytics)
      .where(sql`${apiAnalytics.statusCode} >= 400`)
      .orderBy(desc(apiAnalytics.timestamp))
      .limit(Number(limit));

    res.json({ errors: recentErrors });
  } catch (error) {
    console.error("Error fetching error analytics:", error);
    res.status(500).json({ error: "Failed to fetch error analytics" });
  }
});

export function registerAnalyticsRoutes(app: any) {
  app.use("/api/analytics", router);
}