/**
 * MODULAR ROUTE ARCHITECTURE
 * 
 * This file replaces the monolithic 3,313-line routes.ts file with a clean,
 * modular architecture following the Single Responsibility Principle.
 * 
 * Each route module is responsible for a single domain:
 * - auth.ts: Authentication and session management
 * - suppliers.ts: Supplier CRUD operations
 * - products.ts: Product label management
 * - ddts.ts: DDT (Transport Document) operations
 * - containers.ts: Container management (existing)
 * - activities.ts: Activity logging and retrieval
 * - reports.ts: Data export and reporting
 * - tenant-admin.ts: Multi-tenant administration
 * - users.ts: User management (existing)
 * - claude.ts: AI/Claude integration (existing)
 * - system.ts: System settings (existing)
 * - monitoring.ts: System monitoring (existing)
 */

import type { Express } from "express";
import { setupAuth, registerAuthRoutes } from "./auth";
import { registerSupplierRoutes } from "./suppliers";
import { registerProductRoutes } from "./products";
import { registerDDTRoutes } from "./ddts";
import { registerActivityRoutes } from "./activities";
import { registerReportRoutes } from "./reports";
import { registerTenantAdminRoutes } from "./tenant-admin";
import { registerAnalyticsRoutes } from "./analytics";
import performanceRoutes from "./performance";

// Existing modular routes
import { registerDevTools } from "./dev-tools";
import { registerUserRoutes } from "./users";
import { registerClaudeRoutes } from "./claude";
import { registerAiRoutes } from "./ai-routes";
import { registerSystemRoutes } from "./system";
import { registerContainerRoutes } from "./containers";
import { createSystemStatusRoutes } from "./systemStatus";
import monitoringRoutes from "./monitoring";

// Security middleware
import { 
  fileUploadConfig,
  generalLimiter,
  apiLimiter,
  securityHeaders,
  trackApiAnalytics,
  standardErrorHandler
} from "../middlewares/security";
import fileUpload from "express-fileupload";

export async function registerRoutes(app: Express) {
  console.log("🏗️ Initializing modular route architecture...");

  // 1. Setup authentication and sessions (must be first)
  await setupAuth(app);
  console.log("✅ Authentication and session management initialized");

  // 2. Enhanced Security middleware
  app.use(securityHeaders);
  app.use(generalLimiter);
  app.use(fileUpload(fileUploadConfig));
  app.use(trackApiAnalytics); // Track all API calls for analytics
  console.log("✅ Enhanced security middleware configured");

  // 3. Core business logic routes
  registerAuthRoutes(app);
  console.log("✅ Authentication routes registered");

  registerSupplierRoutes(app);
  console.log("✅ Supplier routes registered");

  registerProductRoutes(app);
  console.log("✅ Product routes registered");

  registerDDTRoutes(app);
  console.log("✅ DDT routes registered");

  registerActivityRoutes(app);
  console.log("✅ Activity logging routes registered");

  registerReportRoutes(app);
  console.log("✅ Reporting routes registered");

  // 4. Multi-tenant administration
  registerTenantAdminRoutes(app);
  console.log("✅ Tenant administration routes registered");

  registerAnalyticsRoutes(app);
  console.log("✅ API Analytics routes registered");

  app.use('/api/performance', performanceRoutes);
  console.log("✅ Performance monitoring routes registered");

  // 4.5. Query optimization routes (elimina N+1 problems)
  const queryOptimizationRoutes = await import('./query-optimization');
  app.use('/api/optimized', queryOptimizationRoutes.default);
  console.log("✅ Database query optimization routes registered");

  // 5. Existing modular routes
  registerUserRoutes(app);
  registerContainerRoutes(app);
  registerClaudeRoutes(app);
  registerAiRoutes(app);
  registerSystemRoutes(app);
  console.log("✅ Existing modular routes registered");

  // 6. System utilities and monitoring
  app.use("/api/monitoring", monitoringRoutes);
  app.use("/api/system", createSystemStatusRoutes());
  
  // Development tools (only in development)
  if (process.env.NODE_ENV === 'development') {
    registerDevTools(app);
    console.log("✅ Development tools registered");
  }

  console.log("🎉 Modular route architecture fully initialized");
  console.log("📊 Routes organized by domain responsibility");
  console.log("🔧 Maintainable, testable, and scalable architecture");

  // Add standardized error handler at the end
  app.use(standardErrorHandler);
}