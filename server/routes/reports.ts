import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import ExcelJS from 'exceljs';
import { jsPDF } from 'jspdf';
// @ts-ignore
import autoTable from 'jspdf-autotable';
import { asyncHandler } from "../lib/async-error-handler";

// Middleware to ensure authentication
const requireAuth = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    next();
  } else {
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerReportRoutes(app: Express) {
  // Export products to Excel
  app.get("/api/reports/products/excel", requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const products = await storage.getAllProductLabels();
    
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Products');
    
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Product Name', key: 'productName', width: 30 },
      { header: 'Batch Number', key: 'batchNumber', width: 20 },
      { header: 'Expiry Date', key: 'expiryDate', width: 15 },
      { header: 'Storage Instructions', key: 'storageInstructions', width: 40 },
      { header: 'Created At', key: 'createdAt', width: 20 }
    ];
    
    products.forEach(product => {
      worksheet.addRow({
        id: product.id,
        productName: product.productName,
        batchNumber: product.batchNumber,
        expiryDate: product.expiryDate,
        storageInstructions: product.storageInstructions,
        createdAt: product.createdAt
      });
    });
    
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=products.xlsx');
    
    await workbook.xlsx.write(res);
    res.end();
  }));

  // Export products to PDF
  app.get("/api/reports/products/pdf", requireAuth, async (req: Request, res: Response) => {
    try {
      const products = await storage.getAllProductLabels();
      
      const doc = new jsPDF();
      doc.setFontSize(20);
      doc.text('Products Report', 20, 20);
      
      const tableData = products.map(product => [
        product.id.toString(),
        product.productName,
        product.batchNumber,
        product.expiryDate,
        product.storageInstructions
      ]);
      
      autoTable(doc, {
        head: [['ID', 'Product Name', 'Batch Number', 'Expiry Date', 'Storage Instructions']],
        body: tableData,
        startY: 30
      });
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename=products.pdf');
      
      const pdfBuffer = doc.output('arraybuffer');
      res.send(Buffer.from(pdfBuffer));
    } catch (error) {
      console.error("Error generating PDF report:", error);
      res.status(500).json({ message: "Error generating PDF report" });
    }
  });

  // Export suppliers to Excel
  app.get("/api/reports/suppliers/excel", requireAuth, async (req: Request, res: Response) => {
    try {
      const suppliers = await storage.getAllSuppliers();
      
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Suppliers');
      
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: 'Company Name', key: 'companyName', width: 30 },
        { header: 'VAT Number', key: 'vatNumber', width: 20 },
        { header: 'Address', key: 'address', width: 40 },
        { header: 'Created At', key: 'createdAt', width: 20 }
      ];
      
      suppliers.forEach(supplier => {
        worksheet.addRow({
          id: supplier.id,
          companyName: supplier.companyName,
          vatNumber: supplier.vatNumber,
          address: supplier.address,
          createdAt: supplier.createdAt
        });
      });
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=suppliers.xlsx');
      
      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error("Error generating suppliers Excel report:", error);
      res.status(500).json({ message: "Error generating suppliers Excel report" });
    }
  });

  // Get dashboard statistics
  app.get("/api/reports/dashboard-stats", requireAuth, async (req: Request, res: Response) => {
    try {
      const [products, suppliers, containers, activities] = await Promise.all([
        storage.getAllProductLabels(),
        storage.getAllSuppliers(),
        storage.getAllContainers(),
        storage.getActivityLogs({}, 10)
      ]);

      const activeProducts = products.filter(p => !p.isRetired);
      const retiredProducts = products.filter(p => p.isRetired);
      const activeContainers = containers.filter(c => !c.isArchived);

      res.json({
        products: {
          total: products.length,
          active: activeProducts.length,
          retired: retiredProducts.length
        },
        suppliers: {
          total: suppliers.length
        },
        containers: {
          total: containers.length,
          active: activeContainers.length,
          archived: containers.length - activeContainers.length
        },
        recentActivities: activities
      });
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ message: "Error fetching dashboard stats" });
    }
  });
}