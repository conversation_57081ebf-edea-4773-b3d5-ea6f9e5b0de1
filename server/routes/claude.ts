import { Express } from "express";
import { isAuthenticated, isAdmin } from "../middlewares/auth";
import { storage } from "../storage";
import { z } from "zod";
import { asyncHandler } from "../lib/async-error-handler";

/**
 * Rotte per la gestione di Claude AI
 * Separiamo queste rotte per migliorare la manutenibilità del codice
 */
export function registerClaudeRoutes(app: Express) {
  
  // Ottieni i modelli disponibili di <PERSON>
  // Questo risolverà il problema dei modelli LLM che non si caricano nelle impostazioni
  app.get("/api/claude/models", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    // Elenco dei modelli disponibili (hardcoded perché provengono da API esterna)
    // In un'implementazione completa, questo potrebbe provenire da una chiamata alla API di Anthropic
    const models = [
      { id: "claude-3-opus-20240229", name: "<PERSON> 3 Opus" },
      { id: "claude-3-sonnet-20240229", name: "Claude 3 Sonnet" },
      { id: "claude-3-haiku-20240307", name: "Claude 3 Haiku" },
      { id: "claude-2.1", name: "Claude 2.1" },
      { id: "claude-2.0", name: "Claude 2.0" },
      { id: "claude-instant-1.2", name: "Claude Instant 1.2" }
    ];
    
    // Recupera le impostazioni del modello corrente
    const settings = await storage.getSystemSettings();
    const currentModel = settings?.claudeModel || "claude-3-sonnet-20240229";
    const defaultModel = "claude-3-sonnet-20240229"; // Modello predefinito
    
    res.json({
      models,
      currentModel,
      defaultModel
    });
    
    console.log("Modelli Claude inviati con successo");
  }));
  
  // Imposta il modello Claude da utilizzare
  app.post("/api/claude/model", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    // Validazione dei dati
    const schema = z.object({
      modelId: z.string().min(1).max(100)
    });
    
    const result = schema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: "Dati non validi", errors: result.error.errors });
    }
    
    const { modelId } = result.data;
    
    // Aggiorna le impostazioni del sistema con il nuovo modello
    await storage.updateSystemSettings({ claudeModel: modelId });
    
    console.log(`Modello Claude aggiornato a: ${modelId}`);
    res.json({ success: true, model: modelId });
  }));
  
  // Esegui un'operazione con Claude (esempio generico)
  app.post("/api/claude/process", isAuthenticated, asyncHandler(async (req, res) => {
    const schema = z.object({
      prompt: z.string().min(1).max(100000),
      temperature: z.number().min(0).max(1).optional().default(0.7),
      maxTokens: z.number().min(1).max(4096).optional().default(1024)
    });
    
    const result = schema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: "Dati non validi", errors: result.error.errors });
    }
    
    const { prompt, temperature, maxTokens } = result.data;
    
    // Recupera il modello dalle impostazioni
    const settings = await storage.getSystemSettings();
    const modelId = settings?.claudeModel || "claude-3-sonnet-20240229";
    
    // Log per debug
    console.log(`Elaborazione richiesta Claude con modello: ${modelId}`);
    console.log(`Prompt: ${prompt.substring(0, 100)}...`);
    
    // In un'implementazione reale, qui chiameresti l'API di Claude
    
    // Simuliamo una risposta per debug
    res.json({
      success: true,
      model: modelId,
      result: "Questa è una risposta di esempio da Claude.",
      tokens: {
        input: prompt.length / 4, // Stima approssimativa
        output: 10
      }
    });
  }));
}