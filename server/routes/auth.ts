import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { ensureDefaultTenant } from "../tenant-migration";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import session from "express-session";
import connectPgSimple from "connect-pg-simple";
import bcrypt from "bcryptjs";
import { pool } from "../db";
import { loginValidation } from "../middlewares/security";
import { getSecureUser, getSecureUserByUsername, getInitializationUserByUsername, enrichUserSession, auditAuthenticationEvent } from "../lib/secure-auth";
import { securityConfig, SecurityAuditLogger } from "../lib/security-validator";
import { productionSecurity } from "../lib/production-security-enforcer";
import { blockMockTokens, secureAuthentication, authRateLimit, securityHeaders, sanitizeRequest } from "../middlewares/production-security";
import { db } from "../db";
import { tenants } from "../../shared/schema";
import { eq } from "drizzle-orm";

const PgSession = connectPgSimple(session);

export async function setupAuth(app: Express) {
  // Set up session middleware
  app.use(
    session({
      store: new PgSession({
        pool: pool,
        tableName: 'sessions',
        createTableIfMissing: true
      }),
      secret: productionSecurity.getSecureSessionSecret(),
      resave: false,
      saveUninitialized: false,
      cookie: productionSecurity.getSecureCookieConfig()
    })
  );

  // Initialize passport and session
  app.use(passport.initialize());
  app.use(passport.session());

  // Passport local strategy
  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        // Use secure authentication helper
        const user = await getSecureUserByUsername(username);

        if (!user) {
          return done(null, false, { message: "Nome utente non corretto." });
        }

        console.log(`Tentativo di login: ${username}`);

        const isValid = await bcrypt.compare(password, user.password);
        console.log(`Risultato autenticazione: ${isValid ? 'Valida' : 'Non valida'}`);

        if (!isValid) {
          return done(null, false, { message: "Incorrect password." });
        }

        // Enrich user session with proper tenant context
        const enrichedUser = enrichUserSession(user);
        auditAuthenticationEvent('login_success', enrichedUser.id, { username: enrichedUser.username, tenant: enrichedUser.tenantId });
        
        return done(null, enrichedUser);
      } catch (error) {
        return done(error);
      }
    })
  );

  // Serialize user to the session
  passport.serializeUser((user: any, done) => {
    console.log("Serializing user:", user);
    if (typeof user === 'object' && user !== null && 'id' in user) {
      console.log("Serialized user ID:", user.id);
      done(null, user.id);
    } else {
      console.log("User is already an ID (or invalid):", user);
      done(null, user);
    }
  });

  // Deserialize user from the session (Updated for UUID support + Tenant Impersonation)
  passport.deserializeUser(async (id: any, done) => {
    try {
      let userId: string;

      // Handle UUID format (new standard)
      if (typeof id === 'string' && id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        userId = id;
      }
      // Handle legacy integer IDs for backwards compatibility during migration
      else if (typeof id === 'number') {
        // Convert legacy integer IDs to their corresponding UUIDs
        const legacyMapping: Record<number, string> = {
          2: 'c2f26b17-b4f9-4920-8b41-f30fbd332920', // admin
          1: 'ba4c58d7-29de-483e-ab8d-4a9fc4de4c46'   // user
        };
        userId = legacyMapping[id];
        if (!userId) {
          console.error("Legacy ID mapping not found:", id);
          return done(new Error("ID utente legacy non trovato"), null);
        }
      } else if (typeof id === 'string' && !isNaN(Number(id))) {
        // Handle string representation of legacy integer IDs
        const legacyId = parseInt(id, 10);
        const legacyMapping: Record<number, string> = {
          2: 'c2f26b17-b4f9-4920-8b41-f30fbd332920', // admin
          1: 'ba4c58d7-29de-483e-ab8d-4a9fc4de4c46'   // user
        };
        userId = legacyMapping[legacyId];
        if (!userId) {
          console.error("Legacy ID mapping not found for string:", id);
          return done(new Error("ID utente legacy non trovato"), null);
        }
      } else if (typeof id === 'object' && id !== null && 'id' in id) {
        // Handle object format
        const objId = id.id;
        if (typeof objId === 'string' && objId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          userId = objId;
        } else {
          console.error("Formato ID oggetto non supportato:", objId);
          return done(new Error("Formato ID oggetto non valido"), null);
        }
      } else if (typeof id === 'string' && (id.startsWith('{') || id.startsWith('['))) {
        const { safeJsonParse } = await import('../lib/safe-json');
        const result = safeJsonParse(id);
        if (result.success && result.data && typeof result.data === 'object' && 'id' in result.data) {
          userId = result.data.id;
        } else {
          console.error("Errore nel parsing della stringa JSON:", result.error);
          return done(new Error("Formato ID non valido"), null);
        }
      } else {
        console.error("Formato ID non supportato:", id);
        return done(new Error("Formato ID non valido"), null);
      }

      if (!userId || typeof userId !== 'string') {
        console.error("ID utente non valido dopo la conversione:", userId);
        return done(new Error("ID utente non valido"), null);
      }

      // Use secure authentication helper to eliminate security warnings
      const user = await getSecureUser(userId);
      
      if (!user) {
        return done(null, null);
      }
      
      // Enrich user data before storing in session
      let enrichedUser = enrichUserSession(user);
      
      // NOTA: Per l'impersonificazione con cambio tenant, il controllo viene fatto nell'endpoint /api/auth/me
      // che ha accesso diretto alla sessione request. Qui manteniamo il comportamento standard.
      
      auditAuthenticationEvent('session_deserialization', userId, { 
        username: user.username, 
        tenant: user.tenantId
      });

      done(null, enrichedUser);
    } catch (error) {
      console.error("Errore nella deserializzazione dell'utente:", error);
      done(error, null);
    }
  });

  // Create default users - using initialization-specific method to avoid security warnings
  try {
    const defaultTenant = await ensureDefaultTenant();
    
    const adminExists = await getInitializationUserByUsername("admin");
    if (!adminExists) {
      const passwordToUse = productionSecurity.getSecurePassword('admin');
      const hashedPassword = await bcrypt.hash(passwordToUse, 14); // Increased to 14 rounds for maximum security
      await storage.createUser({
        username: "admin",
        password: hashedPassword,
        isAdmin: true,
        role: "admin",
        tenantId: defaultTenant.id,
      });
      console.log("✅ SECURE: Default admin user created with production-grade password");
    }

    const regularUserExists = await getInitializationUserByUsername("user");
    if (!regularUserExists) {
      const passwordToUse = productionSecurity.getSecurePassword('user');
      const hashedPassword = await bcrypt.hash(passwordToUse, 14); // Increased to 14 rounds for maximum security
      await storage.createUser({
        username: "user",
        password: hashedPassword,
        isAdmin: false,
        role: "user", 
        tenantId: defaultTenant.id,
      });
      console.log("✅ SECURE: Default regular user created with production-grade password");
    }
  } catch (error) {
    console.error("Error creating default users:", error);
  }
}

export function registerAuthRoutes(app: Express) {
  // Apply production security middleware to all routes
  app.use('/api/auth', securityHeaders, sanitizeRequest, blockMockTokens);
  
  // Login endpoint with rate limiting
  app.post("/api/auth/login", authRateLimit, loginValidation, (req: Request, res: Response, next: any) => {
    const { username } = req.body;
    
    passport.authenticate("local", (err: any, user: any, info: any) => {
      if (err) {
        console.error("Authentication error:", err);
        productionSecurity.auditAuthenticationAttempt(username, false, {
          error: err.message,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
        return res.status(500).json({ message: "Internal server error" });
      }
      if (!user) {
        productionSecurity.auditAuthenticationAttempt(username, false, {
          reason: info?.message || "Invalid credentials",
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
        return res.status(401).json({ message: info?.message || "Invalid credentials" });
      }
      req.logIn(user, (err) => {
        if (err) {
          console.error("Login error:", err);
          productionSecurity.auditAuthenticationAttempt(username, false, {
            userId: user.id,
            error: err.message,
            userAgent: req.get('User-Agent'),
            ip: req.ip
          });
          return res.status(500).json({ message: "Login failed" });
        }
        
        // Audit successful authentication
        productionSecurity.auditAuthenticationAttempt(user.username, true, {
          userId: user.id,
          tenantId: user.tenantId,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
        
        console.log(`✅ SECURE LOGIN: User ${user.username} (${user.id}) authenticated successfully`);
        return res.json({ 
          user: {
            id: user.id,
            username: user.username,
            isAdmin: user.isAdmin,
            email: user.email,
            role: user.role
          }
        });
      });
    })(req, res, next);
  });

  // Logout endpoint
  app.post("/api/auth/logout", (req: Request, res: Response) => {
    req.logout((err) => {
      if (err) {
        console.error("Logout error:", err);
        return res.status(500).json({ message: "Logout failed" });
      }
      req.session.destroy((err) => {
        if (err) {
          console.error("Session destroy error:", err);
          return res.status(500).json({ message: "Session cleanup failed" });
        }
        res.clearCookie('connect.sid');
        res.json({ message: "Logout successful" });
      });
    });
  });

  // Get current user with secure authentication
  app.get("/api/auth/me", secureAuthentication, async (req: Request, res: Response) => {
    if (req.isAuthenticated() && req.user) {
      try {
        // Check if user is being impersonated or admin tenant switching is active
        const isImpersonated = !!(req.session as any)?.isImpersonated;
        const impersonatedUserId = (req.session as any)?.impersonatedUserId;
        const originalAdminId = (req.session as any)?.originalAdminId;
        const activeTenantId = (req.session as any)?.activeTenantId; // Tenant attivo per impersonificazione o admin switch
        const adminTenantSwitch = !!(req.session as any)?.adminTenantSwitch; // Cambio tenant admin via URL
        
        // Use impersonated user ID if impersonation is active, otherwise use authenticated user ID
        const userId = isImpersonated && impersonatedUserId ? impersonatedUserId : (req.user as any).id;
        
        // Use secure authentication helper
        const user = await getSecureUser(userId);
        
        if (!user) {
          return res.status(401).json({ message: "User not found" });
        }
        
        // Durante l'impersonificazione o il cambio tenant admin, usa il tenant attivo dalla sessione se presente
        const effectiveTenantId = ((isImpersonated || adminTenantSwitch) && activeTenantId) ? activeTenantId : user.tenantId;
        
        if (user) {
          auditAuthenticationEvent('auth_me_request', userId, { 
            username: user.username, 
            tenant: effectiveTenantId,
            isImpersonated: isImpersonated,
            adminTenantSwitch: adminTenantSwitch,
            originalAdminId: originalAdminId
          });
        }

        // Fetch tenant information usando l'effective tenant ID
        let tenantInfo = null;
        if (effectiveTenantId) {
          try {
            const tenant = await db
              .select({
                id: tenants.id,
                name: tenants.name,
                code: tenants.code
              })
              .from(tenants)
              .where(eq(tenants.id, effectiveTenantId))
              .limit(1);
            
            if (tenant.length > 0) {
              tenantInfo = tenant[0];
            }
          } catch (tenantError) {
            console.error("Error fetching tenant info:", tenantError);
            // Continue without tenant info rather than failing the request
          }
        }

        res.json({
          id: user.id,
          username: user.username,
          isAdmin: user.isAdmin,
          email: user.email,
          role: user.role,
          tenantId: effectiveTenantId, // Usa il tenant effettivo (può essere diverso durante impersonificazione)
          tenant: tenantInfo,
          isImpersonated: isImpersonated,
          originalAdminId: originalAdminId
        });
      } catch (error) {
        console.error("Error fetching current user:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    } else {
      res.status(401).json({ message: "Unauthorized" });
    }
  });
}