/**
 * Performance Monitoring Routes
 * Riceve metriche dal client e fornisce dashboard monitoring
 */

import { Router } from 'express';
import { logger } from '../lib/logger';
import { sessionCacheManager } from '../lib/session-cache-manager';
import { performanceOptimizer } from '../lib/performance-optimizer';
import { asyncHand<PERSON> } from "../lib/async-error-handler";

const router = Router();

interface ClientMetrics {
  fcp: number;
  lcp: number;
  cls: number;
  fid: number;
  ttfb: number;
  pageLoadTime: number;
  userAgent: string;
  connection?: string;
  timestamp: number;
}

// Storage per metriche performance (in memoria per semplicità)
const performanceMetrics: ClientMetrics[] = [];
const MAX_METRICS = 1000;

/**
 * Endpoint per ricevere metriche client
 */
router.post('/metrics', asyncHandler(async (req, res) => {
  const metrics: ClientMetrics = req.body;
  
  // Validazione base (più permissiva per evitare errori 400)
  if (!metrics || typeof metrics !== 'object' || !metrics.timestamp) {
    return res.status(400).json({ error: 'Invalid metrics data' });
  }
  
  // Assicura che fcp sia un numero valido o usa default
  const fcpValue = typeof metrics.fcp === 'number' && !isNaN(metrics.fcp) ? metrics.fcp : 0;

  // Salva metriche con timestamp server
  const enrichedMetrics = {
    ...metrics,
    serverTimestamp: Date.now(),
    userId: req.user ? (req.user as any).id : 'anonymous'
  };

  performanceMetrics.push(enrichedMetrics);

  // Mantieni solo le ultime metriche
  if (performanceMetrics.length > MAX_METRICS) {
    performanceMetrics.splice(0, performanceMetrics.length - MAX_METRICS);
  }

  // Registra nel performance optimizer con validazione sicura completa
  try {
    const safeMetrics = {
      fcpTime: typeof fcpValue === 'number' && !isNaN(fcpValue) && fcpValue >= 0 ? fcpValue : 0,
      bundleSize: 0, // Da calcolare client-side
      cacheHitRate: 0, // Da cache manager
      loadTime: typeof metrics.pageLoadTime === 'number' && !isNaN(metrics.pageLoadTime) && metrics.pageLoadTime >= 0 ? metrics.pageLoadTime : 0
    };
    
    performanceOptimizer.recordMetrics(safeMetrics);
  } catch (optimizerError) {
    // Solo log in development per ridurre rumore
    if (process.env.NODE_ENV === 'development') {
      logger.debug('Performance optimizer minor issue (safe to ignore)', { error: String(optimizerError) || 'Unknown' });
    }
    // Continue without optimizer failure to prevent request crash
  }

  // Log solo metriche critiche REALI in produzione (permissivo in development)
  const pageLoadTime = typeof metrics.pageLoadTime === 'number' ? metrics.pageLoadTime : 0;
  
  // Solo in produzione e solo per valori estremamente alti
  if (process.env.NODE_ENV === 'production' && fcpValue > 10000 && fcpValue > 0 && pageLoadTime > 10000 && pageLoadTime > 0) {
    logger.warn('Performance metrics - slow load detected', {
      fcp: `${Math.round(fcpValue)}ms`,
      lcp: `${Math.round(typeof metrics.lcp === 'number' ? metrics.lcp : 0)}ms`,
      pageLoad: `${Math.round(pageLoadTime)}ms`,
      userAgent: metrics.userAgent?.substring(0, 50) || 'unknown'
    });
  } else if (fcpValue > 0 || pageLoadTime > 0) {
    logger.debug('Performance metrics received', {
      fcp: `${Math.round(fcpValue)}ms`,
      pageLoad: `${Math.round(pageLoadTime)}ms`
    });
  }

  res.json({ status: 'success', recorded: true });
}));

/**
 * Dashboard metriche performance
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
    // Calcola statistiche aggregate
    const recentMetrics = performanceMetrics
      .filter(m => Date.now() - m.timestamp < 24 * 60 * 60 * 1000) // Ultime 24 ore
      .slice(-100); // Ultime 100 metriche

    if (recentMetrics.length === 0) {
      return res.json({
        message: 'No recent performance data available',
        stats: null,
        cacheStats: sessionCacheManager.getCacheStats(),
        optimizerStats: performanceOptimizer.getPerformanceStats()
      });
    }

    const avgFCP = recentMetrics.reduce((sum, m) => sum + m.fcp, 0) / recentMetrics.length;
    const avgLCP = recentMetrics.reduce((sum, m) => sum + m.lcp, 0) / recentMetrics.length;
    const avgPageLoad = recentMetrics.reduce((sum, m) => sum + m.pageLoadTime, 0) / recentMetrics.length;

    const p95FCP = recentMetrics
      .map(m => m.fcp)
      .sort((a, b) => a - b)[Math.floor(recentMetrics.length * 0.95)];

    // Identifica metriche problematiche
    const slowSessions = recentMetrics.filter(m => m.fcp > 3000);
    const goodSessions = recentMetrics.filter(m => m.fcp <= 1500);

    const stats = {
      totalSessions: recentMetrics.length,
      timeRange: '24 hours',
      
      // Core Web Vitals
      fcp: {
        average: Math.round(avgFCP),
        p95: Math.round(p95FCP),
        target: 1500,
        status: avgFCP <= 1500 ? 'good' : avgFCP <= 3000 ? 'needs-improvement' : 'poor'
      },
      
      lcp: {
        average: Math.round(avgLCP),
        target: 2500,
        status: avgLCP <= 2500 ? 'good' : avgLCP <= 4000 ? 'needs-improvement' : 'poor'
      },
      
      pageLoad: {
        average: Math.round(avgPageLoad),
        target: 3000
      },
      
      // Session analysis
      performance: {
        good: goodSessions.length,
        needsImprovement: recentMetrics.length - slowSessions.length - goodSessions.length,
        poor: slowSessions.length,
        goodPercentage: Math.round((goodSessions.length / recentMetrics.length) * 100)
      },
      
      // Browser/device insights
      browsers: getBrowserStats(recentMetrics),
      connections: getConnectionStats(recentMetrics)
    };

  res.json({
    stats,
    cacheStats: sessionCacheManager.getCacheStats(),
    optimizerStats: performanceOptimizer.getPerformanceStats(),
    recommendations: generateRecommendations(stats)
  });
}));

/**
 * Statistiche browser
 */
function getBrowserStats(metrics: ClientMetrics[]): Record<string, number> {
  const browsers: Record<string, number> = {};
  
  metrics.forEach(m => {
    const ua = m.userAgent || 'Unknown';
    let browser = 'Unknown';
    
    if (ua.includes('Chrome')) browser = 'Chrome';
    else if (ua.includes('Firefox')) browser = 'Firefox';
    else if (ua.includes('Safari')) browser = 'Safari';
    else if (ua.includes('Edge')) browser = 'Edge';
    
    browsers[browser] = (browsers[browser] || 0) + 1;
  });
  
  return browsers;
}

/**
 * Statistiche connessione
 */
function getConnectionStats(metrics: ClientMetrics[]): Record<string, number> {
  const connections: Record<string, number> = {};
  
  metrics.forEach(m => {
    const conn = m.connection || 'unknown';
    connections[conn] = (connections[conn] || 0) + 1;
  });
  
  return connections;
}

/**
 * Genera raccomandazioni basate su metriche
 */
function generateRecommendations(stats: any): string[] {
  const recommendations: string[] = [];
  
  if (stats.fcp.average > 3000) {
    recommendations.push('Critical: FCP is very slow. Implement critical CSS inlining and resource preloading.');
  } else if (stats.fcp.average > 1500) {
    recommendations.push('FCP needs improvement. Consider optimizing bundle size and critical resource loading.');
  }
  
  if (stats.lcp.average > 4000) {
    recommendations.push('Critical: LCP is very slow. Optimize largest contentful paint element.');
  } else if (stats.lcp.average > 2500) {
    recommendations.push('LCP needs improvement. Consider image optimization and lazy loading.');
  }
  
  if (stats.performance.goodPercentage < 50) {
    recommendations.push('Less than 50% of sessions have good performance. Review optimization strategy.');
  }
  
  if (stats.performance.poor > stats.performance.good) {
    recommendations.push('More sessions are performing poorly than well. Urgent optimization needed.');
  }
  
  return recommendations;
}

/**
 * Cache statistics endpoint
 */
router.get('/cache-stats', async (req, res) => {
  try {
    const cacheStats = sessionCacheManager.getCacheStats();
    
    res.json({
      sessionCache: cacheStats,
      recommendations: cacheStats.hitRate < 80 
        ? ['Cache hit rate is below 80%. Consider increasing cache TTL or reviewing cache strategy.']
        : ['Cache performance is good.']
    });
  } catch (error) {
    logger.error('Failed to get cache stats', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
});

/**
 * Performance optimization suggestions
 */
router.get('/optimization-suggestions', async (req, res) => {
  try {
    const bundleAnalysis = performanceOptimizer.analyzeBundleSize();
    const lazyLoadingConfig = performanceOptimizer.optimizeLazyLoading();
    const viteConfig = performanceOptimizer.getViteOptimizationConfig();
    
    res.json({
      bundleOptimization: bundleAnalysis,
      lazyLoading: lazyLoadingConfig,
      viteConfig: viteConfig,
      criticalCSS: performanceOptimizer.generateCriticalCSS(),
      resourceHints: performanceOptimizer.generateResourceHints()
    });
  } catch (error) {
    logger.error('Failed to generate optimization suggestions', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    res.status(500).json({ error: 'Failed to generate suggestions' });
  }
});

export default router;