import { Express, Request, Response } from "express";
import { storage } from "../storage";
import { isAuthenticated, isAdmin } from "../middlewares/auth";  
import { User, insertUserSchema } from "@shared/schema";
import { Session } from "express-session";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { asyncHandler } from "../lib/async-error-handler";

// Estendi il tipo Session per includere le proprietà personalizzate
declare module "express-session" {
  interface Session {
    originalAdminId?: string;
    isImpersonated?: boolean;
    impersonatedUserId?: string;
    activeTenantId?: string;
  }
}

// Funzione per registrare l'attività di impersonificazione
async function logImpersonation(adminId: string, adminUsername: string, targetUserId: string, targetUsername: string, tenantId: string) {
  try {
    await storage.addActivityLog({
      userId: adminId,
      username: adminUsername,
      action: "impersonate_user",
      details: `Admin ${adminUsername} ha impersonato l'utente ${targetUsername}`,
      metadata: { targetUserId, targetUsername },
      tenantId
    });
  } catch (error) {
    console.error("Errore nel log dell'impersonificazione:", error);
  }
}

export function registerUserRoutes(app: Express) {
  // Lista utenti (solo per admin)
  app.get("/api/users", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const user = req.user as any;
    const users = await storage.getAllUsers(user.tenantId);
    res.json(users);
  }));

  // Endpoint per verificare lo stato di impersonificazione
  app.get("/api/auth/check-impersonation", (req: Request, res: Response) => {
    // Forza l'header Content-Type a JSON per evitare redirect HTML
    res.setHeader('Content-Type', 'application/json');
    
    // Verifica se l'utente è autenticato
    if (!req.isAuthenticated()) {
      // Restituisci status 401 con JSON invece di reindirizzare
      return res.status(401).json({
        isImpersonated: false,
        originalAdminId: null,
        authenticated: false,
        message: "Non autenticato"
      });
    }
    
    // Utente autenticato, restituisci lo stato di impersonificazione
    res.json({
      isImpersonated: !!req.session.isImpersonated,
      originalAdminId: req.session.originalAdminId || null,
      authenticated: true
    });
  });
  
  // Endpoint alternativo per verificare lo stato di impersonificazione (più stabile)
  app.get("/api/auth/impersonation-status", (req: Request, res: Response) => {
    // Forza l'header Content-Type a JSON per evitare redirect HTML
    res.setHeader('Content-Type', 'application/json');
    
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        isImpersonated: false,
        originalAdminId: null,
        authenticated: false,
        message: "Non autenticato"
      });
    }
    
    return res.json({
      isImpersonated: !!req.session.isImpersonated,
      originalAdminId: req.session.originalAdminId || null,
      authenticated: true
    });
  });
  
  // Nuovo endpoint per l'impersonificazione diretta con redirect
  app.get("/api/users/direct-impersonate/:userId", isAuthenticated, isAdmin, async (req: Request, res: Response) => {
    try {
      const targetUserId = req.params.userId;
      
      // Ottieni l'utente target
      const targetUser = await storage.getUser(targetUserId);
      
      if (!targetUser) {
        return res.status(404).send("Utente non trovato");
      }
      
      // Salva le informazioni dell'admin originale per ripristino
      const adminUser = req.user as User;
      
      // Verifica che l'admin non stia provando a impersonare se stesso
      if (adminUser.id === targetUser.id) {
        return res.redirect('/?error=self-impersonation');
      }
      
      // Salva l'ID dell'admin originale nella sessione
      req.session.originalAdminId = adminUser.id;
      req.session.isImpersonated = true;
      req.session.impersonatedUserId = targetUser.id;
      
      // Log dettagliato per il debug
      console.log(`Admin ${adminUser.username} (ID: ${adminUser.id}) sta impersonando l'utente ${targetUser.username} (ID: ${targetUser.id})`);
      
      // Registra l'attività di impersonificazione
      await logImpersonation(adminUser.id, adminUser.username, targetUser.id, targetUser.username, adminUser.tenantId);
      
      // Aggiorna l'utente nella sessione direttamente
      // Salviamo SOLO l'ID dell'utente per evitare problemi di serializzazione
      // @ts-ignore - ignoriamo l'errore TypeScript qui perché passport è una proprietà dinamica
      req.session.passport = { user: targetUser.id };
      
      // IMPORTANTE: Forza il refresh del tenant context salvando anche il tenantId del target
      // Questo assicura che quando l'utente viene deserializzato, appartenga al tenant corretto
      req.session.activeTenantId = targetUser.tenantId;
      
      // Salva la sessione in modo sincrono
      req.session.save((err) => {
        if (err) {
          console.error("Errore nel salvataggio della sessione:", err);
          return res.redirect('/?error=session-error');
        }
        
        console.log(`Impersonificazione completata: Admin ${adminUser.username} ora impersona ${targetUser.username} del tenant ${targetUser.tenantId}`);
        
        // Reindirizza alla home dopo aver impostato la sessione
        return res.redirect('/');
      });
    } catch (error) {
      console.error("Errore durante l'impersonificazione diretta:", error);
      return res.redirect('/?error=impersonation-error');
    }
  });

  // Termina impersonificazione con approccio GET e redirect
  app.get("/api/users/stop-impersonating", isAuthenticated, async (req: Request, res: Response) => {
    const redirectUrl = req.query.redirect as string || '/';
    
    try {
      // Verifica che ci sia un'impersonificazione attiva
      if (!req.session.isImpersonated || !req.session.originalAdminId) {
        return res.redirect(`${redirectUrl}?error=not-impersonating`);
      }
      
      // Ottieni l'utente admin originale
      const adminId = req.session.originalAdminId;
      const adminUser = await storage.getUser(adminId);
      
      if (!adminUser) {
        return res.redirect(`${redirectUrl}?error=admin-not-found`);
      }
      
      const currentUser = req.user as User;
      
      // Log dettagliato per debug
      console.log(`Terminando impersonificazione: ${currentUser.username} (ID: ${currentUser.id}). Tornando all'admin: ${adminUser.username} (ID: ${adminId})`);
      
      // Registra la fine dell'impersonificazione
      await storage.addActivityLog({
        userId: adminId,
        username: adminUser.username,
        action: "end_impersonate",
        details: `Terminata impersonificazione dell'utente ${currentUser.username}`,
        metadata: { targetUserId: currentUser.id, targetUsername: currentUser.username },
        tenantId: adminUser.tenantId
      });
      
      // Ripristina l'amministratore nella sessione direttamente, evitando logOut/login
      // Salviamo SOLO l'ID dell'admin originale per evitare problemi di serializzazione
      // @ts-ignore - ignoriamo l'errore TypeScript qui perché passport è una proprietà dinamica
      req.session.passport = { user: adminUser.id };
      req.session.isImpersonated = false;
      
      // Ripristina il tenant originale dell'admin
      req.session.activeTenantId = adminUser.tenantId;
      
      delete req.session.originalAdminId;
      delete req.session.impersonatedUserId;
      
      // Salva la sessione e poi reindirizza
      req.session.save((err) => {
        if (err) {
          console.error("Errore nel salvataggio della sessione:", err);
          return res.redirect(`${redirectUrl}?error=session-error`);
        }
        
        console.log(`Impersonificazione terminata con successo, reindirizzamento a ${redirectUrl}`);
        // Reindirizza all'URL specificato dopo aver impostato la sessione
        return res.redirect(redirectUrl);
      });
    } catch (error) {
      console.error("Errore durante la terminazione dell'impersonificazione:", error);
      return res.redirect(`${redirectUrl}?error=termination-error`);
    }
  });
  
  // Create new user (admin only)
  app.post("/api/users", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const admin = req.user as any;
      const userData = insertUserSchema.parse({ ...req.body, tenantId: admin.tenantId });

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(admin.tenantId, userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      const newUser = await storage.createUser({
        ...userData,
        password: hashedPassword,
        role: userData.isAdmin ? "admin" : "user", // Set role based on isAdmin flag
      });

      // Log activity
      await storage.addActivityLog({
        userId: admin.id,
        username: admin.username,
        action: "create_user",
        details: `Admin created new user: ${userData.username}`,
        tenantId: admin.tenantId
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = newUser;
      res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error("Error creating user:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid user data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Update user (admin only) - PATCH endpoint
  app.patch("/api/users/:id", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const userId = req.params.id;
      const user = req.user as any;
      
      // Validate UUID format
      if (!userId || typeof userId !== 'string') {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      // Check if user exists in the same tenant
      const existingUser = await storage.getUserWithTenant(user.tenantId, userId);
      if (!existingUser) {
        return res.status(404).json({ message: "User not found" });
      }

      // Prepare update data
      const updateData: any = {};

      // Handle username update
      if (req.body.username && req.body.username !== existingUser.username) {
        // Check if new username already exists in the same tenant
        const userWithSameUsername = await storage.getUserByUsername(user.tenantId, req.body.username);
        if (userWithSameUsername && userWithSameUsername.id !== userId) {
          return res.status(400).json({ message: "Username already exists" });
        }
        updateData.username = req.body.username;
      }

      // Handle password update
      if (req.body.password && req.body.password.trim() !== "") {
        updateData.password = await bcrypt.hash(req.body.password, 10);
      }

      // Handle email update - convert empty string to null
      if (req.body.hasOwnProperty('email')) {
        updateData.email = req.body.email === "" ? null : req.body.email;
      }

      // Handle role and admin status updates
      if (req.body.hasOwnProperty('isAdmin')) {
        updateData.isAdmin = req.body.isAdmin;
        // Sync role with isAdmin for consistency
        if (req.body.isAdmin) {
          updateData.role = "admin";
        } else if (!req.body.role) {
          updateData.role = "user";
        }
      }

      if (req.body.hasOwnProperty('role')) {
        updateData.role = req.body.role;
        // Sync isAdmin with role for consistency
        updateData.isAdmin = req.body.role === "admin";
      }

      // Only update if there are changes
      if (Object.keys(updateData).length === 0) {
        return res.json(existingUser);
      }

      // Update the user
      const updatedUser = await storage.updateUser(user.tenantId, userId, updateData);

      // Log activity
      await storage.addActivityLog({
        userId: user.id,
        username: user.username,
        action: "update_user",
        details: `Admin updated user: ${updatedUser.username}`,
        metadata: { updatedFields: Object.keys(updateData) },
        tenantId: user.tenantId
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error("Error updating user:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "The string did not match the expected pattern.", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // Mantenuto per compatibilità con le versioni precedenti
  app.post("/api/users/stop-impersonating", isAuthenticated, (req: Request, res: Response) => {
    const redirectUrl = '/users';
    return res.redirect(`/api/users/stop-impersonating?redirect=${redirectUrl}`);
  });
}