/**
 * CRITICAL SECURITY FIX 12: Secure DDT Processing Routes
 * 
 * Replaces vulnerable DDT processing with enterprise-grade security:
 * - File upload validation and malware scanning
 * - Comprehensive security logging
 * - Input sanitization and injection prevention
 */

import type { Express, Request, Response } from "express";
import { fileUploadValidator, createFileUploadMiddleware } from "../lib/secure-file-upload-validator";
import { comprehensiveSecurityLogger, SecurityEventType, SecuritySeverity } from "../lib/comprehensive-security-logger";
import { xssSanitizer } from "../lib/xss-sanitizer";
import { storage } from "../storage";
import { insertDDTSchema } from "@shared/schema";

// Authentication middleware with security logging
const requireAuthWithLogging = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    // Log authenticated access
    comprehensiveSecurityLogger.logSecurityEvent({
      eventType: SecurityEventType.LOGIN_SUCCESS,
      severity: SecuritySeverity.LOW,
      userId: (req.user as any)?.id,
      username: (req.user as any)?.username,
      tenantId: (req.user as any)?.tenantId,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details: { action: 'AUTHENTICATED_ACCESS' },
      timestamp: new Date()
    });
    next();
  } else {
    // Log unauthorized access attempt
    comprehensiveSecurityLogger.logSecurityEvent({
      eventType: SecurityEventType.UNAUTHORIZED_ACCESS,
      severity: SecuritySeverity.MEDIUM,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details: { reason: 'NOT_AUTHENTICATED' },
      timestamp: new Date()
    });
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerSecureDDTProcessingRoutes(app: Express) {
  
  /**
   * SECURE DDT OCR PROCESSING ENDPOINT
   * Replaces vulnerable /api/ocr/process-ddt with comprehensive security
   */
  app.post(
    "/api/ocr/process-ddt",
    requireAuthWithLogging,
    createFileUploadMiddleware('DDT_IMAGES'),
    async (req: Request, res: Response) => {
      const requestId = `req_${Date.now()}`;
      
      try {
        // Extract and validate image data
        const { imageData } = req.body;
        
        if (!imageData) {
          comprehensiveSecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_POLICY_VIOLATION,
            severity: SecuritySeverity.MEDIUM,
            userId: (req.user as any)?.id,
            username: (req.user as any)?.username,
            tenantId: (req.user as any)?.tenantId,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            endpoint: req.originalUrl,
            method: req.method,
            details: { 
              violation: 'MISSING_IMAGE_DATA',
              requestId 
            },
            timestamp: new Date(),
            requestId
          });
          
          return res.status(400).json({ 
            error: "Image data is required",
            requestId 
          });
        }
        
        // Validate file upload with comprehensive security checks
        const uploadContext = {
          userId: (req.user as any)?.id || 'unknown',
          tenantId: (req.user as any)?.tenantId || 'unknown',
          uploadType: 'DDT_IMAGES' as const,
          endpoint: req.originalUrl
        };
        
        const validationResult = fileUploadValidator.validateBase64Image(
          imageData,
          uploadContext,
          req
        );
        
        if (!validationResult.isValid) {
          return res.status(400).json({
            error: validationResult.error,
            securityThreats: validationResult.securityThreats,
            requestId
          });
        }
        
        // Log successful file validation
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.LOGIN_SUCCESS, // Using as success event
          severity: SecuritySeverity.LOW,
          userId: (req.user as any)?.id,
          username: (req.user as any)?.username,
          tenantId: (req.user as any)?.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            action: 'DDT_IMAGE_VALIDATED',
            fileInfo: validationResult.fileInfo,
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        // Process with AI (existing logic but with security logging)
        const user = req.user as any;
        const { aiManager } = await import('../lib/ai-manager');
        const aiConfig = await aiManager.getCurrentAIConfig(user.id);
        const aiProvider = aiConfig.provider;
        
        let result;
        
        if (aiProvider === 'gemini') {
          const gemini = await import('../lib/gemini');
          result = await gemini.processDDTWithGemini(imageData);
        } else {
          const claude = await import('../lib/claude');
          result = await claude.processDDT(imageData);
        }
        
        // Sanitize AI response to prevent XSS
        if (result.success && result.data) {
          result.data = xssSanitizer.sanitizeInput(JSON.stringify(result.data));
          result.data = JSON.parse(result.data);
        }
        
        // Log AI processing completion
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.LOGIN_SUCCESS,
          severity: SecuritySeverity.LOW,
          userId: user.id,
          username: user.username,
          tenantId: user.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            action: 'DDT_AI_PROCESSING_COMPLETED',
            aiProvider,
            success: result.success,
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        res.json({
          ...result,
          requestId,
          fileValidation: validationResult.fileInfo
        });
        
      } catch (error: any) {
        // Log processing error
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.ANOMALOUS_BEHAVIOR,
          severity: SecuritySeverity.HIGH,
          userId: (req.user as any)?.id,
          username: (req.user as any)?.username,
          tenantId: (req.user as any)?.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            error: error.message,
            stack: error.stack?.substring(0, 500),
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        console.error("DDT processing error:", error);
        res.status(500).json({ 
          error: "Internal server error during DDT processing",
          requestId 
        });
      }
    }
  );
  
  /**
   * SECURE DDT CREATION ENDPOINT
   * Enhanced DDT creation with input validation and security logging
   */
  app.post(
    "/api/ddt",
    requireAuthWithLogging,
    createFileUploadMiddleware('DDT_IMAGES'),
    async (req: Request, res: Response) => {
      const requestId = `req_${Date.now()}`;
      
      try {
        // Sanitize all text inputs to prevent XSS/injection
        const sanitizedBody = {
          ...req.body,
          companyName: xssSanitizer.sanitizeInput(req.body.companyName || ''),
          vatNumber: xssSanitizer.sanitizeInput(req.body.vatNumber || ''),
          address: xssSanitizer.sanitizeInput(req.body.address || ''),
          number: xssSanitizer.sanitizeInput(req.body.number || ''),
          date: xssSanitizer.sanitizeInput(req.body.date || ''),
          notes: req.body.notes ? xssSanitizer.sanitizeInput(req.body.notes) : undefined
        };
        
        // Validate DDT data structure
        const validatedData = insertDDTSchema.parse(sanitizedBody);
        
        // Validate image if present
        if (req.body.image) {
          const uploadContext = {
            userId: (req.user as any)?.id || 'unknown',
            tenantId: (req.user as any)?.tenantId || 'unknown',
            uploadType: 'DDT_IMAGES' as const,
            endpoint: req.originalUrl
          };
          
          const validationResult = fileUploadValidator.validateBase64Image(
            req.body.image,
            uploadContext,
            req
          );
          
          if (!validationResult.isValid) {
            return res.status(400).json({
              error: validationResult.error,
              securityThreats: validationResult.securityThreats,
              requestId
            });
          }
        }
        
        // Check for duplicate DDT
        const existingDDT = await storage.checkDDTDuplicate(
          validatedData.companyName,
          validatedData.number,
          validatedData.date
        );
        
        if (existingDDT) {
          comprehensiveSecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_POLICY_VIOLATION,
            severity: SecuritySeverity.LOW,
            userId: (req.user as any)?.id,
            username: (req.user as any)?.username,
            tenantId: (req.user as any)?.tenantId,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            endpoint: req.originalUrl,
            method: req.method,
            details: {
              violation: 'DUPLICATE_DDT_ATTEMPT',
              company: validatedData.companyName,
              number: validatedData.number,
              requestId
            },
            timestamp: new Date(),
            requestId
          });
          
          return res.status(409).json({ 
            message: "DDT with this company, number and date already exists",
            requestId
          });
        }
        
        // Generate QR code securely
        const { generateQRCode } = await import('../lib/qr-code');
        const qrCode = await generateQRCode(`DDT-${validatedData.number}-${validatedData.date}`);
        
        // Create DDT with security context
        const ddt = await storage.createDDT({
          ...validatedData,
          qrCode,
          createdBy: (req.user as any)?.id,
          tenantId: (req.user as any)?.tenantId || 'default',
          image: req.body.image || null
        });
        
        // Log successful DDT creation
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.CONFIGURATION_CHANGE,
          severity: SecuritySeverity.LOW,
          userId: (req.user as any)?.id,
          username: (req.user as any)?.username,
          tenantId: (req.user as any)?.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            action: 'DDT_CREATED',
            ddtId: ddt.id,
            company: ddt.companyName,
            number: ddt.number,
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        // Log activity for audit trail
        const { logActivity } = await import('../utils/activity-logger');
        await logActivity(
          (req.user as any).id,
          (req.user as any).username,
          "CREATE_DDT",
          `DDT creato: ${ddt.companyName} - #${ddt.number}`,
          { 
            ddtId: ddt.id,
            companyName: ddt.companyName,
            number: ddt.number,
            requestId
          }
        );
        
        res.json({
          ...ddt,
          requestId
        });
        
      } catch (error: any) {
        // Log creation error with security context
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.ANOMALOUS_BEHAVIOR,
          severity: SecuritySeverity.HIGH,
          userId: (req.user as any)?.id,
          username: (req.user as any)?.username,
          tenantId: (req.user as any)?.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            error: error.message,
            validationFailed: error.name === 'ZodError',
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        console.error("DDT creation error:", error);
        
        if (error.name === 'ZodError') {
          return res.status(400).json({ 
            error: "Invalid DDT data",
            details: error.errors,
            requestId
          });
        }
        
        res.status(500).json({ 
          error: "Internal server error during DDT creation",
          requestId
        });
      }
    }
  );
  
  /**
   * SECURE DIRECT LABEL PROCESSING
   * Enhanced direct label upload with security validation
   */
  app.post(
    "/api/direct-upload/process-label",
    requireAuthWithLogging,
    createFileUploadMiddleware('LABEL_IMAGES'),
    async (req: Request, res: Response) => {
      const requestId = `req_${Date.now()}`;
      
      try {
        const { imageData } = req.body;
        
        if (!imageData) {
          return res.status(400).json({ 
            error: "Image data is required",
            requestId 
          });
        }
        
        // Validate image upload
        const uploadContext = {
          userId: (req.user as any)?.id || 'unknown',
          tenantId: (req.user as any)?.tenantId || 'unknown',
          uploadType: 'LABEL_IMAGES' as const,
          endpoint: req.originalUrl
        };
        
        const validationResult = fileUploadValidator.validateBase64Image(
          imageData,
          uploadContext,
          req
        );
        
        if (!validationResult.isValid) {
          return res.status(400).json({
            error: validationResult.error,
            securityThreats: validationResult.securityThreats,
            requestId
          });
        }
        
        // Process with AI
        const user = req.user as any;
        const { aiManager } = await import('../lib/ai-manager');
        const aiConfig = await aiManager.getCurrentAIConfig(user.id);
        const aiProvider = aiConfig.provider;
        
        let result;
        
        if (aiProvider === 'gemini') {
          const gemini = await import('../lib/gemini');
          result = await gemini.processProductLabelWithGemini(imageData);
        } else {
          const claude = await import('../lib/claude');
          result = await claude.processProductLabel(imageData);
        }
        
        // Sanitize response
        if (result.success && result.data) {
          result.data = xssSanitizer.sanitizeInput(JSON.stringify(result.data));
          result.data = JSON.parse(result.data);
        }
        
        // Log processing
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.LOGIN_SUCCESS,
          severity: SecuritySeverity.LOW,
          userId: user.id,
          username: user.username,
          tenantId: user.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            action: 'LABEL_PROCESSING_COMPLETED',
            aiProvider,
            success: result.success,
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        res.json({
          ...result,
          requestId
        });
        
      } catch (error: any) {
        comprehensiveSecurityLogger.logSecurityEvent({
          eventType: SecurityEventType.ANOMALOUS_BEHAVIOR,
          severity: SecuritySeverity.HIGH,
          userId: (req.user as any)?.id,
          username: (req.user as any)?.username,
          tenantId: (req.user as any)?.tenantId,
          ip: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.originalUrl,
          method: req.method,
          details: {
            error: error.message,
            requestId
          },
          timestamp: new Date(),
          requestId
        });
        
        console.error("Label processing error:", error);
        res.status(500).json({ 
          error: "Internal server error during label processing",
          requestId
        });
      }
    }
  );
}

export default registerSecureDDTProcessingRoutes;