import { Express } from "express";
import { isAuthenticated, isAdmin } from "../middlewares/auth";
import { storage } from "../storage";
import { User } from "@shared/schema";
import { asyncHandler } from "../lib/async-error-handler";

/**
 * Rotte per la gestione delle impostazioni di sistema
 */
export function registerSystemRoutes(app: Express) {
  
  // Recupera le impostazioni di sistema
  app.get("/api/system/settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const settings = await storage.getSystemSettings() || {};
    res.json(settings);
  }));

  // IMPOSTAZIONI GLOBALI (solo admin)
  
  // Recupera le impostazioni globali
  app.get("/api/system/global-settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    // Recupera impostazioni PWA dalla tabella global_settings
    const pwaSettings = await storage.getGlobalSettings();
    
    // Recupera impostazioni AI dalla tabella ai_settings
    const { aiManager } = await import('../lib/ai-manager');
    const aiSettings = await aiManager.getAISettings();
    
    // Combina le impostazioni
    const combinedSettings = {
      id: pwaSettings?.id || 1,
      // Impostazioni AI
      defaultAiProvider: aiSettings.defaultProvider,
      defaultClaudeModel: aiSettings.claudeProvider.defaultModel,
      defaultGeminiModel: aiSettings.geminiProvider.defaultModel,
      defaultDdtPromptId: aiSettings.defaultPrompts.ddt || null,
      defaultLabelPromptId: aiSettings.defaultPrompts.label || null,
      defaultGeneralPromptId: aiSettings.defaultPrompts.general || null,
      // Impostazioni PWA
      pwaOfflineMode: pwaSettings?.pwaOfflineMode ?? true,
      pwaDataPersistence: pwaSettings?.pwaDataPersistence ?? true,
      pwaAutoSync: pwaSettings?.pwaAutoSync ?? true,
      pwaCacheManagement: pwaSettings?.pwaCacheManagement ?? true,
      pwaBackgroundSync: pwaSettings?.pwaBackgroundSync ?? false,
      pwaPushNotifications: pwaSettings?.pwaPushNotifications ?? false,
      updatedAt: pwaSettings?.updatedAt || new Date(),
      updatedBy: pwaSettings?.updatedBy || null
    };
    
    res.json(combinedSettings);
  }));

  // Aggiorna le impostazioni globali
  app.patch("/api/system/global-settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const user = req.user as User;
    const newSettings = req.body;
    
    // Separa le impostazioni AI da quelle PWA
    const aiKeys = ["defaultAiProvider", "defaultClaudeModel", "defaultGeminiModel", "defaultDdtPromptId", "defaultLabelPromptId", "defaultGeneralPromptId"] as const;
    const pwaKeys = ["pwaOfflineMode", "pwaDataPersistence", "pwaAutoSync", "pwaCacheManagement", "pwaBackgroundSync", "pwaPushNotifications"] as const;
    
    const aiSettings: Record<string, any> = {};
    const pwaSettings: Record<string, any> = {};
    
    // Filtra le impostazioni AI
    for (const key of aiKeys) {
      if (Object.prototype.hasOwnProperty.call(newSettings, key) && newSettings[key] !== undefined) {
        aiSettings[key] = newSettings[key];
      }
    }
    
    // Filtra le impostazioni PWA
    for (const key of pwaKeys) {
      if (Object.prototype.hasOwnProperty.call(newSettings, key) && newSettings[key] !== undefined) {
        pwaSettings[key] = newSettings[key];
      }
    }
    
    let updatedSettings: any = {};
    
    // Aggiorna le impostazioni AI se presenti
    if (Object.keys(aiSettings).length > 0) {
      const { aiManager } = await import('../lib/ai-manager');
      await aiManager.updateAISettings({
        defaultProvider: aiSettings.defaultAiProvider,
        claudeProvider: {
          models: [], // Mantieni i modelli esistenti
          defaultModel: aiSettings.defaultClaudeModel
        },
        geminiProvider: {
          models: [], // Mantieni i modelli esistenti
          defaultModel: aiSettings.defaultGeminiModel
        }
      }, Number(user.id));
      updatedSettings = { ...updatedSettings, ...aiSettings };
    }
    
    // Aggiorna le impostazioni PWA se presenti
    if (Object.keys(pwaSettings).length > 0) {
      const pwaResult = await storage.updateGlobalSettings(pwaSettings, Number(user.id));
      updatedSettings = { ...updatedSettings, ...pwaResult };
    }
    
    // Log dell'attività
    console.log(`Impostazioni globali aggiornate da ${user.username}:`, Object.keys({ ...aiSettings, ...pwaSettings }));
    
    res.json({
      success: true,
      settings: updatedSettings,
      message: "Impostazioni globali aggiornate e sincronizzate con tutti gli utenti"
    });
  }));
  
  // Aggiorna le impostazioni di sistema
  app.patch("/api/system/settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const user = req.user as User;
    const newSettings = req.body;
    
    // Filtra solo le chiavi valide per evitare dati non autorizzati
    const allowedKeys = ["claudeModel", "cacheExpiryMinutes", "debugMode", "maintenanceMode"];
    const filteredSettings: Record<string, any> = {};
    
    for (const key of allowedKeys) {
      if (newSettings[key] !== undefined) {
        filteredSettings[key] = newSettings[key];
      }
    }
    
    const updatedSettings = await storage.updateSystemSettings(filteredSettings, user.username);
    
    // Log dell'attività
    console.log(`Impostazioni aggiornate da ${user.username}:`, Object.keys(filteredSettings));
    
    res.json(updatedSettings);
  }));
}