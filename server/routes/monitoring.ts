/**
 * API Routes per Sistema di Monitoring HACCP Tracker
 * 
 * @description Endpoints per health checks, metriche e alerting:
 * - /health - Health check completo del sistema
 * - /metrics/business - Metriche business e analytics
 * - /metrics/performance - Metriche di performance
 * - /alerts - Gestione alerting
 * 
 * <AUTHOR> di Monitoring Automatizzato HACCP Tracker
 * @version 1.0.0 - API monitoring avanzato
 * @date 2025-07-26
 */

import { Router, Request, Response } from 'express';
import {
  getSystemHealth,
  collectBusinessMetrics,
  trackBusinessEvent,
  sendAlert,
  checkDatabaseHealth,
  checkExternalAPIs,
  checkStorageHealth,
  checkSystemPerformance,
  getActiveAlerts
} from '../lib/monitoring';
import { asyncHandler } from "../lib/async-error-handler";

const router = Router();

/**
 * GET /health - Health check completo del sistema
 * Utilizzato da load balancer e monitoring esterni
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  console.log('🏥 Health check richiesto da:', req.ip);
  
  const health = await getSystemHealth();
  
  // Status code basato su health generale
  const statusCode = health.status === 'healthy' ? 200 :
                    health.status === 'degraded' ? 503 : 503;
  
  res.status(statusCode).json({
    ...health,
    endpoint: '/health',
    requestId: req.headers['x-request-id'] || `req_${Date.now()}`
  });
}));

/**
 * GET /health/database - Health check specifico database
 */
router.get('/health/database', async (req: Request, res: Response) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    
    const statusCode = dbHealth.status === 'healthy' ? 200 :
                      dbHealth.status === 'degraded' ? 503 : 503;
    
    res.status(statusCode).json({
      component: 'database',
      ...dbHealth
    });
    
  } catch (error) {
    res.status(500).json({
      component: 'database',
      status: 'error',
      error: error.message,
      lastCheck: new Date()
    });
  }
});

/**
 * GET /health/apis - Health check APIs esterne
 */
router.get('/health/apis', async (req: Request, res: Response) => {
  try {
    const apiHealth = await checkExternalAPIs();
    
    const statusCode = apiHealth.status === 'healthy' ? 200 :
                      apiHealth.status === 'degraded' ? 503 : 503;
    
    res.status(statusCode).json({
      component: 'external_apis',
      ...apiHealth
    });
    
  } catch (error) {
    res.status(500).json({
      component: 'external_apis',
      status: 'error',
      error: error.message,
      lastCheck: new Date()
    });
  }
});

/**
 * GET /metrics/business - Metriche business e KPIs
 */
router.get('/metrics/business', async (req: Request, res: Response) => {
  try {
    console.log('📊 Metriche business richieste');
    
    const metrics = await collectBusinessMetrics();
    
    res.json({
      success: true,
      data: metrics,
      generatedAt: new Date(),
      cacheInfo: {
        cached: false, // TODO: implementare cache check
        expiresIn: '5 minutes'
      }
    });
    
  } catch (error) {
    console.error('❌ Errore raccolta metriche business:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to collect business metrics',
      message: error.message,
      timestamp: new Date()
    });
  }
});

/**
 * GET /metrics/performance - Metriche di performance sistema
 */
router.get('/metrics/performance', async (req: Request, res: Response) => {
  try {
    const [dbHealth, storageHealth, cpuHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkStorageHealth(),
      checkSystemPerformance()
    ]);
    
    const performanceMetrics = {
      timestamp: new Date(),
      database: {
        latency: dbHealth.latency,
        status: dbHealth.status,
        details: dbHealth.details
      },
      storage: {
        latency: storageHealth.latency,
        status: storageHealth.status,
        memoryUsage: storageHealth.details?.memoryUsage,
        diskUsage: storageHealth.details?.diskUsage
      },
      cpu: {
        latency: cpuHealth.latency,
        status: cpuHealth.status,
        uptime: cpuHealth.details?.uptime
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version
      }
    };
    
    res.json({
      success: true,
      data: performanceMetrics,
      summary: {
        overallHealth: [dbHealth, storageHealth, cpuHealth].every(h => h.status === 'healthy') ? 'healthy' : 'degraded',
        avgLatency: Math.round(((dbHealth.latency || 0) + (storageHealth.latency || 0) + (cpuHealth.latency || 0)) / 3),
        criticalIssues: [dbHealth, storageHealth, cpuHealth].filter(h => h.status === 'unhealthy').length
      }
    });
    
  } catch (error) {
    console.error('❌ Errore raccolta metriche performance:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to collect performance metrics',
      message: error.message
    });
  }
});

/**
 * POST /metrics/track - Traccia evento business
 */
router.post('/metrics/track', async (req: Request, res: Response) => {
  try {
    const { event, properties = {} } = req.body;
    
    if (!event) {
      return res.status(400).json({
        success: false,
        error: 'Event name is required'
      });
    }
    
    // Estrai user info dalla sessione
    const userId = (req.session as any)?.user?.id;
    const tenantId = (req.session as any)?.user?.tenantId;
    
    await trackBusinessEvent(event, properties, userId, tenantId);
    
    res.json({
      success: true,
      message: 'Event tracked successfully',
      event,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('❌ Errore tracking evento:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to track event',
      message: error.message
    });
  }
});

/**
 * POST /alerts/send - Invia alert manuale (solo admin)
 */
router.post('/alerts/send', async (req: Request, res: Response) => {
  try {
    // Verifica autenticazione admin
    const user = (req.session as any)?.user;
    if (!user?.isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    
    const { severity, message, context } = req.body;
    
    if (!severity || !message) {
      return res.status(400).json({
        success: false,
        error: 'Severity and message are required'
      });
    }
    
    await sendAlert({ severity, message, context });
    
    res.json({
      success: true,
      message: 'Alert sent successfully',
      alert: { severity, message, timestamp: new Date() }
    });
    
  } catch (error) {
    console.error('❌ Errore invio alert:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to send alert',
      message: error.message
    });
  }
});

/**
 * GET /status - Status rapido per monitoring esterno
 * Endpoint semplice per check automatici
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    
    const status = {
      status: dbHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.2.17'
    };
    
    const statusCode = status.status === 'ok' ? 200 : 503;
    res.status(statusCode).json(status);
    
  } catch (error) {
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'System check failed'
    });
  }
});

/**
 * GET /metrics/dashboard - Dati per dashboard monitoring
 * Endpoint ottimizzato per dashboard in tempo reale
 */
router.get('/metrics/dashboard', async (req: Request, res: Response) => {
  try {
    console.log('📊 Dashboard metrics richieste');
    
    const [systemHealth, businessMetrics, alertsData] = await Promise.all([
      getSystemHealth(),
      collectBusinessMetrics(),
      getActiveAlerts()
    ]);
    
    const dashboardData = {
      timestamp: new Date(),
      system: {
        status: systemHealth.status,
        uptime: systemHealth.uptime,
        performance: systemHealth.performance,
        checks: {
          database: systemHealth.checks.database.status,
          apis: systemHealth.checks.external_apis.status,
          storage: systemHealth.checks.storage.status,
          memory: systemHealth.checks.memory.status
        }
      },
      business: {
        activeUsers: businessMetrics.metrics.activeUsers,
        dailyProcessing: {
          ddts: businessMetrics.metrics.ddtProcessed,
          labels: businessMetrics.metrics.productLabelsCreated,
          containers: businessMetrics.metrics.containersManaged
        },
        trends: businessMetrics.trends,
        performanceScore: businessMetrics.metrics.performanceScore
      },
      alerts: {
        active: alertsData.active,
        lastHour: alertsData.lastHour
      }
    };
    
    res.json({
      success: true,
      data: dashboardData,
      refreshRate: 30, // secondi
      nextUpdate: new Date(Date.now() + 30000)
    });
    
  } catch (error) {
    console.error('❌ Errore dashboard metrics:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to load dashboard metrics',
      message: error.message
    });
  }
});

export default router;