import type { Express, Request, Response } from "express";
import { storage } from "../storage";
import { insertSupplierSchema } from "@shared/schema";
import { z } from "zod";
import { supplierValidation } from "../middlewares/security";
import { logActivity } from "../utils/activity-logger";
import { validateRouteId } from "../lib/safe-parsing";

// Middleware to ensure authentication
const requireAuth = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    next();
  } else {
    res.status(401).json({ message: "Unauthorized" });
  }
};

export function registerSupplierRoutes(app: Express) {
  // Get all suppliers
  app.get("/api/suppliers", requireAuth, async (req: Request, res: Response) => {
    try {
      const suppliers = await storage.getAllSuppliers();
      res.json(suppliers);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      res.status(500).json({ message: "Error fetching suppliers" });
    }
  });

  // Get supplier by ID
  app.get("/api/suppliers/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'supplier');

      const supplier = await storage.getSupplier(id);
      if (!supplier) {
        return res.status(404).json({ message: "Supplier not found" });
      }

      res.json(supplier);
    } catch (error) {
      console.error("Error fetching supplier:", error);
      res.status(500).json({ message: "Error fetching supplier" });
    }
  });

  // Create new supplier
  app.post("/api/suppliers", requireAuth, supplierValidation, async (req: Request, res: Response) => {
    try {
      const validatedData = insertSupplierSchema.parse(req.body);
      
      // Check for duplicate VAT number
      const existingSupplier = await storage.getSupplierByVatNumber(validatedData.vatNumber);
      if (existingSupplier) {
        return res.status(409).json({ message: "Supplier with this VAT number already exists" });
      }

      const supplier = await storage.createSupplier({
        ...validatedData,
        createdBy: (req.user as any)?.id
      });

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'CREATE_SUPPLIER',
        `Created supplier: ${supplier.companyName}`,
        { supplierId: supplier.id }
      );

      res.status(201).json(supplier);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating supplier:", error);
      res.status(500).json({ message: "Error creating supplier" });
    }
  });

  // Update supplier
  app.put("/api/suppliers/:id", requireAuth, supplierValidation, async (req: Request, res: Response) => {
    try {
      const id = validateRouteId(req.params.id, 'supplier');

      const validatedData = insertSupplierSchema.partial().parse(req.body);
      
      // Check if supplier exists
      const existingSupplier = await storage.getSupplier(id);
      if (!existingSupplier) {
        return res.status(404).json({ message: "Supplier not found" });
      }

      // Check for duplicate VAT number if it's being changed
      if (validatedData.vatNumber && validatedData.vatNumber !== existingSupplier.vatNumber) {
        const duplicateSupplier = await storage.getSupplierByVatNumber(validatedData.vatNumber);
        if (duplicateSupplier) {
          return res.status(409).json({ message: "Supplier with this VAT number already exists" });
        }
      }

      const updatedSupplier = await storage.updateSupplier(id, validatedData);

      // Log activity
      await logActivity(
        (req.user as any)?.id,
        (req.user as any)?.username || 'Unknown',
        'UPDATE_SUPPLIER',
        `Updated supplier: ${updatedSupplier.companyName}`,
        { supplierId: id }
      );

      res.json(updatedSupplier);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error updating supplier:", error);
      res.status(500).json({ message: "Error updating supplier" });
    }
  });

  // Search suppliers
  app.get("/api/suppliers/search/:query", requireAuth, async (req: Request, res: Response) => {
    try {
      const query = req.params.query.toLowerCase();
      const suppliers = await storage.getAllSuppliers();
      
      const filteredSuppliers = suppliers.filter(supplier =>
        supplier.companyName.toLowerCase().includes(query) ||
        supplier.vatNumber.toLowerCase().includes(query) ||
        supplier.address.toLowerCase().includes(query)
      );

      res.json(filteredSuppliers);
    } catch (error) {
      console.error("Error searching suppliers:", error);
      res.status(500).json({ message: "Error searching suppliers" });
    }
  });
}