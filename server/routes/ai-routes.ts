/**
 * Route unificate per la gestione del sistema AI
 * Consolidamento di tutte le operazioni AI in un unico punto
 */

import { Express } from "express";
import { isAuthenticated, isAdmin } from "../middlewares/auth";
import { aiManager } from "../lib/ai-manager";
import { asyncHand<PERSON> } from "../lib/async-error-handler";

export function registerAiRoutes(app: Express) {
  
  // API per ottenere le impostazioni AI globali (admin only)
  app.get("/api/ai/settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const settings = await aiManager.getAISettings();
    res.json(settings);
  }));

  // API per aggiornare le impostazioni AI globali (admin only)
  app.post("/api/ai/settings", isAuthenticated, isAdmin, asyncHandler(async (req, res) => {
    const user = req.user as any;
    const updates = req.body;
    
    const updatedSettings = await aiManager.updateAISettings(updates, user.id);
    res.json(updatedSettings);
  }));

  // API per ottenere la configurazione AI corrente per l'utente
  app.get("/api/ai/config", isAuthenticated, asyncHandler(async (req, res) => {
    const user = req.user as any;
    const config = await aiManager.getCurrentAIConfig(user.id);
    res.json(config);
  }));

  // API per ottenere tutti i modelli disponibili
  app.get("/api/ai/models", isAuthenticated, asyncHandler(async (req, res) => {
    const settings = await aiManager.getAISettings();
    res.json({
      claude: {
        models: settings.claudeProvider.models,
        defaultModel: settings.claudeProvider.defaultModel
      },
      gemini: {
        models: settings.geminiProvider.models,
        defaultModel: settings.geminiProvider.defaultModel
      },
      currentProvider: settings.defaultProvider
    });
  }));

  // API per validare un modello
  app.post("/api/ai/validate-model", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const { provider, modelId } = req.body;
      
      if (!provider || !modelId) {
        return res.status(400).json({ message: "Provider and modelId are required" });
      }
      
      const isValid = await aiManager.isValidModel(provider, modelId);
      res.json({ valid: isValid });
    } catch (error) {
      console.error("Errore nella validazione modello AI:", error);
      res.status(500).json({ message: "Failed to validate model" });
    }
  });

  // API per processare documenti con il provider selezionato
  app.post("/api/ai/process", isAuthenticated, async (req, res) => {
    try {
      const { imageBase64, category } = req.body;
      
      if (!imageBase64 || !category) {
        return res.status(400).json({ message: "ImageBase64 and category are required" });
      }

      const config = await aiManager.getCurrentAIConfig();
      let result;

      if (config.provider === 'claude') {
        const claude = await import('../lib/claude');
        if (category === 'ddt') {
          result = await claude.processDDT(imageBase64);
        } else if (category === 'label') {
          result = await claude.processProductLabel(imageBase64);
        } else {
          return res.status(400).json({ message: "Invalid category for Claude" });
        }
      } else if (config.provider === 'gemini') {
        const gemini = await import('../lib/gemini');
        const prompt = await aiManager.getPromptForCategory(category as 'ddt' | 'label' | 'general');
        
        if (category === 'ddt') {
          result = await gemini.processDDTWithGemini(imageBase64, prompt);
        } else if (category === 'label') {
          result = await gemini.processLabelWithGemini(imageBase64, prompt);
        } else {
          return res.status(400).json({ message: "Invalid category for Gemini" });
        }
      } else {
        return res.status(400).json({ message: "Invalid AI provider" });
      }

      res.json(result);
    } catch (error) {
      console.error("Errore nell'elaborazione AI:", error);
      res.status(500).json({ message: "Failed to process with AI" });
    }
  });

  // API per pulire la cache delle impostazioni AI
  app.post("/api/ai/clear-cache", isAuthenticated, isAdmin, async (req, res) => {
    try {
      aiManager.clearCache();
      res.json({ success: true, message: "Cache cleared successfully" });
    } catch (error) {
      console.error("Errore nella pulizia cache AI:", error);
      res.status(500).json({ message: "Failed to clear cache" });
    }
  });

  // API per ottenere i prompt per categoria
  app.get("/api/ai/prompt/:category", isAuthenticated, async (req, res) => {
    try {
      const { category } = req.params;
      
      if (!['ddt', 'label', 'general'].includes(category)) {
        return res.status(400).json({ message: "Invalid prompt category" });
      }
      
      const prompt = await aiManager.getPromptForCategory(category as 'ddt' | 'label' | 'general');
      res.json({ category, prompt });
    } catch (error) {
      console.error("Errore nel recupero prompt:", error);
      res.status(500).json({ message: "Failed to retrieve prompt" });
    }
  });
}