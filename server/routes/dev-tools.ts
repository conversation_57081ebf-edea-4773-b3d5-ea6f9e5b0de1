/**
 * Strumenti di sviluppo per migliorare l'esperienza di sviluppo su Replit
 */
import { Express } from "express";
import fs from "fs";
import path from "path";
import { log } from "../vite";

let lastUpdateTimestamp = Date.now();
const WATCHED_DIRECTORIES = [
  path.join(process.cwd(), "client/src"),
  path.join(process.cwd(), "server")
];

// Memorizza le informazioni sui file modificati
interface FileChangeInfo {
  path: string;
  mtime: number;
}

// Array delle ultime modifiche
let recentChanges: FileChangeInfo[] = [];

// Funzione per verificare se c'è stato un cambiamento nei file
const checkFileChanges = (forceUpdate: boolean = false): number => {
  let latestTimestamp = forceUpdate ? Date.now() : lastUpdateTimestamp;
  const newChanges: FileChangeInfo[] = [];
  
  // Controlla ricorsivamente tutti i file nei percorsi monitorati
  for (const dir of WATCHED_DIRECTORIES) {
    const files = walkSync(dir);
    
    for (const file of files) {
      try {
        const stats = fs.statSync(file);
        if (stats.isFile()) {
          // Se il file è stato modificato dopo l'ultimo controllo o stiamo forzando un aggiornamento
          if (forceUpdate || stats.mtime.getTime() > lastUpdateTimestamp) {
            latestTimestamp = Math.max(latestTimestamp, stats.mtime.getTime());
            
            // Aggiungi il file all'elenco delle modifiche recenti
            const relativePath = file.replace(process.cwd(), '').replace(/\\/g, '/');
            newChanges.push({
              path: relativePath,
              mtime: stats.mtime.getTime()
            });
          }
        }
      } catch (error) {
        // Ignora errori di file non trovati
      }
    }
  }
  
  // Aggiorna il timestamp solo se è cambiato o stiamo forzando un aggiornamento
  if (forceUpdate || latestTimestamp > lastUpdateTimestamp) {
    log(`Rilevate ${newChanges.length} modifiche ai file, nuovo timestamp: ${new Date(latestTimestamp).toLocaleTimeString()}`);
    lastUpdateTimestamp = latestTimestamp;
    
    // Aggiorna l'elenco delle modifiche recenti (mantieni solo le ultime 20)
    recentChanges = [...newChanges, ...recentChanges].slice(0, 20);
  }
  
  return latestTimestamp;
};

// Funzione per scansionare ricorsivamente le directory
const walkSync = (dir: string, fileList: string[] = []): string[] => {
  try {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      
      // Ignora i file nascosti
      if (file.startsWith('.')) return;
      
      try {
        const stats = fs.statSync(filePath);
        if (stats.isDirectory()) {
          fileList = walkSync(filePath, fileList);
        } else {
          fileList.push(filePath);
        }
      } catch (error) {
        // Ignora errori di file non trovati
      }
    });
  } catch (error) {
    // Ignora errori di directory non trovate
  }
  
  return fileList;
};

// Registra gli endpoint per gli strumenti di sviluppo
export function registerDevTools(app: Express): void {
  // Attiva gli strumenti solo in ambiente di sviluppo
  if (process.env.NODE_ENV === 'development') {
    log("Strumenti di sviluppo attivati", "dev-tools");
    
    // Inizializza il timestamp corrente
    lastUpdateTimestamp = Date.now();
    
    // Endpoint per verificare gli aggiornamenti dal client
    app.get('/api/dev/check-updates', (req, res) => {
      // Verifica se è richiesto un aggiornamento forzato
      const forceUpdate = req.query.force === 'true';
      const timestamp = checkFileChanges(forceUpdate);
      
      // Invia sia il timestamp che le informazioni sui file modificati
      res.json({ 
        timestamp,
        changes: recentChanges,
        serverTime: new Date().toISOString()
      });
    });
    
    // Endpoint per servire le informazioni dettagliate sulle modifiche recenti
    app.get('/api/dev/recent-changes', (req, res) => {
      // Se richiesto, forza un controllo per le modifiche prima di rispondere
      if (req.query.refresh === 'true') {
        checkFileChanges(true);
      }
      
      res.json({
        lastCheck: new Date(lastUpdateTimestamp).toISOString(),
        changes: recentChanges,
        totalChanges: recentChanges.length,
        serverTime: new Date().toISOString()
      });
    });
  }
}

export default registerDevTools;