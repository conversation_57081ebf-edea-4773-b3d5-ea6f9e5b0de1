import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

// Configura Neon per utilizzare WebSocket
neonConfig.webSocketConstructor = ws;

// Gestione migliorata degli errori di connessione
neonConfig.useSecureWebSocket = true;

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

// Configurazione migliorata del pool di connessioni per Replit
export const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  max: 5,                    // Limita il numero di connessioni per evitare sovraccarichi
  idleTimeoutMillis: 30000,  // Tempo di inattività prima che una connessione venga chiusa
  connectionTimeoutMillis: 5000, // Timeout per le nuove connessioni
  maxUses: 100               // Numero massimo di query per connessione prima di riciclarla
});

// Gestione degli eventi del pool per monitorare lo stato di connessione
pool.on('error', (err: Error) => {
  console.error('Errore imprevisto nel pool di connessione:', err.message);
});

pool.on('connect', () => {
  console.log('Nuova connessione al database stabilita');
});

// Inizializza Drizzle ORM con il pool di connessione e lo schema
export const db = drizzle({ client: pool, schema });
