import { 
  User, InsertUser, 
  Supplier, InsertSupplier, 
  DDT, InsertDDT, 
  ProductLabel, InsertProductLabel, 
  Container, InsertContainer, 
  ContainerType, InsertContainerType,
  ContainerProduct, InsertContainerProduct,
  ActivityLog, InsertActivityLog, ActivityLogFilters,
  UserSetting, InsertUserSetting,
  ClaudePrompt, InsertClaudePrompt,
  GlobalSetting, InsertGlobalSetting,
  AiSetting, InsertAiSetting,
  UserFeedback, InsertUserFeedback,
  Tenant, InsertTenant,
  SystemAdmin, InsertSystemAdmin,
  users, suppliers, ddts, productLabels, containers, containerTypes, containerProducts, activityLogs,
  systemSettings, SystemSetting, InsertSystemSetting, userSettings, claudePrompts, globalSettings, aiSettings, userFeedback,
  tenants, systemAdmins
} from "@shared/schema";
import { db } from "./db";
import { eq, and, gt, desc, sql } from "drizzle-orm";
import { getDefaultTenant } from "./tenant-migration";
import { withTransaction, withTransactionRetry, withTransactionMultiple } from "./lib/transaction-manager";
import { SecureLogger } from "./lib/secure-logger";
import { logger } from "./lib/logger";
import { randomUUID } from "crypto";
import { queryOptimizer } from "./lib/query-optimizer";

// Interfaccia per le impostazioni del sistema
export interface SystemSettings {
  claudeModel?: string;
  cacheExpiryMinutes?: number;
  debugMode?: boolean;
  maintenanceMode?: boolean;
  lastMaintenanceDate?: Date;
  [key: string]: any; // Consente estensioni future
}

export interface IStorage {
  // Multi-tenant management
  getTenant(id: string): Promise<Tenant | undefined>;
  getTenantByCode(code: string): Promise<Tenant | undefined>;
  getAllTenants(): Promise<Tenant[]>;
  createTenant(tenant: InsertTenant): Promise<Tenant>;
  updateTenant(id: string, data: Partial<Tenant>): Promise<Tenant>;
  deleteTenant(id: string): Promise<void>;
  
  // System admin management
  getSystemAdmin(id: string): Promise<SystemAdmin | undefined>;
  getSystemAdminByUsername(username: string): Promise<SystemAdmin | undefined>;
  getAllSystemAdmins(): Promise<SystemAdmin[]>;
  createSystemAdmin(admin: InsertSystemAdmin): Promise<SystemAdmin>;
  updateSystemAdmin(id: string, data: Partial<SystemAdmin>): Promise<SystemAdmin>;
  deleteSystemAdmin(id: string): Promise<void>;

  // System settings (per tenant)
  getSystemSettings(tenantId: string): Promise<SystemSettings | null>;
  updateSystemSettings(tenantId: string, settings: Partial<SystemSettings>): Promise<SystemSettings>;

  // User management - SECURED WITH UUID
  getUser(id: string): Promise<User | undefined>;
  getUserWithTenant(tenantId: string, id: string): Promise<User | undefined>;
  getUserByUsername(tenantId: string, username: string): Promise<User | undefined>;
  getAllUsers(tenantId: string): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(tenantId: string, id: string, data: Partial<User>): Promise<User>;
  deleteUser(tenantId: string, id: string): Promise<void>;
  
  // Legacy methods (DEPRECATED - use UUID versions above)
  getUserLegacy(id: string | number): Promise<User | undefined>;
  getUserByUsernameLegacy(username: string): Promise<User | undefined>;
  getAllUsersLegacy(): Promise<User[]>;
  updateUserLegacy(id: string, data: Partial<User>): Promise<User>;
  deleteUserLegacy(id: string): Promise<void>;

  // Supplier management
  getSupplier(id: string): Promise<Supplier | undefined>;
  getSupplierByVatNumber(vatNumber: string): Promise<Supplier | undefined>;
  getAllSuppliers(): Promise<Supplier[]>;
  createSupplier(supplier: InsertSupplier): Promise<Supplier>;
  updateSupplier(id: string, data: Partial<Supplier>): Promise<Supplier>;

  // DDT management
  getDDT(id: string): Promise<DDT | undefined>;
  getDDTByNumber(number: string): Promise<DDT | undefined>;
  checkDDTDuplicate(companyName: string, number: string, date: string): Promise<DDT | undefined>;
  getAllDDTs(): Promise<DDT[]>;
  createDDT(ddt: InsertDDT): Promise<DDT>;
  updateDDT(id: string, data: Partial<DDT>): Promise<DDT>;

  // Product Label management
  getProductLabel(id: string): Promise<ProductLabel | undefined>;
  getAllProductLabels(): Promise<ProductLabel[]>;
  getProductLabelsByDDT(ddtId: string): Promise<ProductLabel[]>;
  createProductLabel(productLabel: InsertProductLabel): Promise<ProductLabel>;
  updateProductLabel(id: string, data: Partial<ProductLabel>): Promise<ProductLabel>;

  // Product retirement management
  retireProduct(productId: string, reason: string, userId: string): Promise<ProductLabel>;
  getRetiredProducts(): Promise<ProductLabel[]>;
  getActiveProducts(): Promise<ProductLabel[]>;

  // Container management
  getContainer(id: string): Promise<Container | undefined>;
  getContainerByName(name: string): Promise<Container | undefined>;
  getAllContainers(): Promise<Container[]>;
  createContainer(container: InsertContainer): Promise<Container>;
  updateContainer(id: string, data: Partial<Container>): Promise<Container>;

  // Container Type management
  getContainerType(id: string): Promise<ContainerType | undefined>;
  getContainerTypeByValue(value: string): Promise<ContainerType | undefined>;
  getAllContainerTypes(): Promise<ContainerType[]>;
  createContainerType(containerType: InsertContainerType): Promise<ContainerType>;
  updateContainerType(id: string, data: Partial<ContainerType>): Promise<ContainerType>;

  // Container-Product associations
  getContainerProductAssociation(containerId: string, productLabelId: string): Promise<ContainerProduct | undefined>;
  getContainerProducts(containerId: string): Promise<ContainerProduct[]>;
  addProductToContainer(containerProduct: InsertContainerProduct): Promise<ContainerProduct>;
  removeProductFromContainer(id: string): Promise<void>;

  // Activity logging
  addActivityLog(log: InsertActivityLog): Promise<ActivityLog>;
  getActivityLogs(filters?: ActivityLogFilters, limit?: number): Promise<ActivityLog[]>;

  // User settings management
  getUserSettings(userId: string): Promise<UserSetting | undefined>;
  updateUserSettings(userId: string, settings: Partial<UserSetting>): Promise<UserSetting>;

  // Claude prompts management
  getAllClaudePrompts(): Promise<ClaudePrompt[]>;
  getClaudePromptsByCategory(category: string): Promise<ClaudePrompt[]>;
  getClaudePrompt(id: string): Promise<ClaudePrompt | undefined>;
  createClaudePrompt(prompt: InsertClaudePrompt): Promise<ClaudePrompt>;
  updateClaudePrompt(id: string, prompt: Partial<ClaudePrompt>): Promise<ClaudePrompt>;
  deleteClaudePrompt(id: string): Promise<void>;

  // Global settings management (admin only)
  getGlobalSettings(): Promise<GlobalSetting | undefined>;
  updateGlobalSettings(settings: Partial<GlobalSetting>, adminUserId: string): Promise<GlobalSetting>;
  syncGlobalSettingsToAllUsers(adminUserId: string): Promise<void>;

  // AI settings management (admin only)
  getAiSettings(): Promise<AiSetting | undefined>;
  updateAiSettings(settings: Partial<AiSetting>, adminUserId: string): Promise<AiSetting>;
  initializeDefaultAiSettings(): Promise<AiSetting>;

  // User feedback management
  createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback>;
  getAllUserFeedback(): Promise<UserFeedback[]>;
}

export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private suppliers: Map<string, Supplier>;
  private ddts: Map<string, DDT>;
  private productLabels: Map<string, ProductLabel>;
  private containers: Map<string, Container>;
  private containerTypes: Map<string, ContainerType>;
  private containerProducts: Map<string, ContainerProduct>;
  private activityLogs: Map<string, ActivityLog>;

  // UUID-based maps for all data
  private userSettings = new Map<string, UserSetting>();
  private claudePrompts = new Map<string, ClaudePrompt>();
  private globalSettings = new Map<string, GlobalSetting>();
  private aiSettings = new Map<string, AiSetting>();
  private userFeedback = new Map<string, UserFeedback>();
  private tenants = new Map<string, Tenant>();
  private systemAdmins = new Map<string, SystemAdmin>();
  private systemSettings = new Map<string, SystemSetting>();

  // System settings
  private systemSettingsObj: SystemSettings;

  // UUID generation helper
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  constructor() {
    this.users = new Map();
    this.suppliers = new Map();
    this.ddts = new Map();
    this.productLabels = new Map();
    this.containers = new Map();
    this.containerTypes = new Map();
    this.containerProducts = new Map();
    this.activityLogs = new Map();

    // Inizializza le impostazioni di sistema predefinite
    this.systemSettingsObj = {
      claudeModel: "claude-3-sonnet-20240229",
      cacheExpiryMinutes: 60,
      debugMode: false
    };
  }

  // Multi-tenant management - MemStorage implementation
  async getTenant(id: string): Promise<Tenant | undefined> {
    // MemStorage doesn't support real tenants - return mock
    return undefined;
  }

  async getTenantByCode(code: string): Promise<Tenant | undefined> {
    return undefined;
  }

  async getAllTenants(): Promise<Tenant[]> {
    return [];
  }

  async createTenant(tenant: InsertTenant): Promise<Tenant> {
    throw new Error("MemStorage doesn't support tenant management");
  }

  async updateTenant(id: string, data: Partial<Tenant>): Promise<Tenant> {
    throw new Error("MemStorage doesn't support tenant management");
  }

  async deleteTenant(id: string): Promise<void> {
    throw new Error("MemStorage doesn't support tenant management");
  }

  // System admin management - MemStorage implementation
  async getSystemAdmin(id: string): Promise<SystemAdmin | undefined> {
    return undefined;
  }

  async getSystemAdminByUsername(username: string): Promise<SystemAdmin | undefined> {
    return undefined;
  }

  async getAllSystemAdmins(): Promise<SystemAdmin[]> {
    return [];
  }

  async createSystemAdmin(admin: InsertSystemAdmin): Promise<SystemAdmin> {
    throw new Error("MemStorage doesn't support system admin management");
  }

  async updateSystemAdmin(id: string, data: Partial<SystemAdmin>): Promise<SystemAdmin> {
    throw new Error("MemStorage doesn't support system admin management");
  }

  async deleteSystemAdmin(id: string): Promise<void> {
    throw new Error("MemStorage doesn't support system admin management");
  }

  // System settings management (per tenant)
  async getSystemSettings(tenantId: string): Promise<SystemSettings | null> {
    return this.systemSettingsObj;
  }

  async updateSystemSettings(tenantId: string, newSettings: Partial<SystemSettings>): Promise<SystemSettings> {
    this.systemSettingsObj = { ...this.systemSettingsObj, ...newSettings };
    return this.systemSettingsObj;
  }

  // Tenant-secured methods
  // User management - SECURED WITH UUID (MemStorage implementation)
  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserWithTenant(tenantId: string, id: string): Promise<User | undefined> {
    const user = this.users.get(id);
    return user && user.tenantId === tenantId ? user : undefined;
  }

  async getUserByUsername(tenantId: string, username: string): Promise<User | undefined> {
    for (const user of this.users.values()) {
      if (user.username === username && user.tenantId === tenantId) {
        return user;
      }
    }
    return undefined;
  }

  async getAllUsers(tenantId: string): Promise<User[]> {
    return Array.from(this.users.values()).filter(user => user.tenantId === tenantId);
  }

  // Legacy methods (DEPRECATED)
  async getUserLegacy(id: string | number): Promise<User | undefined> {
    logger.info("getUserLegacy called", { userId: id, warning: "without tenant validation" });
    
    if (typeof id === 'string') {
      return this.users.get(id);
    }
    
    // Handle legacy integer ID by searching through users
    for (const user of this.users.values()) {
      if (user.id === String(id)) {
        return user;
      }
    }
    return undefined;
  }

  async getUserByUsernameLegacy(username: string): Promise<User | undefined> {
    logger.info("getUserByUsernameLegacy called", { username, warning: "without tenant validation" });
    for (const user of this.users.values()) {
      if (user.username === username) {
        return user;
      }
    }
    return undefined;
  }

  async getAllUsersLegacy(): Promise<User[]> {
    logger.info("getAllUsersLegacy called", { warning: "without tenant validation" });
    return Array.from(this.users.values());
  }

  async createUser(userData: InsertUser): Promise<User> {
    const id = this.generateUUID();
    const user: User = { 
      id,
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: userData.isActive ?? true,
      isAdmin: userData.isAdmin ?? false,
      lastLogin: null
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(tenantId: string, id: string, data: Partial<User>): Promise<User> {
    const user = await this.getUserWithTenant(tenantId, id);
    if (!user) {
      throw new Error(`User with ID ${id} not found in tenant ${tenantId}`);
    }
    const updatedUser: User = { ...user, ...data, updatedAt: new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUser(tenantId: string, id: string): Promise<void> {
    const user = await this.getUserWithTenant(tenantId, id);
    if (!user) {
      throw new Error(`User with ID ${id} not found in tenant ${tenantId}`);
    }
    this.users.delete(id);
  }

  // Legacy methods (DEPRECATED)  
  async updateUserLegacy(id: string, data: Partial<User>): Promise<User> {
    logger.info("updateUserLegacy called", { userId: id, warning: "without tenant validation" });
    const user = this.users.get(id);
    if (!user) {
      throw new Error(`User with ID ${id} not found`);
    }
    const updatedUser: User = { ...user, ...data, updatedAt: new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUserLegacy(id: string): Promise<void> {
    logger.info("deleteUserLegacy called", { userId: id, warning: "without tenant validation" });
    if (!this.users.has(id)) {
      throw new Error(`User with ID ${id} not found`);
    }
    this.users.delete(id);
  }

  async getSupplier(id: string): Promise<Supplier | undefined> {
    return this.suppliers.get(id);
  }

  async getSupplierByVatNumber(vatNumber: string): Promise<Supplier | undefined> {
    for (const supplier of this.suppliers.values()) {
      if (supplier.vatNumber === vatNumber) {
        return supplier;
      }
    }
    return undefined;
  }

  async getAllSuppliers(): Promise<Supplier[]> {
    return Array.from(this.suppliers.values());
  }

  async createSupplier(supplierData: InsertSupplier): Promise<Supplier> {
    const id = this.generateUUID();
    const supplier: Supplier = { 
      id,
      ...supplierData,
      createdAt: new Date()
    };
    this.suppliers.set(id, supplier);
    return supplier;
  }

  async updateSupplier(id: string, data: Partial<Supplier>): Promise<Supplier> {
    const supplier = this.suppliers.get(id);
    if (!supplier) {
      throw new Error(`Supplier with ID ${id} not found`);
    }
    const updatedSupplier: Supplier = { ...supplier, ...data };
    this.suppliers.set(id, updatedSupplier);
    return updatedSupplier;
  }

  async getDDT(id: string): Promise<DDT | undefined> {
    return this.ddts.get(id);
  }

  async getDDTByNumber(number: string): Promise<DDT | undefined> {
    for (const ddt of this.ddts.values()) {
      if (ddt.number === number) {
        return ddt;
      }
    }
    return undefined;
  }

  async checkDDTDuplicate(companyName: string, number: string, date: string): Promise<DDT | undefined> {
    for (const ddt of this.ddts.values()) {
      if (ddt.companyName === companyName && ddt.number === number && ddt.date === date) {
        return ddt;
      }
    }
    return undefined;
  }

  async getAllDDTs(): Promise<DDT[]> {
    return Array.from(this.ddts.values());
  }

  async createDDT(ddtData: InsertDDT): Promise<DDT> {
    const id = this.generateUUID();
    const ddt: DDT = { 
      id,
      ...ddtData,
      createdAt: new Date()
    };
    this.ddts.set(id, ddt);
    return ddt;
  }

  async updateDDT(id: string, data: Partial<DDT>): Promise<DDT> {
    const ddt = await this.getDDT(id);
    if (!ddt) {
      throw new Error(`DDT with ID ${id} not found`);
    }
    const updatedDDT: DDT = { ...ddt, ...data };
    this.ddts.set(id, updatedDDT);
    return updatedDDT;
  }

  async getProductLabel(id: string): Promise<ProductLabel | undefined> {
    return this.productLabels.get(id);
  }

  async getAllProductLabels(): Promise<ProductLabel[]> {
    return Array.from(this.productLabels.values());
  }

  async getProductLabelsByDDT(ddtId: string): Promise<ProductLabel[]> {
    return Array.from(this.productLabels.values())
      .filter(pl => pl.ddtId === ddtId);
  }

  async createProductLabel(productLabelData: InsertProductLabel): Promise<ProductLabel> {
    const id = this.generateUUID();
    const productLabel: ProductLabel = { 
      id,
      ...productLabelData,
      createdAt: new Date()
    };
    this.productLabels.set(id, productLabel);
    return productLabel;
  }

  async updateProductLabel(id: string, data: Partial<ProductLabel>): Promise<ProductLabel> {
    const productLabel = await this.getProductLabel(id);
    if (!productLabel) {
      throw new Error(`Product label with ID ${id} not found`);
    }
    const updatedProductLabel: ProductLabel = { 
      ...productLabel, 
      ...data 
    };
    this.productLabels.set(id, updatedProductLabel);
    return updatedProductLabel;
  }

  // Product retirement functions (MemStorage implementation)
  async retireProduct(productId: string, reason: string, userId: string): Promise<ProductLabel> {
    const productLabel = await this.getProductLabel(productId);
    if (!productLabel) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    const retiredProduct: ProductLabel = {
      ...productLabel,
      isRetired: true,
      retiredAt: new Date(),
      retiredBy: userId,
      retiredReason: reason
    };

    this.productLabels.set(productId, retiredProduct);
    return retiredProduct;
  }

  async getRetiredProducts(): Promise<ProductLabel[]> {
    return Array.from(this.productLabels.values())
      .filter(product => product.isRetired === true)
      .sort((a, b) => {
        if (!a.retiredAt || !b.retiredAt) return 0;
        return b.retiredAt.getTime() - a.retiredAt.getTime();
      });
  }

  async getActiveProducts(): Promise<ProductLabel[]> {
    return Array.from(this.productLabels.values())
      .filter(product => product.isRetired !== true);
  }

  async getContainer(id: string): Promise<Container | undefined> {
    return this.containers.get(id);
  }

  async getContainerByName(name: string): Promise<Container | undefined> {
    for (const container of this.containers.values()) {
      if (container.name === name) {
        return container;
      }
    }
    return undefined;
  }

  async getAllContainers(): Promise<Container[]> {
    return Array.from(this.containers.values());
  }

  async createContainer(containerData: InsertContainer): Promise<Container> {
    const id = this.generateUUID();
    const container: Container = { 
      id,
      ...containerData,
      createdAt: new Date(),
      currentItems: 0,
      isArchived: false
    };
    this.containers.set(id, container);
    return container;
  }

  async updateContainer(id: string, data: Partial<Container>): Promise<Container> {
    const container = await this.getContainer(id);
    if (!container) {
      throw new Error(`Container with ID ${id} not found`);
    }
    const updatedContainer: Container = { ...container, ...data };
    this.containers.set(id, updatedContainer);
    return updatedContainer;
  }

  async getContainerType(id: string): Promise<ContainerType | undefined> {
    return this.containerTypes.get(id);
  }

  async getContainerTypeByValue(value: string): Promise<ContainerType | undefined> {
    for (const containerType of this.containerTypes.values()) {
      if (containerType.value === value) {
        return containerType;
      }
    }
    return undefined;
  }

  async getAllContainerTypes(): Promise<ContainerType[]> {
    return Array.from(this.containerTypes.values());
  }

  async createContainerType(containerTypeData: InsertContainerType): Promise<ContainerType> {
    const id = this.generateUUID();
    const containerType: ContainerType = { 
      id,
      ...containerTypeData,
      createdAt: new Date()
    };
    this.containerTypes.set(id, containerType);
    return containerType;
  }

  async updateContainerType(id: string, data: Partial<ContainerType>): Promise<ContainerType> {
    const containerType = await this.getContainerType(id);
    if (!containerType) {
      throw new Error(`Container type with ID ${id} not found`);
    }
    const updatedContainerType: ContainerType = { ...containerType, ...data };
    this.containerTypes.set(id, updatedContainerType);
    return updatedContainerType;
  }

  async getContainerProductAssociation(
    containerId: string, 
    productLabelId: string
  ): Promise<ContainerProduct | undefined> {
    for (const cp of this.containerProducts.values()) {
      if (cp.containerId === containerId && cp.productLabelId === productLabelId) {
        return cp;
      }
    }
    return undefined;
  }

  async getContainerProducts(containerId: string): Promise<ContainerProduct[]> {
    return Array.from(this.containerProducts.values())
      .filter(cp => cp.containerId === containerId);
  }

  async addProductToContainer(containerProductData: InsertContainerProduct): Promise<ContainerProduct> {
    const id = this.generateUUID();
    const containerProduct: ContainerProduct = { 
      id,
      ...containerProductData,
      createdAt: new Date()
    };
    this.containerProducts.set(id, containerProduct);

    // Aggiorna il contatore di prodotti nel container
    const container = await this.getContainer(containerProductData.containerId);
    if (container) {
      const updatedContainer = {
        ...container,
        currentItems: container.currentItems + 1
      };
      this.containers.set(container.id, updatedContainer);
    }

    return containerProduct;
  }

  async removeProductFromContainer(id: string): Promise<void> {
    if (!this.containerProducts.has(id)) {
      throw new Error(`Container-product association with ID ${id} not found`);
    }

    // Ottieni l'associazione prima di eliminarla per aggiornare il contatore del container
    const association = this.containerProducts.get(id);
    if (association) {
      // Aggiorna il contatore di prodotti nel container
      const container = await this.getContainer(association.containerId);
      if (container && container.currentItems > 0) {
        const updatedContainer = {
          ...container,
          currentItems: container.currentItems - 1
        };
        this.containers.set(container.id, updatedContainer);
      }
    }

    this.containerProducts.delete(id);
  }

  async addActivityLog(logData: InsertActivityLog): Promise<ActivityLog> {
    const id = this.generateUUID();
    const log: ActivityLog = { 
      id,
      ...logData,
      timestamp: new Date(),
      containerId: logData.containerId ?? null,
      userId: logData.userId ?? null,
      productId: logData.productId ?? null
    };
    this.activityLogs.set(id, log);
    return log;
  }

  async getActivityLogs(filters?: ActivityLogFilters, limit?: number): Promise<ActivityLog[]> {
    let logs = Array.from(this.activityLogs.values());

    if (filters) {
      if (filters.startDate) {
        const startDate = new Date(filters.startDate);
        logs = logs.filter(log => log.timestamp >= startDate);
      }

      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        logs = logs.filter(log => log.timestamp <= endDate);
      }

      if (filters.userId) {
        logs = logs.filter(log => log.userId === filters.userId);
      }

      if (filters.containerId) {
        logs = logs.filter(log => log.containerId === filters.containerId);
      }

      if (filters.action) {
        logs = logs.filter(log => log.action === filters.action);
      }
    }

    // Sort by timestamp, most recent first
    logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    return limit ? logs.slice(0, limit) : logs;
  }

  // Missing interface methods - MemStorage implementations
  async getUserSettings(userId: string): Promise<UserSetting | undefined> {
    // MemStorage doesn't support user settings
    return undefined;
  }

  async updateUserSettings(userId: string, settings: Partial<UserSetting>): Promise<UserSetting> {
    throw new Error("MemStorage doesn't support user settings management");
  }

  async getAllClaudePrompts(): Promise<ClaudePrompt[]> {
    return [];
  }

  async getClaudePromptsByCategory(category: string): Promise<ClaudePrompt[]> {
    return [];
  }

  async getClaudePrompt(id: string): Promise<ClaudePrompt | undefined> {
    return undefined;
  }

  async createClaudePrompt(prompt: InsertClaudePrompt): Promise<ClaudePrompt> {
    throw new Error("MemStorage doesn't support Claude prompts management");
  }

  async updateClaudePrompt(id: string, prompt: Partial<ClaudePrompt>): Promise<ClaudePrompt> {
    throw new Error("MemStorage doesn't support Claude prompts management");
  }

  async deleteClaudePrompt(id: string): Promise<void> {
    throw new Error("MemStorage doesn't support Claude prompts management");
  }

  async getGlobalSettings(): Promise<GlobalSetting | undefined> {
    return undefined;
  }

  async updateGlobalSettings(settings: Partial<GlobalSetting>, adminUserId: string): Promise<GlobalSetting> {
    throw new Error("MemStorage doesn't support global settings management");
  }

  async syncGlobalSettingsToAllUsers(adminUserId: string): Promise<void> {
    throw new Error("MemStorage doesn't support global settings management");
  }

  async getAiSettings(): Promise<AiSetting | undefined> {
    return undefined;
  }

  async updateAiSettings(settings: Partial<AiSetting>, adminUserId: string): Promise<AiSetting> {
    throw new Error("MemStorage doesn't support AI settings management");
  }

  async initializeDefaultAiSettings(): Promise<AiSetting> {
    throw new Error("MemStorage doesn't support AI settings management");
  }

  async createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback> {
    throw new Error("MemStorage doesn't support user feedback management");
  }

  async getAllUserFeedback(): Promise<UserFeedback[]> {
    return [];
  }
}

export class DatabaseStorage implements IStorage {
  constructor() {
    // Nota: la creazione degli utenti predefiniti è gestita in server/routes.ts
  }

  // Multi-tenant management
  async getTenant(id: string): Promise<Tenant | undefined> {
    const [tenant] = await db.select().from(tenants).where(eq(tenants.id, id));
    return tenant || undefined;
  }

  async getTenantByCode(code: string): Promise<Tenant | undefined> {
    const [tenant] = await db.select().from(tenants).where(eq(tenants.code, code));
    return tenant || undefined;
  }

  async getAllTenants(): Promise<Tenant[]> {
    return await db.select().from(tenants);
  }

  async createTenant(tenantData: InsertTenant): Promise<Tenant> {
    const [tenant] = await db
      .insert(tenants)
      .values(tenantData)
      .returning();
    return tenant;
  }

  async updateTenant(id: string, data: Partial<Tenant>): Promise<Tenant> {
    const [updatedTenant] = await db
      .update(tenants)
      .set(data)
      .where(eq(tenants.id, id))
      .returning();

    if (!updatedTenant) {
      throw new Error(`Tenant with ID ${id} not found`);
    }

    return updatedTenant;
  }

  async deleteTenant(id: string): Promise<void> {
    await db.delete(tenants).where(eq(tenants.id, id));
  }

  // System admin management
  async getSystemAdmin(id: string): Promise<SystemAdmin | undefined> {
    const [admin] = await db.select().from(systemAdmins).where(eq(systemAdmins.id, id));
    return admin || undefined;
  }

  async getSystemAdminByUsername(username: string): Promise<SystemAdmin | undefined> {
    const [admin] = await db.select().from(systemAdmins).where(eq(systemAdmins.username, username));
    return admin || undefined;
  }

  async getAllSystemAdmins(): Promise<SystemAdmin[]> {
    return await db.select().from(systemAdmins);
  }

  async createSystemAdmin(adminData: InsertSystemAdmin): Promise<SystemAdmin> {
    const [admin] = await db
      .insert(systemAdmins)
      .values(adminData)
      .returning();
    return admin;
  }

  async updateSystemAdmin(id: string, data: Partial<SystemAdmin>): Promise<SystemAdmin> {
    const [updatedAdmin] = await db
      .update(systemAdmins)
      .set(data)
      .where(eq(systemAdmins.id, id))
      .returning();

    if (!updatedAdmin) {
      throw new Error(`System admin with ID ${id} not found`);
    }

    return updatedAdmin;
  }

  async deleteSystemAdmin(id: string): Promise<void> {
    await db.delete(systemAdmins).where(eq(systemAdmins.id, id));
  }

  // System settings management (per tenant)
  async getSystemSettings(tenantId: string): Promise<SystemSettings | null> {
    try {
      // Recupera tutte le impostazioni dalla tabella system_settings per questo tenant
      const settings = await db.select().from(systemSettings).where(eq(systemSettings.tenantId, tenantId));

      if (settings.length === 0) {
        return {
          claudeModel: "claude-3-sonnet-20240229", // Valore predefinito
          cacheExpiryMinutes: 60,
          debugMode: false
        };
      }

      // Converte le impostazioni in un oggetto
      const result: SystemSettings = {};

      for (const setting of settings) {
        // Estrae il valore dalla colonna jsonb
        const value = setting.value as unknown;
        if (value !== null && value !== undefined) {
          result[setting.key] = value;
        }
      }

      return result;
    } catch (error) {
      logger.error("Error retrieving system settings", { error });
      // Restituisci valori predefiniti in caso di errore
      return {
        claudeModel: "claude-3-sonnet-20240229",
        cacheExpiryMinutes: 60,
        debugMode: false
      };
    }
  }

  async updateSystemSettings(tenantId: string, newSettings: Partial<SystemSettings>): Promise<SystemSettings> {
    try {
      // Recupera le impostazioni attuali per questo tenant
      const currentSettings = await this.getSystemSettings(tenantId) || {};

      // Combina le impostazioni attuali con quelle nuove
      const updatedSettings = { ...currentSettings, ...newSettings };

      // Aggiorna o inserisci ogni impostazione nella tabella per questo tenant
      for (const [key, value] of Object.entries(newSettings)) {
        if (value !== undefined) {
          await db
            .insert(systemSettings)
            .values({
              key,
              value: value as any,
              tenantId: tenantId,
              updatedAt: new Date()
            })
            .onConflictDoUpdate({
              target: [systemSettings.key, systemSettings.tenantId],
              set: {
                value: value as any,
                updatedAt: new Date()
              }
            });
        }
      }

      return updatedSettings;
    } catch (error) {
      logger.error("Error updating system settings", { error });
      throw new Error(`Impossibile aggiornare le impostazioni di sistema: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // User management - TENANT-SECURED (UUID-compatible)
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserWithTenant(tenantId: string, id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(
      and(
        eq(users.id, id),
        eq(users.tenantId, tenantId)
      )
    );
    return user || undefined;
  }

  // LEGACY METHOD - DEPRECATED (Integer ID support)
  async getUserLegacyInt(tenantId: string, id: number): Promise<User | undefined> {
    logger.warn('SECURITY: Legacy integer ID method called', { userId: id });
    const [user] = await db.select().from(users).where(
      and(
        eq(users.id, id as any), // This will fail with UUID schema
        eq(users.tenantId, tenantId)
      )
    );
    return user || undefined;
  }

  async getUserByUsername(tenantId: string, username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(
      and(
        eq(users.username, username),
        eq(users.tenantId, tenantId)
      )
    );
    return user || undefined;
  }

  async getAllUsers(tenantId: string): Promise<User[]> {
    return await db.select().from(users).where(eq(users.tenantId, tenantId));
  }

  // LEGACY METHODS - DEPRECATED BUT MAINTAINED FOR BACKWARDS COMPATIBILITY
  // ⚠️ SECURITY WARNING: These methods bypass tenant validation
  async getUserLegacy(id: string | number): Promise<User | undefined> {
    logger.warn('SECURITY: getUserLegacy called without tenant validation', { userId: id });
    
    // Handle UUID format (new standard)
    if (typeof id === 'string') {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user || undefined;
    }
    
    // Handle legacy integer IDs - convert to UUID
    const legacyMapping: Record<number, string> = {
      2: 'c2f26b17-b4f9-4920-8b41-f30fbd332920', // admin
      1: 'ba4c58d7-29de-483e-ab8d-4a9fc4de4c46'   // user
    };
    
    const uuidId = legacyMapping[id];
    if (!uuidId) {
      logger.warn('Legacy ID mapping not found', { legacyId: id });
      return undefined;
    }
    
    const [user] = await db.select().from(users).where(eq(users.id, uuidId));
    return user || undefined;
  }

  async getUserByUsernameLegacy(username: string): Promise<User | undefined> {
    logger.warn('SECURITY: getUserByUsernameLegacy called without tenant validation', { username });
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getAllUsersLegacy(): Promise<User[]> {
    logger.warn('SECURITY: getAllUsersLegacy called without tenant validation');
    return await db.select().from(users);
  }

  async createUser(userData: InsertUser): Promise<User> {
    // Se non è specificato un tenantId, usa il tenant di default
    if (!userData.tenantId) {
      const defaultTenant = await getDefaultTenant();
      userData.tenantId = defaultTenant.id;
    }
    
    // Generate UUID explicitly to avoid null ID issues
    const userId = randomUUID();
    
    const [user] = await db
      .insert(users)
      .values({
        ...userData,
        id: userId
      })
      .returning();
    return user;
  }

  async updateUser(tenantId: string, id: string, data: Partial<User>): Promise<User> {
    // Se non ci sono dati da aggiornare, recupera e restituisci l'utente corrente
    if (Object.keys(data).length === 0) {
      const user = await this.getUserWithTenant(tenantId, id);
      if (!user) {
        throw new Error(`User with ID ${id} not found in tenant ${tenantId}`);
      }
      return user;
    }

    logger.debug('DatabaseStorage.updateUser called', { tenantId, userId: id, dataKeys: Object.keys(data) });

    // Gestiamo esplicitamente email null o stringa vuota
    if (data.hasOwnProperty('email')) {
      if (data.email === "" || data.email === null) {
        data.email = null;
      }
      logger.debug('Email field processed for database update', { emailValue: data.email });
    }

    const [updatedUser] = await db
      .update(users)
      .set(data)
      .where(
        and(
          eq(users.id, id),
          eq(users.tenantId, tenantId)
        )
      )
      .returning();

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found in tenant ${tenantId}`);
    }

    logger.info('User updated successfully', { tenantId, userId: id, username: updatedUser.username });
    return updatedUser;
  }

  async deleteUser(tenantId: string, id: string): Promise<void> {
    const result = await db.delete(users).where(
      and(
        eq(users.id, id),
        eq(users.tenantId, tenantId)
      )
    );
    logger.info('User deleted', { userId: id, tenantId });
  }

  // LEGACY METHODS - DEPRECATED
  async updateUserLegacy(id: number, data: Partial<User>): Promise<User> {
    console.warn(`⚠️ SECURITY: updateUserLegacy called without tenant validation for user ID ${id}`);
    
    if (Object.keys(data).length === 0) {
      const user = await this.getUserLegacy(id);
      if (!user) {
        throw new Error(`User with ID ${id} not found`);
      }
      return user;
    }

    if (data.hasOwnProperty('email')) {
      if (data.email === "" || data.email === null) {
        data.email = null;
      }
    }

    const [updatedUser] = await db
      .update(users)
      .set(data)
      .where(eq(users.id, id))
      .returning();

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found`);
    }

    return updatedUser;
  }

  async deleteUserLegacy(id: number): Promise<void> {
    console.warn(`⚠️ SECURITY: deleteUserLegacy called without tenant validation for user ID ${id}`);
    await db.delete(users).where(eq(users.id, id));
  }

  async getSupplier(id: number): Promise<Supplier | undefined> {
    const [supplier] = await db.select().from(suppliers).where(eq(suppliers.id, id));
    return supplier || undefined;
  }

  async getSupplierByVatNumber(vatNumber: string): Promise<Supplier | undefined> {
    const [supplier] = await db.select().from(suppliers).where(eq(suppliers.vatNumber, vatNumber));
    return supplier || undefined;
  }

  async getAllSuppliers(): Promise<Supplier[]> {
    return await db.select().from(suppliers);
  }

  // TRANSACTIONAL: Create supplier with duplicate VAT check
  async createSupplier(supplierData: InsertSupplier): Promise<Supplier> {
    return await withTransaction(async (tx) => {
      // 1. Check for duplicate VAT number within transaction
      const [existingSupplier] = await tx
        .select()
        .from(suppliers)
        .where(eq(suppliers.vatNumber, supplierData.vatNumber));

      if (existingSupplier) {
        throw new Error(`Supplier with VAT number ${supplierData.vatNumber} already exists`);
      }

      // 2. Create the supplier
      const [supplier] = await tx
        .insert(suppliers)
        .values(supplierData)
        .returning();

      // 3. Log supplier creation activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'CREATE_SUPPLIER',
        description: `Supplier created: ${supplier.companyName} (VAT: ${supplier.vatNumber})`,
        userId: supplierData.createdBy,
        supplierId: supplier.id,
        tenantId: supplierData.tenantId,
        timestamp: new Date()
      });

      return supplier;
    });
  }

  async updateSupplier(id: number, data: Partial<Supplier>): Promise<Supplier> {
    const [updatedSupplier] = await db
      .update(suppliers)
      .set(data)
      .where(eq(suppliers.id, id))
      .returning();

    if (!updatedSupplier) {
      throw new Error(`Supplier with ID ${id} not found`);
    }

    return updatedSupplier;
  }

  async getDDT(id: number): Promise<DDT | undefined> {
    const [ddt] = await db.select().from(ddts).where(eq(ddts.id, id));
    return ddt || undefined;
  }

  async getDDTByNumber(number: string): Promise<DDT | undefined> {
    const [ddt] = await db.select().from(ddts).where(eq(ddts.number, number));
    return ddt || undefined;
  }

  async checkDDTDuplicate(companyName: string, number: string, date: string): Promise<DDT | undefined> {
    const [existingDDT] = await db
      .select()
      .from(ddts)
      .where(
        and(
          eq(ddts.companyName, companyName),
          eq(ddts.number, number),
          eq(ddts.date, date)
        )
      );
    return existingDDT || undefined;
  }

  async getAllDDTs(): Promise<DDT[]> {
    return await db.select().from(ddts);
  }

  // NUOVO: DDTs con supplier details precaricati (elimina N+1)
  async getAllDDTsWithSupplierDetails(limit?: number): Promise<any[]> {
    return await queryOptimizer.getDDTsWithSupplierDetails(limit || 100);
  }

  // TRANSACTIONAL: Create DDT with duplicate check and activity logging
  async createDDT(ddtData: InsertDDT): Promise<DDT> {
    return await withTransaction(async (tx) => {
      // 1. Check for duplicates within transaction to prevent race conditions
      const [existingDDT] = await tx
        .select()
        .from(ddts)
        .where(
          and(
            eq(ddts.companyName, ddtData.companyName),
            eq(ddts.number, ddtData.number),
            eq(ddts.date, ddtData.date)
          )
        );

      if (existingDDT) {
        throw new Error(`DDT with company ${ddtData.companyName}, number ${ddtData.number}, and date ${ddtData.date} already exists`);
      }

      // 2. Create the DDT
      const [ddt] = await tx
        .insert(ddts)
        .values(ddtData)
        .returning();

      // 3. Update or create supplier if provided
      if (ddtData.supplierId) {
        // Verify supplier exists and update lastDDTDate
        const [updatedSupplier] = await tx
          .update(suppliers)
          .set({
            lastDDTDate: ddtData.date,
            updatedAt: new Date()
          })
          .where(eq(suppliers.id, ddtData.supplierId))
          .returning();

        if (!updatedSupplier) {
          throw new Error(`Supplier with ID ${ddtData.supplierId} not found`);
        }
      }

      // 4. Log DDT creation activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'CREATE_DDT',
        description: `DDT created: ${ddt.number} from ${ddt.companyName}`,
        userId: ddtData.createdBy,
        ddtId: ddt.id,
        tenantId: ddtData.tenantId,
        timestamp: new Date()
      });

      return ddt;
    });
  }

  async updateDDT(id: number, data: Partial<DDT>): Promise<DDT> {
    const [updatedDDT] = await db
      .update(ddts)
      .set(data)
      .where(eq(ddts.id, id))
      .returning();

    if (!updatedDDT) {
      throw new Error(`DDT with ID ${id} not found`);
    }

    return updatedDDT;
  }

  async getProductLabel(id: number): Promise<ProductLabel | undefined> {
    const [productLabel] = await db.select().from(productLabels).where(eq(productLabels.id, id));
    return productLabel || undefined;
  }

  async getAllProductLabels(): Promise<ProductLabel[]> {
    return await db.select().from(productLabels);
  }

  async getProductLabelsByDDT(ddtId: number): Promise<ProductLabel[]> {
    return await db.select().from(productLabels).where(eq(productLabels.ddtId, ddtId));
  }

  async getValidProductLabels(): Promise<ProductLabel[]> {
    // Nota: Nel DB l'expiryDate è un text, non una data,
    // quindi non possiamo usare direttamente gt() per la comparazione
    const now = new Date().toISOString().split('T')[0]; // formato YYYY-MM-DD

    // Filtriamo solo in base alla data di scadenza perché il campo consumed non esiste nel DB
    return await db
      .select()
      .from(productLabels)
      .where(
        sql`${productLabels.expiryDate} > ${now}`
      );
  }

  // TRANSACTIONAL: Create product label with DDT validation
  async createProductLabel(productLabelData: InsertProductLabel): Promise<ProductLabel> {
    return await withTransaction(async (tx) => {
      // 1. Verify DDT exists if provided
      if (productLabelData.ddtId) {
        const [ddt] = await tx
          .select()
          .from(ddts)
          .where(eq(ddts.id, productLabelData.ddtId));

        if (!ddt) {
          throw new Error(`DDT with ID ${productLabelData.ddtId} not found`);
        }
      }

      // 2. Create the product label
      const [productLabel] = await tx
        .insert(productLabels)
        .values(productLabelData)
        .returning();

      // 3. Log product creation activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'CREATE_PRODUCT',
        description: `Product created: ${productLabel.productName} (Lot: ${productLabel.lotNumber})`,
        userId: productLabelData.createdBy,
        productId: productLabel.id,
        ddtId: productLabelData.ddtId,
        tenantId: productLabelData.tenantId,
        timestamp: new Date()
      });

      return productLabel;
    });
  }

  async updateProductLabel(id: number, data: Partial<ProductLabel>): Promise<ProductLabel> {
    // Se i dati contengono 'consumed', lo rimuoviamo per evitare errori con il DB
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { consumed, ...validData } = data as any;

    const [updatedProductLabel] = await db
      .update(productLabels)
      .set(validData)
      .where(eq(productLabels.id, id))
      .returning();

    if (!updatedProductLabel) {
      throw new Error(`Product label with ID ${id} not found`);
    }

    return updatedProductLabel;
  }

  // TRANSACTIONAL: Product retirement with full data consistency
  async retireProduct(productId: string, reason: string, userId: string): Promise<ProductLabel> {
    return await withTransaction(async (tx) => {
      // 1. Get the product label first to check current status
      const [currentProduct] = await tx
        .select()
        .from(productLabels)
        .where(eq(productLabels.id, productId));

      if (!currentProduct) {
        throw new Error(`Product with ID ${productId} not found`);
      }

      if (currentProduct.isRetired) {
        throw new Error(`Product with ID ${productId} is already retired`);
      }

      // 2. Retire the product
      const [retiredProduct] = await tx
        .update(productLabels)
        .set({
          isRetired: true,
          retiredAt: new Date(),
          retiredBy: userId,
          retiredReason: reason
        })
        .where(eq(productLabels.id, productId))
        .returning();

      // 3. Remove product from all containers
      const containerProducts = await tx
        .select()
        .from(containerProducts)
        .where(eq(containerProducts.productLabelId, productId));

      for (const cp of containerProducts) {
        // Remove from container
        await tx
          .delete(containerProducts)
          .where(eq(containerProducts.id, cp.id));

        // Update container count
        await tx
          .update(containers)
          .set({
            currentItems: sql`GREATEST(0, ${containers.currentItems} - 1)`,
            updatedAt: new Date()
          })
          .where(eq(containers.id, cp.containerId));
      }

      // 4. Log retirement activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'RETIRE_PRODUCT',
        description: `Product retired: ${retiredProduct.productName} (Reason: ${reason})`,
        userId: userId,
        productId: productId,
        tenantId: retiredProduct.tenantId,
        timestamp: new Date()
      });

      return retiredProduct;
    });
  }

  async getRetiredProducts(): Promise<ProductLabel[]> {
    return await db
      .select()
      .from(productLabels)
      .where(eq(productLabels.isRetired, true))
      .orderBy(desc(productLabels.retiredAt));
  }

  async getActiveProducts(): Promise<ProductLabel[]> {
    return await db
      .select()
      .from(productLabels)
      .where(eq(productLabels.isRetired, false));
  }

  async getContainer(id: number): Promise<Container | undefined> {
    const [container] = await db.select().from(containers).where(eq(containers.id, id));
    return container || undefined;
  }

  async getContainerByName(name: string): Promise<Container | undefined> {
    const [container] = await db.select().from(containers).where(eq(containers.name, name));
    return container || undefined;
  }

  // OTTIMIZZATO: getAllContainers con products eager loading (elimina N+1)
  async getAllContainers(): Promise<Container[]> {
    return await db.select().from(containers);
  }

  // NUOVO: getAllContainers con products precaricati (elimina N+1)
  async getAllContainersWithProducts(): Promise<any[]> {
    return await queryOptimizer.getContainersWithProducts();
  }

  // TRANSACTIONAL: Create container with capacity validation
  async createContainer(containerData: InsertContainer): Promise<Container> {
    return await withTransaction(async (tx) => {
      // 1. Check for duplicate container name within transaction
      const [existingContainer] = await tx
        .select()
        .from(containers)
        .where(eq(containers.name, containerData.name));

      if (existingContainer) {
        throw new Error(`Container with name ${containerData.name} already exists`);
      }

      // 2. Verify container type exists if provided
      if (containerData.typeId) {
        const [containerType] = await tx
          .select()
          .from(containerTypes)
          .where(eq(containerTypes.id, containerData.typeId));

        if (!containerType) {
          throw new Error(`Container type with ID ${containerData.typeId} not found`);
        }
      }

      // 3. Create the container
      const [container] = await tx
        .insert(containers)
        .values({
          ...containerData,
          currentItems: 0,
          isArchived: false
        })
        .returning();

      // 4. Log container creation activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'CREATE_CONTAINER',
        description: `Container created: ${container.name} (Capacity: ${container.capacity})`,
        userId: containerData.createdBy,
        containerId: container.id,
        tenantId: containerData.tenantId,
        timestamp: new Date()
      });

      return container;
    });
  }

  async updateContainer(id: number, data: Partial<Container>): Promise<Container> {
    const [updatedContainer] = await db
      .update(containers)
      .set(data)
      .where(eq(containers.id, id))
      .returning();

    if (!updatedContainer) {
      throw new Error(`Container with ID ${id} not found`);
    }

    return updatedContainer;
  }

  async getContainerType(id: number): Promise<ContainerType | undefined> {
    const [containerType] = await db.select().from(containerTypes).where(eq(containerTypes.id, id));
    return containerType || undefined;
  }

  async getContainerTypeByValue(value: string): Promise<ContainerType | undefined> {
    const [containerType] = await db.select().from(containerTypes).where(eq(containerTypes.value, value));
    return containerType || undefined;
  }

  async getAllContainerTypes(): Promise<ContainerType[]> {
    return await db.select().from(containerTypes);
  }

  // TRANSACTIONAL: Create container type with duplicate value check
  async createContainerType(containerTypeData: InsertContainerType): Promise<ContainerType> {
    return await withTransaction(async (tx) => {
      // 1. Check for duplicate value within transaction
      const [existingType] = await tx
        .select()
        .from(containerTypes)
        .where(eq(containerTypes.value, containerTypeData.value));

      if (existingType) {
        throw new Error(`Container type with value ${containerTypeData.value} already exists`);
      }

      // 2. Create the container type
      const [containerType] = await tx
        .insert(containerTypes)
        .values(containerTypeData)
        .returning();

      // 3. Log container type creation activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'CREATE_CONTAINER_TYPE',
        description: `Container type created: ${containerType.name} (${containerType.value})`,
        userId: containerTypeData.createdBy,
        tenantId: containerTypeData.tenantId,
        timestamp: new Date()
      });

      return containerType;
    });
  }

  async updateContainerType(id: number, data: Partial<ContainerType>): Promise<ContainerType> {
    const [updatedContainerType] = await db
      .update(containerTypes)
      .set(data)
      .where(eq(containerTypes.id, id))
      .returning();

    if (!updatedContainerType) {
      throw new Error(`Container type with ID ${id} not found`);
    }

    return updatedContainerType;
  }

  async getContainerProductAssociation(
    containerId: number, 
    productLabelId: number
  ): Promise<ContainerProduct | undefined> {
    const [containerProduct] = await db
      .select()
      .from(containerProducts)
      .where(
        and(
          eq(containerProducts.containerId, containerId),
          eq(containerProducts.productLabelId, productLabelId)
        )
      );
    return containerProduct || undefined;
  }

  async getContainerProducts(containerId: number): Promise<ContainerProduct[]> {
    return await db
      .select()
      .from(containerProducts)
      .where(eq(containerProducts.containerId, containerId));
  }

  // NUOVO: Batch loading per evitare N+1 queries
  async getProductsByContainerIds(containerIds: string[]): Promise<Map<string, any[]>> {
    return await queryOptimizer.getProductsByContainerIds(containerIds);
  }

  // TRANSACTIONAL: Add product to container with atomic operations
  async addProductToContainer(containerProductData: InsertContainerProduct): Promise<ContainerProduct> {
    return await withTransaction(async (tx) => {
      // 1. Insert the container-product association
      const [containerProduct] = await tx
        .insert(containerProducts)
        .values(containerProductData)
        .returning();

      // 2. Update container's current items count atomically
      const [updatedContainer] = await tx
        .update(containers)
        .set({
          currentItems: sql`${containers.currentItems} + 1`,
          updatedAt: new Date()
        })
        .where(eq(containers.id, containerProductData.containerId))
        .returning();

      if (!updatedContainer) {
        throw new Error(`Container with ID ${containerProductData.containerId} not found`);
      }

      // 3. Log activity within the same transaction
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'ADD_PRODUCT_TO_CONTAINER',
        description: `Added product to container ${updatedContainer.name}`,
        userId: containerProductData.createdBy,
        containerId: containerProductData.containerId,
        productId: containerProductData.productLabelId,
        tenantId: containerProductData.tenantId,
        timestamp: new Date()
      });

      return containerProduct;
    });
  }

  // TRANSACTIONAL: Remove product from container with atomic operations
  async removeProductFromContainer(id: string, userId?: string): Promise<void> {
    return await withTransaction(async (tx) => {
      // 1. Get the container-product association first
      const [containerProduct] = await tx
        .select()
        .from(containerProducts)
        .where(eq(containerProducts.id, id));

      if (!containerProduct) {
        throw new Error(`Container-product association with ID ${id} not found`);
      }

      // 2. Delete the container-product association
      await tx
        .delete(containerProducts)
        .where(eq(containerProducts.id, id));

      // 3. Update container's current items count atomically
      const [updatedContainer] = await tx
        .update(containers)
        .set({
          currentItems: sql`GREATEST(0, ${containers.currentItems} - 1)`,
          updatedAt: new Date()
        })
        .where(eq(containers.id, containerProduct.containerId))
        .returning();

      if (!updatedContainer) {
        throw new Error(`Container with ID ${containerProduct.containerId} not found`);
      }

      // 4. Log activity within the same transaction
      if (userId) {
        await tx.insert(activityLogs).values({
          id: this.generateUUID(),
          action: 'REMOVE_PRODUCT_FROM_CONTAINER',
          details: `Removed product from container ${updatedContainer.name}`,
          username: 'system', // Add required username field
          userId: userId,
          containerId: containerProduct.containerId,
          productId: containerProduct.productLabelId,
          tenantId: containerProduct.tenantId,
          timestamp: new Date()
        });
      }
    });
  }

  async addActivityLog(logData: InsertActivityLog): Promise<ActivityLog> {
    const [log] = await db
      .insert(activityLogs)
      .values({
        ...logData,
        id: this.generateUUID()
      })
      .returning();
    return log;
  }

  async getActivityLogs(filters?: ActivityLogFilters, limit?: number): Promise<ActivityLog[]> {
    // Costruisci la query di base
    let query = db.select().from(activityLogs).orderBy(desc(activityLogs.timestamp));

    // Applica i filtri se presenti
    if (filters) {
      // Filtro per intervallo di date
      if (filters.startDate) {
        const startDate = new Date(filters.startDate);
        query = query.where(sql`${activityLogs.timestamp} >= ${startDate}`);
      }

      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        query = query.where(sql`${activityLogs.timestamp} <= ${endDate}`);
      }

      // Filtro per utente
      if (filters.userId) {
        query = query.where(eq(activityLogs.userId, filters.userId));
      }

      // Filtro per contenitore
      if (filters.containerId) {
        query = query.where(eq(activityLogs.containerId, filters.containerId));
      }

      // Filtro per tipo di azione
      if (filters.action) {
        query = query.where(eq(activityLogs.action, filters.action));
      }
    }

    // Applica il limite se specificato
    if (limit) {
      query = query.limit(limit);
    }

    return await query;
  }

  // NUOVO: Activity logs con user details precaricati (elimina N+1)
  async getActivityLogsWithUserDetails(limit?: number): Promise<any[]> {
    return await queryOptimizer.getActivityLogsWithUserDetails(limit || 50);
  }

  // NUOVO: Metodi per test delle ottimizzazioni
  async testQueryOptimizations(): Promise<{ [key: string]: number }> {
    const results: { [key: string]: number } = {};
    
    // Test containers con products
    const startContainers = performance.now();
    await this.getAllContainersWithProducts();
    results.containersWithProducts = performance.now() - startContainers;
    
    // Test activity logs con user details  
    const startLogs = performance.now();
    await this.getActivityLogsWithUserDetails(20);
    results.activityLogsWithUsers = performance.now() - startLogs;
    
    // Test DDTs con supplier details
    const startDDTs = performance.now();
    await this.getAllDDTsWithSupplierDetails(20);
    results.ddtsWithSuppliers = performance.now() - startDDTs;
    
    return results;
  }

  /**
   * Elimina i log di attività più vecchi di una certa data
   * @param cutoffDate Data limite: i log con timestamp precedente a questa data verranno eliminati
   * @returns Il numero di log eliminati
   */
  async deleteOldActivityLogs(cutoffDate: Date): Promise<number> {
    const result = await db
      .delete(activityLogs)
      .where(sql`${activityLogs.timestamp} < ${cutoffDate}`)
      .returning();

    return result.length;
  }

  // User settings management
  async getUserSettings(userId: number): Promise<UserSetting | undefined> {
    const [userSetting] = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, userId));
    return userSetting;
  }

  async updateUserSettings(userId: number, settings: Partial<UserSetting>): Promise<UserSetting> {
    // Prima verifica se esistono già impostazioni per questo utente
    const existingSettings = await this.getUserSettings(userId);

    if (existingSettings) {
      // Aggiorna le impostazioni esistenti
      const [updatedSettings] = await db
        .update(userSettings)
        .set({
          ...settings,
          updatedAt: new Date()
        })
        .where(eq(userSettings.userId, userId))
        .returning();
      return updatedSettings;
    } else {
      // Crea nuove impostazioni per l'utente
      const [newSettings] = await db
        .insert(userSettings)
        .values({
          userId,
          ...settings,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newSettings;
    }
  }

  // Claude prompts management
  async getAllClaudePrompts(): Promise<ClaudePrompt[]> {
    return await db.select().from(claudePrompts);
  }

  async getClaudePromptsByCategory(category: string): Promise<ClaudePrompt[]> {
    return await db.select().from(claudePrompts).where(eq(claudePrompts.category, category));
  }

  async getClaudePrompt(id: string): Promise<ClaudePrompt | undefined> {
    const result = await db.select().from(claudePrompts).where(eq(claudePrompts.id, id));
    return result[0];
  }

  async createClaudePrompt(prompt: InsertClaudePrompt): Promise<ClaudePrompt> {
    const [newPrompt] = await db.insert(claudePrompts).values({
      ...prompt,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return newPrompt;
  }

  async updateClaudePrompt(id: string, prompt: Partial<ClaudePrompt>): Promise<ClaudePrompt> {
    const [updatedPrompt] = await db.update(claudePrompts)
      .set({
        ...prompt,
        updatedAt: new Date()
      })
      .where(eq(claudePrompts.id, id))
      .returning();
    return updatedPrompt;
  }

  async deleteClaudePrompt(id: string): Promise<void> {
    await db.delete(claudePrompts).where(eq(claudePrompts.id, id));
  }

  // Global settings management (admin only)
  async getGlobalSettings(): Promise<GlobalSetting | undefined> {
    const result = await db.select().from(globalSettings).limit(1);
    return result[0];
  }

  async updateGlobalSettings(settings: Partial<GlobalSetting>, adminUserId: number): Promise<GlobalSetting> {
    // Verifica se esistono già impostazioni globali
    const existingSettings = await this.getGlobalSettings();

    if (existingSettings) {
      // Aggiorna le impostazioni esistenti
      const [updatedSettings] = await db
        .update(globalSettings)
        .set({
          ...settings,
          updatedAt: new Date(),
          updatedBy: adminUserId
        })
        .where(eq(globalSettings.id, existingSettings.id))
        .returning();

      // Sincronizza con tutti gli utenti dopo l'aggiornamento
      await this.syncGlobalSettingsToAllUsers(adminUserId);

      return updatedSettings;
    } else {
      // Crea nuove impostazioni globali
      const [newSettings] = await db
        .insert(globalSettings)
        .values({
          ...settings,
          updatedAt: new Date(),
          updatedBy: adminUserId
        })
        .returning();

      // Sincronizza con tutti gli utenti dopo la creazione
      await this.syncGlobalSettingsToAllUsers(adminUserId);

      return newSettings;
    }
  }

  async syncGlobalSettingsToAllUsers(adminUserId: number): Promise<void> {
    const globalConfig = await this.getGlobalSettings();
    if (!globalConfig) return;

    // Ottieni tutti gli utenti tranne l'admin che ha fatto la modifica
    const allUsers = await db.select().from(users).where(sql`${users.id} != ${adminUserId}`);

    // Aggiorna le impostazioni di ogni utente con quelle globali
    for (const user of allUsers) {
      const existingUserSettings = await this.getUserSettings(user.id);

      if (existingUserSettings) {
        // Aggiorna le impostazioni esistenti dell'utente
        await db
          .update(userSettings)
          .set({
            aiProvider: globalConfig.defaultAiProvider,
            claudeModel: globalConfig.defaultClaudeModel,
            geminiModel: globalConfig.defaultGeminiModel,
            updatedAt: new Date()
          })
          .where(eq(userSettings.userId, user.id));
      } else {
        // Crea nuove impostazioni per l'utente basate su quelle globali
        await db
          .insert(userSettings)
          .values({
            userId: user.id,
            aiProvider: globalConfig.defaultAiProvider,
            claudeModel: globalConfig.defaultClaudeModel,
            geminiModel: globalConfig.defaultGeminiModel,
            createdAt: new Date(),
            updatedAt: new Date()
          });
      }
    }
  }

  // User feedback management
  async createUserFeedback(feedback: InsertUserFeedback): Promise<UserFeedback> {
    const [newFeedback] = await db
      .insert(userFeedback)
      .values(feedback)
      .returning();
    return newFeedback;
  }

  async getAllUserFeedback(): Promise<UserFeedback[]> {
    const feedbacks = await db
      .select()
      .from(userFeedback)
      .orderBy(desc(userFeedback.createdAt));
    return feedbacks;
  }

  async deleteUserFeedback(feedbackId: number): Promise<void> {
    await db
      .delete(userFeedback)
      .where(eq(userFeedback.id, feedbackId));
  }

  // ==========================================
  // COMPLEX TRANSACTIONAL OPERATIONS
  // ==========================================

  /**
   * TRANSACTIONAL: Complete DDT processing workflow
   * Creates DDT, processes products, creates containers if needed
   */
  async processDDTWorkflow(
    ddtData: InsertDDT,
    productData: InsertProductLabel[],
    containerMappings?: { productId: string; containerId: string }[]
  ): Promise<{ ddt: DDT; products: ProductLabel[] }> {
    return await withTransactionRetry(async (tx) => {
      console.log('🔄 Starting complete DDT processing workflow...');

      // 1. Create or update supplier if provided
      let supplierId = ddtData.supplierId;
      if (!supplierId && ddtData.companyName && ddtData.address) {
        // Try to find existing supplier by company name
        const [existingSupplier] = await tx
          .select()
          .from(suppliers)
          .where(eq(suppliers.name, ddtData.companyName));

        if (existingSupplier) {
          supplierId = existingSupplier.id;
          // Update last DDT date
          await tx
            .update(suppliers)
            .set({
              lastDDTDate: ddtData.date,
              updatedAt: new Date()
            })
            .where(eq(suppliers.id, existingSupplier.id));
        }
      }

      // 2. Create DDT with duplicate check
      const [existingDDT] = await tx
        .select()
        .from(ddts)
        .where(
          and(
            eq(ddts.companyName, ddtData.companyName),
            eq(ddts.number, ddtData.number),
            eq(ddts.date, ddtData.date)
          )
        );

      if (existingDDT) {
        throw new Error(`DDT already exists: ${ddtData.companyName} - ${ddtData.number} - ${ddtData.date}`);
      }

      const [ddt] = await tx
        .insert(ddts)
        .values({ ...ddtData, supplierId })
        .returning();

      // 3. Process all products atomically
      const createdProducts: ProductLabel[] = [];
      for (const product of productData) {
        const [createdProduct] = await tx
          .insert(productLabels)
          .values({
            ...product,
            ddtId: ddt.id,
            tenantId: ddtData.tenantId
          })
          .returning();

        createdProducts.push(createdProduct);
      }

      // 4. Handle container assignments if provided
      if (containerMappings && containerMappings.length > 0) {
        for (const mapping of containerMappings) {
          const product = createdProducts.find(p => p.id === mapping.productId);
          if (product) {
            // Add product to container
            await tx.insert(containerProducts).values({
              containerId: mapping.containerId,
              productLabelId: mapping.productId,
              createdBy: ddtData.createdBy,
              tenantId: ddtData.tenantId
            });

            // Update container count
            await tx
              .update(containers)
              .set({
                currentItems: sql`${containers.currentItems} + 1`,
                updatedAt: new Date()
              })
              .where(eq(containers.id, mapping.containerId));
          }
        }
      }

      // 5. Log the complete workflow
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'COMPLETE_DDT_WORKFLOW',
        description: `DDT workflow completed: ${ddt.number} from ${ddt.companyName} with ${createdProducts.length} products`,
        userId: ddtData.createdBy,
        ddtId: ddt.id,
        tenantId: ddtData.tenantId,
        timestamp: new Date()
      });

      console.log('✅ DDT processing workflow completed successfully');
      return { ddt, products: createdProducts };
    });
  }

  /**
   * TRANSACTIONAL: Batch product retirement
   * Retires multiple products and removes them from containers
   */
  async batchRetireProducts(
    productIds: string[],
    reason: string,
    userId: string,
    tenantId: string
  ): Promise<ProductLabel[]> {
    return await withTransactionRetry(async (tx) => {
      console.log(`🔄 Starting batch retirement of ${productIds.length} products...`);

      const retiredProducts: ProductLabel[] = [];
      
      for (const productId of productIds) {
        // Get current product state
        const [currentProduct] = await tx
          .select()
          .from(productLabels)
          .where(eq(productLabels.id, productId));

        if (!currentProduct) {
          throw new Error(`Product with ID ${productId} not found`);
        }

        if (currentProduct.isRetired) {
          console.log(`⚠️ Product ${productId} already retired, skipping...`);
          continue;
        }

        // Retire the product
        const [retiredProduct] = await tx
          .update(productLabels)
          .set({
            isRetired: true,
            retiredAt: new Date(),
            retiredBy: userId,
            retiredReason: reason
          })
          .where(eq(productLabels.id, productId))
          .returning();

        retiredProducts.push(retiredProduct);

        // Remove from all containers
        const containerProducts = await tx
          .select()
          .from(containerProducts)
          .where(eq(containerProducts.productLabelId, productId));

        for (const cp of containerProducts) {
          await tx
            .delete(containerProducts)
            .where(eq(containerProducts.id, cp.id));

          await tx
            .update(containers)
            .set({
              currentItems: sql`GREATEST(0, ${containers.currentItems} - 1)`,
              updatedAt: new Date()
            })
            .where(eq(containers.id, cp.containerId));
        }
      }

      // Log batch retirement
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'BATCH_RETIRE_PRODUCTS',
        description: `Batch retired ${retiredProducts.length} products (Reason: ${reason})`,
        userId: userId,
        tenantId: tenantId,
        timestamp: new Date()
      });

      console.log(`✅ Batch retirement completed: ${retiredProducts.length} products retired`);
      return retiredProducts;
    });
  }

  /**
   * TRANSACTIONAL: Archive container with all products
   * Archives container and handles all contained products appropriately
   */
  async archiveContainerWithProducts(
    containerId: string,
    archiveReason: string,
    userId: string,
    tenantId: string
  ): Promise<{ container: Container; affectedProducts: number }> {
    return await withTransactionRetry(async (tx) => {
      console.log(`🔄 Starting container archival process for container ${containerId}...`);

      // 1. Get container details
      const [container] = await tx
        .select()
        .from(containers)
        .where(eq(containers.id, containerId));

      if (!container) {
        throw new Error(`Container with ID ${containerId} not found`);
      }

      if (container.isArchived) {
        throw new Error(`Container ${container.name} is already archived`);
      }

      // 2. Get all products in container
      const containerProducts = await tx
        .select()
        .from(containerProducts)
        .where(eq(containerProducts.containerId, containerId));

      // 3. Remove all products from container
      if (containerProducts.length > 0) {
        await tx
          .delete(containerProducts)
          .where(eq(containerProducts.containerId, containerId));
      }

      // 4. Archive the container
      const [archivedContainer] = await tx
        .update(containers)
        .set({
          isArchived: true,
          currentItems: 0,
          archivedAt: new Date(),
          archivedBy: userId,
          archiveReason: archiveReason,
          updatedAt: new Date()
        })
        .where(eq(containers.id, containerId))
        .returning();

      // 5. Log archival activity
      await tx.insert(activityLogs).values({
        id: this.generateUUID(),
        action: 'ARCHIVE_CONTAINER',
        description: `Container archived: ${container.name} with ${containerProducts.length} products (Reason: ${archiveReason})`,
        userId: userId,
        containerId: containerId,
        tenantId: tenantId,
        timestamp: new Date()
      });

      console.log(`✅ Container ${container.name} archived successfully with ${containerProducts.length} products affected`);
      return {
        container: archivedContainer,
        affectedProducts: containerProducts.length
      };
    });
  }
}

export interface GlobalSetting {
  id: number;
  defaultAiProvider?: 'claude' | 'gemini';
  defaultClaudeModel?: string;
  defaultGeminiModel?: string;
  updatedAt?: Date;
  updatedBy?: number;
}

export interface AISettings {
  defaultProvider: 'claude' | 'gemini';
  claudeModel: string;
  geminiModel: string;
}

// AI Settings helper functions
export async function getAISettings(): Promise<AISettings> {
  const settings = await storage.getGlobalSettings();
  return {
    defaultProvider: (settings?.defaultAiProvider as 'claude' | 'gemini') || 'gemini',
    claudeModel: settings?.defaultClaudeModel || 'claude-3-sonnet-20240229',
    geminiModel: settings?.defaultGeminiModel || 'gemini-1.5-pro-latest'
  };
}

export async function updateAISettings(aiSettings: Partial<AISettings>) {
  const updates: Partial<GlobalSetting> = {};
  if (aiSettings.defaultProvider) updates.defaultAiProvider = aiSettings.defaultProvider;
  if (aiSettings.claudeModel) updates.defaultClaudeModel = aiSettings.claudeModel;
  if (aiSettings.geminiModel) updates.defaultGeminiModel = aiSettings.geminiModel;

  // Assuming admin user id is 1, since the users are created in server/routes.ts
  return await storage.updateGlobalSettings(updates, 1);
}

export const storage = new DatabaseStorage();