/**
 * Script per verificare e standardizzare i QR code in tutto il database
 * 
 * Questo script:
 * 1. Verifica tutti i QR code di contenitori, DDT ed etichette prodotto
 * 2. Rigenera quelli non conformi allo standard "type:ID:NAME"
 * 3. Produce un report delle modifiche effettuate
 */

// Utilizziamo le variabili d'ambiente di Replit che sono già disponibili
import { pool, db } from '../db';
import { 
  containers, 
  ddts, 
  productLabels,
  type Container,
  type DDT,
  type ProductLabel
} from '../../shared/schema';
import { eq } from 'drizzle-orm';
import QRCode from 'qrcode';

// Funzione per generare QR code come data URL
async function generateQRCode(data: string): Promise<string> {
  try {
    return await QRCode.toDataURL(data, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });
  } catch (error) {
    console.error('Errore nella generazione del QR code:', error);
    throw error;
  }
}

// Funzione che verifica se un QR code è già standardizzato
function isStandardizedQRCode(qrCode: string | null, type: string): boolean {
  if (!qrCode || typeof qrCode !== 'string') return false;
  
  // Verifica se è già un data URL (inizia con "data:image/")
  if (qrCode.startsWith('data:image/')) {
    // Non possiamo verificare direttamente il contenuto di un data URL
    // ma possiamo considerare tutti i QR code esistenti come da rinnovare
    return false;
  }
  
  // Verifica il pattern standard "type:ID:NAME"
  const pattern = new RegExp(`^${type}:\\d+:.*$`);
  return pattern.test(qrCode);
}

// Funzione per standardizzare i QR code dei contenitori
async function standardizeContainerQRCodes(): Promise<{ total: number, updated: number }> {
  console.log('Standardizzazione QR code contenitori...');
  
  // Ottieni tutti i contenitori
  const allContainers = await db.select().from(containers);
  console.log(`Trovati ${allContainers.length} contenitori`);
  
  let updated = 0;
  
  for (const contenitore of allContainers) {
    // Verifica se il QR code è già standardizzato
    if (!isStandardizedQRCode(contenitore.qrCode, 'contenitore')) {
      // Genera un nuovo QR code standardizzato
      const standardQrValue = `contenitore:${contenitore.id}:${contenitore.name.replace(/\s+/g, '_')}`;
      const newQrCode = await generateQRCode(standardQrValue);
      
      // Aggiorna il contenitore con il nuovo QR code
      await db.update(containers)
        .set({ qrCode: newQrCode })
        .where(eq(containers.id, contenitore.id));
      
      updated++;
      console.log(`Aggiornato contenitore ${contenitore.id}: ${contenitore.name}`);
    }
  }
  
  console.log(`Aggiornati ${updated} contenitori su ${allContainers.length}`);
  return { total: allContainers.length, updated };
}

// Funzione per standardizzare i QR code dei DDT
async function standardizeDDTQRCodes(): Promise<{ total: number, updated: number }> {
  console.log('Standardizzazione QR code DDT...');
  
  // Ottieni tutti i DDT
  const allDDTs = await db.select().from(ddts);
  console.log(`Trovati ${allDDTs.length} DDT`);
  
  let updated = 0;
  
  for (const ddt of allDDTs) {
    // Verifica se il QR code è già standardizzato
    if (!isStandardizedQRCode(ddt.qrCode, 'ddt')) {
      // Genera un nuovo QR code standardizzato
      const standardQrValue = `ddt:${ddt.id}:${ddt.number.replace(/[^a-zA-Z0-9]/g, '_')}`;
      const newQrCode = await generateQRCode(standardQrValue);
      
      // Aggiorna il DDT con il nuovo QR code
      await db.update(ddts)
        .set({ qrCode: newQrCode })
        .where(eq(ddts.id, ddt.id));
      
      updated++;
      console.log(`Aggiornato DDT ${ddt.id}: ${ddt.number}`);
    }
  }
  
  console.log(`Aggiornati ${updated} DDT su ${allDDTs.length}`);
  return { total: allDDTs.length, updated };
}

// Funzione per standardizzare i QR code delle etichette prodotto
async function standardizeProductLabelQRCodes(): Promise<{ total: number, updated: number }> {
  console.log('Standardizzazione QR code etichette prodotto...');
  
  // Ottieni tutte le etichette prodotto
  const allProductLabels = await db.select().from(productLabels);
  console.log(`Trovate ${allProductLabels.length} etichette prodotto`);
  
  let updated = 0;
  
  for (const productLabel of allProductLabels) {
    // Verifica se il QR code è già standardizzato
    if (!isStandardizedQRCode(productLabel.qrCode, 'product')) {
      // Genera un nuovo QR code standardizzato
      const standardQrValue = `product:${productLabel.id}:${productLabel.productName.replace(/\s+/g, '_')}`;
      const newQrCode = await generateQRCode(standardQrValue);
      
      // Aggiorna l'etichetta prodotto con il nuovo QR code
      await db.update(productLabels)
        .set({ qrCode: newQrCode })
        .where(eq(productLabels.id, productLabel.id));
      
      updated++;
      console.log(`Aggiornata etichetta prodotto ${productLabel.id}: ${productLabel.productName}`);
    }
  }
  
  console.log(`Aggiornate ${updated} etichette prodotto su ${allProductLabels.length}`);
  return { total: allProductLabels.length, updated };
}

// Funzione principale
async function main() {
  console.log('Inizio standardizzazione QR code...');
  
  try {
    // Standardizza i QR code per ogni tipo di entità
    const contenitoreResults = await standardizeContainerQRCodes();
    const ddtResults = await standardizeDDTQRCodes();
    const productLabelResults = await standardizeProductLabelQRCodes();
    
    // Report finale
    console.log('\n--- REPORT FINALE ---');
    console.log(`Contenitori: ${contenitoreResults.updated}/${contenitoreResults.total} aggiornati`);
    console.log(`DDT: ${ddtResults.updated}/${ddtResults.total} aggiornati`);
    console.log(`Etichette prodotto: ${productLabelResults.updated}/${productLabelResults.total} aggiornate`);
    console.log(`Totale: ${contenitoreResults.updated + ddtResults.updated + productLabelResults.updated}/${contenitoreResults.total + ddtResults.total + productLabelResults.total} QR code aggiornati`);
    
    console.log('\nStandardizzazione QR code completata con successo!');
  } catch (error) {
    console.error('Errore durante la standardizzazione dei QR code:', error);
  } finally {
    // Chiudi la connessione al database
    await pool.end();
  }
}

// Esegui lo script
main();