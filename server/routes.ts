/**
 * REFACTORED ROUTE ENTRY POINT
 * 
 * This file replaces the previous monolithic 3,313-line routes.ts file
 * with a clean export that delegates to the modular route architecture.
 * 
 * The old monolithic file has been backed up as routes-MONOLITHIC-BACKUP.ts
 * for reference and emergency rollback purposes.
 * 
 * New Architecture Benefits:
 * - Single Responsibility Principle compliance
 * - Maintainable code with clear separation of concerns
 * - Easier testing and debugging
 * - Better code organization and readability
 * - Scalable architecture for future features
 */

export { registerRoutes } from "./routes/index";