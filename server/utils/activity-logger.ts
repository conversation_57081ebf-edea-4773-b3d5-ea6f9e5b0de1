import { storage } from "../storage";

export async function logActivity(
  userId: number | null,
  username: string,
  action: string,
  details: string,
  metadata?: any
) {
  try {
    // Get user to get tenantId
    const user = await storage.getUserLegacy(userId || 0);
    if (user) {
      await storage.addActivityLog({
        userId: userId,
        username: username,
        action: action,
        details: details,
        metadata: metadata || null,
        tenantId: user.tenantId,
        containerId: metadata?.containerId || null,
        productId: metadata?.productId || null
      });
    }
  } catch (error) {
    console.error("Error logging activity:", error);
    // Don't throw error to avoid breaking the main operation
  }
}