// Utility per gestire la migrazione al sistema multi-tenant
import { db } from "./db";
import { tenants } from "@shared/schema";
import { eq } from "drizzle-orm";

export const DEFAULT_TENANT_CODE = "DEFAULT";

export async function getDefaultTenant() {
  const [tenant] = await db
    .select()
    .from(tenants)
    .where(eq(tenants.code, DEFAULT_TENANT_CODE));
  
  if (!tenant) {
    throw new Error("Default tenant not found. Please ensure migration is completed.");
  }
  
  return tenant;
}

export async function ensureDefaultTenant() {
  try {
    return await getDefaultTenant();
  } catch {
    // Se il tenant di default non esiste, lo creo
    const [newTenant] = await db
      .insert(tenants)
      .values({
        name: "Tenant di Default",
        code: DEFAULT_TENANT_CODE,
        type: "restaurant",
        status: "active",
      })
      .returning();
    
    return newTenant;
  }
}