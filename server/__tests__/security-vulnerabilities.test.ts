/**
 * Critical Security Vulnerabilities Test Suite
 * Tests all four identified critical security issues to ensure they are fixed
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { ProductionSecurityEnforcer } from '../lib/production-security-enforcer';

// Mock environment for testing
const originalEnv = process.env;

beforeEach(() => {
  jest.resetModules();
  process.env = { ...originalEnv };
});

afterAll(() => {
  process.env = originalEnv;
});

describe('CRITICAL SECURITY VULNERABILITIES - ALL FIXED', () => {
  
  describe('1. Mock JWT Token Bypass - FIXED', () => {
    test('Mock tokens should be blocked in production', () => {
      process.env.NODE_ENV = 'production';
      const security = new (ProductionSecurityEnforcer as any)();
      
      const mockTokens = [
        'mock-token-123',
        'test-api-key',
        'dev-secret-key',
        'fake-auth-token',
        'demo-jwt-token'
      ];
      
      mockTokens.forEach(token => {
        const result = security.validateAuthToken(token);
        expect(result.isValid).toBe(false);
        expect(result.isMock).toBe(true);
        expect(result.error).toContain('Mock tokens are disabled');
      });
    });

    test('Mock tokens should be allowed in development only with explicit flag', () => {
      process.env.NODE_ENV = 'development';
      process.env.ALLOW_MOCK_TOKENS = 'true';
      const security = new (ProductionSecurityEnforcer as any)();
      
      const result = security.validateAuthToken('mock-token-123');
      expect(result.isValid).toBe(true);
      expect(result.isMock).toBe(true);
    });

    test('Real tokens should pass validation', () => {
      const security = new (ProductionSecurityEnforcer as any)();
      const realToken = 'sk-abcdef123456789012345678901234567890abcdef';
      
      const result = security.validateAuthToken(realToken);
      expect(result.isValid).toBe(true);
      expect(result.isMock).toBe(false);
    });
  });

  describe('2. Hardcoded Admin Credentials - FIXED', () => {
    test('Production should require ADMIN_DEFAULT_PASSWORD environment variable', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.ADMIN_DEFAULT_PASSWORD;
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).toThrow('ADMIN_DEFAULT_PASSWORD environment variable is required');
    });

    test('Production should require USER_DEFAULT_PASSWORD environment variable', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.USER_DEFAULT_PASSWORD;
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).toThrow('USER_DEFAULT_PASSWORD environment variable is required');
    });

    test('Weak passwords should be rejected in production', () => {
      process.env.NODE_ENV = 'production';
      process.env.ADMIN_DEFAULT_PASSWORD = 'weak';
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).toThrow('must be at least 12 characters with mixed case, numbers, and symbols');
    });

    test('Strong passwords should be accepted', () => {
      process.env.NODE_ENV = 'production';
      process.env.ADMIN_DEFAULT_PASSWORD = 'StrongPassword123!';
      process.env.USER_DEFAULT_PASSWORD = 'AnotherStrong456@';
      process.env.SESSION_SECRET = 'ultra-secure-session-secret-32-chars-min';
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).not.toThrow();
    });

    test('Development should use secure fallbacks when credentials not set', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.ADMIN_DEFAULT_PASSWORD;
      delete process.env.USER_DEFAULT_PASSWORD;
      
      const security = new (ProductionSecurityEnforcer as any)();
      const adminPassword = security.getSecurePassword('admin');
      const userPassword = security.getSecurePassword('user');
      
      expect(adminPassword).toMatch(/admin_[a-f0-9]+_CHANGE_ME/);
      expect(userPassword).toMatch(/user_[a-f0-9]+_CHANGE_ME/);
      expect(adminPassword.length).toBeGreaterThan(20);
      expect(userPassword.length).toBeGreaterThan(20);
    });
  });

  describe('3. Production Secrets in Plain Text - FIXED', () => {
    test('Production should require SESSION_SECRET environment variable', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.SESSION_SECRET;
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).toThrow('SESSION_SECRET environment variable is required');
    });

    test('SESSION_SECRET should be at least 32 characters in production', () => {
      process.env.NODE_ENV = 'production';
      process.env.SESSION_SECRET = 'short';
      
      expect(() => {
        new (ProductionSecurityEnforcer as any)();
      }).toThrow('must be at least 32 characters long');
    });

    test('Development should generate secure fallback when SESSION_SECRET not set', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.SESSION_SECRET;
      
      const security = new (ProductionSecurityEnforcer as any)();
      const secret = security.getSecureSessionSecret();
      
      expect(secret).toMatch(/haccp-dev-[a-f0-9]{64}/);
      expect(secret.length).toBeGreaterThan(64);
    });

    test('Environment-based secrets should be properly validated', () => {
      process.env.NODE_ENV = 'production';
      process.env.SESSION_SECRET = 'production-grade-session-secret-ultra-secure-32-plus-chars';
      process.env.ADMIN_DEFAULT_PASSWORD = 'ProductionAdmin123!';
      process.env.USER_DEFAULT_PASSWORD = 'ProductionUser456@';
      
      const security = new (ProductionSecurityEnforcer as any)();
      const secret = security.getSecureSessionSecret();
      const adminPwd = security.getSecurePassword('admin');
      const userPwd = security.getSecurePassword('user');
      
      expect(secret).toBe('production-grade-session-secret-ultra-secure-32-plus-chars');
      expect(adminPwd).toBe('ProductionAdmin123!');
      expect(userPwd).toBe('ProductionUser456@');
    });
  });

  describe('4. Predictable JWT Secrets in Development - FIXED', () => {
    test('Development fallback secrets should be cryptographically secure', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.SESSION_SECRET;
      
      const security1 = new (ProductionSecurityEnforcer as any)();
      const security2 = new (ProductionSecurityEnforcer as any)();
      
      const secret1 = security1.getSecureSessionSecret();
      const secret2 = security2.getSecureSessionSecret();
      
      // Should be different each time (not predictable)
      expect(secret1).not.toBe(secret2);
      
      // Should contain crypto.randomBytes output
      expect(secret1).toMatch(/haccp-dev-[a-f0-9]{64}/);
      expect(secret2).toMatch(/haccp-dev-[a-f0-9]{64}/);
      
      // Should be cryptographically secure length
      expect(security1.getSecureSessionSecret().length).toBeGreaterThan(64);
    });

    test('Development password fallbacks should be unique and secure', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.ADMIN_DEFAULT_PASSWORD;
      delete process.env.USER_DEFAULT_PASSWORD;
      
      const security1 = new (ProductionSecurityEnforcer as any)();
      const security2 = new (ProductionSecurityEnforcer as any)();
      
      const admin1 = security1.getSecurePassword('admin');
      const admin2 = security2.getSecurePassword('admin');
      const user1 = security1.getSecurePassword('user');
      const user2 = security2.getSecurePassword('user');
      
      // Should be different each time (not predictable)
      expect(admin1).not.toBe(admin2);
      expect(user1).not.toBe(user2);
      
      // Should contain crypto.randomBytes output
      expect(admin1).toMatch(/admin_[a-f0-9]+_CHANGE_ME/);
      expect(user1).toMatch(/user_[a-f0-9]+_CHANGE_ME/);
    });
  });

  describe('5. Additional Security Enhancements', () => {
    test('Secure cookie configuration should be production-ready', () => {
      process.env.NODE_ENV = 'production';
      process.env.SESSION_SECRET = 'production-grade-session-secret-ultra-secure-32-plus-chars';
      process.env.ADMIN_DEFAULT_PASSWORD = 'ProductionAdmin123!';
      process.env.USER_DEFAULT_PASSWORD = 'ProductionUser456@';
      process.env.COOKIE_DOMAIN = 'secure-domain.com';
      
      const security = new (ProductionSecurityEnforcer as any)();
      const cookieConfig = security.getSecureCookieConfig();
      
      expect(cookieConfig.secure).toBe(true);
      expect(cookieConfig.sameSite).toBe('strict');
      expect(cookieConfig.httpOnly).toBe(true);
      expect(cookieConfig.maxAge).toBe(2 * 60 * 60 * 1000); // 2 hours
      expect(cookieConfig.domain).toBe('secure-domain.com');
    });

    test('Database security validation should check SSL in production', () => {
      process.env.NODE_ENV = 'production';
      process.env.DATABASE_URL = '********************************/db';
      process.env.SESSION_SECRET = 'production-grade-session-secret-ultra-secure-32-plus-chars';
      process.env.ADMIN_DEFAULT_PASSWORD = 'ProductionAdmin123!';
      process.env.USER_DEFAULT_PASSWORD = 'ProductionUser456@';
      
      const security = new (ProductionSecurityEnforcer as any)();
      
      // Should warn about missing SSL but not throw
      expect(() => {
        security.validateDatabaseSecurity();
      }).not.toThrow();
    });
  });

  describe('6. Security Audit Logging', () => {
    test('Security events should be properly logged', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      process.env.NODE_ENV = 'production';
      process.env.SESSION_SECRET = 'production-grade-session-secret-ultra-secure-32-plus-chars';
      process.env.ADMIN_DEFAULT_PASSWORD = 'ProductionAdmin123!';
      process.env.USER_DEFAULT_PASSWORD = 'ProductionUser456@';
      
      const security = new (ProductionSecurityEnforcer as any)();
      
      // Test authentication audit
      security.auditAuthenticationAttempt('testuser', true, {
        userId: '123',
        userAgent: 'Test Browser',
        ip: '127.0.0.1'
      });
      
      security.auditAuthenticationAttempt('baduser', false, {
        reason: 'Invalid credentials',
        userAgent: 'Test Browser',
        ip: '127.0.0.1'
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY-AUDIT]')
      );
      
      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });
  });
});

describe('SECURITY MIDDLEWARE TESTS', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
  });

  test('Security headers should be properly set', async () => {
    const { securityHeaders } = await import('../middlewares/production-security');
    
    app.use(securityHeaders);
    app.get('/test', (req, res) => res.json({ success: true }));
    
    const response = await request(app).get('/test');
    
    expect(response.headers['x-content-type-options']).toBe('nosniff');
    expect(response.headers['x-frame-options']).toBe('DENY');
    expect(response.headers['x-xss-protection']).toBe('1; mode=block');
    expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
    expect(response.headers['content-security-policy']).toContain("default-src 'self'");
  });

  test('Rate limiting should work for authentication endpoints', async () => {
    const { authRateLimit } = await import('../middlewares/production-security');
    
    app.use(authRateLimit);
    app.post('/login', (req, res) => res.json({ success: true }));
    
    // Make multiple requests quickly
    const promises = Array(6).fill(0).map(() => request(app).post('/login'));
    const responses = await Promise.all(promises);
    
    // Should have at least one rate-limited response
    const rateLimited = responses.some(res => res.status === 429);
    expect(rateLimited).toBe(true);
  });
});