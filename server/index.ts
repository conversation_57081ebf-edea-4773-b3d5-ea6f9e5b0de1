import express, { type Request, Response, NextFunction } from "express";
import { createServer } from "http";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { 
  securityHeaders, 
  generalLimiter, 
  authLimiter, 
  uploadLimiter,
  validateApiRequest 
} from "./middlewares/security";
import { productionSecurity } from "./lib/production-security-enforcer";
import { 
  enhancedRateLimiting
} from "./middlewares/medium-risk-security";
import { comprehensiveSecurityLogger } from "./lib/comprehensive-security-logger";
import registerSecureDDTProcessingRoutes from "./routes/secure-ddt-processing";
import cors from "cors";
import rateLimit from "express-rate-limit";

const app = express();

// Trust proxy for Replit environment  
app.set('trust proxy', 1);

// CRITICAL SECURITY FIX 1: Secure CORS Configuration
const secureCORSConfig = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // In development, allow all origins for Vite/HMR compatibility
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    // Define allowed origins for production
    const allowedOrigins = [
      process.env.PRODUCTION_DOMAIN,
      'https://replit.app',
      /^https:\/\/.*\.replit\.app$/,
      /^https:\/\/.*\.replit\.dev$/
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, Postman)
    if (!origin) return callback(null, true);

    // Check if origin is allowed
    const isAllowed = allowedOrigins.some(allowedOrigin => {
      if (typeof allowedOrigin === 'string') {
        return allowedOrigin === origin;
      } else if (allowedOrigin instanceof RegExp) {
        return allowedOrigin.test(origin);
      }
      return false;
    });

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn(`🚨 SECURITY: Blocked CORS request from unauthorized origin: ${origin}`);
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true, // Allow cookies/auth headers
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-CSRF-Token'
  ],
  exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining'],
  maxAge: 86400 // 24 hours
};

app.use(cors(secureCORSConfig));

// Basic security headers (CSP is handled by Helmet middleware below)
app.use((req: Request, res: Response, next: NextFunction) => {
  // Only set basic headers, let Helmet handle CSP
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
});

// CRITICAL SECURITY FIX 3: Distributed Rate Limiting with IPv6 Support
const distributedRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000, // Strict in production
  message: { 
    error: "Rate limit exceeded. Too many requests from this IP.",
    retryAfter: "15 minutes"
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Enhanced skip logic
  skip: (req: Request) => {
    // Skip for authenticated admin users in development
    if (process.env.NODE_ENV === 'development' && req.user && (req.user as any).isAdmin) {
      return true;
    }
    return false;
  },
  // Custom handler for rate limit exceeded
  handler: (req: Request, res: Response) => {
    console.warn(`🚨 SECURITY: Rate limit exceeded for IP ${req.ip}, User-Agent: ${req.get('User-Agent')?.substring(0, 100)}`);
    res.status(429).json({
      error: "Rate limit exceeded",
      message: "Too many requests from this IP address",
      retryAfter: 900 // 15 minutes in seconds
    });
  }
});

app.use(distributedRateLimiter);

// Apply existing security headers (keeping backward compatibility)
app.use(securityHeaders);

// Apply enhanced medium-risk security (individual components)

// CRITICAL SECURITY FIX 4: Enhanced Route-Specific Rate Limiting
const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Very strict for auth endpoints
  message: { 
    error: "Too many authentication attempts",
    lockoutTime: "15 minutes",
    security: "Account temporarily locked for security"
  },
  handler: (req: Request, res: Response) => {
    console.warn(`🚨 SECURITY: Auth rate limit exceeded for IP ${req.ip}, Username: ${req.body?.username}`);
    res.status(429).json({
      error: "Authentication rate limit exceeded",
      message: "Account temporarily locked due to too many failed attempts",
      retryAfter: 900,
      lockoutTime: "15 minutes"
    });
  }
});

const uploadRateLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes  
  max: 10, // Limit file uploads
  message: { error: "Upload rate limit exceeded. Wait 10 minutes." },
  handler: (req: Request, res: Response) => {
    console.warn(`🚨 SECURITY: Upload rate limit exceeded for IP ${req.ip}`);
    res.status(429).json({
      error: "Upload rate limit exceeded",
      message: "Too many file uploads from this IP",
      retryAfter: 600
    });
  }
});

const adminRateLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Strict for admin operations
  message: { error: "Admin operation rate limit exceeded" },
  handler: (req: Request, res: Response) => {
    console.warn(`🚨 SECURITY: Admin rate limit exceeded for IP ${req.ip}, User: ${req.user ? (req.user as any).username : 'unauthenticated'}`);
    res.status(429).json({
      error: "Admin operation rate limit exceeded",
      message: "Too many administrative operations",
      retryAfter: 300
    });
  }
});

// Apply route-specific rate limiting
app.use('/api/auth/login', authRateLimiter);
app.use('/api/auth/register', authRateLimiter);
app.use('/api/upload', uploadRateLimiter);
app.use('/api/direct-upload', uploadRateLimiter);
app.use('/api/admin', adminRateLimiter);
app.use('/api/users/impersonate', adminRateLimiter);

// Apply simple rate limiting to avoid middleware conflicts
// The enhanced security features are already implemented above

// Apply input validation to all API routes
app.use('/api', validateApiRequest);

// Secure JSON and URL parsing with size limits (reduced from 50MB to 10MB for security)
app.use(express.json({ 
  limit: '10mb',
  strict: true,
  type: 'application/json'
}));
app.use(express.urlencoded({ 
  extended: false, 
  limit: '10mb',
  parameterLimit: 1000
}));

// Middleware per gestire le richieste API e garantire risposte JSON
app.use('/api', (req, res, next) => {
  // Imposta l'header Content-Type a application/json per la maggior parte delle route API
  // ma non per quelle che necessitano di fare redirect (come l'impersonificazione degli utenti)
  if (!req.path.includes('/users/direct-impersonate') && 
      !req.path.includes('/users/stop-impersonating')) {
    res.setHeader('Content-Type', 'application/json');
    
    // Sovrascrive il metodo redirect per ritornare JSON invece di reindirizzare
    // solo per le route che non sono specifiche per l'impersonificazione
    const originalRedirect = res.redirect;
    res.redirect = function(url) {
      return res.status(401).json({ 
        redirectUrl: url,
        message: "Authentication required",
        authenticated: false
      });
    };
  }
  
  next();
});

// Admin dashboard routes are handled by the React app routing

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Initialize environment configuration and security validation
  const { environmentConfig } = await import('./lib/environment-config-manager');
  
  try {
    productionSecurity.validateDatabaseSecurity();
    console.log('✅ Database security validation passed');
  } catch (error) {
    console.error('❌ Database security validation failed:', error);
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }

  await registerRoutes(app);
  
  // Register secure DDT processing routes
  registerSecureDDTProcessingRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // Create the HTTP server
  const server = createServer(app);

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();
