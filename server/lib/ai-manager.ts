/**
 * Gestore unificato per le impostazioni AI
 * Consolida la gestione di Claude e Gemini in un unico sistema
 */

import { db } from "../db";
import { aiSettings } from "@shared/schema";
import { eq } from "drizzle-orm";

export interface AIProvider {
  name: 'claude' | 'gemini';
  models: string[];
  defaultModel: string;
}

export interface AISettings {
  defaultProvider: 'claude' | 'gemini';
  claudeProvider: AIProvider;
  geminiProvider: AIProvider;
  defaultPrompts: {
    ddt?: string;
    label?: string;
    general?: string;
  };
}

class AIManager {
  private cachedSettings: AISettings | null = null;
  private lastCacheUpdate: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minuti

  /**
   * Ottiene le impostazioni AI globali dal database
   */
  async getAISettings(): Promise<AISettings> {
    // Usa cache se valida
    if (this.cachedSettings && (Date.now() - this.lastCacheUpdate < this.CACHE_DURATION)) {
      return this.cachedSettings;
    }

    try {
      const settings = await db.select().from(aiSettings).limit(1);
      
      if (settings.length === 0) {
        // Inizializza impostazioni predefinite se non esistono
        await this.initializeDefaultSettings();
        return await this.getAISettings();
      }

      const dbSettings = settings[0];
      
      this.cachedSettings = {
        defaultProvider: dbSettings.defaultAiProvider as 'claude' | 'gemini',
        claudeProvider: {
          name: 'claude',
          models: dbSettings.availableClaudeModels as string[],
          defaultModel: dbSettings.defaultClaudeModel
        },
        geminiProvider: {
          name: 'gemini',
          models: dbSettings.availableGeminiModels as string[],
          defaultModel: dbSettings.defaultGeminiModel
        },
        defaultPrompts: {
          ddt: dbSettings.defaultDdtPromptId || undefined,
          label: dbSettings.defaultLabelPromptId || undefined,
          general: dbSettings.defaultGeneralPromptId || undefined
        }
      };

      this.lastCacheUpdate = Date.now();
      return this.cachedSettings;
    } catch (error) {
      console.error('Errore nel recupero impostazioni AI:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Aggiorna le impostazioni AI globali
   */
  async updateAISettings(updates: Partial<AISettings>, adminUserId: number): Promise<AISettings> {
    try {
      const currentSettings = await this.getAISettings();
      
      const updateData: any = {
        updatedAt: new Date(),
        updatedBy: adminUserId
      };

      if (updates.defaultProvider) {
        updateData.defaultAiProvider = updates.defaultProvider;
      }

      if (updates.claudeProvider?.models) {
        updateData.availableClaudeModels = updates.claudeProvider.models;
      }

      if (updates.claudeProvider?.defaultModel) {
        updateData.defaultClaudeModel = updates.claudeProvider.defaultModel;
      }

      if (updates.geminiProvider?.models) {
        updateData.availableGeminiModels = updates.geminiProvider.models;
      }

      if (updates.geminiProvider?.defaultModel) {
        updateData.defaultGeminiModel = updates.geminiProvider.defaultModel;
      }

      if (updates.defaultPrompts?.ddt !== undefined) {
        updateData.defaultDdtPromptId = updates.defaultPrompts.ddt;
      }

      if (updates.defaultPrompts?.label !== undefined) {
        updateData.defaultLabelPromptId = updates.defaultPrompts.label;
      }

      if (updates.defaultPrompts?.general !== undefined) {
        updateData.defaultGeneralPromptId = updates.defaultPrompts.general;
      }

      await db.update(aiSettings).set(updateData);

      // Invalida cache
      this.cachedSettings = null;
      
      return await this.getAISettings();
    } catch (error) {
      console.error('Errore nell\'aggiornamento impostazioni AI:', error);
      throw error;
    }
  }

  /**
   * Invalida manualmente la cache
   */
  invalidateCache(): void {
    this.cachedSettings = null;
    this.lastCacheUpdate = 0;
  }

  /**
   * Ottiene il provider AI e modello correnti per un utente
   */
  async getCurrentAIConfig(userId?: number): Promise<{provider: 'claude' | 'gemini', model: string}> {
    // Invalida la cache per assicurarsi di ottenere i dati più recenti
    this.invalidateCache();
    const settings = await this.getAISettings();
    
    // Per ora usa le impostazioni globali
    // In futuro si potranno aggiungere override per utente
    if (settings.defaultProvider === 'claude') {
      return {
        provider: 'claude',
        model: settings.claudeProvider.defaultModel
      };
    } else {
      return {
        provider: 'gemini',
        model: settings.geminiProvider.defaultModel
      };
    }
  }

  /**
   * Ottiene il prompt appropriato per una categoria
   */
  async getPromptForCategory(category: 'ddt' | 'label' | 'general'): Promise<string> {
    const settings = await this.getAISettings();
    const promptId = settings.defaultPrompts[category];
    
    if (promptId) {
      try {
        // Importa dinamicamente per evitare dipendenze circolari
        const claude = await import('./claude');
        const prompts = await claude.getAllPrompts();
        const prompt = prompts.find(p => p.id === promptId);
        
        if (prompt) {
          return prompt.content;
        }
      } catch (error) {
        console.warn(`Errore nel recupero prompt ${promptId}:`, error);
      }
    }

    // Fallback ai prompt predefiniti
    const claude = await import('./claude');
    return await claude.getPromptFromStorage(category);
  }

  /**
   * Inizializza le impostazioni predefinite
   */
  private async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = {
      defaultAiProvider: 'claude' as const,
      defaultClaudeModel: 'claude-3-5-sonnet-20241022',
      availableClaudeModels: [
        'claude-3-5-sonnet-20241022',
        'claude-3-5-haiku-20241022',
        'claude-3-5-sonnet-20240620',
        'claude-3-opus-20240229',
        'claude-3-sonnet-20240229',
        'claude-3-haiku-20240307'
      ],
      defaultGeminiModel: 'gemini-1.5-pro',
      availableGeminiModels: [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-pro',
        'gemini-pro-vision'
      ],
      updatedAt: new Date(),
      updatedBy: 1 // Admin user
    };

    await db.insert(aiSettings).values([{
      ...defaultSettings,
      tenantId: 'default' // Aggiungi tenantId richiesto
    }]);
  }

  /**
   * Impostazioni predefinite di fallback
   */
  private getDefaultSettings(): AISettings {
    return {
      defaultProvider: 'claude',
      claudeProvider: {
        name: 'claude',
        models: [
          'claude-3-5-sonnet-20241022',
          'claude-3-5-haiku-20241022',
          'claude-3-opus-20240229',
          'claude-3-sonnet-20240229',
          'claude-3-haiku-20240307'
        ],
        defaultModel: 'claude-3-5-sonnet-20241022'
      },
      geminiProvider: {
        name: 'gemini',
        models: [
          'gemini-1.5-pro',
          'gemini-1.5-flash',
          'gemini-pro',
          'gemini-pro-vision'
        ],
        defaultModel: 'gemini-1.5-pro'
      },
      defaultPrompts: {}
    };
  }

  /**
   * Valida un identificatore di modello
   */
  async isValidModel(provider: 'claude' | 'gemini', modelId: string): Promise<boolean> {
    const settings = await this.getAISettings();
    
    if (provider === 'claude') {
      return settings.claudeProvider.models.includes(modelId);
    } else {
      return settings.geminiProvider.models.includes(modelId);
    }
  }

  /**
   * Pulisce la cache delle impostazioni
   */
  clearCache(): void {
    this.cachedSettings = null;
    this.lastCacheUpdate = 0;
  }
}

// Esporta un'istanza singleton
export const aiManager = new AIManager();