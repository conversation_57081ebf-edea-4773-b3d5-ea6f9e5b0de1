/**
 * Enhanced Security Validation System
 * Addresses critical security vulnerabilities identified in security audit
 */

import crypto from 'crypto';

export interface SecurityConfig {
  enableStrictValidation: boolean;
  requireApiKeys: boolean;
  allowMockTokens: boolean;
  maxRequestSize: string;
  sessionTimeout: number;
}

export interface ApiKeyValidationResult {
  isValid: boolean;
  keyType?: 'admin' | 'system' | 'ai' | 'mock';
  error?: string;
  warnings?: string[];
}

/**
 * Security Configuration Manager
 * Prevents hardcoded secrets and ensures environment-based configuration
 */
export class SecurityConfigManager {
  private static instance: SecurityConfigManager;
  private config: SecurityConfig;

  private constructor() {
    this.config = this.loadSecurityConfig();
    this.validateSecurityConfig();
  }

  public static getInstance(): SecurityConfigManager {
    if (!SecurityConfigManager.instance) {
      SecurityConfigManager.instance = new SecurityConfigManager();
    }
    return SecurityConfigManager.instance;
  }

  private loadSecurityConfig(): SecurityConfig {
    const isProduction = process.env.NODE_ENV === 'production';
    
    return {
      enableStrictValidation: isProduction || process.env.ENABLE_STRICT_VALIDATION === 'true',
      requireApiKeys: isProduction || process.env.REQUIRE_API_KEYS === 'true',
      allowMockTokens: !isProduction && process.env.ALLOW_MOCK_TOKENS === 'true',
      maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '7200', 10) // 2 hours default
    };
  }

  private validateSecurityConfig(): void {
    const requiredEnvVars: Record<string, string> = {};
    const warnings: string[] = [];

    // Check critical environment variables
    if (this.config.enableStrictValidation) {
      if (!process.env.SESSION_SECRET) {
        requiredEnvVars.SESSION_SECRET = 'Session encryption secret';
      }
      if (!process.env.ADMIN_DEFAULT_PASSWORD) {
        requiredEnvVars.ADMIN_DEFAULT_PASSWORD = 'Admin default password';
      }
      if (!process.env.USER_DEFAULT_PASSWORD) {
        requiredEnvVars.USER_DEFAULT_PASSWORD = 'User default password';
      }
    }

    // Check API key configuration
    if (this.config.requireApiKeys) {
      if (!process.env.ADMIN_API_KEY) {
        requiredEnvVars.ADMIN_API_KEY = 'Admin API key';
      }
      if (!process.env.SYSTEM_API_KEY) {
        requiredEnvVars.SYSTEM_API_KEY = 'System API key';
      }
    }

    // Check AI service configuration
    if (!process.env.ANTHROPIC_API_KEY) {
      warnings.push('ANTHROPIC_API_KEY not set - Claude AI features will be disabled');
    }
    if (!process.env.GOOGLE_AI_API_KEY) {
      warnings.push('GOOGLE_AI_API_KEY not set - Gemini AI features will be disabled');
    }

    // Log warnings in development
    if (warnings.length > 0 && !this.config.enableStrictValidation) {
      console.warn('🚨 SECURITY WARNINGS:');
      warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    // Throw errors for missing required variables
    if (Object.keys(requiredEnvVars).length > 0) {
      const errorMessage = 'CRITICAL SECURITY ERROR: Missing required environment variables:\n' +
        Object.entries(requiredEnvVars)
          .map(([key, desc]) => `  - ${key}: ${desc}`)
          .join('\n');
      throw new Error(errorMessage);
    }
  }

  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * Validates API keys with enhanced security checks
   */
  public validateApiKey(apiKey: string, requiredType?: string): ApiKeyValidationResult {
    if (!apiKey) {
      return { isValid: false, error: 'API key is required' };
    }

    const warnings: string[] = [];
    
    // Check for development mock tokens
    if (this.isMockToken(apiKey)) {
      if (!this.config.allowMockTokens) {
        return { 
          isValid: false, 
          error: 'Mock tokens are disabled in this environment',
          warnings: ['Mock token usage detected in secure environment']
        };
      }
      warnings.push('Mock token accepted in development environment');
      return { 
        isValid: true, 
        keyType: 'mock', 
        warnings 
      };
    }

    // Validate API key format
    if (!this.isValidKeyFormat(apiKey)) {
      return { 
        isValid: false, 
        error: 'Invalid API key format',
        warnings: ['API key does not match expected format']
      };
    }

    // Check against environment variables
    const keyValidation = this.checkEnvironmentKeys(apiKey, requiredType);
    if (keyValidation.isValid) {
      return keyValidation;
    }

    return { 
      isValid: false, 
      error: 'Invalid or unauthorized API key',
      warnings: ['API key not found in authorized keys']
    };
  }

  private isMockToken(apiKey: string): boolean {
    const mockPatterns = [
      /^mock-/i,
      /^test-/i,
      /^dev-/i,
      /^fake-/i,
      /^demo-/i
    ];
    return mockPatterns.some(pattern => pattern.test(apiKey));
  }

  private isValidKeyFormat(apiKey: string): boolean {
    // API keys should be at least 32 characters and contain base64-like characters
    return /^[A-Za-z0-9+/=]{32,}$/.test(apiKey);
  }

  private checkEnvironmentKeys(apiKey: string, requiredType?: string): ApiKeyValidationResult {
    const validKeys = {
      'admin': process.env.ADMIN_API_KEY,
      'system': process.env.SYSTEM_API_KEY,
      'ai': process.env.AI_SERVICE_API_KEY
    };

    for (const [type, key] of Object.entries(validKeys)) {
      if (!key) continue;
      
      // If a specific type is required, only check that type
      if (requiredType && type !== requiredType) continue;
      
      if (crypto.timingSafeEqual(Buffer.from(key), Buffer.from(apiKey))) {
        return { 
          isValid: true, 
          keyType: type as 'admin' | 'system' | 'ai'
        };
      }
    }

    return { isValid: false, error: 'Key not found in authorized keys' };
  }

  /**
   * Enhanced JWT secret validation
   */
  public getSecureSessionSecret(): string {
    const secret = process.env.SESSION_SECRET;
    
    if (!secret) {
      if (this.config.enableStrictValidation) {
        throw new Error('CRITICAL SECURITY ERROR: SESSION_SECRET environment variable is required');
      }
      console.warn('🚨 SECURITY WARNING: Using fallback session secret in development');
      return 'haccp-tracker-dev-secret-' + Date.now();
    }

    // Validate secret strength
    if (secret.length < 32) {
      const warning = 'SESSION_SECRET should be at least 32 characters long';
      if (this.config.enableStrictValidation) {
        throw new Error(`CRITICAL SECURITY ERROR: ${warning}`);
      }
      console.warn(`🚨 SECURITY WARNING: ${warning}`);
    }

    return secret;
  }

  /**
   * Secure password validation for default users
   */
  public getSecurePassword(userType: 'admin' | 'user'): string {
    const envVar = userType === 'admin' ? 'ADMIN_DEFAULT_PASSWORD' : 'USER_DEFAULT_PASSWORD';
    const password = process.env[envVar];
    
    if (!password) {
      if (this.config.enableStrictValidation) {
        throw new Error(`CRITICAL SECURITY ERROR: ${envVar} environment variable is required`);
      }
      const fallback = `${userType}123-CHANGE-ME-${Date.now()}`;
      console.warn(`🚨 SECURITY WARNING: Using fallback password for ${userType}. Set ${envVar} environment variable!`);
      return fallback;
    }

    // Validate password strength
    if (password.length < 8) {
      const warning = `${envVar} should be at least 8 characters long`;
      if (this.config.enableStrictValidation) {
        throw new Error(`CRITICAL SECURITY ERROR: ${warning}`);
      }
      console.warn(`🚨 SECURITY WARNING: ${warning}`);
    }

    return password;
  }

  /**
   * Database security validation
   */
  public validateDatabaseConnection(): void {
    const dbUrl = process.env.DATABASE_URL;
    
    if (!dbUrl) {
      throw new Error('CRITICAL SECURITY ERROR: DATABASE_URL environment variable is required');
    }

    // Check for SSL in production
    if (this.config.enableStrictValidation && !dbUrl.includes('sslmode=require')) {
      console.warn('🚨 SECURITY WARNING: Database SSL should be enabled in production');
    }
  }
}

/**
 * Security audit logger
 */
export class SecurityAuditLogger {
  private static logSecurityEvent(event: string, details: any, level: 'INFO' | 'WARN' | 'ERROR' = 'INFO'): void {
    // In development, ridurre verbosità dei log sicurezza per endpoint pubblici
    const isDev = process.env.NODE_ENV === 'development';
    const isPublicEndpoint = details?.endpoint && (
      details.endpoint.includes('/auth/me') ||
      details.endpoint.includes('/containers') ||
      details.endpoint.includes('/product-labels')
    );
    
    // Skip logging in dev per endpoint pubblici non critici
    if (isDev && isPublicEndpoint && level === 'INFO') {
      return;
    }
    
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      event,
      level,
      details: typeof details === 'object' ? details : { message: details }
    };
    
    const logMessage = `[SECURITY-AUDIT] ${timestamp} - ${event} - ${JSON.stringify(logEntry.details)}`;
    
    switch (level) {
      case 'ERROR':
        console.error(logMessage);
        break;
      case 'WARN':
        console.warn(logMessage);
        break;
      default:
        console.log(logMessage);
    }
  }

  public static logAuthentication(userId: string, username: string, success: boolean, details?: any): void {
    this.logSecurityEvent(
      success ? 'authentication_success' : 'authentication_failure',
      { userId, username, ...details },
      success ? 'INFO' : 'WARN'
    );
  }

  public static logApiKeyUsage(keyType: string, endpoint: string, success: boolean): void {
    this.logSecurityEvent(
      'api_key_usage',
      { keyType, endpoint, success },
      success ? 'INFO' : 'WARN'
    );
  }

  public static logSecurityViolation(violation: string, details: any): void {
    this.logSecurityEvent('security_violation', { violation, ...details }, 'ERROR');
  }

  public static logConfigurationIssue(issue: string, details: any): void {
    this.logSecurityEvent('configuration_issue', { issue, ...details }, 'WARN');
  }
}

// Export singleton instance
export const securityConfig = SecurityConfigManager.getInstance();