/**
 * CRIT<PERSON>AL SECURITY FIX 12: Secure File Upload Validator
 * 
 * Enterprise-grade file upload security for DDT processing and image uploads.
 * Prevents malicious file uploads, validates file types, and enforces size limits.
 */

import crypto from 'crypto';
import { comprehensiveSecurityLogger, SecurityEventType, SecuritySeverity } from './comprehensive-security-logger';

// Allowed MIME types for different upload contexts
const ALLOWED_MIME_TYPES = {
  DDT_IMAGES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ],
  LABEL_IMAGES: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp'
  ]
};

// File size limits (in bytes)
const SIZE_LIMITS = {
  DDT_IMAGES: 10 * 1024 * 1024, // 10MB
  LABEL_IMAGES: 5 * 1024 * 1024, // 5MB
  GENERAL: 2 * 1024 * 1024 // 2MB default
};

// Malicious file signatures (magic bytes)
const MALICIOUS_SIGNATURES = [
  // Executable files
  { signature: [0x4D, 0x5A], description: 'PE Executable' },
  { signature: [0x7F, 0x45, 0x4C, 0x46], description: 'ELF Executable' },
  { signature: [0xCA, 0xFE, 0xBA, 0xBE], description: 'Mach-O Binary' },
  
  // Script files embedded in images
  { signature: [0x3C, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74], description: 'Script tag' },
  { signature: [0x3C, 0x3F, 0x70, 0x68, 0x70], description: 'PHP code' },
  
  // Archive files (potential zip bombs)
  { signature: [0x50, 0x4B, 0x03, 0x04], description: 'ZIP archive' },
  { signature: [0x52, 0x61, 0x72, 0x21], description: 'RAR archive' }
];

interface FileValidationResult {
  isValid: boolean;
  error?: string;
  fileInfo?: {
    originalName: string;
    mimeType: string;
    size: number;
    hash: string;
  };
  securityThreats?: string[];
}

interface UploadContext {
  userId: string;
  tenantId: string;
  uploadType: 'DDT_IMAGES' | 'LABEL_IMAGES' | 'GENERAL';
  endpoint: string;
}

export class SecureFileUploadValidator {
  
  /**
   * Validate base64 encoded image for DDT/Label processing
   */
  validateBase64Image(
    base64Data: string,
    context: UploadContext,
    req: any
  ): FileValidationResult {
    try {
      // Remove data URL prefix if present
      let cleanBase64 = base64Data;
      let detectedMimeType = 'image/jpeg'; // default
      
      if (base64Data.includes('data:')) {
        const parts = base64Data.split(',');
        if (parts.length !== 2) {
          this.logUploadViolation('FILE_TYPE', req, {
            reason: 'Invalid base64 format',
            uploadType: context.uploadType
          });
          return { isValid: false, error: 'Invalid base64 format' };
        }
        
        const dataUrlPart = parts[0];
        cleanBase64 = parts[1];
        
        // Extract MIME type from data URL
        const mimeMatch = dataUrlPart.match(/data:([^;]+)/);
        if (mimeMatch) {
          detectedMimeType = mimeMatch[1];
        }
      }
      
      // Validate base64 encoding
      if (!this.isValidBase64(cleanBase64)) {
        this.logUploadViolation('FILE_TYPE', req, {
          reason: 'Invalid base64 encoding',
          uploadType: context.uploadType
        });
        return { isValid: false, error: 'Invalid base64 encoding' };
      }
      
      // Convert to buffer for analysis
      const buffer = Buffer.from(cleanBase64, 'base64');
      
      // Validate file size
      const sizeLimit = SIZE_LIMITS[context.uploadType] || SIZE_LIMITS.GENERAL;
      if (buffer.length > sizeLimit) {
        this.logUploadViolation('FILE_SIZE', req, {
          actualSize: buffer.length,
          maxSize: sizeLimit,
          uploadType: context.uploadType
        });
        return { 
          isValid: false, 
          error: `File size exceeds limit. Max: ${Math.round(sizeLimit / 1024 / 1024)}MB` 
        };
      }
      
      // Validate MIME type
      const allowedTypes = ALLOWED_MIME_TYPES[context.uploadType];
      if (!allowedTypes.includes(detectedMimeType)) {
        this.logUploadViolation('FILE_TYPE', req, {
          detectedType: detectedMimeType,
          allowedTypes,
          uploadType: context.uploadType
        });
        return { 
          isValid: false, 
          error: `Unsupported file type: ${detectedMimeType}` 
        };
      }
      
      // Validate file signature against MIME type
      const actualMimeType = this.detectMimeTypeFromBuffer(buffer);
      if (actualMimeType && actualMimeType !== detectedMimeType) {
        this.logUploadViolation('FILE_TYPE', req, {
          declaredType: detectedMimeType,
          actualType: actualMimeType,
          reason: 'MIME type mismatch',
          uploadType: context.uploadType
        });
        return { 
          isValid: false, 
          error: 'File type mismatch detected' 
        };
      }
      
      // Check for malicious content
      const threats = this.detectMaliciousContent(buffer);
      if (threats.length > 0) {
        this.logUploadViolation('MALICIOUS_CONTENT', req, {
          threats,
          uploadType: context.uploadType,
          fileSize: buffer.length
        });
        return { 
          isValid: false, 
          error: 'Malicious content detected',
          securityThreats: threats
        };
      }
      
      // Generate file hash for integrity
      const hash = crypto.createHash('sha256').update(buffer).digest('hex');
      
      // Log successful validation
      comprehensiveSecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS, // Using as general success event
        severity: SecuritySeverity.LOW,
        userId: context.userId,
        tenantId: context.tenantId,
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: context.endpoint,
        method: req.method,
        details: {
          action: 'FILE_UPLOAD_VALIDATED',
          uploadType: context.uploadType,
          fileSize: buffer.length,
          mimeType: detectedMimeType,
          hash: hash.substring(0, 16) + '...'
        },
        timestamp: new Date()
      });
      
      return {
        isValid: true,
        fileInfo: {
          originalName: `upload_${Date.now()}`,
          mimeType: detectedMimeType,
          size: buffer.length,
          hash
        }
      };
      
    } catch (error) {
      this.logUploadViolation('FILE_TYPE', req, {
        error: error.message,
        uploadType: context.uploadType
      });
      return { isValid: false, error: 'File validation failed' };
    }
  }
  
  /**
   * Validate base64 string format
   */
  private isValidBase64(base64: string): boolean {
    try {
      // Check if string contains only valid base64 characters
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(base64)) {
        return false;
      }
      
      // Try to decode
      Buffer.from(base64, 'base64');
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * Detect MIME type from file buffer using magic bytes
   */
  private detectMimeTypeFromBuffer(buffer: Buffer): string | null {
    if (buffer.length < 4) return null;
    
    // JPEG
    if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
      return 'image/jpeg';
    }
    
    // PNG
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
      return 'image/png';
    }
    
    // WebP
    if (buffer.subarray(0, 4).toString() === 'RIFF' && 
        buffer.subarray(8, 12).toString() === 'WEBP') {
      return 'image/webp';
    }
    
    return null;
  }
  
  /**
   * Detect malicious content in file buffer
   */
  private detectMaliciousContent(buffer: Buffer): string[] {
    const threats: string[] = [];
    
    // Check against known malicious signatures
    for (const sig of MALICIOUS_SIGNATURES) {
      if (this.bufferContainsSignature(buffer, sig.signature)) {
        threats.push(sig.description);
      }
    }
    
    // Check for embedded scripts in image files
    const bufferString = buffer.toString('utf8').toLowerCase();
    if (bufferString.includes('<script>') || 
        bufferString.includes('javascript:') ||
        bufferString.includes('<?php') ||
        bufferString.includes('eval(') ||
        bufferString.includes('exec(')) {
      threats.push('Embedded script content');
    }
    
    // Check for suspicious file size ratios (potential zip bombs)
    if (buffer.length > 0) {
      const entropy = this.calculateEntropy(buffer);
      if (entropy < 0.1) { // Very low entropy might indicate repeated patterns
        threats.push('Suspicious low entropy (potential compression bomb)');
      }
    }
    
    return threats;
  }
  
  /**
   * Check if buffer contains a specific byte signature
   */
  private bufferContainsSignature(buffer: Buffer, signature: number[]): boolean {
    for (let i = 0; i <= buffer.length - signature.length; i++) {
      let match = true;
      for (let j = 0; j < signature.length; j++) {
        if (buffer[i + j] !== signature[j]) {
          match = false;
          break;
        }
      }
      if (match) return true;
    }
    return false;
  }
  
  /**
   * Calculate Shannon entropy of buffer
   */
  private calculateEntropy(buffer: Buffer): number {
    const freq: number[] = new Array(256).fill(0);
    
    // Count byte frequencies
    for (let i = 0; i < buffer.length; i++) {
      freq[buffer[i]]++;
    }
    
    // Calculate entropy
    let entropy = 0;
    for (let i = 0; i < 256; i++) {
      if (freq[i] > 0) {
        const p = freq[i] / buffer.length;
        entropy -= p * Math.log2(p);
      }
    }
    
    return entropy / 8; // Normalize to 0-1 range
  }
  
  /**
   * Log file upload violations
   */
  private logUploadViolation(
    violationType: 'FILE_TYPE' | 'FILE_SIZE' | 'MALICIOUS_CONTENT',
    req: any,
    details: Record<string, any>
  ): void {
    comprehensiveSecurityLogger.logFileUploadViolation(violationType, req, details);
  }
  
  /**
   * Sanitize filename for safe storage
   */
  sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars
      .replace(/\.+/g, '.') // Replace multiple dots
      .substring(0, 100); // Limit length
  }
  
  /**
   * Generate secure random filename
   */
  generateSecureFilename(extension: string): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}_${random}.${extension}`;
  }
}

// Export singleton instance
export const fileUploadValidator = new SecureFileUploadValidator();

// Middleware for Express routes
export function createFileUploadMiddleware(uploadType: 'DDT_IMAGES' | 'LABEL_IMAGES' | 'GENERAL') {
  return (req: any, res: any, next: any) => {
    // Add upload context to request
    req.uploadContext = {
      userId: req.user?.id || 'anonymous',
      tenantId: req.user?.tenantId || 'unknown',
      uploadType,
      endpoint: req.originalUrl
    };
    
    next();
  };
}

export default fileUploadValidator;