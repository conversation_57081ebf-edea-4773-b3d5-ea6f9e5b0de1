import qrcode from 'qrcode';

export enum QRCodeType {
  DDT = 'DDT',
  PRODUCT_LABEL = 'PRODUCT_LABEL',
  CONTAINER = 'CONTAINER'
}

export async function generateQRCode(
  data: string,
  options = { errorCorrectionLevel: 'M', width: 200 }
): Promise<string> {
  try {
    const qrDataUrl = await qrcode.toDataURL(data, options);
    return qrDataUrl;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now().toString();
  const randomNumber = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${timestamp}${randomNumber}`;
}

/**
 * Genera il nome del file per un QR code secondo le convenzioni specificate:
 * - DDT: RagioneSociale-ddmmyy.png
 * - Etichetta: nomeprodotto-ddmmyy-hhmm.png
 * - Contenitore: nomecontenitore-ddmmyy.png
 * 
 * Gli spazi vengono sostituiti con underscore (_).
 */
export function generateQRCodeFilename(
  type: QRCodeType,
  name: string,
  creationDate: Date = new Date()
): string {
  // Sostituisci spazi con underscore
  const formattedName = name.replace(/\s+/g, '_');
  
  // Formatta la data nel formato ddmmyy
  const day = creationDate.getDate().toString().padStart(2, '0');
  const month = (creationDate.getMonth() + 1).toString().padStart(2, '0');
  const year = creationDate.getFullYear().toString().slice(-2);
  const date = `${day}${month}${year}`;
  
  // Per le etichette aggiungi anche l'ora nel formato hhmm
  if (type === QRCodeType.PRODUCT_LABEL) {
    const hour = creationDate.getHours().toString().padStart(2, '0');
    const minute = creationDate.getMinutes().toString().padStart(2, '0');
    const time = `${hour}${minute}`;
    return `${formattedName}-${date}-${time}.png`;
  }
  
  // Per DDT e Contenitori
  return `${formattedName}-${date}.png`;
}
