import { safeParseDateString } from "../../shared/safe-date-utils";
/**
 * Parsing sicuro di stringhe numeriche per componenti di data
 * Previene NaN e valori non validi da parseInt()
 */
function safeParseInt(value: string, min: number = 1, max: number = 3000): number | null {
  // Rimuovi spazi e caratteri non numerici
  const cleaned = value?.trim().replace(/[^\d]/g, '');
  
  if (!cleaned || cleaned.length === 0) {
    return null;
  }
  
  const parsed = parseInt(cleaned, 10);
  
  // Verifica che il parsing sia riuscito e il valore sia nel range
  if (isNaN(parsed) || parsed < min || parsed > max) {
    return null;
  }
  
  return parsed;
}

/**
 * Normalizza le date nel formato standard DD-MM-YYYY per il database
 * Gestisce diversi formati di input e restituisce sempre DD-MM-YYYY
 * Implementa parsing sicuro per prevenire NaN e crash
 */
export function normalizeDateToDDMMYYYY(dateInput: string): string {
  if (!dateInput || typeof dateInput !== 'string') {
    return '';
  }

  // Rimuovi spazi e caratteri non necessari
  let dateStr = dateInput.trim();
  
  console.log(`Server - Normalizing date: input = "${dateStr}"`);

  // Se è già nel formato DD/MM/YYYY, verifica e restituisci
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    const result = safeParseDateString(dateStr); if (!result.success || !result.parts) return "";
    const { day, month, year } = result.parts;
    const monthNum = safeParseInt(month, 1, 12);
    const yearNum = safeParseInt(year, 1900, 2100);
    
    if (dayNum && monthNum && yearNum && isValidDate(dayNum, monthNum, yearNum)) {
      console.log(`Server - Date already in DD/MM/YYYY format: ${dateStr}`);
      return dateStr;
    }
  }

  // Se è nel formato DD-MM-YYYY, convertilo a DD/MM/YYYY
  if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('-');
    if (!result.success || !result.parts) return "";
    const { day, month, year } = result.parts;
    const yearNum = safeParseInt(year, 1900, 2100);
    
    if (dayNum && monthNum && yearNum && isValidDate(dayNum, monthNum, yearNum)) {
      const normalized = `${day}/${month}/${year}`;
      console.log(`Server - Converted DD-MM-YYYY to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
      return normalized;
    }
  }

  // Formato DD-MM-YY -> DD/MM/YYYY
  if (/^\d{2}-\d{2}-\d{2}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('-');
    const dayNum = safeParseInt(day, 1, 31);
    const monthNum = safeParseInt(month, 1, 12);
    const yearNum = safeParseInt(year, 0, 99);
    
    if (dayNum && monthNum && yearNum !== null) {
      const fullYear = yearNum > 50 ? `19${year}` : `20${year}`;
      const fullYearNum = safeParseInt(fullYear, 1900, 2100);
      
      if (fullYearNum && isValidDate(dayNum, monthNum, fullYearNum)) {
        const normalized = `${day}/${month}/${fullYear}`;
        console.log(`Server - Converted DD-MM-YY to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
        return normalized;
      }
    }
  }

  // Formato DD/MM/YYYY -> mantieni DD/MM/YYYY
  if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
    const parts = dateStr.split('/');
    const day = parts[0].padStart(2, '0');
    const month = parts[1].padStart(2, '0');
    const year = parts[2];
    const normalized = `${day}/${month}/${year}`;
    console.log(`Server - Kept DD/MM/YYYY format: ${dateStr} -> ${normalized}`);
    return normalized;
  }

  // Formato DD/MM/YY -> DD/MM/YYYY
  if (/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(dateStr)) {
    const parts = dateStr.split('/');
    const dayNum = safeParseInt(parts[0], 1, 31);
    const monthNum = safeParseInt(parts[1], 1, 12);
    const yearNum = safeParseInt(parts[2], 0, 99);
    
    if (dayNum && monthNum && yearNum !== null) {
      const day = parts[0].padStart(2, '0');
      const month = parts[1].padStart(2, '0');
      const yearShort = parts[2];
      const fullYear = yearNum > 50 ? `19${yearShort}` : `20${yearShort}`;
      const fullYearNum = safeParseInt(fullYear, 1900, 2100);
      
      if (fullYearNum && isValidDate(dayNum, monthNum, fullYearNum)) {
        const normalized = `${day}/${month}/${fullYear}`;
        console.log(`Server - Converted DD/MM/YY to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
        return normalized;
      }
    }
  }

  // Formato YYYY-MM-DD -> DD/MM/YYYY
  if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateStr)) {
    const [year, month, day] = dateStr.split('-');
    const normalized = `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
    console.log(`Server - Converted YYYY-MM-DD to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
    return normalized;
  }

  // Formato con separatori vari (punti, spazi, ecc.)
  const generalPattern = /^(\d{1,2})[\/\-\.\s]+(\d{1,2})[\/\-\.\s]+(\d{2,4})$/;
  if (generalPattern.test(dateStr)) {
    const match = dateStr.match(generalPattern);
    if (match) {
      const dayNum = safeParseInt(match[1], 1, 31);
      const monthNum = safeParseInt(match[2], 1, 12);
      let yearNum = safeParseInt(match[3], 0, 2100);
      
      if (!dayNum || !monthNum || yearNum === null) {
        return ''; // Componenti non valide
      }
      
      const day = match[1].padStart(2, '0');
      const month = match[2].padStart(2, '0');
      let year = match[3];
      
      // Se l'anno è in formato a 2 cifre, espandilo a 4
      if (year.length === 2) {
        const yearShortNum = safeParseInt(year, 0, 99);
        if (yearShortNum !== null) {
          year = yearShortNum > 50 ? `19${year}` : `20${year}`;
          yearNum = safeParseInt(year, 1900, 2100);
          if (!yearNum) return ''; // Anno espanso non valido
        } else {
          return ''; // Anno non valido
        }
      }
      
      // Validazione finale della data
      if (isValidDate(dayNum, monthNum, yearNum)) {
        const normalized = `${day}/${month}/${year}`;
        console.log(`Server - Converted general format to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
        return normalized;
      }
      
      return ''; // Data non valida
    }
  }

  // Se non riconosciuto, prova a parsare come data standard
  try {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      const normalized = `${day}/${month}/${year}`;
      console.log(`Server - Parsed as Date object to DD/MM/YYYY: ${dateStr} -> ${normalized}`);
      return normalized;
    }
  } catch (error) {
    console.log(`Server - Failed to parse date: ${dateStr}`);
  }

  console.log(`Server - Unable to normalize date, returning empty string: ${dateStr}`);
  return '';
}

/**
 * Verifica se una data è valida
 */
function isValidDate(day: number, month: number, year: number): boolean {
  if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > 2100) {
    return false;
  }
  
  // Verifica giorni per mese
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  
  // Anno bisestile
  if (month === 2 && ((year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0))) {
    return day <= 29;
  }
  
  return day <= daysInMonth[month - 1];
}