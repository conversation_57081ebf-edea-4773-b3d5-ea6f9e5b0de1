/**
 * XSS Sanitization Utility
 * Addresses Security Issue: Unsafe innerHTML usage (Report M3)
 * 
 * Features:
 * - HTML sanitization for safe innerHTML usage
 * - Text-only content extraction
 * - Whitelist-based HTML filtering
 * - DOM manipulation safety
 */

import { SecureLogger } from './secure-logger';

// Allowed HTML tags (whitelist approach)
const ALLOWED_TAGS = [
  'p', 'br', 'strong', 'em', 'b', 'i', 'u', 'span', 'div',
  'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'ul', 'ol', 'li',
  'a' // with href restrictions
];

// Allowed attributes per tag
const ALLOWED_ATTRIBUTES: Record<string, string[]> = {
  'a': ['href', 'title'],
  'span': ['class'],
  'div': ['class'],
  'p': ['class'],
  'strong': ['class'],
  'em': ['class']
};

// Dangerous patterns to remove
const DANGEROUS_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gis,
  /<iframe[^>]*>.*?<\/iframe>/gis,
  /<object[^>]*>.*?<\/object>/gis,
  /<embed[^>]*>.*?<\/embed>/gis,
  /<link[^>]*>/gis,
  /<meta[^>]*>/gis,
  /<style[^>]*>.*?<\/style>/gis,
  /javascript:/gi,
  /vbscript:/gi,
  /data:/gi,
  /on\w+\s*=/gi, // Remove all event handlers
];

/**
 * XSS Sanitization Class
 */
export class XSSSanitizer {
  
  /**
   * Sanitize HTML content for safe innerHTML usage
   * Removes dangerous tags and attributes
   */
  static sanitizeHTML(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    try {
      let sanitized = input;
      
      // Remove dangerous patterns
      DANGEROUS_PATTERNS.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '');
      });
      
      // Remove disallowed tags (keep content)
      sanitized = sanitized.replace(/<\/?(\w+)[^>]*>/g, (match, tagName) => {
        const tag = tagName.toLowerCase();
        
        if (!ALLOWED_TAGS.includes(tag)) {
          return ''; // Remove the tag but keep content
        }
        
        // Clean attributes for allowed tags
        return match.replace(/(\w+)\s*=\s*["']([^"']*)["']/g, (attrMatch, attrName, attrValue) => {
          const allowedAttrs = ALLOWED_ATTRIBUTES[tag] || [];
          
          if (!allowedAttrs.includes(attrName.toLowerCase())) {
            return ''; // Remove disallowed attribute
          }
          
          // Special handling for href attributes
          if (attrName.toLowerCase() === 'href') {
            if (attrValue.match(/^(https?:\/\/|\/|#)/i)) {
              return attrMatch; // Allow safe URLs
            } else {
              return ''; // Remove potentially dangerous URLs
            }
          }
          
          return attrMatch;
        });
      });
      
      SecureLogger.debug("HTML sanitized", { 
        originalLength: input.length,
        sanitizedLength: sanitized.length,
        changesMade: input !== sanitized
      });
      
      return sanitized;
      
    } catch (error) {
      SecureLogger.error("HTML sanitization failed", error);
      return ''; // Return empty string on error for safety
    }
  }
  
  /**
   * Extract plain text content from HTML
   * Safer alternative to innerHTML for text-only content
   */
  static extractText(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    try {
      // Remove all HTML tags and decode entities
      const textOnly = input
        .replace(/<[^>]*>/g, '') // Remove all HTML tags
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ')
        .trim();
      
      return textOnly;
    } catch (error) {
      SecureLogger.error("Text extraction failed", error);
      return '';
    }
  }
  
  /**
   * Safe DOM element creation with text content
   * Alternative to innerHTML for simple content
   */
  static createSafeElement(tagName: string, textContent: string, className?: string): HTMLElement | null {
    try {
      if (!ALLOWED_TAGS.includes(tagName.toLowerCase())) {
        SecureLogger.warn("Attempted to create disallowed element", { tagName });
        return null;
      }
      
      const element = document.createElement(tagName);
      element.textContent = textContent; // Safe text assignment
      
      if (className) {
        element.className = this.sanitizeClassName(className);
      }
      
      return element;
    } catch (error) {
      SecureLogger.error("Safe element creation failed", { tagName, error });
      return null;
    }
  }
  
  /**
   * Sanitize CSS class names
   */
  static sanitizeClassName(className: string): string {
    if (!className || typeof className !== 'string') {
      return '';
    }
    
    // Allow only alphanumeric, hyphens, underscores, and spaces
    return className.replace(/[^a-zA-Z0-9\-_\s]/g, '').trim();
  }
  
  /**
   * Validate and sanitize URLs
   */
  static sanitizeURL(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }
    
    try {
      // Allow only http, https, and relative URLs
      if (url.match(/^(https?:\/\/|\/|#)/i)) {
        return url;
      } else {
        SecureLogger.warn("Blocked potentially dangerous URL", { url });
        return '';
      }
    } catch (error) {
      SecureLogger.error("URL sanitization failed", { url, error });
      return '';
    }
  }
  
  /**
   * Safe innerHTML replacement
   * Use this instead of element.innerHTML = content
   */
  static safeInnerHTML(element: HTMLElement, content: string): boolean {
    try {
      const sanitizedContent = this.sanitizeHTML(content);
      element.innerHTML = sanitizedContent;
      
      SecureLogger.debug("Safe innerHTML applied", {
        elementTag: element.tagName,
        contentLength: sanitizedContent.length
      });
      
      return true;
    } catch (error) {
      SecureLogger.error("Safe innerHTML failed", error);
      return false;
    }
  }
}

// Convenience functions
export const xssSanitizer = {
  html: XSSSanitizer.sanitizeHTML,
  text: XSSSanitizer.extractText,
  createElement: XSSSanitizer.createSafeElement,
  className: XSSSanitizer.sanitizeClassName,
  url: XSSSanitizer.sanitizeURL,
  safeHTML: XSSSanitizer.safeInnerHTML,
  sanitizeInput: XSSSanitizer.extractText // Alias for text extraction to sanitize user input
};

// Global replacement for innerHTML (development mode warning)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // Monkey patch innerHTML setter to warn about direct usage
  const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
  
  if (originalInnerHTMLDescriptor && originalInnerHTMLDescriptor.set) {
    Object.defineProperty(Element.prototype, 'innerHTML', {
      set: function(value: string) {
        SecureLogger.warn("Direct innerHTML usage detected - consider using XSSSanitizer.safeInnerHTML", {
          element: this.tagName,
          value: value.substring(0, 100) + (value.length > 100 ? '...' : '')
        });
        originalInnerHTMLDescriptor.set!.call(this, value);
      },
      get: originalInnerHTMLDescriptor.get,
      configurable: true,
      enumerable: true
    });
  }
}