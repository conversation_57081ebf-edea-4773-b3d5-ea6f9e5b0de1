/**
 * Utilità per JSON parsing sicuro
 * Previene crash dell'applicazione con input malformato
 */

export interface SafeJsonResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Parse sicuro di JSON con error handling
 */
export function safeJsonParse<T = any>(jsonString: string, fallback?: T): SafeJsonResult<T> {
  try {
    if (!jsonString || typeof jsonString !== 'string') {
      return {
        success: false,
        error: 'Input non valido: deve essere una stringa non vuota',
        data: fallback
      };
    }

    const trimmed = jsonString.trim();
    if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) {
      return {
        success: false,
        error: 'Input non è un JSON valido',
        data: fallback
      };
    }

    const parsed = JSON.parse(trimmed);
    return {
      success: true,
      data: parsed
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Errore parsing JSON',
      data: fallback
    };
  }
}

/**
 * Parse sicuro con fallback automatico
 */
export function safeJsonParseWithFallback<T>(jsonString: string, fallback: T): T {
  const result = safeJsonParse<T>(jsonString, fallback);
  return result.data ?? fallback;
}

/**
 * Stringify sicuro per JSON
 */
export function safeJsonStringify(obj: any): SafeJsonResult<string> {
  try {
    const stringified = JSON.stringify(obj);
    return {
      success: true,
      data: stringified
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Errore stringify JSON'
    };
  }
}