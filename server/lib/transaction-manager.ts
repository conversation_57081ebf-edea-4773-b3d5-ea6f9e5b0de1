import { db } from "../db";
import { sql } from "drizzle-orm";

/**
 * Transaction Manager per operazioni multi-tabella
 * Garantisce consistenza dei dati tramite transazioni ACID
 */

export type TransactionCallback<T> = (tx: any) => Promise<T>;

/**
 * Esegue una funzione all'interno di una transazione
 * Garantisce rollback automatico in caso di errore
 */
export async function withTransaction<T>(
  callback: TransactionCallback<T>
): Promise<T> {
  return await db.transaction(async (tx) => {
    try {
      console.log('🔄 Iniziando transazione...');
      const result = await callback(tx);
      console.log('✅ Transazione completata con successo');
      return result;
    } catch (error) {
      console.error('❌ Errore durante la transazione, eseguendo rollback:', error);
      throw error; // Il rollback è automatico quando viene lanciato un errore
    }
  });
}

/**
 * Esegue multiple operazioni in una singola transazione
 */
export async function withTransactionMultiple<T>(
  operations: Array<(tx: any) => Promise<any>>
): Promise<T[]> {
  return await db.transaction(async (tx) => {
    try {
      console.log(`🔄 Iniziando transazione multipla con ${operations.length} operazioni...`);
      const results = [];
      
      for (let i = 0; i < operations.length; i++) {
        console.log(`📝 Eseguendo operazione ${i + 1}/${operations.length}...`);
        const result = await operations[i](tx);
        results.push(result);
      }
      
      console.log('✅ Transazione multipla completata con successo');
      return results;
    } catch (error) {
      console.error('❌ Errore durante la transazione multipla, eseguendo rollback:', error);
      throw error;
    }
  });
}

/**
 * Verifica lo stato della connessione al database
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await db.execute(sql`SELECT 1`);
    return true;
  } catch (error) {
    console.error('❌ Errore connessione database:', error);
    return false;
  }
}

/**
 * Retry wrapper per operazioni critiche
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Tentativo ${attempt}/${maxRetries}...`);
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.error(`❌ Tentativo ${attempt} fallito:`, error);
      
      if (attempt < maxRetries) {
        console.log(`⏳ Attendo ${delayMs}ms prima del prossimo tentativo...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
        delayMs *= 2; // Exponential backoff
      }
    }
  }
  
  throw lastError!;
}

/**
 * Operazione transazionale con retry automatico
 */
export async function withTransactionRetry<T>(
  callback: TransactionCallback<T>,
  maxRetries: number = 3
): Promise<T> {
  return await withRetry(
    () => withTransaction(callback),
    maxRetries
  );
}