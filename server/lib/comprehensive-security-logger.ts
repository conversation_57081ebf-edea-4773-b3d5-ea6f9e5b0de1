/**
 * CRITICAL SECURITY FIX 11: Comprehensive Security Logging System
 * 
 * Enterprise-grade security logging for incident detection and forensic analysis.
 * Tracks all security events, attempted attacks, and suspicious activities.
 */

import { createLogger, format, transports } from 'winston';
import fs from 'fs';
import path from 'path';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Security event categories
export enum SecurityEventType {
  // Authentication & Authorization
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  SESSION_HIJACK_ATTEMPT = 'SESSION_HIJACK_ATTEMPT',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  
  // Network Security  
  CORS_VIOLATION = 'CORS_VIOLATION',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_IP_ACTIVITY = 'SUSPICIOUS_IP_ACTIVITY',
  
  // Data Security
  SQL_INJECTION_ATTEMPT = 'SQL_INJECTION_ATTEMPT',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  PATH_TRAVERSAL_ATTEMPT = 'PATH_TRAVERSAL_ATTEMPT',
  
  // File Upload Security
  MALICIOUS_FILE_UPLOAD = 'MALICIOUS_FILE_UPLOAD',
  FILE_TYPE_VIOLATION = 'FILE_TYPE_VIOLATION',
  FILE_SIZE_VIOLATION = 'FILE_SIZE_VIOLATION',
  
  // System Security
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',
  SECURITY_POLICY_VIOLATION = 'SECURITY_POLICY_VIOLATION',
  ANOMALOUS_BEHAVIOR = 'ANOMALOUS_BEHAVIOR'
}

// Security event severity levels
export enum SecuritySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM', 
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Security event interface
export interface SecurityEvent {
  eventType: SecurityEventType;
  severity: SecuritySeverity;
  userId?: string;
  username?: string;
  tenantId?: string;
  ip: string;
  userAgent: string;
  endpoint?: string;
  method?: string;
  payload?: any;
  details: Record<string, any>;
  timestamp: Date;
  requestId?: string;
}

// Create specialized security logger
const securityLogger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  transports: [
    // Security events file
    new transports.File({
      filename: path.join(logsDir, 'security-events.log'),
      maxsize: 100 * 1024 * 1024, // 100MB
      maxFiles: 10,
      tailable: true
    }),
    
    // Critical security events (separate file for alerts)
    new transports.File({
      filename: path.join(logsDir, 'critical-security.log'),
      level: 'error',
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Console output in development
    ...(process.env.NODE_ENV === 'development' ? [
      new transports.Console({
        format: format.combine(
          format.colorize(),
          format.simple()
        )
      })
    ] : [])
  ]
});

class ComprehensiveSecurityLogger {
  
  /**
   * Log a security event with full context
   */
  logSecurityEvent(event: SecurityEvent): void {
    const logLevel = this.getLogLevel(event.severity);
    const logData = {
      ...event,
      environment: process.env.NODE_ENV,
      serverInstance: process.env.REPL_ID || 'unknown',
      pid: process.pid
    };
    
    securityLogger.log(logLevel, 'Security Event', logData);
    
    // For critical events, also trigger immediate alerts
    if (event.severity === SecuritySeverity.CRITICAL) {
      this.triggerSecurityAlert(event);
    }
  }
  
  /**
   * Log authentication events
   */
  logAuthEvent(
    eventType: SecurityEventType.LOGIN_SUCCESS | SecurityEventType.LOGIN_FAILURE | SecurityEventType.LOGOUT,
    req: any,
    details: Record<string, any> = {}
  ): void {
    const severity = eventType === SecurityEventType.LOGIN_FAILURE ? 
      SecuritySeverity.MEDIUM : SecuritySeverity.LOW;
      
    this.logSecurityEvent({
      eventType,
      severity,
      userId: req.user?.id,
      username: req.user?.username || req.body?.username,
      tenantId: req.user?.tenantId,
      ip: req.ip || req.connection?.remoteAddress || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details,
      timestamp: new Date(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    });
  }
  
  /**
   * Log CORS violations
   */
  logCORSViolation(origin: string, req: any): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.CORS_VIOLATION,
      severity: SecuritySeverity.HIGH,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details: {
        blockedOrigin: origin,
        allowedOrigins: process.env.NODE_ENV === 'production' ? 
          'production-domains-only' : 'development-localhost'
      },
      timestamp: new Date(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    });
  }
  
  /**
   * Log rate limiting violations
   */
  logRateLimitViolation(req: any, limitType: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
      severity: SecuritySeverity.MEDIUM,
      userId: req.user?.id,
      username: req.user?.username,
      tenantId: req.user?.tenantId,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details: {
        limitType,
        requestsInWindow: req.rateLimit?.used || 'unknown',
        windowLimit: req.rateLimit?.limit || 'unknown'
      },
      timestamp: new Date(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    });
  }
  
  /**
   * Log file upload security violations
   */
  logFileUploadViolation(
    violationType: 'FILE_TYPE' | 'FILE_SIZE' | 'MALICIOUS_CONTENT',
    req: any,
    fileDetails: Record<string, any>
  ): void {
    const eventType = violationType === 'MALICIOUS_CONTENT' ? 
      SecurityEventType.MALICIOUS_FILE_UPLOAD :
      violationType === 'FILE_TYPE' ?
      SecurityEventType.FILE_TYPE_VIOLATION :
      SecurityEventType.FILE_SIZE_VIOLATION;
      
    const severity = violationType === 'MALICIOUS_CONTENT' ? 
      SecuritySeverity.CRITICAL : SecuritySeverity.HIGH;
    
    this.logSecurityEvent({
      eventType,
      severity,
      userId: req.user?.id,
      username: req.user?.username,
      tenantId: req.user?.tenantId,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      details: {
        violationType,
        ...fileDetails
      },
      timestamp: new Date(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    });
  }
  
  /**
   * Log injection attempts
   */
  logInjectionAttempt(
    injectionType: 'SQL' | 'XSS' | 'PATH_TRAVERSAL',
    req: any,
    suspiciousPayload: string
  ): void {
    const eventTypeMap = {
      'SQL': SecurityEventType.SQL_INJECTION_ATTEMPT,
      'XSS': SecurityEventType.XSS_ATTEMPT,
      'PATH_TRAVERSAL': SecurityEventType.PATH_TRAVERSAL_ATTEMPT
    };
    
    this.logSecurityEvent({
      eventType: eventTypeMap[injectionType],
      severity: SecuritySeverity.CRITICAL,
      userId: req.user?.id,
      username: req.user?.username,
      tenantId: req.user?.tenantId,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      payload: suspiciousPayload.substring(0, 500), // Truncate for logging
      details: {
        injectionType,
        payloadLength: suspiciousPayload.length,
        blocked: true
      },
      timestamp: new Date(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    });
  }
  
  /**
   * Trigger security alerts for critical events
   */
  private triggerSecurityAlert(event: SecurityEvent): void {
    console.error(`🚨 CRITICAL SECURITY ALERT: ${event.eventType}`, {
      severity: event.severity,
      ip: event.ip,
      user: event.username || 'anonymous',
      timestamp: event.timestamp,
      details: event.details
    });
    
    // In production, this would integrate with:
    // - SIEM systems
    // - Email alerts
    // - Slack notifications
    // - PagerDuty incidents
  }
  
  /**
   * Convert security severity to log level
   */
  private getLogLevel(severity: SecuritySeverity): string {
    switch (severity) {
      case SecuritySeverity.LOW:
        return 'info';
      case SecuritySeverity.MEDIUM:
        return 'warn';
      case SecuritySeverity.HIGH:
      case SecuritySeverity.CRITICAL:
        return 'error';
      default:
        return 'info';
    }
  }
  
  /**
   * Get security metrics for monitoring
   */
  async getSecurityMetrics(): Promise<Record<string, any>> {
    // In a production environment, this would query log aggregation systems
    // For now, return basic metrics
    return {
      timestamp: new Date(),
      environment: process.env.NODE_ENV,
      logFiles: {
        securityEvents: path.join(logsDir, 'security-events.log'),
        criticalEvents: path.join(logsDir, 'critical-security.log')
      },
      status: 'active'
    };
  }
}

// Export singleton instance
export const comprehensiveSecurityLogger = new ComprehensiveSecurityLogger();

// Export for use in middleware
export default comprehensiveSecurityLogger;