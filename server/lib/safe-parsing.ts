/**
 * Utilità di parsing sicuro per parametri di route
 * Previene errori NaN e fornisce validazione robusta
 */

/**
 * Parse sicuro di un parametro intero con validazione
 */
export function safeParseInt(value: string | undefined, paramName: string, min?: number, max?: number): number {
  if (value === undefined || value === null || value === '') {
    throw new Error(`Parametro ${paramName} mancante o vuoto`);
  }

  // Trim e validazione formato
  const trimmed = value.toString().trim();
  if (!/^\d+$/.test(trimmed)) {
    throw new Error(`Parametro ${paramName} deve essere un numero intero positivo`);
  }

  const parsed = parseInt(trimmed, 10);
  
  if (isNaN(parsed)) {
    throw new Error(`Parametro ${paramName} non è un numero valido`);
  }

  if (parsed < 0) {
    throw new Error(`Parametro ${paramName} non può essere negativo`);
  }

  if (min !== undefined && parsed < min) {
    throw new Error(`Parametro ${paramName} deve essere almeno ${min}`);
  }

  if (max !== undefined && parsed > max) {
    throw new Error(`Parametro ${paramName} non può superare ${max}`);
  }

  return parsed;
}

/**
 * Parse sicuro di un parametro float con validazione
 */
export function safeParseFloat(value: string | undefined, paramName: string, min?: number, max?: number): number {
  if (value === undefined || value === null || value === '') {
    throw new Error(`Parametro ${paramName} mancante o vuoto`);
  }

  // Trim e validazione formato base
  const trimmed = value.toString().trim();
  if (!/^\d*\.?\d+$/.test(trimmed)) {
    throw new Error(`Parametro ${paramName} deve essere un numero valido`);
  }

  const parsed = parseFloat(trimmed);
  
  if (isNaN(parsed)) {
    throw new Error(`Parametro ${paramName} non è un numero valido`);
  }

  if (parsed < 0) {
    throw new Error(`Parametro ${paramName} non può essere negativo`);
  }

  if (min !== undefined && parsed < min) {
    throw new Error(`Parametro ${paramName} deve essere almeno ${min}`);
  }

  if (max !== undefined && parsed > max) {
    throw new Error(`Parametro ${paramName} non può superare ${max}`);
  }

  return parsed;
}

/**
 * Parse sicuro opzionale per query parameters
 */
export function safeParseOptionalInt(value: string | undefined, defaultValue: number, min?: number, max?: number): number {
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }

  try {
    return safeParseInt(value, 'optional parameter', min, max);
  } catch (error) {
    return defaultValue;
  }
}

/**
 * Validazione ID per parametri di route
 */
export function validateRouteId(id: string | undefined, entityName: string): string {
  if (!id || typeof id !== 'string' || id.trim() === '') {
    throw new Error(`ID ${entityName} mancante o non valido`);
  }
  
  // Regex per validare un formato UUID
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  
  if (!uuidRegex.test(id)) {
    throw new Error(`ID ${entityName} non è un UUID valido`);
  }
  
  return id;
}

/**
 * Validazione range per paginazione
 */
export function validatePaginationParams(page?: string, limit?: string): { page: number; limit: number } {
  const pageNum = safeParseOptionalInt(page, 1, 1, 1000);
  const limitNum = safeParseOptionalInt(limit, 50, 1, 500);
  
  return { page: pageNum, limit: limitNum };
}