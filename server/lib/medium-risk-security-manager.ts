/**
 * MEDIUM-RISK SECURITY MANAGER
 * 
 * Addresses 4 medium-risk security vulnerabilities:
 * 1. Weak rate limiting (100 requests/15 min)
 * 2. Sensitive data logged (PII, tokens)
 * 3. Insecure token storage in localStorage
 * 4. Missing CSRF protection
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import crypto from 'crypto';
import { SecureLogger } from './secure-logger.js';

// 1. ENHANCED RATE LIMITING SYSTEM
class EnhancedRateLimiter {
  private static rateLimiters: Map<string, any> = new Map();
  
  // Progressive rate limiting based on endpoint sensitivity
  public static createRateLimiter(config: {
    windowMs: number;
    max: number;
    name: string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: Request) => string;
  }) {
    const limiter = rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      message: {
        error: 'Too many requests',
        retryAfter: Math.ceil(config.windowMs / 1000),
        limit: config.max,
        window: config.windowMs
      },
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || false,
      keyGenerator: config.keyGenerator || ((req: Request) => {
        // Enhanced key generation with user context
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent') || 'unknown';
        const userId = (req as any).user?.id || 'anonymous';
        
        return crypto.createHash('sha256')
          .update(`${ip}:${userAgent.substring(0, 50)}:${userId}`)
          .digest('hex');
      }),
      handler: (req: Request, res: Response) => {
        SecureLogger.audit('Rate limit exceeded', {
          ip: req.ip,
          endpoint: req.path,
          method: req.method,
          userAgent: req.get('User-Agent')?.substring(0, 100),
          userId: (req as any).user?.id || 'anonymous',
          limiterName: config.name,
          timestamp: new Date().toISOString()
        });
        
        res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil(config.windowMs / 1000),
          limit: config.max,
          window: config.windowMs
        });
      }
    });
    
    this.rateLimiters.set(config.name, limiter);
    return limiter;
  }
  
  // Tiered rate limiting system
  public static getTieredRateLimiters() {
    return {
      // Strict limits for authentication endpoints
      authLimiter: this.createRateLimiter({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 10, // 10 attempts per 15 minutes (much stricter)
        name: 'auth',
        skipSuccessfulRequests: true
      }),
      
      // Medium limits for API endpoints
      apiLimiter: this.createRateLimiter({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 300, // 300 requests per 15 minutes (3x more than current)
        name: 'api',
        skipSuccessfulRequests: false
      }),
      
      // Strict limits for sensitive operations
      sensitiveLimiter: this.createRateLimiter({
        windowMs: 5 * 60 * 1000, // 5 minutes
        max: 5, // 5 requests per 5 minutes
        name: 'sensitive',
        skipSuccessfulRequests: false
      }),
      
      // Very strict limits for password operations
      passwordLimiter: this.createRateLimiter({
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3, // 3 password attempts per hour
        name: 'password',
        skipSuccessfulRequests: true
      }),
      
      // Upload limits
      uploadLimiter: this.createRateLimiter({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 50, // 50 uploads per 15 minutes
        name: 'upload',
        skipSuccessfulRequests: false
      }),
      
      // General application limits
      generalLimiter: this.createRateLimiter({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000, // 1000 requests per 15 minutes for general use
        name: 'general',
        skipSuccessfulRequests: true
      })
    };
  }
}

// 2. SECURE LOGGING SYSTEM (Enhanced)
class SecureDataLogger {
  private static readonly SENSITIVE_KEYS = new Set([
    'password', 'token', 'secret', 'key', 'auth', 'session', 'cookie',
    'authorization', 'bearer', 'api_key', 'apikey', 'jwt', 'refresh_token',
    'access_token', 'csrf', 'ssn', 'social_security', 'credit_card', 'card_number',
    'cvv', 'pin', 'phone', 'email', 'address', 'name', 'birth', 'age'
  ]);
  
  private static readonly PII_PATTERNS = [
    /\b\d{3}-\d{2}-\d{4}\b/g, // SSN pattern
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card pattern
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email pattern
    /\b\d{3}[\s.-]?\d{3}[\s.-]?\d{4}\b/g, // Phone pattern
    /\b(?:\d{1,3}\.){3}\d{1,3}\b/g, // IP address pattern (for internal IPs)
  ];
  
  public static sanitizeForLogging(data: any): any {
    if (typeof data === 'string') {
      let sanitized = data;
      
      // Remove PII patterns
      this.PII_PATTERNS.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '[PII-REDACTED]');
      });
      
      // Truncate very long strings
      if (sanitized.length > 500) {
        sanitized = sanitized.substring(0, 500) + '[TRUNCATED]';
      }
      
      return sanitized;
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized: any = Array.isArray(data) ? [] : {};
      
      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();
        
        // Check if key contains sensitive information
        const isSensitive = this.SENSITIVE_KEYS.has(lowerKey) || 
          Array.from(this.SENSITIVE_KEYS).some(sensitiveKey => 
            lowerKey.includes(sensitiveKey)
          );
        
        if (isSensitive) {
          sanitized[key] = '[SENSITIVE-REDACTED]';
        } else {
          sanitized[key] = this.sanitizeForLogging(value);
        }
      }
      
      return sanitized;
    }
    
    return data;
  }
  
  public static logSecurely(level: 'info' | 'warn' | 'error' | 'audit', message: string, data?: any) {
    const sanitizedData = data ? this.sanitizeForLogging(data) : undefined;
    
    switch (level) {
      case 'info':
        SecureLogger.info(message, sanitizedData);
        break;
      case 'warn':
        SecureLogger.warn(message, sanitizedData);
        break;
      case 'error':
        SecureLogger.error(message, sanitizedData);
        break;
      case 'audit':
        SecureLogger.audit(message, sanitizedData);
        break;
    }
  }
}

// 3. SECURE TOKEN STORAGE MANAGER
class SecureTokenStorage {
  private static readonly STORAGE_ENCRYPTION_KEY = crypto.randomBytes(32);
  private static readonly FORBIDDEN_STORAGE_KEYS = new Set([
    'token', 'jwt', 'access_token', 'refresh_token', 'session_token',
    'auth_token', 'bearer_token', 'api_key', 'secret', 'password',
    'user_credentials', 'auth_data', 'session_data'
  ]);
  
  public static validateStorageOperation(key: string, value: any): {
    allowed: boolean;
    reason?: string;
    secureAlternative?: string;
  } {
    const lowerKey = key.toLowerCase();
    
    // Check if key is explicitly forbidden
    if (this.FORBIDDEN_STORAGE_KEYS.has(lowerKey)) {
      return {
        allowed: false,
        reason: `Direct storage of sensitive key '${key}' is not allowed`,
        secureAlternative: 'Use secure server-side session storage instead'
      };
    }
    
    // Check if key contains forbidden patterns
    const forbiddenPattern = Array.from(this.FORBIDDEN_STORAGE_KEYS).find(pattern =>
      lowerKey.includes(pattern)
    );
    
    if (forbiddenPattern) {
      return {
        allowed: false,
        reason: `Storage key '${key}' contains sensitive pattern '${forbiddenPattern}'`,
        secureAlternative: 'Use encrypted server-side storage or session-only data'
      };
    }
    
    // Check if value contains sensitive data
    if (typeof value === 'string') {
      const sensitivePatterns = [
        /^[A-Za-z0-9+/]{20,}={0,2}$/, // Base64 pattern (likely token)
        /^[a-f0-9]{32,}$/i, // Hex pattern (likely hash/token)
        /bearer\s+/i, // Bearer token
        /^sk-/, // API key pattern
        /^jwt\./, // JWT pattern
      ];
      
      const foundPattern = sensitivePatterns.find(pattern => pattern.test(value));
      if (foundPattern) {
        return {
          allowed: false,
          reason: `Value appears to be a sensitive token or credential`,
          secureAlternative: 'Use secure HTTP-only cookies or server-side sessions'
        };
      }
    }
    
    return { allowed: true };
  }
  
  public static createSecureStorageMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Override JSON responses to validate storage operations
      const originalJson = res.json;
      res.json = function(body: any) {
        if (body && typeof body === 'object') {
          // Check for potential client-side storage instructions
          const storageWarnings: string[] = [];
          
          const checkObject = (obj: any, path = '') => {
            for (const [key, value] of Object.entries(obj)) {
              const fullPath = path ? `${path}.${key}` : key;
              const validation = SecureTokenStorage.validateStorageOperation(key, value);
              
              if (!validation.allowed) {
                storageWarnings.push(`${fullPath}: ${validation.reason}`);
                SecureLogger.audit('Insecure storage attempt blocked', {
                  key: fullPath,
                  reason: validation.reason,
                  alternative: validation.secureAlternative,
                  endpoint: req.path,
                  userId: (req as any).user?.id
                });
              }
              
              if (typeof value === 'object' && value !== null) {
                checkObject(value, fullPath);
              }
            }
          };
          
          checkObject(body);
          
          if (storageWarnings.length > 0) {
            body._securityWarnings = storageWarnings;
          }
        }
        
        return originalJson.call(this, body);
      };
      
      next();
    };
  }
}

// 4. CSRF PROTECTION SYSTEM
class CSRFProtectionManager {
  private static tokenStore: Map<string, { token: string; expires: Date }> = new Map();
  
  public static generateCSRFToken(sessionId: string): string {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    this.tokenStore.set(sessionId, { token, expires });
    
    // Clean up expired tokens
    this.cleanupExpiredTokens();
    
    SecureLogger.audit('CSRF token generated', {
      sessionId: sessionId.substring(0, 8) + '...',
      expires: expires.toISOString()
    });
    
    return token;
  }
  
  private static cleanupExpiredTokens(): void {
    const now = new Date();
    for (const [sessionId, tokenData] of this.tokenStore.entries()) {
      if (tokenData.expires < now) {
        this.tokenStore.delete(sessionId);
      }
    }
  }
  
  public static validateCSRFToken(sessionId: string, providedToken: string): boolean {
    const tokenData = this.tokenStore.get(sessionId);
    
    if (!tokenData) {
      SecureLogger.audit('CSRF validation failed - no token found', { sessionId: sessionId.substring(0, 8) + '...' });
      return false;
    }
    
    if (tokenData.expires < new Date()) {
      this.tokenStore.delete(sessionId);
      SecureLogger.audit('CSRF validation failed - token expired', { sessionId: sessionId.substring(0, 8) + '...' });
      return false;
    }
    
    const isValid = crypto.timingSafeEqual(
      Buffer.from(tokenData.token, 'hex'),
      Buffer.from(providedToken, 'hex')
    );
    
    if (isValid) {
      SecureLogger.audit('CSRF validation successful', { sessionId: sessionId.substring(0, 8) + '...' });
    } else {
      SecureLogger.audit('CSRF validation failed - token mismatch', { sessionId: sessionId.substring(0, 8) + '...' });
    }
    
    return isValid;
  }
  
  public static createCSRFMiddleware(options: {
    excludePaths?: string[];
    methods?: string[];
  } = {}) {
    const excludePaths = options.excludePaths || ['/api/auth/csrf-token'];
    const methods = options.methods || ['POST', 'PUT', 'PATCH', 'DELETE'];
    
    return (req: Request, res: Response, next: NextFunction) => {
      // Skip for excluded paths
      if (excludePaths.some(path => req.path.startsWith(path))) {
        return next();
      }
      
      // Skip for safe methods
      if (!methods.includes(req.method)) {
        return next();
      }
      
      const sessionId = req.sessionID || req.session?.id;
      if (!sessionId) {
        return res.status(401).json({
          error: 'Session required for CSRF protection',
          code: 'NO_SESSION'
        });
      }
      
      const csrfToken = req.headers['x-csrf-token'] as string || req.body._csrf;
      
      if (!csrfToken) {
        return res.status(403).json({
          error: 'CSRF token required',
          code: 'MISSING_CSRF_TOKEN'
        });
      }
      
      if (!this.validateCSRFToken(sessionId, csrfToken)) {
        return res.status(403).json({
          error: 'Invalid CSRF token',
          code: 'INVALID_CSRF_TOKEN'
        });
      }
      
      next();
    };
  }
  
  // Middleware to provide CSRF token to clients
  public static createCSRFTokenProvider() {
    return (req: Request, res: Response, next: NextFunction) => {
      if (req.path === '/api/auth/csrf-token' && req.method === 'GET') {
        const sessionId = req.sessionID || req.session?.id;
        
        if (!sessionId) {
          return res.status(401).json({
            error: 'Session required to generate CSRF token'
          });
        }
        
        const token = this.generateCSRFToken(sessionId);
        return res.json({ csrfToken: token });
      }
      
      next();
    };
  }
}

// Complete Medium-Risk Security Stack
export const completeMediumRiskSecurityStack = [
  // Enhanced rate limiting
  ...Object.values(EnhancedRateLimiter.getTieredRateLimiters()),
  
  // Secure storage validation
  SecureTokenStorage.createSecureStorageMiddleware(),
  
  // CSRF protection
  CSRFProtectionManager.createCSRFTokenProvider(),
  CSRFProtectionManager.createCSRFMiddleware(),
  
  // Enhanced logging middleware
  (req: Request, res: Response, next: NextFunction) => {
    SecureDataLogger.logSecurely('audit', 'Request processed', {
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent')?.substring(0, 100)
    });
    next();
  }
];

export {
  EnhancedRateLimiter,
  SecureDataLogger,
  SecureTokenStorage,
  CSRFProtectionManager
};