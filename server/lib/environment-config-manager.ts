/**
 * Environment Configuration Manager
 * Risolve warning di configurazione per API keys e password sicure
 * Gestisce environment variables in modo sicuro per produzione
 */

import { logger } from './logger';
import { randomBytes } from 'crypto';

interface EnvironmentConfig {
  // Security settings
  adminDefaultPassword: string;
  userDefaultPassword: string;
  sessionSecret: string;
  
  // API Keys
  anthropicApiKey?: string;
  googleAiApiKey?: string;
  
  // Database
  databaseUrl: string;
  
  // Environment info
  nodeEnv: string;
  isProduction: boolean;
  isDevelopment: boolean;
}

interface SecurityValidation {
  isValid: boolean;
  warnings: string[];
  errors: string[];
  recommendations: string[];
}

export class EnvironmentConfigManager {
  private static instance: EnvironmentConfigManager;
  private config: EnvironmentConfig;
  private validationResult: SecurityValidation;

  private constructor() {
    this.config = this.loadEnvironmentConfig();
    this.validationResult = this.validateConfiguration();
    this.logConfigurationStatus();
  }

  public static getInstance(): EnvironmentConfigManager {
    if (!EnvironmentConfigManager.instance) {
      EnvironmentConfigManager.instance = new EnvironmentConfigManager();
    }
    return EnvironmentConfigManager.instance;
  }

  /**
   * Carica configurazione da environment variables
   */
  private loadEnvironmentConfig(): EnvironmentConfig {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const isProduction = nodeEnv === 'production';
    const isDevelopment = nodeEnv === 'development';

    return {
      // Security - Password con fallback sicuri
      adminDefaultPassword: process.env.ADMIN_DEFAULT_PASSWORD || 
        (isDevelopment ? this.generateSecureFallback('admin') : ''),
      
      userDefaultPassword: process.env.USER_DEFAULT_PASSWORD || 
        (isDevelopment ? this.generateSecureFallback('user') : ''),
      
      sessionSecret: process.env.SESSION_SECRET || 
        (isDevelopment ? this.generateSecureFallback('session', 64) : ''),

      // API Keys - Optional in development
      anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      googleAiApiKey: process.env.GOOGLE_AI_API_KEY,

      // Database
      databaseUrl: process.env.DATABASE_URL || '',

      // Environment
      nodeEnv,
      isProduction,
      isDevelopment
    };
  }

  /**
   * Genera fallback sicuri per development
   */
  private generateSecureFallback(type: string, length: number = 32): string {
    const randomPart = randomBytes(length / 2).toString('hex');
    const timestamp = Date.now().toString(36);
    
    switch (type) {
      case 'admin':
        return `Dev-Admin-${randomPart}-${timestamp}!`;
      case 'user':
        return `Dev-User-${randomPart}-${timestamp}!`;
      case 'session':
        return `DevSession-${randomPart}-${timestamp}`;
      default:
        return `Dev-${type}-${randomPart}-${timestamp}`;
    }
  }

  /**
   * Valida configurazione sicurezza
   */
  private validateConfiguration(): SecurityValidation {
    const warnings: string[] = [];
    const errors: string[] = [];
    const recommendations: string[] = [];

    // Validazione password production
    if (this.config.isProduction) {
      if (!process.env.ADMIN_DEFAULT_PASSWORD) {
        errors.push('ADMIN_DEFAULT_PASSWORD required in production');
      }
      if (!process.env.USER_DEFAULT_PASSWORD) {
        errors.push('USER_DEFAULT_PASSWORD required in production');
      }
      if (!process.env.SESSION_SECRET) {
        errors.push('SESSION_SECRET required in production');
      }
    } else {
      // Development warnings
      if (!process.env.ADMIN_DEFAULT_PASSWORD) {
        warnings.push('ADMIN_DEFAULT_PASSWORD not set - using development fallback');
      }
      if (!process.env.USER_DEFAULT_PASSWORD) {
        warnings.push('USER_DEFAULT_PASSWORD not set - using development fallback');
      }
    }

    // Validazione API Keys
    if (!this.config.anthropicApiKey) {
      warnings.push('ANTHROPIC_API_KEY not set - Claude AI features will be disabled');
      recommendations.push('Set ANTHROPIC_API_KEY to enable AI-powered OCR and data extraction');
    }

    if (!this.config.googleAiApiKey) {
      warnings.push('GOOGLE_AI_API_KEY not set - Gemini AI features will be disabled');
      recommendations.push('Set GOOGLE_AI_API_KEY to enable backup AI processing');
    }

    // Validazione forza password (più permissiva in development)
    const isStrongPassword = (password: string): boolean => {
      return password.length >= 12 && 
             /[A-Z]/.test(password) && 
             /[a-z]/.test(password) && 
             /[0-9]/.test(password) && 
             /[!@#$%^&*]/.test(password);
    };

    const isMinimumPassword = (password: string): boolean => {
      return password.length >= 8;
    };

    // Validazione admin password - completamente permissiva in development
    if (!isStrongPassword(this.config.adminDefaultPassword)) {
      if (this.config.isDevelopment) {
        // In development, accetta qualsiasi password senza warning
        // Solo raccomandazione per produzione
        if (this.config.adminDefaultPassword.length >= 8) {
          recommendations.push('Consider stronger admin password for production deployment');
        }
      } else {
        // In production, errore per password deboli
        errors.push('Admin password too weak (minimum 12 characters with mixed case, numbers, symbols)');
      }
    }

    // Validazione user password - più permissiva in development
    if (!isStrongPassword(this.config.userDefaultPassword)) {
      if (this.config.isDevelopment) {
        // In development, solo warning se la password è troppo corta
        if (!isMinimumPassword(this.config.userDefaultPassword)) {
          warnings.push('User password very short - consider 8+ characters for development');
        } else {
          // Password >= 8 caratteri in development va bene, solo debug
          recommendations.push('Consider stronger user password for production deployment');
        }
      } else {
        // In production, errore per password deboli
        errors.push('User password too weak (minimum 12 characters with mixed case, numbers, symbols)');
      }
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors,
      recommendations
    };
  }

  /**
   * Log stato configurazione con livelli appropriati
   */
  private logConfigurationStatus(): void {
    // Log errori come errori critici
    this.validationResult.errors.forEach(error => {
      logger.error(`[CONFIG-ERROR] ${error}`);
    });

    // Log warning come debug in development per ridurre rumore
    if (this.validationResult.warnings.length > 0) {
      if (this.config.isProduction) {
        logger.warn('🚨 Configuration warnings detected:', {
          warnings: this.validationResult.warnings,
          environment: this.config.nodeEnv,
          recommendations: this.validationResult.recommendations
        });
      } else {
        logger.debug('ℹ️ Development configuration notices:', {
          warnings: this.validationResult.warnings,
          environment: this.config.nodeEnv
        });
      }
    }

    // Log raccomandazioni per miglioramento
    if (this.validationResult.recommendations.length > 0) {
      logger.info('💡 Configuration recommendations:', {
        recommendations: this.validationResult.recommendations
      });
    }

    // Status summary
    if (this.validationResult.isValid) {
      logger.info(`✅ Environment configuration validated successfully (${this.config.nodeEnv})`);
    } else {
      logger.error(`❌ Environment configuration has ${this.validationResult.errors.length} critical errors`);
    }
  }

  /**
   * Getter per configurazione
   */
  public getConfig(): EnvironmentConfig {
    return { ...this.config }; // Clone per sicurezza
  }

  /**
   * Getter per stato validazione
   */
  public getValidationResult(): SecurityValidation {
    return { ...this.validationResult };
  }

  /**
   * Controlla se API key è disponibile
   */
  public isApiKeyAvailable(provider: 'anthropic' | 'google'): boolean {
    switch (provider) {
      case 'anthropic':
        return !!this.config.anthropicApiKey;
      case 'google':
        return !!this.config.googleAiApiKey;
      default:
        return false;
    }
  }

  /**
   * Ottiene API key sicura (senza logging)
   */
  public getApiKey(provider: 'anthropic' | 'google'): string | undefined {
    switch (provider) {
      case 'anthropic':
        return this.config.anthropicApiKey;
      case 'google':
        return this.config.googleAiApiKey;
      default:
        return undefined;
    }
  }

  /**
   * Verifica configurazione production-ready
   */
  public isProductionReady(): {
    ready: boolean;
    missingRequirements: string[];
  } {
    const missing: string[] = [];

    if (this.config.isProduction) {
      if (!process.env.ADMIN_DEFAULT_PASSWORD) missing.push('ADMIN_DEFAULT_PASSWORD');
      if (!process.env.USER_DEFAULT_PASSWORD) missing.push('USER_DEFAULT_PASSWORD');
      if (!process.env.SESSION_SECRET) missing.push('SESSION_SECRET');
      if (!this.config.databaseUrl) missing.push('DATABASE_URL');
    }

    return {
      ready: missing.length === 0,
      missingRequirements: missing
    };
  }

  /**
   * Genera esempio .env file
   */
  public generateEnvExample(): string {
    return `# HACCP Tracker Environment Configuration
# Copy to .env and set your values

# Required for Production
ADMIN_DEFAULT_PASSWORD=your-secure-admin-password-12-chars-min
USER_DEFAULT_PASSWORD=your-secure-user-password-12-chars-min  
SESSION_SECRET=your-64-character-session-secret-key

# Database
DATABASE_URL=your-postgresql-connection-string

# AI Services (Optional)
ANTHROPIC_API_KEY=your-claude-api-key
GOOGLE_AI_API_KEY=your-gemini-api-key

# Environment
NODE_ENV=production
`;
  }

  /**
   * Refresh configurazione (per hot reload)
   */
  public refreshConfiguration(): void {
    this.config = this.loadEnvironmentConfig();
    this.validationResult = this.validateConfiguration();
    this.logConfigurationStatus();
    logger.info('Environment configuration refreshed');
  }
}

// Export singleton
export const environmentConfig = EnvironmentConfigManager.getInstance();