/**
 * Advanced Async Error Handler for Express Routes
 * 
 * @description Enterprise-grade error handling per operazioni asincrone
 * che previene unhandled promise rejections e memory leaks
 */

import type { Request, Response, NextFunction } from 'express';
import { logger } from './logger';

export interface AsyncErrorContext {
  route: string;
  method: string;
  userId?: number;
  tenantId?: number;
  ip?: string;
  userAgent?: string;
}

export interface AsyncError extends Error {
  statusCode?: number;
  code?: string;
  context?: Record<string, any>;
}

/**
 * Wrapper per route handlers async che garantisce gestione errori
 */
export function asyncHandler<T = any>(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<T>
) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const context: AsyncErrorContext = {
      route: req.route?.path || req.path,
      method: req.method,
      userId: (req as any).user?.id,
      tenantId: (req as any).user?.tenantId,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    };

    // Execute async function with comprehensive error handling
    Promise.resolve(fn(req, res, next))
      .catch((error: AsyncError) => {
        // Enhanced error logging with context
        logger.error(`[ASYNC-ERROR] Route: ${context.route} (${context.method})`, {
          error: {
            message: error.message,
            stack: error.stack,
            code: error.code,
            statusCode: error.statusCode
          },
          context,
          timestamp: new Date().toISOString()
        });

        // Prevent double response sending
        if (res.headersSent) {
          console.error('Headers already sent, cannot send error response');
          return next(error);
        }

        // Send structured error response
        const statusCode = error.statusCode || 500;
        const errorResponse = {
          error: process.env.NODE_ENV === 'production' 
            ? 'Internal server error' 
            : error.message,
          code: error.code || 'INTERNAL_ERROR',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        };

        res.status(statusCode).json(errorResponse);
      });
  };
}

/**
 * Safe Promise wrapper con timeout e retry logic
 */
export async function safePromiseWithTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number = 30000,
  context?: string
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Promise timeout after ${timeoutMs}ms: ${context || 'unknown operation'}`));
    }, timeoutMs);
  });

  try {
    return await Promise.race([promise, timeoutPromise]);
  } catch (error) {
    logger.error(`[SAFE-PROMISE] Timeout or error in: ${context}`, {
      error: error instanceof Error ? error.message : String(error),
      timeoutMs,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
}

/**
 * Batch promise handler con fail-safe per operazioni multiple
 */
export async function safeBatchPromises<T>(
  promises: Promise<T>[],
  maxConcurrency: number = 5,
  failFast: boolean = false
): Promise<Array<T | Error>> {
  const batches: Promise<T>[][] = [];
  
  // Divide promises in batch per controllare concorrenza
  for (let i = 0; i < promises.length; i += maxConcurrency) {
    batches.push(promises.slice(i, i + maxConcurrency));
  }

  const results: Array<T | Error> = [];

  for (const batch of batches) {
    try {
      if (failFast) {
        // Fail on first error
        const batchResults = await Promise.all(batch);
        results.push(...batchResults);
      } else {
        // Continue even if some promises fail
        const batchResults = await Promise.allSettled(batch);
        const processedResults = batchResults.map(result => 
          result.status === 'fulfilled' ? result.value : new Error(result.reason)
        );
        results.push(...processedResults);
      }
    } catch (error) {
      logger.error('[SAFE-BATCH] Batch processing failed', {
        error: error instanceof Error ? error.message : String(error),
        batchSize: batch.length,
        failFast
      });
      
      if (failFast) {
        throw error;
      } else {
        // Add errors for all promises in failed batch
        results.push(...batch.map(() => error instanceof Error ? error : new Error(String(error))));
      }
    }
  }

  return results;
}

/**
 * Database operation wrapper con retry automatico
 */
export async function safeDbOperation<T>(
  operation: () => Promise<T>,
  retries: number = 3,
  context?: string
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      logger.warn(`[SAFE-DB] Operation failed (attempt ${attempt}/${retries}): ${context}`, {
        error: lastError.message,
        attempt,
        retries
      });

      // Don't retry on client errors (4xx)
      if ('statusCode' in lastError && 
          typeof lastError.statusCode === 'number' && 
          lastError.statusCode >= 400 && 
          lastError.statusCode < 500) {
        break;
      }

      // Wait before retry (exponential backoff)
      if (attempt < retries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error(`Database operation failed after ${retries} attempts: ${context}`);
}

/**
 * API call wrapper con error handling avanzato
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  context?: string,
  timeoutMs?: number
): Promise<T> {
  try {
    const promise = apiCall();
    
    if (timeoutMs) {
      return await safePromiseWithTimeout(promise, timeoutMs, context);
    }
    
    return await promise;
  } catch (error) {
    const apiError = error instanceof Error ? error : new Error(String(error));
    
    logger.error(`[SAFE-API] API call failed: ${context}`, {
      error: apiError.message,
      stack: apiError.stack,
      context,
      timestamp: new Date().toISOString()
    });
    
    // Re-throw with enhanced context
    const enhancedError = new Error(`API call failed: ${context} - ${apiError.message}`) as AsyncError;
    enhancedError.statusCode = 'statusCode' in apiError ? (apiError as any).statusCode : 500;
    enhancedError.code = 'code' in apiError ? (apiError as any).code : 'API_ERROR';
    enhancedError.context = { originalError: apiError.message, operation: context };
    
    throw enhancedError;
  }
}

/**
 * Express error middleware globale per unhandled promise rejections
 */
export function globalAsyncErrorHandler(
  error: AsyncError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Log dell'errore con contesto completo
  logger.error('[GLOBAL-ASYNC-ERROR] Unhandled async error', {
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode
    },
    request: {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id
    },
    timestamp: new Date().toISOString()
  });

  // Don't send response if headers already sent
  if (res.headersSent) {
    return next(error);
  }

  // Send error response
  const statusCode = error.statusCode || 500;
  const response = {
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message,
    code: error.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString()
  };

  res.status(statusCode).json(response);
}

/**
 * Process-level handlers per unhandled promise rejections
 */
export function setupGlobalAsyncErrorHandlers(): void {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('[UNHANDLED-REJECTION] Process-level unhandled promise rejection', {
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined,
      promise: promise.toString(),
      timestamp: new Date().toISOString()
    });

    // In production, exit gracefully
    if (process.env.NODE_ENV === 'production') {
      console.error('Unhandled promise rejection, shutting down gracefully');
      process.exit(1);
    }
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error: Error) => {
    logger.error('[UNCAUGHT-EXCEPTION] Process-level uncaught exception', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Always exit on uncaught exceptions
    console.error('Uncaught exception, shutting down');
    process.exit(1);
  });

  logger.info('[ASYNC-ERROR-HANDLER] Global async error handlers initialized');
}

/**
 * Utility per validare che tutte le promises in una funzione siano gestite
 */
export function validatePromiseHandling(fn: Function): Function {
  return function(this: any, ...args: any[]) {
    const result = fn.apply(this, args);
    
    if (result && typeof result.then === 'function') {
      // È una promise, assicurati che abbia un catch handler
      if (!result._handled) {
        console.warn(`⚠️ Promise without catch handler detected in function: ${fn.name}`);
        
        // Add automatic catch handler
        result.catch((error: Error) => {
          logger.error(`[UNHANDLED-PROMISE] Auto-caught promise in ${fn.name}`, {
            error: error.message,
            stack: error.stack,
            functionName: fn.name
          });
        });
      }
    }
    
    return result;
  };
}