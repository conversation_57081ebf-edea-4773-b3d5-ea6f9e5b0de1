import { GoogleGenerativeAI } from "@google/generative-ai";
import { AIErrorHandler } from "./error-handler";

interface OCRResult {
  success: boolean;
  data?: any;
  error?: string;
}

// Modelli Gemini disponibili
export const GEMINI_MODELS = {
  GEMINI_PRO: "gemini-pro",
  GEMINI_PRO_VISION: "gemini-pro-vision",
  GEMINI_1_5_PRO: "gemini-1.5-pro",
  GEMINI_1_5_FLASH: "gemini-1.5-flash"
};

// Modello predefinito
export const DEFAULT_GEMINI_MODEL = GEMINI_MODELS.GEMINI_1_5_PRO;

// Inizializzazione del client Gemini
let genAI: GoogleGenerativeAI | null = null;

function initializeGemini() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  if (!apiKey) {
    const errorMsg = "GOOGLE_AI_API_KEY not configured - Gemini features disabled";
    console.warn(`🔑 ${errorMsg}`);
    throw new Error(errorMsg);
  }

  // Validate API key format for security
  if (!apiKey.startsWith('AI') || apiKey.length < 30) {
    const errorMsg = "Invalid GOOGLE_AI_API_KEY format - check your configuration";
    console.error(`🚨 SECURITY: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  if (!genAI) {
    genAI = new GoogleGenerativeAI(apiKey);
  }

  return genAI;
}

// Estrae JSON dal testo con sicurezza migliorata
async function extractJsonFromText(text: string, defaultData: any = null): Promise<any> {
  try {
    // Cerca di trovare un blocco JSON nel testo
    const jsonRegex = /\{[\s\S]*\}/;
    const match = text.match(jsonRegex);

    if (match) {
      const jsonStr = match[0];
      
      // Limite di sicurezza per evitare JSON molto grandi
      if (jsonStr.length > 100000) {
        console.error("JSON troppo grande per sicurezza:", jsonStr.length);
        return defaultData;
      }
      
      const { safeJsonParse } = await import('./safe-json');
      const result = safeJsonParse(jsonStr);
      if (!result.success) {
        throw new Error(`JSON parsing failed: ${result.error}`);
      }
      return result.data;
    }
    return defaultData;
  } catch (e) {
    // Log sicuro senza esporre dati sensibili
    console.error("Errore nell'estrazione JSON:", e instanceof Error ? e.message : 'Unknown error');
    return defaultData;
  }
}

// Ottieni il modello Gemini dalle impostazioni globali AI
async function getSelectedGeminiModel(): Promise<string> {
  try {
    const { aiManager } = await import('./ai-manager');
    const config = await aiManager.getCurrentAIConfig();
    
    if (config.provider === 'gemini') {
      return config.model;
    }
    
    // Se il provider non è Gemini, restituisce il modello predefinito Gemini
    const settings = await aiManager.getAISettings();
    return settings.geminiProvider.defaultModel;
  } catch (error) {
    console.error('Errore nel recupero del modello Gemini:', error);
    return DEFAULT_GEMINI_MODEL;
  }
}

// TODO: Implementare la funzione getGeminiPrompt
async function getGeminiPrompt(promptType: string): Promise<string> {
  try {
    // Usa lo stesso sistema di storage dei prompt di Claude
    const { getPromptFromStorage } = await import('./claude');
    return await getPromptFromStorage(promptType);
  } catch (error) {
    console.warn(`Fallback ai prompt predefiniti per ${promptType}:`, error);
    
    switch (promptType) {
      case 'ddt':
        return "Sei un assistente esperto nell'analisi di documenti di trasporto (DDT). Estrai le seguenti informazioni dal documento fornito e restituisci ESCLUSIVAMENTE un oggetto JSON con ESATTAMENTE QUESTI NOMI DI CAMPI:\n- rag_soc_ddt: la ragione sociale del fornitore\n- partita_iva_ddt: la partita IVA del fornitore\n- indirizzo_ddt: l'indirizzo completo del fornitore\n- numero_ddt: il numero identificativo del DDT\n- data_ddt: la data del DDT nel formato GG/MM/YY\n\nRispondi SOLO con l'oggetto JSON, senza altro testo. Se non riesci a identificare un'informazione, lascia il campo come stringa vuota.";
      case 'label':
        return "Sei un assistente esperto nell'analisi di etichette alimentari. Estrai le seguenti informazioni dall'etichetta fornita e restituisci ESCLUSIVAMENTE un oggetto JSON con ESATTAMENTE QUESTI NOMI DI CAMPI:\n- productName: il nome del prodotto\n- batchNumber: il numero di lotto/batch\n- expiryDate: la data di scadenza nel formato GG/MM/AAAA\n- storageInstructions: requisiti di conservazione\n- notes: informazioni aggiuntive come valori nutrizionali, ingredienti, dichiarazioni, peso, ricette e altre informazioni presenti sull'etichetta. Riporta tabelle in formato leggibile usando | per separare colonne e a capo per righe.\n\nRispondi SOLO con l'oggetto JSON, senza altro testo. Se non riesci a identificare un'informazione, lascia il campo come stringa vuota.";
      default:
        return "Analizza il documento fornito e restituisci le informazioni principali in un oggetto JSON con campi appropriati. Rispondi SOLO con l'oggetto JSON.";
    }
  }
}

// Elabora un'immagine DDT con Gemini
export async function processDDTWithGemini(imageBase64: string, prompt: string, customPrompt: string | null = null): Promise<OCRResult> {
  try {
    const genAI = initializeGemini();

    // Assicurati che il base64 sia formattato correttamente
    let cleanBase64 = imageBase64;

    // Rimuovi eventuali prefissi di metadati
    if (cleanBase64.includes('base64,')) {
      cleanBase64 = cleanBase64.split('base64,')[1];
    }

    console.log("Base64 image length:", cleanBase64.length);

    // Ottieni il modello selezionato
    const selectedModel = await getSelectedGeminiModel();
    console.log("Usando modello Gemini:", selectedModel);

    const model = genAI.getGenerativeModel({ 
      model: await getSelectedGeminiModel(),
      generationConfig: {
        responseMimeType: "application/json",
        maxOutputTokens: 2048
      }
    });

    // Usa prompt personalizzato o ottieni quello appropriato
    const finalPrompt = customPrompt || await getGeminiPrompt('ddt');

    const imagePart = {
      inlineData: {
        data: cleanBase64,
        mimeType: "image/jpeg"
      }
    };

    const result = await model.generateContent([finalPrompt, imagePart]);
    const response = await result.response;
    const responseText = response.text();

    console.log("Risposta Gemini:", responseText);

    // Try direct JSON parsing first (for JSON mode)
    try {
      const { safeJsonParse } = await import('./safe-json');
      const result = safeJsonParse(responseText);
      if (!result.success) {
        throw new Error(`JSON parsing failed for Gemini response: ${result.error}`);
      }
      const directJson = result.data;
      return {
        success: true,
        data: directJson
      };
    } catch (directParseError) {
      // Fallback to regex extraction
      console.warn('Direct JSON parse failed, falling back to regex extraction');
      const extractedData = await extractJsonFromText(responseText);

      return {
        success: true,
        data: extractedData,
        // warning: 'Used fallback JSON extraction' // Rimuovo proprietà non valida
      };
    }

  } catch (error: any) {
    console.error("Errore nell'elaborazione DDT con Gemini:", error);
    return {
      success: false,
      error: error.message || 'Errore sconosciuto'
    };
  }
}

// Ottieni la lista dei modelli Gemini disponibili
export async function getGeminiModels() {
  try {
    const genAI = initializeGemini();

    // Chiamata diretta all'API per ottenere i modelli
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': process.env.GOOGLE_AI_API_KEY!
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Filtra solo i modelli Gemini generativi e rimuovi duplicati
    const allGeminiModels = data.models
      .filter((model: any) => 
        model.name.includes('gemini') && 
        model.supportedGenerationMethods?.includes('generateContent')
      )
      .map((model: any) => ({
        id: model.name.replace('models/', ''),
        name: model.displayName || model.name.replace('models/', ''),
        description: model.description || ''
      }));

    // Rimuovi duplicati basati sull'ID e ordina per preferenza
    const uniqueModels = allGeminiModels.filter((model, index, self) => 
      index === self.findIndex(m => m.id === model.id)
    );

    // Ordina i modelli per versione (più recenti prima)
    const sortedModels = uniqueModels.sort((a, b) => {
      // Priorità ai modelli stabili (senza -exp, -preview)
      const aIsStable = !a.id.includes('-exp') && !a.id.includes('-preview');
      const bIsStable = !b.id.includes('-exp') && !b.id.includes('-preview');

      if (aIsStable && !bIsStable) return -1;
      if (!aIsStable && bIsStable) return 1;

      return a.name.localeCompare(b.name);
    });

    console.log('Modelli Gemini trovati:', sortedModels.length);
    console.log('Modelli principali:', sortedModels.slice(0, 10).map(m => m.id));

    return {
      success: true,
      models: sortedModels,
      currentModel: DEFAULT_GEMINI_MODEL
    };
  } catch (error: any) {
    console.error('Errore nel recupero modelli Gemini:', error);

    // Fallback ai modelli hardcoded se l'API non risponde
    return {
      success: true,
      models: [
        { id: GEMINI_MODELS.GEMINI_1_5_PRO, name: "gemini-1.5-pro", description: "Modello avanzato multimodale" },
        { id: GEMINI_MODELS.GEMINI_1_5_FLASH, name: "gemini-1.5-flash", description: "Modello veloce" },
        { id: GEMINI_MODELS.GEMINI_PRO, name: "gemini-pro", description: "Modello standard" },
        { id: GEMINI_MODELS.GEMINI_PRO_VISION, name: "gemini-pro-vision", description: "Modello per visione" }
      ],
      currentModel: DEFAULT_GEMINI_MODEL
    };
  }
}

// Elabora un'etichetta con Gemini
export async function processLabelWithGemini(imageBase64: string, prompt: string, customPrompt: string | null = null): Promise<OCRResult> {
  try {
    const genAI = initializeGemini();

    // Assicurati che il base64 sia formattato correttamente
    let cleanBase64 = imageBase64;

    // Rimuovi eventuali prefissi di metadati
    if (cleanBase64.includes('base64,')) {
      cleanBase64 = cleanBase64.split('base64,')[1];
    }

    console.log("Base64 image length (label):", cleanBase64.length);

    // Ottieni il modello selezionato
    const selectedModel = await getSelectedGeminiModel();
    console.log("Usando modello Gemini per etichetta:", selectedModel);

    const model = genAI.getGenerativeModel({ model: selectedModel });

    // Usa prompt personalizzato o ottieni quello appropriato
    const finalPrompt = customPrompt || await getGeminiPrompt('label');

    const imagePart = {
      inlineData: {
        data: cleanBase64,
        mimeType: "image/jpeg"
      }
    };

    const result = await model.generateContent([finalPrompt, imagePart]);
    const response = await result.response;
    const text = response.text();

    console.log("Risposta Gemini per etichetta:", text);

    // Estrai il JSON dalla risposta
    const jsonData = await extractJsonFromText(text, {});

    return {
      success: true,
      data: jsonData
    };

  } catch (error: any) {
    console.error("Errore nell'elaborazione etichetta con Gemini:", error);
    return {
      success: false,
      error: error.message || 'Errore sconosciuto'
    };
  }
}