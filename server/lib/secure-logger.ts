/**
 * Secure Logger - Production-safe logging utility
 * Addresses Critical Security Issue: Console statements in production (Report C1-M1)
 * 
 * Features:
 * - Environment-based log level filtering
 * - Sensitive data redaction
 * - Production log silence
 * - Development-only diagnostic logs
 */

import { createLogger, format, transports } from 'winston';

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev';
const isProduction = process.env.NODE_ENV === 'production';

// Sensitive data patterns to redact
const SENSITIVE_PATTERNS = [
  /password['":\s]*['"]\w+['"]/gi,
  /token['":\s]*['"]\w+['"]/gi,
  /secret['":\s]*['"]\w+['"]/gi,
  /apikey['":\s]*['"]\w+['"]/gi,
  /authorization['":\s]*['"]\w+['"]/gi,
];

// Data sanitization function
function sanitizeData(data: any): any {
  if (typeof data === 'string') {
    let sanitized = data;
    SENSITIVE_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    return sanitized;
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      if (key.toLowerCase().includes('password') || 
          key.toLowerCase().includes('token') || 
          key.toLowerCase().includes('secret')) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = sanitizeData(data[key]);
      }
    }
    return sanitized;
  }
  
  return data;
}

// Logger configuration
const logger = createLogger({
  level: isDevelopment ? 'debug' : 'warn',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  transports: [
    // In production, only log to file or external service
    // In development, also log to console
    ...(isDevelopment ? [new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })] : [])
  ],
});

/**
 * Secure Logger Class
 * Replaces all console.log usage with environment-aware logging
 */
export class SecureLogger {
  
  /**
   * Development-only debug logs
   * Completely silent in production
   */
  static debug(message: string, data?: any) {
    if (isDevelopment) {
      logger.debug(message, data ? sanitizeData(data) : undefined);
    }
  }
  
  /**
   * Informational logs - development only
   * Used for application flow tracking
   */
  static info(message: string, data?: any) {
    if (isDevelopment) {
      logger.info(message, data ? sanitizeData(data) : undefined);
    }
  }
  
  /**
   * Warning logs - production safe
   * Only logs sanitized, non-sensitive warnings
   */
  static warn(message: string, data?: any) {
    logger.warn(message, data ? sanitizeData(data) : undefined);
  }
  
  /**
   * Error logs - production safe
   * Always logs errors but sanitizes sensitive data
   */
  static error(message: string, error?: any) {
    logger.error(message, error ? sanitizeData(error) : undefined);
  }
  
  /**
   * Security audit logs - always logged
   * For compliance and security monitoring
   */
  static security(action: string, details: any) {
    logger.warn(`[SECURITY-AUDIT] ${action}`, sanitizeData(details));
  }

  /**
   * Audit logs - always logged
   * For compliance and security monitoring
   */
  static audit(action: string, details: any) {
    logger.warn(`[AUDIT] ${action}`, sanitizeData(details));
  }
  
  /**
   * Performance logs - development only
   * For optimization and debugging
   */
  static performance(metric: string, value: number, context?: any) {
    if (isDevelopment) {
      logger.info(`[PERFORMANCE] ${metric}: ${value}ms`, context ? sanitizeData(context) : undefined);
    }
  }
  
  /**
   * Database operation logs - development only
   * For query debugging and optimization
   */
  static database(operation: string, query?: string, duration?: number) {
    if (isDevelopment) {
      logger.debug(`[DATABASE] ${operation}`, {
        query: query ? sanitizeData(query) : undefined,
        duration: duration ? `${duration}ms` : undefined
      });
    }
  }
  
  /**
   * Authentication logs - sanitized for production
   * Critical for security monitoring
   */
  static auth(action: string, userId?: string, details?: any) {
    logger.info(`[AUTH] ${action}`, {
      userId,
      timestamp: new Date().toISOString(),
      ...(details ? sanitizeData(details) : {})
    });
  }
}

/**
 * Console replacement functions
 * Drop-in replacements for console methods
 */
export const secureConsole = {
  log: SecureLogger.debug,
  info: SecureLogger.info,
  warn: SecureLogger.warn,
  error: SecureLogger.error,
  debug: SecureLogger.debug,
};

// Environment verification
if (isProduction) {
  SecureLogger.info("🔒 Production logging mode: Console output disabled");
} else {
  SecureLogger.info("🔧 Development logging mode: All logs enabled");
}