/**
 * Performance Optimizer
 * Ottimizza First Contentful Paint e Bundle Loading
 * Risolve il problema di FCP a 8.9 secondi (target <3s)
 */

import { logger } from './logger';

interface PerformanceMetrics {
  fcpTime: number;
  bundleSize: number;
  cacheHitRate: number;
  loadTime: number;
}

interface OptimizationConfig {
  enableCriticalCSS: boolean;
  enableResourcePreloading: boolean;
  enableLazyLoadingOptimization: boolean;
  bundleSplitThreshold: number;
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private metrics: PerformanceMetrics[] = [];
  private config: OptimizationConfig = {
    enableCriticalCSS: true,
    enableResourcePreloading: true,
    enableLazyLoadingOptimization: true,
    bundleSplitThreshold: 1024 * 1024 // 1MB
  };

  private constructor() {
    logger.info('PerformanceOptimizer initialized');
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  /**
   * Genera critical CSS inline per migliorare FCP
   */
  public generateCriticalCSS(): string {
    return `
      <style>
        /* Critical CSS per above-the-fold content */
        body { 
          margin: 0; 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
        }
        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background: #f8fafc;
        }
        .header {
          position: sticky;
          top: 0;
          z-index: 1000;
          background: white;
          border-bottom: 1px solid #e2e8f0;
          padding: 0.75rem 1rem;
        }
        .logo {
          height: 2rem;
          width: auto;
        }
        .nav-container {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background: white;
          border-top: 1px solid #e2e8f0;
          padding: 0.5rem;
        }
        /* Prevenire layout shifts */
        .container { 
          max-width: 1200px; 
          margin: 0 auto; 
          padding: 1rem;
        }
        .card {
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          padding: 1rem;
          margin-bottom: 1rem;
        }
      </style>
    `;
  }

  /**
   * Genera resource preloading hints
   */
  public generateResourceHints(): string {
    return `
      <!-- DNS prefetch per servizi esterni -->
      <link rel="dns-prefetch" href="//fonts.googleapis.com">
      <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
      
      <!-- Preload risorse critiche -->
      <link rel="preload" href="/api/auth/me" as="fetch" crossorigin="anonymous">
      <link rel="preload" href="/api/containers" as="fetch" crossorigin="anonymous">
      <link rel="preload" href="/logo.png" as="image">
      
      <!-- Preconnect per risorse esterne -->
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      
      <!-- Early hints per JavaScript modulare -->
      <link rel="modulepreload" href="/src/main.tsx">
      <link rel="modulepreload" href="/src/App.tsx">
    `;
  }

  /**
   * Ottimizza lazy loading con priorità intelligente
   */
  public optimizeLazyLoading(): {
    criticalComponents: string[];
    deferredComponents: string[];
    backgroundComponents: string[];
  } {
    return {
      // Componenti critici da caricare immediatamente
      criticalComponents: [
        '/login',
        '/dashboard', 
        '/containers',
        '/incoming-goods'
      ],
      
      // Componenti da caricare dopo il first paint
      deferredComponents: [
        '/search',
        '/users',
        '/activities'
      ],
      
      // Componenti da caricare in background
      backgroundComponents: [
        '/admin-dashboard',
        '/reports',
        '/settings'
      ]
    };
  }

  /**
   * Analizza bundle size e suggerisce ottimizzazioni
   */
  public analyzeBundleSize(): {
    currentSize: number;
    suggestions: string[];
    splitRecommendations: string[];
  } {
    return {
      currentSize: 2.1 * 1024 * 1024, // Stima 2.1MB
      suggestions: [
        'Tree-shake unused Radix UI components',
        'Lazy load Chart.js e Recharts',
        'Split vendor bundle separatamente',
        'Compressione Brotli in produzione',
        'Remove unused TailwindCSS classes'
      ],
      splitRecommendations: [
        'vendor.js - React, Radix UI, TanStack Query',
        'charts.js - Recharts, Chart utilities',
        'admin.js - Admin dashboard components',
        'auth.js - Authentication components'
      ]
    };
  }

  /**
   * Registra metriche performance
   */
  public recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push({
      ...metrics,
      timestamp: Date.now()
    } as any);

    // Mantieni solo le ultime 100 metriche
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Validazione sicura dei parametri per prevenire crash
    const fcpTime = typeof metrics.fcpTime === 'number' ? metrics.fcpTime : 0;
    const cacheHitRate = typeof metrics.cacheHitRate === 'number' ? metrics.cacheHitRate : 0;
    
    // Log solo performance critiche (>8000ms) per ridurre rumore
    if (fcpTime > 8000) {
      logger.warn(`Critical FCP performance issue: ${fcpTime}ms (target: <3000ms)`);
    } else if (fcpTime > this.targets.fcpTime) {
      logger.debug(`FCP above target: ${fcpTime}ms (target: <${this.targets.fcpTime}ms)`);
    }

    // Log solo cache hit rate molto basso (<50%)
    if (cacheHitRate < 50) {
      logger.warn(`Low cache hit rate: ${cacheHitRate}% (target: >80%)`);
    } else if (cacheHitRate < this.targets.cacheHitRate) {
      logger.debug(`Cache hit rate below target: ${cacheHitRate}% (target: >${this.targets.cacheHitRate}%)`);
    }

    logger.debug('Performance metrics recorded', { 
      fcp: `${fcpTime}ms`,
      cache: `${cacheHitRate}%`,
      bundle: `${(metrics.bundleSize / 1024 / 1024).toFixed(2)}MB`
    });
  }

  /**
   * Ottimizza configurazione Vite per produzione
   */
  public getViteOptimizationConfig(): object {
    return {
      build: {
        rollupOptions: {
          output: {
            manualChunks: {
              // Vendor chunk separato
              vendor: ['react', 'react-dom', '@tanstack/react-query'],
              
              // UI components chunk
              ui: [
                '@radix-ui/react-dialog',
                '@radix-ui/react-dropdown-menu',
                '@radix-ui/react-toast',
                '@radix-ui/react-tabs'
              ],
              
              // Charts chunk per lazy loading
              charts: ['recharts', 'jspdf', 'exceljs'],
              
              // Utils chunk
              utils: ['date-fns', 'zod', 'clsx', 'tailwind-merge']
            }
          }
        },
        
        // Ottimizzazioni compression
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            unused: true,
            dead_code: true
          }
        },
        
        // Source maps solo in development
        sourcemap: process.env.NODE_ENV === 'development',
        
        // Chunk size warnings
        chunkSizeWarningLimit: 500
      },
      
      // Ottimizzazioni dependency pre-bundling
      optimizeDeps: {
        include: [
          'react',
          'react-dom',
          '@tanstack/react-query'
        ],
        exclude: [
          // Lazy load heavy dependencies
          'recharts',
          'jspdf',
          'exceljs'
        ]
      }
    };
  }

  /**
   * Genera service worker ottimizzato per caching
   */
  public generateServiceWorkerConfig(): object {
    return {
      // Cache strategia per diversi tipi di risorse
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/api\//,
          handler: 'StaleWhileRevalidate',
          options: {
            cacheName: 'api-cache',
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 5 * 60 // 5 minuti
            }
          }
        },
        {
          urlPattern: /\.(?:png|jpg|jpeg|gif|svg|ico)$/,
          handler: 'CacheFirst',
          options: {
            cacheName: 'images-cache',
            expiration: {
              maxEntries: 50,
              maxAgeSeconds: 24 * 60 * 60 // 24 ore
            }
          }
        },
        {
          urlPattern: /\.(?:js|css)$/,
          handler: 'StaleWhileRevalidate',
          options: {
            cacheName: 'static-resources',
            expiration: {
              maxEntries: 60,
              maxAgeSeconds: 12 * 60 * 60 // 12 ore
            }
          }
        }
      ],
      
      // Precache risorse critiche
      precacheEntries: [
        '/',
        '/login',
        '/logo.png',
        '/manifest.json'
      ]
    };
  }

  /**
   * Statistiche performance aggregate
   */
  public getPerformanceStats(): {
    averageFCP: number;
    averageCacheHit: number;
    improvementSuggestions: string[];
  } {
    if (this.metrics.length === 0) {
      return {
        averageFCP: 0,
        averageCacheHit: 0,
        improvementSuggestions: ['No metrics collected yet']
      };
    }

    const avgFCP = this.metrics.reduce((sum, m) => sum + m.fcpTime, 0) / this.metrics.length;
    const avgCache = this.metrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / this.metrics.length;

    const suggestions: string[] = [];
    
    if (avgFCP > 3000) {
      suggestions.push('Implement critical CSS inlining');
      suggestions.push('Optimize bundle splitting');
      suggestions.push('Enable resource preloading');
    }
    
    if (avgCache < 80) {
      suggestions.push('Improve cache strategy');
      suggestions.push('Increase cache TTL for static resources');
    }

    return {
      averageFCP: Math.round(avgFCP),
      averageCacheHit: Math.round(avgCache),
      improvementSuggestions: suggestions
    };
  }
}

// Export singleton
export const performanceOptimizer = PerformanceOptimizer.getInstance();