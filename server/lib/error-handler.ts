/**
 * Sistema avanzato di gestione errori per il sistema AI
 * Fornisce messaggi contestuali, logging dettagliato e retry intelligenti
 */

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_KEY_ERROR = 'API_KEY_ERROR',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  MODEL_ERROR = 'MODEL_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  PROMPT_ERROR = 'PROMPT_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AIError {
  type: ErrorType;
  message: string;
  originalError?: Error;
  context?: {
    provider?: 'claude' | 'gemini';
    model?: string;
    category?: string;
    imageSize?: number;
    promptLength?: number;
    retryCount?: number;
  };
  userMessage: string;
  suggestions: string[];
  timestamp: Date;
}

export class AIErrorHandler {
  private static readonly MAX_RETRIES = 3;
  private static readonly BASE_DELAY = 1000; // 1 secondo
  private static readonly MAX_DELAY = 30000; // 30 secondi

  /**
   * Classifica e gestisce gli errori AI
   */
  static handleError(error: any, context?: any): AIError {
    const timestamp = new Date();
    
    // Log dettagliato per debug
    console.error('[AI Error Handler]', {
      error: error.message || error,
      stack: error.stack,
      context,
      timestamp: timestamp.toISOString()
    });

    // Analizza il tipo di errore
    const errorType = this.classifyError(error, context);
    const errorDetails = this.getErrorDetails(errorType, error, context);

    return {
      type: errorType,
      message: error.message || String(error),
      originalError: error instanceof Error ? error : new Error(String(error)),
      context,
      userMessage: errorDetails.userMessage,
      suggestions: errorDetails.suggestions,
      timestamp
    };
  }

  /**
   * Classifica il tipo di errore in base al messaggio e contesto
   */
  private static classifyError(error: any, context?: any): ErrorType {
    const errorMessage = (error.message || String(error)).toLowerCase();

    // Errori di API key
    if (errorMessage.includes('api key') || 
        errorMessage.includes('authentication') ||
        errorMessage.includes('unauthorized') ||
        error.status === 401) {
      return ErrorType.API_KEY_ERROR;
    }

    // Errori di quota
    if (errorMessage.includes('quota') || 
        errorMessage.includes('rate limit') ||
        errorMessage.includes('too many requests') ||
        error.status === 429) {
      return ErrorType.QUOTA_EXCEEDED;
    }

    // Errori di rete
    if (errorMessage.includes('network') ||
        errorMessage.includes('connection') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('fetch') ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'ETIMEDOUT') {
      return ErrorType.NETWORK_ERROR;
    }

    // Errori di modello
    if (errorMessage.includes('model') ||
        errorMessage.includes('invalid model') ||
        error.status === 404) {
      return ErrorType.MODEL_ERROR;
    }

    // Errori di parsing JSON
    if (errorMessage.includes('json') ||
        errorMessage.includes('parse') ||
        errorMessage.includes('invalid format')) {
      return ErrorType.PARSING_ERROR;
    }

    // Errori di prompt
    if (errorMessage.includes('prompt') ||
        errorMessage.includes('content too long') ||
        error.status === 413) {
      return ErrorType.PROMPT_ERROR;
    }

    // Errori di database
    if (errorMessage.includes('database') ||
        errorMessage.includes('connection') && context?.database) {
      return ErrorType.DATABASE_ERROR;
    }

    // Richieste non valide
    if (error.status === 400 || errorMessage.includes('bad request')) {
      return ErrorType.INVALID_REQUEST;
    }

    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * Fornisce dettagli specifici per ogni tipo di errore
   */
  private static getErrorDetails(errorType: ErrorType, error: any, context?: any): {
    userMessage: string;
    suggestions: string[];
  } {
    switch (errorType) {
      case ErrorType.API_KEY_ERROR:
        return {
          userMessage: 'Problema di autenticazione con il servizio AI. Verificare le credenziali.',
          suggestions: [
            'Controllare che la chiave API sia corretta',
            'Verificare che la chiave non sia scaduta',
            'Contattare l\'amministratore per aggiornare le credenziali'
          ]
        };

      case ErrorType.QUOTA_EXCEEDED:
        return {
          userMessage: 'Limite di utilizzo del servizio AI raggiunto. Riprovare più tardi.',
          suggestions: [
            'Attendere prima di riprovare',
            'Ridurre la frequenza delle richieste',
            'Contattare l\'amministratore per aumentare i limiti'
          ]
        };

      case ErrorType.NETWORK_ERROR:
        return {
          userMessage: 'Problema di connessione al servizio AI. Verificare la connessione internet.',
          suggestions: [
            'Controllare la connessione internet',
            'Riprovare tra qualche minuto',
            'Verificare che il servizio AI sia disponibile'
          ]
        };

      case ErrorType.MODEL_ERROR:
        return {
          userMessage: `Modello AI non disponibile o non valido: ${context?.model || 'sconosciuto'}`,
          suggestions: [
            'Provare con un modello diverso',
            'Verificare che il modello sia supportato',
            'Contattare l\'amministratore per aggiornare la configurazione'
          ]
        };

      case ErrorType.PARSING_ERROR:
        return {
          userMessage: 'Risposta del servizio AI non valida. L\'estrazione dei dati non è riuscita.',
          suggestions: [
            'Riprovare con un\'immagine più chiara',
            'Verificare che il documento sia leggibile',
            'Provare con un prompt diverso'
          ]
        };

      case ErrorType.PROMPT_ERROR:
        return {
          userMessage: 'Prompt troppo lungo o non valido per il modello AI.',
          suggestions: [
            'Ridurre la dimensione dell\'immagine',
            'Utilizzare un prompt più breve',
            'Contattare l\'amministratore per ottimizzare i prompt'
          ]
        };

      case ErrorType.DATABASE_ERROR:
        return {
          userMessage: 'Errore di accesso al database. Riprovare.',
          suggestions: [
            'Riprovare tra qualche minuto',
            'Verificare la connessione al database',
            'Contattare l\'amministratore se il problema persiste'
          ]
        };

      case ErrorType.INVALID_REQUEST:
        return {
          userMessage: 'Richiesta non valida. Verificare i dati inseriti.',
          suggestions: [
            'Controllare che l\'immagine sia valida',
            'Verificare che tutti i campi siano compilati',
            'Provare con un formato di immagine diverso'
          ]
        };

      default:
        return {
          userMessage: 'Si è verificato un errore imprevisto. Riprovare.',
          suggestions: [
            'Riprovare l\'operazione',
            'Aggiornare la pagina',
            'Contattare l\'amministratore se il problema persiste'
          ]
        };
    }
  }

  /**
   * Implementa retry intelligente con exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    context?: any,
    maxRetries: number = AIErrorHandler.MAX_RETRIES
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = this.calculateBackoffDelay(attempt);
          console.log(`[AI Retry] Tentativo ${attempt + 1}/${maxRetries + 1} dopo ${delay}ms`, {
            context,
            previousError: lastError?.message
          });
          await this.sleep(delay);
        }

        return await operation();
      } catch (error) {
        lastError = error;
        
        const aiError = this.handleError(error, { ...context, retryCount: attempt });
        
        // Non ritentare per alcuni tipi di errori
        if (this.shouldNotRetry(aiError.type)) {
          throw aiError;
        }

        // Se è l'ultimo tentativo, lancia l'errore
        if (attempt === maxRetries) {
          console.error(`[AI Retry] Tutti i tentativi falliti per:`, {
            context,
            attempts: attempt + 1,
            finalError: (error as Error)?.message || 'Unknown error'
          });
          throw aiError;
        }
      }
    }

    throw this.handleError(lastError, context);
  }

  /**
   * Determina se l'errore non dovrebbe essere riprovato
   */
  private static shouldNotRetry(errorType: ErrorType): boolean {
    return [
      ErrorType.API_KEY_ERROR,
      ErrorType.INVALID_REQUEST,
      ErrorType.MODEL_ERROR,
      ErrorType.PROMPT_ERROR
    ].includes(errorType);
  }

  /**
   * Calcola il delay per exponential backoff con jitter
   */
  private static calculateBackoffDelay(attempt: number): number {
    const exponentialDelay = Math.min(
      this.BASE_DELAY * Math.pow(2, attempt - 1),
      this.MAX_DELAY
    );
    
    // Aggiunge jitter per evitare il "thundering herd"
    const jitter = Math.random() * 0.3 * exponentialDelay;
    
    return Math.floor(exponentialDelay + jitter);
  }

  /**
   * Utility per sleep
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Crea un log strutturato per l'errore
   */
  static logError(aiError: AIError, additionalContext?: any): void {
    const logEntry = {
      timestamp: aiError.timestamp.toISOString(),
      type: aiError.type,
      message: aiError.message,
      context: aiError.context,
      additionalContext,
      stack: aiError.originalError?.stack
    };

    console.error('[AI Error Log]', JSON.stringify(logEntry, null, 2));
  }

  /**
   * Formatta l'errore per la risposta API
   */
  static formatForAPI(aiError: AIError): {
    success: false;
    error: string;
    type: string;
    suggestions: string[];
    context?: any;
  } {
    return {
      success: false,
      error: aiError.userMessage,
      type: aiError.type,
      suggestions: aiError.suggestions,
      context: aiError.context
    };
  }
}