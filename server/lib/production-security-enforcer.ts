/**
 * CRITICAL SECURITY ENFORCER - Production-Grade Security Implementation
 * Addresses all identified security vulnerabilities
 */

import crypto from 'crypto';
import { SecurityAuditLogger } from './security-validator';

export interface ProductionSecurityConfig {
  requireSecurePasswords: boolean;
  requireSecureSecrets: boolean;
  disableMockTokens: boolean;
  enforceSessionSecurity: boolean;
  auditSecurityEvents: boolean;
}

/**
 * Critical Security Enforcer - Production-Grade Protection
 */
export class ProductionSecurityEnforcer {
  private static instance: ProductionSecurityEnforcer;
  private config: ProductionSecurityConfig;
  private isProduction: boolean;

  private constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.config = {
      requireSecurePasswords: this.isProduction,
      requireSecureSecrets: this.isProduction,
      disableMockTokens: this.isProduction,
      enforceSessionSecurity: this.isProduction,
      auditSecurityEvents: true
    };
    
    this.performCriticalSecurityValidation();
  }

  public static getInstance(): ProductionSecurityEnforcer {
    if (!ProductionSecurityEnforcer.instance) {
      ProductionSecurityEnforcer.instance = new ProductionSecurityEnforcer();
    }
    return ProductionSecurityEnforcer.instance;
  }

  /**
   * CRITICAL: Validates all security requirements for production
   */
  private performCriticalSecurityValidation(): void {
    const criticalErrors: string[] = [];
    const warnings: string[] = [];

    // 1. SESSION SECRET VALIDATION
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret) {
      if (this.config.requireSecureSecrets) {
        criticalErrors.push('SESSION_SECRET environment variable is required in production');
      } else {
        warnings.push('SESSION_SECRET not set - using development fallback');
      }
    } else if (sessionSecret.length < 32) {
      if (this.config.requireSecureSecrets) {
        criticalErrors.push('SESSION_SECRET must be at least 32 characters long in production');
      } else {
        warnings.push('SESSION_SECRET should be at least 32 characters long');
      }
    }

    // 2. ADMIN CREDENTIALS VALIDATION
    const adminPassword = process.env.ADMIN_DEFAULT_PASSWORD;
    if (!adminPassword) {
      if (this.config.requireSecurePasswords) {
        criticalErrors.push('ADMIN_DEFAULT_PASSWORD environment variable is required in production');
      } else {
        warnings.push('ADMIN_DEFAULT_PASSWORD not set - using development fallback');
      }
    } else if (adminPassword.length < 12 || !this.isPasswordStrong(adminPassword)) {
      if (this.config.requireSecurePasswords) {
        criticalErrors.push('ADMIN_DEFAULT_PASSWORD must be at least 12 characters with mixed case, numbers, and symbols');
      } else {
        // In development, accetta qualsiasi password senza warning
        // Password strength è responsabilità dell'utente in development
      }
    }

    // 3. USER CREDENTIALS VALIDATION
    const userPassword = process.env.USER_DEFAULT_PASSWORD;
    if (!userPassword) {
      if (this.config.requireSecurePasswords) {
        criticalErrors.push('USER_DEFAULT_PASSWORD environment variable is required in production');
      } else {
        warnings.push('USER_DEFAULT_PASSWORD not set - using development fallback');
      }
    } else if (userPassword.length < 12 || !this.isPasswordStrong(userPassword)) {
      if (this.config.requireSecurePasswords) {
        criticalErrors.push('USER_DEFAULT_PASSWORD must be at least 12 characters with mixed case, numbers, and symbols');
      } else {
        // In development, accetta qualsiasi password senza warning
        // Password strength è responsabilità dell'utente in development
      }
    }

    // 4. MOCK TOKEN PROTECTION
    if (this.config.disableMockTokens) {
      if (process.env.ALLOW_MOCK_TOKENS === 'true') {
        criticalErrors.push('Mock tokens are disabled in production - remove ALLOW_MOCK_TOKENS=true');
      }
    }

    // Log security events
    if (warnings.length > 0) {
      warnings.forEach(warning => {
        SecurityAuditLogger.logConfigurationIssue('security_warning', { warning });
      });
    }

    // CRITICAL: Stop application if production security requirements are not met
    if (criticalErrors.length > 0) {
      const errorMessage = 'CRITICAL SECURITY ERRORS DETECTED:\n' + 
        criticalErrors.map(error => `  - ${error}`).join('\n') +
        '\n\nApplication startup terminated for security compliance.';
      
      SecurityAuditLogger.logSecurityViolation('production_security_failure', { 
        errors: criticalErrors,
        environment: process.env.NODE_ENV 
      });
      
      throw new Error(errorMessage);
    }
    
    if (this.isProduction) {
      console.log('✅ PRODUCTION SECURITY VALIDATION PASSED');
    }
  }

  /**
   * Password strength validation
   */
  private isPasswordStrong(password: string): boolean {
    // Must contain at least:
    // - 12 characters
    // - 1 uppercase letter
    // - 1 lowercase letter
    // - 1 number
    // - 1 special character
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$/;
    return strongPasswordRegex.test(password);
  }

  /**
   * SECURE SESSION SECRET - No predictable fallbacks in production
   */
  public getSecureSessionSecret(): string {
    const secret = process.env.SESSION_SECRET;
    
    if (!secret) {
      if (this.config.requireSecureSecrets) {
        throw new Error('CRITICAL SECURITY ERROR: SESSION_SECRET environment variable is required');
      }
      
      // Development-only secure fallback
      const fallbackSecret = 'haccp-dev-' + crypto.randomBytes(32).toString('hex');
      console.warn('🚨 DEVELOPMENT: Generated secure session secret. Set SESSION_SECRET environment variable!');
      return fallbackSecret;
    }

    return secret;
  }

  /**
   * SECURE PASSWORD RETRIEVAL - No hardcoded fallbacks in production
   */
  public getSecurePassword(userType: 'admin' | 'user'): string {
    const envVar = userType === 'admin' ? 'ADMIN_DEFAULT_PASSWORD' : 'USER_DEFAULT_PASSWORD';
    const password = process.env[envVar];
    
    if (!password) {
      if (this.config.requireSecurePasswords) {
        throw new Error(`CRITICAL SECURITY ERROR: ${envVar} environment variable is required`);
      }
      
      // Development-only secure fallback
      const secureRandom = crypto.randomBytes(8).toString('hex');
      const fallbackPassword = `${userType}_${secureRandom}_CHANGE_ME`;
      console.warn(`🚨 DEVELOPMENT: Generated secure ${userType} password. Set ${envVar} environment variable!`);
      return fallbackPassword;
    }

    return password;
  }

  /**
   * MOCK TOKEN VALIDATION - Completely disabled in production
   */
  public validateAuthToken(token: string): { isValid: boolean; isMock: boolean; error?: string } {
    if (!token) {
      return { isValid: false, isMock: false, error: 'Token is required' };
    }

    // Check for mock token patterns
    const mockPatterns = [
      /^mock-/i,
      /^test-/i,
      /^dev-/i,
      /^fake-/i,
      /^demo-/i,
      /^sample-/i
    ];

    const isMock = mockPatterns.some(pattern => pattern.test(token));

    if (isMock) {
      if (this.config.disableMockTokens) {
        SecurityAuditLogger.logSecurityViolation('mock_token_attempt', {
          token: token.substring(0, 10) + '...',
          environment: process.env.NODE_ENV
        });
        return { 
          isValid: false, 
          isMock: true, 
          error: 'Mock tokens are disabled in production environment' 
        };
      }
      
      console.warn('🚨 DEVELOPMENT: Mock token accepted in development environment');
      return { isValid: true, isMock: true };
    }

    // Validate real token format
    if (token.length < 32) {
      return { isValid: false, isMock: false, error: 'Token too short' };
    }

    return { isValid: true, isMock: false };
  }

  /**
   * SECURE COOKIE CONFIGURATION
   */
  public getSecureCookieConfig() {
    return {
      secure: this.isProduction, // HTTPS required in production
      sameSite: 'strict' as const, // CSRF protection
      maxAge: 2 * 60 * 60 * 1000, // 2 hours (reduced from longer sessions)
      httpOnly: true, // Prevent XSS
      domain: this.isProduction ? process.env.COOKIE_DOMAIN : undefined
    };
  }

  /**
   * Database security validation
   */
  public validateDatabaseSecurity(): void {
    const dbUrl = process.env.DATABASE_URL;
    
    if (!dbUrl) {
      throw new Error('CRITICAL SECURITY ERROR: DATABASE_URL environment variable is required');
    }

    // Check for SSL in production
    if (this.isProduction && !dbUrl.includes('ssl=true') && !dbUrl.includes('sslmode=require')) {
      console.warn('🚨 PRODUCTION WARNING: Database SSL should be enabled in production');
    }
  }

  /**
   * Security audit for authentication events
   */
  public auditAuthenticationAttempt(username: string, success: boolean, details?: any): void {
    SecurityAuditLogger.logAuthentication(
      details?.userId || 'unknown',
      username,
      success,
      {
        timestamp: new Date().toISOString(),
        userAgent: details?.userAgent,
        ip: details?.ip,
        ...details
      }
    );
  }
}

// Export singleton instance
export const productionSecurity = ProductionSecurityEnforcer.getInstance();