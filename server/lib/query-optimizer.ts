/**
 * DATABASE QUERY OPTIMIZER - Eliminazione N+1 Query Problems
 * 
 * Ottimizza le query del database con:
 * - Eager loading con JOIN ottimizzate
 * - Batch loading per evitare multiple query
 * - Cache intelligente per query frequenti
 * - Indici suggeriti per performance
 * - Query planning e analysis
 */

import { db } from "../db";
import { 
  containers, containerProducts, productLabels, suppliers, ddts,
  activityLogs, users, containerTypes 
} from "@shared/schema";
import { eq, inArray, sql, and, desc } from "drizzle-orm";
import { logger } from "./logger";

interface QueryPerformanceMetrics {
  queryType: string;
  executionTime: number;
  rowsReturned: number;
  timestamp: Date;
}

class DatabaseQueryOptimizer {
  private performanceMetrics: QueryPerformanceMetrics[] = [];
  private queryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minuti

  /**
   * OTTIMIZZAZIONE CRITICA: Container con Products (elimina N+1)
   * Prima: 1 query per container + N query per products = N+1 queries
   * Dopo: 1 query con JOIN = 1 query totale
   */
  async getContainersWithProducts(): Promise<any[]> {
    const startTime = performance.now();
    
    try {
      // Query ottimizzata con LEFT JOIN per evitare N+1
      const result = await db
        .select({
          // Container fields
          containerId: containers.id,
          containerName: containers.name,
          containerType: containers.type,
          containerMaxItems: containers.maxItems,
          containerCurrentItems: containers.currentItems,
          containerCreatedAt: containers.createdAt,
          containerIsArchived: containers.isArchived,
          containerTenantId: containers.tenantId,
          
          // Product fields (può essere null se container vuoto)
          productId: productLabels.id,
          productName: productLabels.productName,
          productDdtId: productLabels.ddtId,
          productExpiryDate: productLabels.expiryDate,
          productLot: productLabels.lot,
          productCreatedAt: productLabels.createdAt,
          
          // Association fields
          associationId: containerProducts.id,
          associationCreatedBy: containerProducts.createdBy
        })
        .from(containers)
        .leftJoin(containerProducts, eq(containers.id, containerProducts.containerId))
        .leftJoin(productLabels, eq(containerProducts.productLabelId, productLabels.id))
        .where(eq(containers.isArchived, false));

      // Raggruppa i risultati per container
      const containersMap = new Map<string, any>();
      
      for (const row of result) {
        if (!containersMap.has(row.containerId)) {
          containersMap.set(row.containerId, {
            id: row.containerId,
            name: row.containerName,
            type: row.containerType,
            maxItems: row.containerMaxItems,
            currentItems: row.containerCurrentItems,
            createdAt: row.containerCreatedAt,
            isArchived: row.containerIsArchived,
            tenantId: row.containerTenantId,
            products: []
          });
        }

        // Aggiungi prodotto se presente
        if (row.productId) {
          const container = containersMap.get(row.containerId);
          container.products.push({
            id: row.productId,
            productName: row.productName,
            ddtId: row.productDdtId,
            expiryDate: row.productExpiryDate,
            lot: row.productLot,
            createdAt: row.productCreatedAt,
            association: {
              id: row.associationId,
              createdBy: row.associationCreatedBy
            }
          });
        }
      }

      const containers_with_products = Array.from(containersMap.values());
      
      // Tracking performance
      const executionTime = performance.now() - startTime;
      this.recordPerformanceMetric('getContainersWithProducts', executionTime, containers_with_products.length);
      
      logger.info('Query ottimizzata containers con products', { 
        count: containers_with_products.length, 
        executionTime: `${executionTime.toFixed(2)}ms` 
      });
      
      return containers_with_products;
      
    } catch (error) {
      logger.error('Errore in getContainersWithProducts:', error);
      throw error;
    }
  }

  /**
   * OTTIMIZZAZIONE CRITICA: Activity Logs con User Details (elimina N+1)
   * Prima: 1 query per logs + N query per user details = N+1 queries  
   * Dopo: 1 query con JOIN = 1 query totale
   */
  async getActivityLogsWithUserDetails(limit: number = 50): Promise<any[]> {
    const startTime = performance.now();
    
    try {
      const result = await db
        .select({
          // Activity log fields
          logId: activityLogs.id,
          logAction: activityLogs.action,
          logDetails: activityLogs.details,
          logTimestamp: activityLogs.timestamp,
          logTenantId: activityLogs.tenantId,
          logContainerId: activityLogs.containerId,
          
          // User fields
          userId: users.id,
          userName: users.username,
          userUsername: users.username,
          userRole: users.role,
          
          // Container name (if present)
          containerName: containers.name,
        })
        .from(activityLogs)
        .leftJoin(users, eq(activityLogs.userId, users.id))
        .leftJoin(containers, eq(activityLogs.containerId, containers.id))
        .orderBy(sql`${activityLogs.timestamp} DESC`)
        .limit(limit);

      const enrichedLogs = result.map(row => ({
        id: row.logId,
        action: row.logAction,
        details: row.logDetails,
        timestamp: row.logTimestamp,
        tenantId: row.logTenantId,
        containerId: row.logContainerId,
        user: row.userId ? {
          id: row.userId,
          username: row.userName,
          role: row.userRole
        } : null,
        container: row.containerName ? {
          id: row.logContainerId,
          name: row.containerName
        } : null
      }));

      const executionTime = performance.now() - startTime;
      this.recordPerformanceMetric('getActivityLogsWithUserDetails', executionTime, enrichedLogs.length);
      
      logger.info('Query ottimizzata activity logs con user details', { 
        count: enrichedLogs.length, 
        executionTime: `${executionTime.toFixed(2)}ms` 
      });
      
      return enrichedLogs;
      
    } catch (error) {
      logger.error('Errore in getActivityLogsWithUserDetails:', error);
      throw error;
    }
  }

  /**
   * BATCH LOADING: Products by Container IDs (elimina N+1)
   * Invece di N query separate, una sola query con IN clause
   */
  async getProductsByContainerIds(containerIds: string[]): Promise<Map<string, any[]>> {
    if (containerIds.length === 0) return new Map();
    
    const startTime = performance.now();
    
    try {
      const result = await db
        .select({
          containerId: containerProducts.containerId,
          productId: productLabels.id,
          productName: productLabels.productName,
          productExpiryDate: productLabels.expiryDate,
          productLot: productLabels.lot,
          createdBy: containerProducts.createdBy
        })
        .from(containerProducts)
        .innerJoin(productLabels, eq(containerProducts.productLabelId, productLabels.id))
        .where(inArray(containerProducts.containerId, containerIds));

      // Raggruppa per container ID
      const productsByContainer = new Map<string, any[]>();
      
      for (const row of result) {
        if (!productsByContainer.has(row.containerId)) {
          productsByContainer.set(row.containerId, []);
        }
        
        productsByContainer.get(row.containerId)!.push({
          id: row.productId,
          productName: row.productName,
          expiryDate: row.productExpiryDate,
          lot: row.productLot,
          createdBy: row.createdBy
        });
      }

      const executionTime = performance.now() - startTime;
      this.recordPerformanceMetric('getProductsByContainerIds', executionTime, result.length);
      
      logger.info('Batch loading products per containers', { 
        products: result.length, 
        containers: containerIds.length, 
        executionTime: `${executionTime.toFixed(2)}ms` 
      });
      
      return productsByContainer;
      
    } catch (error) {
      logger.error('Errore in getProductsByContainerIds:', error);
      throw error;
    }
  }

  /**
   * OTTIMIZZAZIONE DDT: DDT con Supplier Details (elimina N+1)
   */
  async getDDTsWithSupplierDetails(limit: number = 100): Promise<any[]> {
    const startTime = performance.now();
    
    try {
      const result = await db
        .select({
          // DDT fields
          ddtId: ddts.id,
          ddtNumber: ddts.number,
          ddtDate: ddts.date,
          ddtCreatedAt: ddts.createdAt,
          ddtTenantId: ddts.tenantId,
          
          // Supplier fields
          supplierId: suppliers.id,
          supplierCompanyName: suppliers.companyName,
          supplierVatNumber: suppliers.vatNumber,
          supplierAddress: suppliers.address
        })
        .from(ddts)
        .leftJoin(suppliers, eq(ddts.supplierId, suppliers.id))
        .orderBy(sql`${ddts.createdAt} DESC`)
        .limit(limit);

      const enrichedDDTs = result.map(row => ({
        id: row.ddtId,
        number: row.ddtNumber,
        date: row.ddtDate,
        createdAt: row.ddtCreatedAt,
        tenantId: row.ddtTenantId,
        supplier: row.supplierId ? {
          id: row.supplierId,
          companyName: row.supplierCompanyName,
          vatNumber: row.supplierVatNumber,
          address: row.supplierAddress
        } : null
      }));

      const executionTime = performance.now() - startTime;
      this.recordPerformanceMetric('getDDTsWithSupplierDetails', executionTime, enrichedDDTs.length);
      
      logger.info('Query ottimizzata DDTs con supplier details', { 
        count: enrichedDDTs.length, 
        executionTime: `${executionTime.toFixed(2)}ms` 
      });
      
      return enrichedDDTs;
      
    } catch (error) {
      logger.error('Errore in getDDTsWithSupplierDetails:', error);
      throw error;
    }
  }

  /**
   * CACHE LAYER: Query caching per richieste frequenti
   */
  async getCachedQuery<T>(cacheKey: string, queryFn: () => Promise<T>, ttl: number = this.CACHE_TTL): Promise<T> {
    const cached = this.queryCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
      logger.debug(`🎯 Cache hit per ${cacheKey}`);
      return cached.data;
    }

    const startTime = performance.now();
    const result = await queryFn();
    const executionTime = performance.now() - startTime;
    
    // Salva in cache
    this.queryCache.set(cacheKey, {
      data: result,
      timestamp: Date.now(),
      ttl
    });

    logger.info('Query cached', { cacheKey, executionTime: `${executionTime.toFixed(2)}ms` });
    
    return result;
  }

  /**
   * PERFORMANCE MONITORING
   */
  private recordPerformanceMetric(queryType: string, executionTime: number, rowsReturned: number) {
    this.performanceMetrics.push({
      queryType,
      executionTime,
      rowsReturned,
      timestamp: new Date()
    });

    // Mantieni solo le ultime 1000 metriche
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }

    // Log warning per query lente
    if (executionTime > 500) {
      logger.warn(`⚠️ Query lenta rilevata: ${queryType} - ${executionTime.toFixed(2)}ms`);
    }
  }

  /**
   * ANALISI PERFORMANCE
   */
  getPerformanceReport() {
    const metrics = this.performanceMetrics;
    if (metrics.length === 0) return null;

    const queryTypes = [...new Set(metrics.map(m => m.queryType))];
    const report: any = {
      totalQueries: metrics.length,
      queryTypes: {},
      averageExecutionTime: metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length,
      slowQueries: metrics.filter(m => m.executionTime > 500).length
    };

    queryTypes.forEach(type => {
      const typeMetrics = metrics.filter(m => m.queryType === type);
      report.queryTypes[type] = {
        count: typeMetrics.length,
        averageTime: typeMetrics.reduce((sum, m) => sum + m.executionTime, 0) / typeMetrics.length,
        averageRows: typeMetrics.reduce((sum, m) => sum + m.rowsReturned, 0) / typeMetrics.length
      };
    });

    return report;
  }

  /**
   * SUGGERIMENTI INDICI DATABASE
   */
  getDatabaseIndexSuggestions(): string[] {
    return [
      // Indici per eliminare N+1 problems
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_container_products_container_id ON container_products(container_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_container_products_product_label_id ON container_products(product_label_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_container_id ON activity_logs(container_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_timestamp ON activity_logs(timestamp DESC);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ddts_supplier_id ON ddts(supplier_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_containers_tenant_id ON containers(tenant_id);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_labels_ddt_id ON product_labels(ddt_id);",
      
      // Indici compositi per query complesse
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_containers_tenant_archived ON containers(tenant_id, is_archived);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_tenant_timestamp ON activity_logs(tenant_id, timestamp DESC);",
      "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_labels_tenant_retired ON product_labels(tenant_id, is_retired);"
    ];
  }

  /**
   * PULIZIA CACHE
   */
  clearCache() {
    this.queryCache.clear();
    logger.info('🧹 Cache delle query pulita');
  }
}

// Singleton instance
export const queryOptimizer = new DatabaseQueryOptimizer();

/**
 * MIDDLEWARE PER QUERY OPTIMIZATION
 */
export function withQueryOptimization<T extends any[], R>(
  queryFn: (...args: T) => Promise<R>,
  cacheKey?: string,
  ttl?: number
) {
  return async (...args: T): Promise<R> => {
    if (cacheKey) {
      return queryOptimizer.getCachedQuery(
        `${cacheKey}_${JSON.stringify(args)}`,
        () => queryFn(...args),
        ttl
      );
    }
    return queryFn(...args);
  };
}