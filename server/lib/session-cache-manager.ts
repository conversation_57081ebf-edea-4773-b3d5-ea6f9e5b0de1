/**
 * Session Cache Manager
 * Ottimizza la deserializzazione delle sessioni con caching intelligente
 * Risolve il problema di 12-15 deserializzazioni al secondo per lo stesso utente
 */

import { User } from '../../shared/schema';
import { logger } from './logger';

interface CachedSession {
  user: User;
  timestamp: number;
  accessCount: number;
}

interface AuditRateLimit {
  lastLog: number;
  count: number;
}

export class SessionCacheManager {
  private static instance: SessionCacheManager;
  private sessionCache = new Map<string, CachedSession>();
  private auditRateLimit = new Map<string, AuditRateLimit>();
  
  // Configuration - Optimized for performance
  private readonly SESSION_CACHE_TTL = 10000; // 10 secondi (doubled for less frequent lookups)
  private readonly AUDIT_COOLDOWN = 5000; // 5 secondi tra audit dello stesso tipo (increased)
  private readonly MAX_CACHE_SIZE = 1000; // Limite cache per evitare memory leak
  private readonly CLEANUP_INTERVAL = 30000; // Cleanup ogni 30 secondi

  private constructor() {
    // Avvia cleanup periodico
    this.startPeriodicCleanup();
    logger.info('SessionCacheManager initialized with intelligent caching');
  }

  public static getInstance(): SessionCacheManager {
    if (!SessionCacheManager.instance) {
      SessionCacheManager.instance = new SessionCacheManager();
    }
    return SessionCacheManager.instance;
  }

  /**
   * Recupera utente dalla cache o esegue lookup se necessario
   */
  public async getCachedUser(
    userId: string, 
    lookupFunction: () => Promise<User | undefined>
  ): Promise<User | undefined> {
    try {
      // Controlla cache
      const cached = this.sessionCache.get(userId);
      
      if (cached && this.isCacheValid(cached)) {
        // Incrementa contatore accessi
        cached.accessCount++;
        
        // Log solo se necessario (rate limited)
        this.logWithRateLimit('cache_hit', userId, cached.user);
        
        return cached.user;
      }

      // Cache miss o expired - esegui lookup
      const user = await lookupFunction();
      
      if (user) {
        // Salva in cache
        this.cacheUser(userId, user);
        
        // Log cache miss
        this.logWithRateLimit('cache_miss', userId, user);
      }

      return user;
    } catch (error) {
      logger.error(`Session cache error for user ${userId}:`, { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return undefined;
    }
  }

  /**
   * Verifica se la cache è ancora valida
   */
  private isCacheValid(cached: CachedSession): boolean {
    return Date.now() - cached.timestamp < this.SESSION_CACHE_TTL;
  }

  /**
   * Salva utente in cache con gestione dimensioni
   */
  private cacheUser(userId: string, user: User): void {
    // Controlla dimensioni cache
    if (this.sessionCache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntries();
    }

    this.sessionCache.set(userId, {
      user: { ...user }, // Clone per evitare riferimenti
      timestamp: Date.now(),
      accessCount: 1
    });
  }

  /**
   * Logging con rate limiting per evitare spam
   */
  private logWithRateLimit(eventType: string, userId: string, user: User): void {
    const key = `${eventType}-${userId}`;
    const now = Date.now();
    const rateLimit = this.auditRateLimit.get(key);

    if (!rateLimit || now - rateLimit.lastLog > this.AUDIT_COOLDOWN) {
      // Log audit event
      if (eventType === 'cache_hit') {
        logger.debug(`[SESSION-CACHE] Hit for user ${user.username} (${userId})`);
      } else {
        // Throttled logging in development to reduce console noise
        if (process.env.NODE_ENV === 'production') {
          logger.info(`Secure user lookup successful for ${user.username} (ID: ${userId}, Tenant: ${user.tenantId})`);
          logger.debug(`[AUTH-AUDIT] ${now} - session_deserialization - User: ${userId} { username: '${user.username}', tenant: '${user.tenantId}' }`);
        } else {
          // In development, log less frequently to reduce performance impact
          logger.debug(`[SESSION-CACHE] ${eventType === 'cache_hit' ? 'Hit' : 'Lookup'} for ${user.username}`);
        }
      }

      this.auditRateLimit.set(key, {
        lastLog: now,
        count: 1
      });
    } else {
      // Incrementa contatore senza logging
      rateLimit.count++;
    }
  }

  /**
   * Rimuove le voci più vecchie dalla cache
   */
  private evictOldestEntries(): void {
    const entries = Array.from(this.sessionCache.entries());
    
    // Ordina per timestamp (più vecchie prima)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // Rimuovi 20% delle voci più vecchie
    const toRemove = Math.floor(entries.length * 0.2);
    
    for (let i = 0; i < toRemove; i++) {
      this.sessionCache.delete(entries[i][0]);
    }

    logger.debug(`[SESSION-CACHE] Evicted ${toRemove} old entries`);
  }

  /**
   * Cleanup periodico per rimuovere voci scadute
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Rimuove voci scadute dalla cache
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [userId, cached] of this.sessionCache.entries()) {
      if (now - cached.timestamp > this.SESSION_CACHE_TTL) {
        this.sessionCache.delete(userId);
        cleaned++;
      }
    }

    // Cleanup audit rate limits
    for (const [key, rateLimit] of this.auditRateLimit.entries()) {
      if (now - rateLimit.lastLog > this.AUDIT_COOLDOWN * 5) {
        this.auditRateLimit.delete(key);
      }
    }

    if (cleaned > 0) {
      logger.debug(`[SESSION-CACHE] Cleaned ${cleaned} expired entries`);
    }
  }

  /**
   * Invalida cache per un utente specifico
   */
  public invalidateUser(userId: string): void {
    this.sessionCache.delete(userId);
    logger.debug(`[SESSION-CACHE] Invalidated cache for user ${userId}`);
  }

  /**
   * Statistiche cache per monitoring
   */
  public getCacheStats(): {
    size: number;
    hitRate: number;
    totalAccesses: number;
  } {
    let totalAccesses = 0;
    let hitCount = 0;

    for (const [key, rateLimit] of this.auditRateLimit.entries()) {
      if (key.startsWith('cache_hit-')) {
        hitCount += rateLimit.count;
      }
      totalAccesses += rateLimit.count;
    }

    return {
      size: this.sessionCache.size,
      hitRate: totalAccesses > 0 ? (hitCount / totalAccesses) * 100 : 0,
      totalAccesses
    };
  }

  /**
   * Pulisce completamente la cache (per testing o reset)
   */
  public clearCache(): void {
    this.sessionCache.clear();
    this.auditRateLimit.clear();
    logger.info('[SESSION-CACHE] Cache cleared completely');
  }
}

// Export singleton instance
export const sessionCacheManager = SessionCacheManager.getInstance();