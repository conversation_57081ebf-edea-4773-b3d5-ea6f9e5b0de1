import Anthropic from "@anthropic-ai/sdk";
import { aiManager } from "./ai-manager";

interface OCRResult {
  success: boolean;
  data?: any;
  error?: string;
}

// Modelli AI disponibili - identificatori corretti dall'API Anthropic (aggiornato giugno 2025)
export const CLAUDE_MODELS = {
  // Claude 4.0 - Famiglia più recente (UFFICIALMENTE DISPONIBILE)
  CLAUDE_4_OPUS: "claude-opus-4-20250514",
  CLAUDE_4_SONNET: "claude-sonnet-4-20250514",  
  CLAUDE_4_HAIKU: "claude-4-haiku-20250514",
  
  // <PERSON><PERSON> ufficiali Claude 4.0 (dalla documentazione Anthropic)
  CLAUDE_4_OPUS_ALIAS: "claude-opus-4-0",
  CLAUDE_4_SONNET_ALIAS: "claude-sonnet-4-0",
  
  // Claude 3.5 - <PERSON>li più recenti (2024)
  CLAUDE_35_SONNET_LATEST: "claude-3-5-sonnet-20241022",
  CLAUDE_35_SONNET_JUNE: "claude-3-5-sonnet-20240620",
  CLAUDE_35_HAIKU_LATEST: "claude-3-5-haiku-20241022",
  
  // Claude 3.5 Sonnet v2 (dicembre 2024)
  CLAUDE_35_SONNET_V2: "claude-3-5-sonnet-20241213",
  CLAUDE_35_HAIKU_V2: "claude-3-5-haiku-20241213",

  // Claude 3 - Modelli stabili
  CLAUDE_3_OPUS: "claude-3-opus-20240229",
  CLAUDE_3_SONNET: "claude-3-sonnet-20240229", 
  CLAUDE_3_HAIKU: "claude-3-haiku-20240307",
  
  // Versioni legacy per compatibilità
  CLAUDE_35_SONNET_LEGACY: "claude-3-5-sonnet-20240229",
  CLAUDE_3_HAIKU_OLD: "claude-3-haiku-20240229"
};

// Modello predefinito - Claude 4.0 Sonnet (migrazione dalla 3.5)
export const DEFAULT_CLAUDE_MODEL = CLAUDE_MODELS.CLAUDE_4_SONNET;

// Funzione per ottenere tutti i modelli Claude disponibili
export function getAvailableClaudeModels() {
  return [
    { id: CLAUDE_MODELS.CLAUDE_4_OPUS, name: "Claude 4 Opus (Newest)", description: "Il modello più potente della famiglia Claude 4.0" },
    { id: CLAUDE_MODELS.CLAUDE_4_SONNET, name: "Claude 4 Sonnet (Newest)", description: "Bilanciato e versatile della famiglia Claude 4.0" },
    { id: CLAUDE_MODELS.CLAUDE_4_HAIKU, name: "Claude 4 Haiku (Newest)", description: "Veloce ed efficiente della famiglia Claude 4.0" },
    { id: CLAUDE_MODELS.CLAUDE_35_SONNET_V2, name: "Claude 3.5 Sonnet v2", description: "Il modello più recente e avanzato di dicembre 2024" },
    { id: CLAUDE_MODELS.CLAUDE_35_HAIKU_V2, name: "Claude 3.5 Haiku v2", description: "Versione più recente veloce ed efficiente di dicembre 2024" },
    { id: CLAUDE_MODELS.CLAUDE_35_SONNET_LATEST, name: "Claude 3.5 Sonnet (Latest)", description: "Il modello più avanzato per ragionamento complesso" },
    { id: CLAUDE_MODELS.CLAUDE_35_SONNET_JUNE, name: "Claude 3.5 Sonnet (June)", description: "Versione stabile di Claude 3.5 Sonnet" },
    { id: CLAUDE_MODELS.CLAUDE_35_HAIKU_LATEST, name: "Claude 3.5 Haiku (Latest)", description: "Veloce ed efficiente per compiti semplici" },
    { id: CLAUDE_MODELS.CLAUDE_3_OPUS, name: "Claude 3 Opus", description: "Massima capacità per compiti complessi" },
    { id: CLAUDE_MODELS.CLAUDE_3_SONNET, name: "Claude 3 Sonnet", description: "Bilanciato tra velocità e capacità" },
    { id: CLAUDE_MODELS.CLAUDE_3_HAIKU, name: "Claude 3 Haiku", description: "Veloce e economico per compiti semplici" },
    { id: CLAUDE_MODELS.CLAUDE_35_SONNET_LEGACY, name: "Claude 3.5 Sonnet (Legacy)", description: "Versione precedente per compatibilità" },
    { id: CLAUDE_MODELS.CLAUDE_3_HAIKU_OLD, name: "Claude 3 Haiku (Old)", description: "Versione precedente per compatibilità" }
  ];
}

// Inizializzazione sicura del client Anthropic
function initializeAnthropic(): Anthropic {
  const apiKey = process.env.ANTHROPIC_API_KEY;
  
  if (!apiKey) {
    const errorMsg = "ANTHROPIC_API_KEY not configured - Claude features disabled";
    console.warn(`🔑 ${errorMsg}`);
    throw new Error(errorMsg);
  }

  // Validate API key format for security
  if (!apiKey.startsWith('sk-ant-') || apiKey.length < 50) {
    const errorMsg = "Invalid ANTHROPIC_API_KEY format - check your configuration";
    console.error(`🚨 SECURITY: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  return new Anthropic({
    apiKey: apiKey,
    // Additional security headers
    maxRetries: 3,
    timeout: 30000, // 30 second timeout
  });
}

// Lazy initialization for better error handling
let anthropic: Anthropic | null = null;
function getAnthropicClient(): Anthropic {
  if (!anthropic) {
    anthropic = initializeAnthropic();
  }
  return anthropic;
}

// Estrae JSON dal testo con sicurezza migliorata
async function extractJsonFromText(text: string, defaultData: any = null): Promise<any> {
  try {
    // Cerca di trovare un blocco JSON nel testo
    const jsonRegex = /\{[\s\S]*\}/;
    const match = text.match(jsonRegex);

    if (match) {
      const jsonStr = match[0];
      
      // Limite di sicurezza per evitare JSON molto grandi
      if (jsonStr.length > 100000) {
        console.error("JSON troppo grande per sicurezza:", jsonStr.length);
        return defaultData;
      }
      
      const { safeJsonParse } = await import('./safe-json');
      const result = safeJsonParse(jsonStr);
      if (!result.success) {
        throw new Error(`JSON parsing failed: ${result.error}`);
      }
      return result.data;
    }
    return defaultData;
  } catch (e) {
    // Log sicuro senza esporre dati sensibili
    console.error("Errore nell'estrazione JSON:", e instanceof Error ? e.message : 'Unknown error');
    return defaultData;
  }
}

// Interfaccia per i prompt
export interface AIPrompt {
  id: string;
  name: string;
  content: string;
  description?: string;
  category: "ddt" | "label" | "general";
  isDefault?: boolean;
}

// Prompt predefiniti
export const defaultPrompts: AIPrompt[] = [
  {
    id: "default_ddt_1",
    name: "Estrazione DDT Standard",
    content: "Sei un assistente esperto nell'analisi di documenti di trasporto (DDT). Estrai le seguenti informazioni dal documento fornito e restituisci ESCLUSIVAMENTE un oggetto JSON con ESATTAMENTE QUESTI NOMI DI CAMPI (importantissimo non alterare i nomi dei campi):\n- rag_soc_ddt: la ragione sociale del fornitore (mittente)\n- partita_iva_ddt: la partita IVA del fornitore\n- indirizzo_ddt: l'indirizzo completo del fornitore\n- numero_ddt: il numero identificativo del DDT\n- data_ddt: la data del DDT. MOLTO IMPORTANTE: questa data deve SEMPRE essere rappresentata nel formato GG/MM/YY (giorno/mese/anno a 2 cifre), ad esempio: 08/04/25. In Italia le date sono sempre scritte nel formato GG/MM. Non invertire mai giorno e mese. Se vedi una data come 08/04/25, deve rimanere in questo formato.\n\nRispondi SOLO con l'oggetto JSON, senza altro testo o spiegazioni. È FONDAMENTALE che i nomi dei campi siano ESATTAMENTE quelli specificati sopra. Per esempio: { \"rag_soc_ddt\": \"Nome Azienda\", \"data_ddt\": \"08/04/25\", ... }. Se non riesci a identificare un'informazione, lascia il campo corrispondente come stringa vuota.",
    category: "ddt",
    isDefault: true,
    description: "Estrae le informazioni principali da un documento di trasporto standard"
  },
  {
    id: "default_label_1",
    name: "Estrazione Etichetta Prodotto",
    content: "Sei un assistente esperto nell'analisi di etichette alimentari. Estrai le seguenti informazioni dall'etichetta fornita e restituisci ESCLUSIVAMENTE un oggetto JSON con ESATTAMENTE QUESTI NOMI DI CAMPI (importantissimo non alterare i nomi dei campi):\n- productName: il nome del prodotto\n- batchNumber: il numero di lotto/batch\n- expiryDate: la data di scadenza nel formato GG/MM/AAAA\n- storageInstructions: requisiti di conservazione\n- notes: informazioni aggiuntive come valori nutrizionali, ingredienti, dichiarazioni, peso, ricette e altre informazioni presenti sull'etichetta. Riporta tabelle in formato leggibile usando | per separare colonne e a capo per righe. Usa punti elenco quando appropriato.\n\nRispondi SOLO con l'oggetto JSON, senza altro testo o spiegazioni. È FONDAMENTALE che i nomi dei campi siano ESATTAMENTE quelli specificati sopra. Per esempio: { \"productName\": \"Nome Prodotto\", \"notes\": \"Valori nutrizionali: Energia 120kJ | Proteine 2.5g | Carboidrati 15g\", ... }. Se non riesci a identificare un'informazione, lascia il campo corrispondente come stringa vuota.",
    category: "label",
    isDefault: true,
    description: "Estrae le informazioni principali da un'etichetta di prodotto alimentare incluse note aggiuntive"
  },
  {
    id: "default_general_1",
    name: "Analisi Documento Generale",
    content: `Analizza il documento fornito e restituisci le informazioni principali in un oggetto JSON con campi appropriati. Cerca di identificare il tipo di documento e di estrarre le informazioni più rilevanti come date, nomi, codici identificativi, quantità e prezzi.\n\nRispondi SOLO con l'oggetto JSON, senza altro testo.`,
    category: "general",
    isDefault: true,
    description: "Analisi generale di un documento non specificato"
  }
];

// Memorizza i prompt (in memoria per ora)
let prompts: AIPrompt[] = [...defaultPrompts];

// Funzioni di gestione prompt - aggiornate per usare il database
export async function getAllPrompts(): Promise<AIPrompt[]> {
  try {
    const { storage } = await import('../storage');
    const dbPrompts = await storage.getAllClaudePrompts();
    console.log('DB Prompts raw:', dbPrompts);
    const mappedPrompts = dbPrompts.map(p => ({
      id: p.id,
      name: p.name,
      content: p.content,
      description: p.description || '',
      category: p.category as "ddt" | "label" | "general",
      isDefault: p.isDefault || false
    }));
    console.log('Mapped prompts:', mappedPrompts);
    return mappedPrompts;
  } catch (error) {
    console.error('Error in getAllPrompts:', error);
    return [];
  }
}

export async function getPromptsByCategory(category: string): Promise<AIPrompt[]> {
  const { storage } = await import('../storage');
  const dbPrompts = await storage.getClaudePromptsByCategory(category);
  return dbPrompts.map(p => ({
    id: p.id,
    name: p.name,
    content: p.content,
    description: p.description || '',
    category: p.category as "ddt" | "label" | "general",
    isDefault: p.isDefault
  }));
}

export async function addPrompt(prompt: AIPrompt): Promise<AIPrompt> {
  const { storage } = await import('../storage');

  // Genera ID se non presente
  if (!prompt.id) {
    prompt.id = `prompt_${Date.now()}`;
  }

  // Se il nuovo prompt viene impostato come predefinito, disattiva tutti gli altri predefiniti della stessa categoria
  if (prompt.isDefault) {
    const allPrompts = await storage.getAllClaudePrompts();
    for (const existingPrompt of allPrompts) {
      if (existingPrompt.category === prompt.category && existingPrompt.isDefault) {
        await storage.updateClaudePrompt(existingPrompt.id, {
          name: existingPrompt.name,
          content: existingPrompt.content,
          description: existingPrompt.description,
          category: existingPrompt.category,
          isDefault: false
        });
      }
    }
  }

  const newPrompt = await storage.createClaudePrompt({
    id: prompt.id,
    name: prompt.name,
    content: prompt.content,
    description: prompt.description,
    category: prompt.category,
    isDefault: prompt.isDefault
  });

  return {
    id: newPrompt.id,
    name: newPrompt.name,
    content: newPrompt.content,
    description: newPrompt.description || '',
    category: newPrompt.category as "ddt" | "label" | "general",
    isDefault: newPrompt.isDefault
  };
}

export async function updatePrompt(id: string, updatedPrompt: AIPrompt): Promise<AIPrompt | null> {
  const { storage } = await import('../storage');

  try {
    // Se il prompt viene impostato come predefinito, disattiva tutti gli altri predefiniti della stessa categoria
    if (updatedPrompt.isDefault) {
      console.log(`[DEBUG] Impostando prompt ${id} come default per categoria ${updatedPrompt.category}`);
      const allPrompts = await storage.getAllClaudePrompts();
      console.log(`[DEBUG] Trovati ${allPrompts.length} prompt totali`);

      for (const prompt of allPrompts) {
        if (prompt.category === updatedPrompt.category && prompt.id !== id && prompt.isDefault) {
          console.log(`[DEBUG] Disattivando prompt ${prompt.id} (${prompt.name}) come default`);
          await storage.updateClaudePrompt(prompt.id, {
            name: prompt.name,
            content: prompt.content,
            description: prompt.description,
            category: prompt.category,
            isDefault: false
          });
        }
      }
    }

    const updated = await storage.updateClaudePrompt(id, {
      name: updatedPrompt.name,
      content: updatedPrompt.content,
      description: updatedPrompt.description,
      category: updatedPrompt.category,
      isDefault: updatedPrompt.isDefault
    });

    return {
      id: updated.id,
      name: updated.name,
      content: updated.content,
      description: updated.description || '',
      category: updated.category as "ddt" | "label" | "general",
      isDefault: updated.isDefault
    };
  } catch (error) {
    console.error('Errore nell\'aggiornamento del prompt:', error);
    return null;
  }
}

export async function deletePrompt(id: string): Promise<boolean> {
  const { storage } = await import('../storage');

  try {
    await storage.deleteClaudePrompt(id);
    return true;
  } catch (error) {
    console.error('Errore nella cancellazione del prompt:', error);
    return false;
  }
}

export async function resetDefaultPrompts(): Promise<void> {
  const { storage } = await import('../storage');

  // Cancella tutti i prompt esistenti
  const allPrompts = await storage.getAllClaudePrompts();
  for (const prompt of allPrompts) {
    await storage.deleteClaudePrompt(prompt.id);
  }

  // Aggiungi nuovamente i prompt predefiniti
  for (const defaultPrompt of defaultPrompts) {
    await storage.createClaudePrompt({
      id: defaultPrompt.id,
      name: defaultPrompt.name,
      content: defaultPrompt.content,
      description: defaultPrompt.description,
      category: defaultPrompt.category,
      isDefault: defaultPrompt.isDefault
    });
  }
}

// Recupera il prompt dal storage
export async function getPromptFromStorage(category: string): Promise<string> {
  // Cerca i prompt per la categoria dal database
  const categoryPrompts = await getPromptsByCategory(category);
  if (categoryPrompts.length > 0) {
    // Cerca prima il prompt predefinito (is_default = true)
    let selectedPrompt = categoryPrompts.find(p => p.isDefault);

    // Se non c'è un prompt predefinito, usa il primo disponibile
    if (!selectedPrompt) {
      selectedPrompt = categoryPrompts[0];
    }

    console.log(`Usando prompt per categoria ${category}: ${selectedPrompt.name}`);

    // Verifica che il prompt contenga i campi attesi per garantire compatibilità col frontend
    if (category === "ddt") {
      const requiredFields = ["rag_soc_ddt", "partita_iva_ddt", "indirizzo_ddt", "numero_ddt", "data_ddt"];
      const prompt = selectedPrompt.content;
      const allFieldsPresent = requiredFields.every(field => prompt.includes(field));

      if (!allFieldsPresent) {
        console.warn("Attenzione: il prompt personalizzato potrebbe non contenere tutti i campi richiesti.");

        // Fallback al prompt predefinito se quello personalizzato non contiene tutti i campi
        const defaultDDTPrompt = defaultPrompts.find(p => p.id === "default_ddt_1");
        if (defaultDDTPrompt) {
          console.log("Utilizzando il prompt DDT predefinito come fallback");
          return defaultDDTPrompt.content;
        }
      }
    } else if (category === "label") {
      // Per le etichette, assicuriamo che contenga i campi necessari
      const requiredFields = ["productName", "expiryDate", "batchNumber", "storageInstructions", "notes"];
      const prompt = selectedPrompt.content;
      const allFieldsPresent = requiredFields.every(field => prompt.includes(field));

      if (!allFieldsPresent) {
        console.warn("Attenzione: il prompt personalizzato per etichette potrebbe non contenere tutti i campi richiesti.");

        // Fallback al prompt predefinito se quello personalizzato non contiene tutti i campi
        const defaultLabelPrompt = defaultPrompts.find(p => p.id === "default_label_1");
        if (defaultLabelPrompt) {
          console.log("Utilizzando il prompt etichetta predefinito come fallback");
          return defaultLabelPrompt.content;
        }
      }
    }

    return selectedPrompt.content;
  }

  // Se non ci sono prompt per questa categoria, usa quelli predefiniti
  const defaultPrompt = defaultPrompts.find(p => p.category === category && p.isDefault);
  if (defaultPrompt) {
    console.log(`Usando prompt predefinito per categoria ${category}: ${defaultPrompt.name}`);
    return defaultPrompt.content;
  }

  // Se ancora non trova nulla, lancia un errore
  throw new Error(`Nessun prompt trovato per la categoria: ${category}`);
}

// Ottieni il modello Claude dalle impostazioni globali AI
export async function getSelectedClaudeModel(): Promise<string> {
  try {
    const { aiManager } = await import('./ai-manager');
    const config = await aiManager.getCurrentAIConfig();
    
    if (config.provider === 'claude') {
      return config.model;
    }
    
    // Se il provider non è Claude, restituisce il modello predefinito Claude
    const settings = await aiManager.getAISettings();
    return settings.claudeProvider.defaultModel;
  } catch (error) {
    console.error('Errore nel recupero del modello Claude:', error);
    return DEFAULT_CLAUDE_MODEL;
  }
}

// Imposta il modello Claude corrente attraverso AI Manager
export async function setSelectedClaudeModel(modelId: string): Promise<string> {
  try {
    const { aiManager } = await import('./ai-manager');
    
    if (await aiManager.isValidModel('claude', modelId)) {
      await aiManager.updateAISettings({
        claudeProvider: {
          name: 'claude',
          models: [], // Non modifica i modelli disponibili
          defaultModel: modelId
        }
      }, 1); // Admin user ID
      
      return modelId;
    }
    return DEFAULT_CLAUDE_MODEL;
  } catch (error) {
    console.error('Errore nel salvataggio del modello Claude:', error);
    return DEFAULT_CLAUDE_MODEL;
  }
}

// Elabora un'immagine DDT
export async function processDDT(imageBase64: string): Promise<OCRResult> {
  try {
    // Ottieni il prompt per DDT
    const prompt = await getPromptFromStorage("ddt");

    // Assicurati che il base64 sia formattato correttamente
    let cleanBase64 = imageBase64;

    // Rimuovi eventuali prefissi di metadati (es. data:image/jpeg;base64,)
    if (cleanBase64.includes('base64,')) {
      cleanBase64 = cleanBase64.split('base64,')[1];
    }

    console.log("Base64 image length:", cleanBase64.length);

    // Ottieni il modello selezionato
    const selectedModel = await getSelectedClaudeModel();
    console.log("Usando modello Claude:", selectedModel);

    // Richiesta a Claude con client sicuro
    const client = getAnthropicClient();
    const response = await client.messages.create({
      model: selectedModel,
      max_tokens: 1024,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/jpeg", 
                data: cleanBase64
              }
            }
          ]
        }
      ],
    });

    // Estrai il JSON dalla risposta
    const content = response.content[0];
    if (content.type === 'text') {
      const jsonData = await extractJsonFromText(content.text, {});

      return {
        success: true,
        data: jsonData
      };
    } else {
      return {
        success: false,
        error: "Risposta non valida da Claude AI"
      };
    }
  } catch (error: any) {
    console.error("Errore nell'elaborazione del DDT:", error);
    return {
      success: false,
      error: `Errore nell'elaborazione dell'immagine: ${error?.message || 'Errore sconosciuto'}`
    };
  }
}

// Verifica disponibilità di un modello Claude con retry intelligente
async function testClaudeModel(modelId: string): Promise<boolean> {
  const maxRetries = 3;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const client = getAnthropicClient();
      const testMessage = await client.messages.create({
        model: modelId,
        max_tokens: 10,
        messages: [{ role: "user", content: "test" }]
      });
      return true;
    } catch (error: any) {
      // Modello non trovato - non ritentare
      if (error.status === 404 && error.error?.type === 'not_found_error') {
        return false;
      }
      
      // Rate limit - implementa exponential backoff
      if (error.status === 429) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        console.log(`Rate limit raggiunto per ${modelId}, attendo ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // Altri errori API - ritenta solo se non è l'ultimo tentativo
      if (attempt < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
    }
  }
  
  return false;
}

// Trova il primo modello Claude disponibile (priorità a Claude 4.0)
async function getWorkingClaudeModel(): Promise<string> {
  const modelsToTest = [
    // Priorità assoluta a Claude 4.0 quando disponibile
    CLAUDE_MODELS.CLAUDE_4_SONNET,
    CLAUDE_MODELS.CLAUDE_4_OPUS,
    CLAUDE_MODELS.CLAUDE_4_HAIKU,
    // Fallback ai modelli Claude 3.5 più recenti
    CLAUDE_MODELS.CLAUDE_35_SONNET_V2,
    CLAUDE_MODELS.CLAUDE_35_SONNET_LATEST,
    CLAUDE_MODELS.CLAUDE_35_SONNET_JUNE,
    CLAUDE_MODELS.CLAUDE_3_OPUS,
    CLAUDE_MODELS.CLAUDE_3_SONNET,
    CLAUDE_MODELS.CLAUDE_3_HAIKU
  ];

  for (const model of modelsToTest) {
    console.log(`Testing Claude model: ${model}`);
    if (await testClaudeModel(model)) {
      console.log(`Found working Claude model: ${model}`);
      if (model.includes('claude-4-')) {
        console.log('🎉 Claude 4.0 è ora disponibile!');
      }
      return model;
    }
  }
  
  // Fallback finale
  return CLAUDE_MODELS.CLAUDE_35_SONNET_LATEST;
}

// Elabora più immagini di etichette prodotto
export async function processMultipleProductLabels(images: string[]): Promise<OCRResult> {
  try {
    // Ottieni le configurazioni AI dall'AI Manager
    const aiConfig = await aiManager.getCurrentAIConfig();
    
    // Verifica che sia configurato per usare Claude
    if (aiConfig.provider !== 'claude') {
      console.log('Provider non Claude rilevato per etichette, forzo Claude');
    }

    // Ottieni il prompt per etichetta
    const basePrompt = await getPromptFromStorage("label");
    
    // Modifica il prompt per gestire più immagini
    const multipleImagePrompt = `${basePrompt}

IMPORTANTE: Ti sono state fornite ${images.length} immagini che potrebbero contenere informazioni dello stesso prodotto su diverse parti dell'etichetta. Analizza TUTTE le immagini fornite e combina le informazioni per creare un singolo JSON con i dati più completi possibili. Se trovi informazioni contradittorie, usa quelle che appaiono più chiaramente leggibili o complete.

Le informazioni potrebbero essere distribuite su diverse immagini:
- Una foto potrebbe mostrare il nome del prodotto
- Un'altra la data di scadenza
- Un'altra ancora il numero di lotto o le istruzioni di conservazione

Combina intelligentemente tutte le informazioni disponibili dalle ${images.length} immagini per fornire il JSON più accurato e completo possibile.`;

    console.log(`Usando prompt per ${images.length} etichette:`, multipleImagePrompt.length > 50 ? multipleImagePrompt.substring(0, 50) + "..." : multipleImagePrompt);

    // Pulisci tutte le immagini
    const cleanImages = images.map(imageBase64 => {
      let cleanBase64 = imageBase64;
      // Rimuovi eventuali prefissi di metadati (es. data:image/jpeg;base64,)
      if (cleanBase64.includes('base64,')) {
        cleanBase64 = cleanBase64.split('base64,')[1];
      }
      return cleanBase64;
    });

    console.log(`Base64 images lengths: [${cleanImages.map(img => img.length).join(', ')}]`);

    // Trova un modello Claude funzionante
    const selectedModel = await getWorkingClaudeModel();
    console.log("Usando modello Claude per etichette multiple:", selectedModel);
    
    // Log speciale per Claude 4.0
    if (selectedModel.includes('claude-4-')) {
      console.log('🚀 Claude 4.0 Sonnet in uso per analisi multi-immagine - capacità avanzate abilitate');
    }

    // Prepara il contenuto con testo e tutte le immagini
    const contentArray: any[] = [
      {
        type: "text",
        text: multipleImagePrompt
      }
    ];

    // Aggiungi tutte le immagini al contenuto
    cleanImages.forEach((imageData, index) => {
      contentArray.push({
        type: "image",
        source: {
          type: "base64",
          media_type: "image/jpeg",
          data: imageData
        }
      });
    });

    // Configurazione ottimizzata per Claude 4.0 Sonnet con multiple immagini
    const requestConfig: any = {
      model: selectedModel,
      max_tokens: 3072, // Aumentato per gestire multiple immagini
      messages: [
        {
          role: "user",
          content: contentArray
        }
      ],
    };

    // Ottimizzazioni per Claude 4.0
    if (selectedModel.includes('sonnet-4') || selectedModel.includes('opus-4')) {
      console.log('🚀 Claude 4.0 Sonnet in uso per multi-immagine - capacità avanzate abilitate');
      // Aumentiamo max_tokens per sfruttare le capacità migliorate con multiple immagini
      requestConfig.max_tokens = 5120;
    }

    const client = getAnthropicClient();
    const response = await client.messages.create(requestConfig);

    // Estrai il JSON dalla risposta
    const content = response.content[0];
    if (content.type === 'text') {
      const jsonData = await extractJsonFromText(content.text, {});

      return {
        success: true,
        data: jsonData
      };
    } else {
      return {
        success: false,
        error: "Risposta non valida da Claude AI per multiple immagini"
      };
    }
  } catch (error: any) {
    console.error("Errore nell'elaborazione delle etichette multiple:", error);
    return {
      success: false,
      error: `Errore nell'elaborazione delle immagini: ${error?.message || 'Errore sconosciuto'}`
    };
  }
}

// Elabora un'immagine di etichetta prodotto
export async function processProductLabel(imageBase64: string): Promise<OCRResult> {
  try {
    // Ottieni le configurazioni AI dall'AI Manager
    const aiConfig = await aiManager.getCurrentAIConfig();
    
    // Verifica che sia configurato per usare Claude
    if (aiConfig.provider !== 'claude') {
      console.log('Provider non Claude rilevato per etichette, forzo Claude');
    }

    // Ottieni il prompt per etichetta
    const prompt = await getPromptFromStorage("label");
    console.log("Usando prompt per categoria label:", prompt.length > 50 ? prompt.substring(0, 50) + "..." : prompt);

    // Assicurati che il base64 sia formattato correttamente
    let cleanBase64 = imageBase64;

    // Rimuovi eventuali prefissi di metadati (es. data:image/jpeg;base64,)
    if (cleanBase64.includes('base64,')) {
      cleanBase64 = cleanBase64.split('base64,')[1];
    }

    console.log("Base64 image length (label):", cleanBase64.length);

    // Trova un modello Claude funzionante
    const selectedModel = await getWorkingClaudeModel();
    console.log("Usando modello Claude per etichetta:", selectedModel);
    
    // Log speciale per Claude 4.0
    if (selectedModel.includes('claude-4-')) {
      console.log('✅ Migrazione completata: ora usiamo Claude 4.0 per l\'elaborazione etichette');
    }

    // Configurazione ottimizzata per Claude 4.0 Sonnet
    const requestConfig: any = {
      model: selectedModel,
      max_tokens: 2048, // Aumentato per Claude 4.0
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/jpeg",
                data: cleanBase64
              }
            }
          ]
        }
      ],
    };

    // Ottimizzazioni per Claude 4.0 (Extended Thinking non ancora disponibile in API)
    if (selectedModel.includes('sonnet-4') || selectedModel.includes('opus-4')) {
      console.log('🚀 Claude 4.0 Sonnet in uso - capacità avanzate abilitate');
      // Aumentiamo max_tokens per sfruttare le capacità migliorate
      requestConfig.max_tokens = 4096;
    }

    const client = getAnthropicClient();
    const response = await client.messages.create(requestConfig);

    // Estrai il JSON dalla risposta
    const content = response.content[0];
    if (content.type === 'text') {
      const jsonData = await extractJsonFromText(content.text, {});

      return {
        success: true,
        data: jsonData
      };
    } else {
      return {
        success: false,
        error: "Risposta non valida da Claude AI"
      };
    }
  } catch (error: any) {
    console.error("Errore nell'elaborazione dell'etichetta:", error);
    return {
      success: false,
      error: `Errore nell'elaborazione dell'immagine: ${error?.message || 'Errore sconosciuto'}`
    };
  }
}