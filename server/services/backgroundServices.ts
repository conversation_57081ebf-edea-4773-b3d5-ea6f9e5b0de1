/**
 * Servizi di background per HACCP Tracker
 * Include archiviazione automatica, pulizia dati e manutenzione
 */

import { Storage } from '../storage';
import { activityLogger } from '../utils/activityLogger';

export interface BackgroundServiceConfig {
  autoArchiveContainersAfterDays: number;
  autoDeleteRetiredProductsAfterDays: number;
  cleanupSessionsAfterDays: number;
  autoBackupEnabled: boolean;
  autoBackupFrequencyHours: number;
  maintenanceHour: number; // 0-23, ora del giorno per la manutenzione
}

export class BackgroundServices {
  private storage: Storage;
  private config: BackgroundServiceConfig;
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;

  constructor(storage: Storage, config: BackgroundServiceConfig) {
    this.storage = storage;
    this.config = config;
  }

  /**
   * Avvia tutti i servizi di background
   */
  start() {
    if (this.isRunning) {
      console.log('🔄 Background services già in esecuzione');
      return;
    }

    console.log('🚀 Avvio servizi di background HACCP Tracker...');
    this.isRunning = true;

    // Esegui immediatamente un controllo iniziale
    this.performInitialCheck();

    // Pianifica la manutenzione giornaliera
    this.scheduleDailyMaintenance();

    // Pianifica il backup automatico (se abilitato)
    if (this.config.autoBackupEnabled) {
      this.scheduleAutoBackup();
    }

    // Pianifica la pulizia delle sessioni ogni ora
    this.scheduleSessionCleanup();

    console.log('✅ Servizi di background avviati con successo');
  }

  /**
   * Ferma tutti i servizi di background
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Arresto servizi di background...');
    
    // Cancella tutti gli intervalli
    this.intervals.forEach((interval, name) => {
      clearInterval(interval);
      console.log(`⏹️ Fermato servizio: ${name}`);
    });
    
    this.intervals.clear();
    this.isRunning = false;
    
    console.log('✅ Servizi di background arrestati');
  }

  /**
   * Controllo iniziale all'avvio
   */
  private async performInitialCheck() {
    try {
      console.log('🔍 Esecuzione controllo iniziale...');
      
      // Esegui una pulizia rapida all'avvio
      await this.cleanupExpiredSessions();
      await this.checkExpiredProducts();
      
      console.log('✅ Controllo iniziale completato');
    } catch (error) {
      console.error('❌ Errore durante il controllo iniziale:', error);
    }
  }

  /**
   * Pianifica la manutenzione giornaliera
   */
  private scheduleDailyMaintenance() {
    // Calcola il tempo fino alla prossima manutenzione
    const now = new Date();
    const nextMaintenance = new Date();
    nextMaintenance.setHours(this.config.maintenanceHour, 0, 0, 0);
    
    // Se l'ora è già passata oggi, pianifica per domani
    if (nextMaintenance <= now) {
      nextMaintenance.setDate(nextMaintenance.getDate() + 1);
    }

    const timeUntilMaintenance = nextMaintenance.getTime() - now.getTime();
    
    console.log(`⏰ Prossima manutenzione programmata: ${nextMaintenance.toLocaleString()}`);

    // Pianifica la prima manutenzione
    setTimeout(() => {
      this.performDailyMaintenance();
      
      // Poi pianifica ogni 24 ore
      const dailyInterval = setInterval(() => {
        this.performDailyMaintenance();
      }, 24 * 60 * 60 * 1000); // 24 ore
      
      this.intervals.set('dailyMaintenance', dailyInterval);
    }, timeUntilMaintenance);
  }

  /**
   * Esegue la manutenzione giornaliera
   */
  private async performDailyMaintenance() {
    try {
      console.log('🧹 Inizio manutenzione giornaliera...');
      
      const startTime = Date.now();
      const results = {
        containersArchived: 0,
        productsDeleted: 0,
        sessionsDeleted: 0,
        errors: 0
      };

      // 1. Archivia container inattivi
      try {
        results.containersArchived = await this.autoArchiveInactiveContainers();
      } catch (error) {
        console.error('❌ Errore archiviazione container:', error);
        results.errors++;
      }

      // 2. Elimina prodotti ritirati vecchi
      try {
        results.productsDeleted = await this.deleteOldRetiredProducts();
      } catch (error) {
        console.error('❌ Errore eliminazione prodotti:', error);
        results.errors++;
      }

      // 3. Pulizia sessioni scadute
      try {
        results.sessionsDeleted = await this.cleanupExpiredSessions();
      } catch (error) {
        console.error('❌ Errore pulizia sessioni:', error);
        results.errors++;
      }

      // 4. Controlla prodotti in scadenza
      try {
        await this.checkExpiredProducts();
      } catch (error) {
        console.error('❌ Errore controllo prodotti scaduti:', error);
        results.errors++;
      }

      const duration = Date.now() - startTime;
      
      console.log('✅ Manutenzione giornaliera completata:', {
        durata: `${duration}ms`,
        ...results
      });

      // Log dell'attività di manutenzione
      await this.logMaintenanceActivity('daily_maintenance', 
        'Manutenzione giornaliera automatica completata', {
        duration,
        ...results
      });

    } catch (error) {
      console.error('❌ Errore durante la manutenzione giornaliera:', error);
      await this.logMaintenanceActivity('daily_maintenance_error', 
        'Errore durante la manutenzione giornaliera', { error: error.message });
    }
  }

  /**
   * Archivia automaticamente i container inattivi
   */
  private async autoArchiveInactiveContainers(): Promise<number> {
    console.log('📦 Controllo container inattivi...');
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.autoArchiveContainersAfterDays);
    
    const containers = await this.storage.getAllContainers();
    let archivedCount = 0;

    for (const container of containers) {
      if (container.isArchived) continue; // Già archiviato
      
      // Verifica se il container è vuoto e inattivo
      const containerProducts = await this.storage.getContainerProducts(container.id);
      const isEmpty = containerProducts.length === 0;
      const isOld = new Date(container.createdAt) < cutoffDate;
      
      if (isEmpty && isOld) {
        // Archivia il container
        await this.storage.updateContainer(container.id, { isArchived: true });
        archivedCount++;
        
        console.log(`📦 Container archiviato automaticamente: ${container.name}`);
        
        await this.logMaintenanceActivity('auto_archive_container',
          `Container archiviato automaticamente: ${container.name}`,
          { containerId: container.id, reason: 'Inattivo per oltre ' + this.config.autoArchiveContainersAfterDays + ' giorni' }
        );
      }
    }

    console.log(`📦 Archiviati ${archivedCount} container inattivi`);
    return archivedCount;
  }

  /**
   * Elimina prodotti ritirati vecchi (oltre il periodo di conservazione)
   */
  private async deleteOldRetiredProducts(): Promise<number> {
    console.log('🗑️ Controllo prodotti ritirati vecchi...');
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.autoDeleteRetiredProductsAfterDays);
    
    const retiredProducts = await this.storage.getRetiredProducts();
    let deletedCount = 0;

    for (const product of retiredProducts) {
      if (product.retiredAt && new Date(product.retiredAt) < cutoffDate) {
        // Elimina il prodotto ritirato
        await this.storage.deleteProductLabel(product.id);
        deletedCount++;
        
        console.log(`🗑️ Prodotto ritirato eliminato: ${product.productName}`);
        
        await this.logMaintenanceActivity('auto_delete_retired_product',
          `Prodotto ritirato eliminato automaticamente: ${product.productName}`,
          { productId: product.id, retiredAt: product.retiredAt }
        );
      }
    }

    console.log(`🗑️ Eliminati ${deletedCount} prodotti ritirati vecchi`);
    return deletedCount;
  }

  /**
   * Pulisce le sessioni scadute
   */
  private async cleanupExpiredSessions(): Promise<number> {
    console.log('🧹 Pulizia sessioni scadute...');
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.cleanupSessionsAfterDays);
    
    try {
      // Query diretta per eliminare sessioni scadute
      const result = await this.storage.db.execute(`
        DELETE FROM sessions 
        WHERE expire < $1
      `, [cutoffDate]);
      
      const deletedCount = result.rowCount || 0;
      console.log(`🧹 Eliminate ${deletedCount} sessioni scadute`);
      
      return deletedCount;
    } catch (error) {
      console.error('❌ Errore durante la pulizia delle sessioni:', error);
      return 0;
    }
  }

  /**
   * Controlla prodotti scaduti o in scadenza
   */
  private async checkExpiredProducts(): Promise<void> {
    console.log('⏰ Controllo prodotti scaduti...');
    
    const now = new Date();
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + 3); // Avvisa 3 giorni prima
    
    const activeProducts = await this.storage.getActiveProducts();
    let expiredCount = 0;
    let warningCount = 0;

    for (const product of activeProducts) {
      if (!product.expiryDate) continue;
      
      const expiryDate = new Date(product.expiryDate);
      
      if (expiryDate < now) {
        // Prodotto scaduto
        expiredCount++;
        console.log(`⚠️ Prodotto scaduto rilevato: ${product.productName} (scaduto il ${expiryDate.toLocaleDateString()})`);
        
        await this.logMaintenanceActivity('expired_product_detected',
          `Prodotto scaduto rilevato: ${product.productName}`,
          { productId: product.id, expiryDate: product.expiryDate }
        );
      } else if (expiryDate < warningDate) {
        // Prodotto in scadenza
        warningCount++;
        console.log(`⚠️ Prodotto in scadenza: ${product.productName} (scade il ${expiryDate.toLocaleDateString()})`);
        
        await this.logMaintenanceActivity('expiring_product_warning',
          `Prodotto in scadenza: ${product.productName}`,
          { productId: product.id, expiryDate: product.expiryDate, daysUntilExpiry: Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) }
        );
      }
    }

    console.log(`⏰ Controllo completato: ${expiredCount} scaduti, ${warningCount} in scadenza`);
  }

  /**
   * Pianifica il backup automatico
   */
  private scheduleAutoBackup() {
    const intervalMs = this.config.autoBackupFrequencyHours * 60 * 60 * 1000;
    
    const backupInterval = setInterval(async () => {
      try {
        console.log('💾 Esecuzione backup automatico...');
        // Qui potresti chiamare il tuo script di backup
        // await this.performAutoBackup();
        console.log('💾 Backup automatico completato');
      } catch (error) {
        console.error('❌ Errore durante il backup automatico:', error);
      }
    }, intervalMs);
    
    this.intervals.set('autoBackup', backupInterval);
    console.log(`💾 Backup automatico pianificato ogni ${this.config.autoBackupFrequencyHours} ore`);
  }

  /**
   * Pianifica la pulizia delle sessioni ogni ora
   */
  private scheduleSessionCleanup() {
    const sessionCleanupInterval = setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        console.error('❌ Errore durante la pulizia sessioni:', error);
      }
    }, 60 * 60 * 1000); // Ogni ora
    
    this.intervals.set('sessionCleanup', sessionCleanupInterval);
    console.log('🧹 Pulizia sessioni pianificata ogni ora');
  }

  /**
   * Log delle attività di manutenzione
   */
  private async logMaintenanceActivity(action: string, details: string, metadata?: any) {
    try {
      await activityLogger.log(
        null, // userId - sistema
        'system',
        action,
        details,
        metadata
      );
    } catch (error) {
      console.error('❌ Errore nel logging dell\'attività:', error);
    }
  }

  /**
   * Ottieni lo stato dei servizi di background
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeServices: Array.from(this.intervals.keys()),
      config: this.config
    };
  }

  /**
   * Aggiorna la configurazione
   */
  updateConfig(newConfig: Partial<BackgroundServiceConfig>) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Configurazione servizi di background aggiornata:', newConfig);
  }
}

// Configurazione predefinita
export const defaultBackgroundConfig: BackgroundServiceConfig = {
  autoArchiveContainersAfterDays: 30, // Archivia container vuoti dopo 30 giorni
  autoDeleteRetiredProductsAfterDays: 730, // Elimina prodotti ritirati dopo 2 anni
  cleanupSessionsAfterDays: 7, // Pulisci sessioni dopo 7 giorni
  autoBackupEnabled: false, // Backup automatico disabilitato per default
  autoBackupFrequencyHours: 24, // Backup ogni 24 ore
  maintenanceHour: 2 // Manutenzione alle 2:00 del mattino
};