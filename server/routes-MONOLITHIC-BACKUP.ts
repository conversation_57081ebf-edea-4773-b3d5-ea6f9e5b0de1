import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { db } from "./db";
import * as claude from "./lib/claude";
import { processProductLabel, processMultipleProductLabels } from "./lib/claude";
import { generateQRCode, generateUniqueId } from "./lib/qr-code";
import * as schema from "@shared/schema";
import { insertUserSchema, insertSupplierSchema, insertDDTSchema, insertProductLabelSchema, insertContainerSchema, User, containerProducts, ActivityLogFilters, retireProductSchema } from "@shared/schema";
import { eq, like, desc, and, or, sql, isNull, isNotNull, count } from "drizzle-orm";
import { registerDevTools } from "./routes/dev-tools";
import { registerUserRoutes } from "./routes/users";
import { registerClaudeRoutes } from "./routes/claude";
import { registerAiRoutes } from "./routes/ai-routes";
import { registerSystemRoutes } from "./routes/system";
import { registerContainerRoutes } from "./routes/containers";
import { createSystemStatusRoutes } from "./routes/systemStatus";
import monitoringRoutes from "./routes/monitoring";
import { z } from "zod";
import { ZodError } from "zod-validation-error";
import { AIErrorHandler } from "./lib/error-handler";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import session from "express-session";
import { normalizeDateToDDMMYYYY } from "./lib/dateNormalization";
import connectPgSimple from "connect-pg-simple";
import bcrypt from "bcryptjs";
import { exec } from "child_process";
import { promisify } from "util";
import { pool } from "./db";
import ExcelJS from 'exceljs';
import { jsPDF } from 'jspdf';
// @ts-ignore
import autoTable from 'jspdf-autotable';
import { ensureDefaultTenant } from "./tenant-migration";
import { 
  loginValidation, 
  createUserValidation, 
  validateId, 
  fileUploadConfig,
  supplierValidation,
  productLabelValidation,
  containerValidation,
  validateImageData,
  fileUpload
} from "./middlewares/security";

// Inizializza lo store di sessione PostgreSQL
const PgSession = connectPgSimple(session);

// Initialize passport
export async function setupAuth(app: Express) {
  // Set up session middleware
  app.use(
    session({
      store: new PgSession({
        pool: pool,
        tableName: 'sessions',
        createTableIfMissing: true
      }),
      secret: process.env.SESSION_SECRET || "haccp-tracker-secret",
      resave: false,
      saveUninitialized: false,
      cookie: { 
        secure: process.env.NODE_ENV === 'production', // HTTPS obbligatorio in produzione
        sameSite: 'strict', // Protezione CSRF più rigida
        maxAge: 2 * 60 * 60 * 1000, // 2 ore invece di 7 giorni per sicurezza
        httpOnly: true
      }
    })
  );

  // Initialize passport and session
  app.use(passport.initialize());
  app.use(passport.session());

  // Passport local strategy
  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        const user = await storage.getUserByUsernameLegacy(username);

        if (!user) {
          return done(null, false, { message: "Nome utente non corretto." });
        }

        console.log(`Tentativo di login: ${username}`);

        const isValid = await bcrypt.compare(password, user.password);
        console.log(`Risultato autenticazione: ${isValid ? 'Valida' : 'Non valida'}`);

        if (!isValid) {
          return done(null, false, { message: "Incorrect password." });
        }

        return done(null, user);
      } catch (error) {
        return done(error);
      }
    })
  );

  // Serialize user to the session
  passport.serializeUser((user: any, done) => {
    console.log("Serializing user:", user);
    // Assicuriamoci di serializzare solo l'ID
    if (typeof user === 'object' && user !== null && 'id' in user) {
      console.log("Serialized user ID:", user.id);
      done(null, user.id);
    } else {
      console.log("User is already an ID (or invalid):", user);
      done(null, user);
    }
  });

  // Deserialize user from the session
  passport.deserializeUser(async (id: any, done) => {
    try {
      // Non logghiamo più ogni deserializzazione per migliorare le prestazioni

      let userId: number;

      // Caso 1: ID è già un numero
      if (typeof id === 'number') {
        userId = id;
      }
      // Caso 2: ID è una stringa che rappresenta un numero
      else if (typeof id === 'string' && !isNaN(parseInt(id, 10)) && !id.includes('{')) {
        userId = parseInt(id, 10);
      }
      // Caso 3: ID è un oggetto completo
      else if (typeof id === 'object' && id !== null && 'id' in id && typeof id.id === 'number') {
        userId = id.id;
      }
      // Caso 4: ID è una stringa JSON che rappresenta un oggetto completo
      else if (typeof id === 'string' && (id.startsWith('{') || id.startsWith('['))) {
        try {
          const parsedObj = JSON.parse(id);
          if (parsedObj && typeof parsedObj === 'object' && 'id' in parsedObj) {
            userId = parsedObj.id;
          } else {
            throw new Error("Non ho trovato un ID valido nell'oggetto JSON");
          }
        } catch (e) {
          console.error("Errore nel parsing della stringa JSON:", e);
          return done(new Error("Formato ID non valido"), null);
        }
      }
      // Caso non gestito: formato non supportato
      else {
        console.error("Formato ID non supportato:", id);
        return done(new Error("Formato ID non valido"), null);
      }

      // Verifica finale che userId sia un numero valido
      if (isNaN(userId)) {
        console.error("ID utente non valido dopo la conversione:", userId);
        return done(new Error("ID utente non valido"), null);
      }

      // Ottieni l'utente dal database
      const user = await storage.getUserLegacy(userId);

      if (!user) {
        return done(null, null);
      }

      done(null, user);
    } catch (error) {
      console.error("Errore nella deserializzazione dell'utente:", error);
      done(error, null);
    }
  });

  // Create a default admin user if none exists
  try {
    const defaultTenant = await ensureDefaultTenant();
    
    const adminExists = await storage.getUserByUsernameLegacy("admin");
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash("admin123", 10);
      await storage.createUser({
        username: "admin",
        password: hashedPassword,
        isAdmin: true,
        tenantId: defaultTenant.id,
      });
      console.log("Default admin user created");
    }

    const regularUserExists = await storage.getUserByUsernameLegacy("user");
    if (!regularUserExists) {
      const hashedPassword = await bcrypt.hash("user123", 10);
      await storage.createUser({
        username: "user",
        password: hashedPassword,
        isAdmin: false,
        tenantId: defaultTenant.id,
      });
      console.log("Default regular user created");
    }
  } catch (error) {
    console.error("Error creating default users:", error);
  }
}

// Authentication middleware
function isAuthenticated(req: Request, res: Response, next: Function) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: "Unauthorized" });
}

function isAdmin(req: Request, res: Response, next: Function) {
  if (req.isAuthenticated() && req.user && (req.user as any).isAdmin) {
    return next();
  }
  res.status(403).json({ message: "Forbidden: Admin access required" });
}

function isManagerOrAdmin(req: Request, res: Response, next: Function) {
  const user = req.user as any;

  if (req.isAuthenticated() && user && (
    user.isAdmin || 
    user.role === schema.UserRole.ADMIN || 
    user.role === schema.UserRole.MANAGER
  )) {
    return next();
  }
  res.status(403).json({ message: "Forbidden: Manager or Admin access required" });
}

// Log activity helper
async function logActivity(
  userId: number,
  username: string,
  action: string,
  details: string,
  metadata?: any
) {
  try {
    // Get user to get tenantId
    const user = await storage.getUserLegacy(userId);
    if (user) {
      await storage.addActivityLog({
        tenantId: user.tenantId,
        userId,
        username,
        action,
        details: details,
        metadata: metadata || {},
      });
    }
  } catch (error) {
    console.error("Error logging activity:", error);
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Configure secure file uploads
  app.use(fileUpload(fileUploadConfig));
  
  // Initialize authentication
  await setupAuth(app);

  // Authentication routes with validation
  app.post("/api/auth/login", loginValidation, (req: Request, res: Response, next: NextFunction) => {
    passport.authenticate("local", (err: any, user: any, info: any) => {
      if (err) return next(err);
      if (!user) {
        return res.status(401).json({ message: info?.message || "Authentication failed" });
      }
      req.logIn(user, (loginErr: any) => {
        if (loginErr) return next(loginErr);

        // Log login activity
        logActivity(
          user.id,
          user.username,
          "login",
          `L'utente ${user.username} ha effettuato l'accesso`
        );

        return res.json({
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.isAdmin,
        });
      });
    })(req, res, next);
  });

  app.post("/api/auth/logout", (req, res) => {
    const user = req.user as any;
    if (user) {
      logActivity(
        user.id,
        user.username,
        "logout",
        `L'utente ${user.username} si è disconnesso`
      );
    }

    req.logout((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      res.json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/auth/me", isAuthenticated, (req, res) => {
    const user = req.user as any;
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      isAdmin: user.isAdmin,
      role: user.role || (user.isAdmin ? schema.UserRole.ADMIN : schema.UserRole.USER), // Retrocompatibilità
    });
  });

  // User settings routes - Personal settings for each user
  app.get("/api/user/settings", isAuthenticated, async (req, res) => {
    try {
      const userId = (req.user as any).id;
      const settings = await storage.getUserSettings(userId);

      if (!settings) {
        // Return default settings if none exist
        const defaultSettings = {
          userId,
          cameraFacingMode: "environment",
          cameraResolution: "fullhd",
          showScanner: false,
          vibrationFeedback: true,
          soundFeedback: true,
          confirmScans: false,
          lightFeedback: true,
          aiProvider: "claude"
        };
        res.json(defaultSettings);
      } else {
        res.json(settings);
      }
    } catch (error) {
      console.error("Error fetching user settings:", error);
      res.status(500).json({ message: "Failed to fetch user settings" });
    }
  });

  // New route that frontend expects
  app.get("/api/users/settings", isAuthenticated, async (req, res) => {
    try {
      const userId = (req.user as any).id;
      const settings = await storage.getUserSettings(userId);

      if (!settings) {
        // Return default settings if none exist
        const defaultSettings = {
          userId,
          cameraFacingMode: "environment",
          cameraResolution: "fullhd", 
          showScanner: false,
          vibrationFeedback: true,
          soundFeedback: true,
          confirmScans: false,
          lightFeedback: true,
          aiProvider: "claude"
        };
        res.json(defaultSettings);
      } else {
        res.json(settings);
      }
    } catch (error) {
      console.error("Error fetching user settings:", error);
      res.status(500).json({ message: "Failed to fetch user settings" });
    }
  });

  // POST route for frontend compatibility
  app.post("/api/users/settings", isAuthenticated, async (req, res) => {
    try {
      const userId = (req.user as any).id;
      const user = req.user as any;
      const settingsUpdate = req.body;

      console.log("POST /api/users/settings called by user:", user.username);
      console.log("Settings update payload:", settingsUpdate);

      // Validate the settings data - include AI and PWA fields
      const allowedFields = [
        'cameraFacingMode', 'cameraResolution', 'showScanner', 
        'vibrationFeedback', 'soundFeedback', 'confirmScans', 'lightFeedback', 
        'aiProvider', 'claudeModel', 'geminiModel',
        'offlineMode', 'dataPersistence', 'autoSync', 'cacheManagement', 'backgroundSync'
      ];

      const filteredSettings: any = {};
      for (const field of allowedFields) {
        if (settingsUpdate[field] !== undefined) {
          filteredSettings[field] = settingsUpdate[field];
        }
      }

      const updatedSettings = await storage.updateUserSettings(userId, filteredSettings);

      // Log activity
      await logActivity(
        userId,
        user.username,
        "UPDATE_USER_SETTINGS",
        `Aggiornate impostazioni personali: ${Object.keys(filteredSettings).join(", ")}`,
        { updatedFields: Object.keys(filteredSettings) }
      );

      res.json({ success: true, settings: updatedSettings });
    } catch (error) {
      console.error("Error updating user settings:", error);
      res.status(500).json({ message: "Failed to update user settings" });
    }
  });

  app.patch("/api/user/settings", isAuthenticated, async (req, res) => {
    try {
      const userId = (req.user as any).id;
      const user = req.user as any;
      const settingsUpdate = req.body;

      // Validate the settings data
      const allowedFields = [
        'cameraFacingMode', 'cameraResolution', 'showScanner', 
        'vibrationFeedback', 'soundFeedback', 'confirmScans', 'lightFeedback'
      ];

      const filteredSettings: any = {};
      for (const field of allowedFields) {
        if (settingsUpdate[field] !== undefined) {
          filteredSettings[field] = settingsUpdate[field];
        }
      }

      const updatedSettings = await storage.updateUserSettings(userId, filteredSettings);

      // Log activity
      await logActivity(
        userId,
        user.username,
        "UPDATE_USER_SETTINGS",
        `Aggiornate impostazioni personali: ${Object.keys(filteredSettings).join(", ")}`,
        { updatedFields: Object.keys(filteredSettings) }
      );

      res.json(updatedSettings);
    } catch (error) {
      console.error("Error updating user settings:", error);
      res.status(500).json({ message: "Failed to update user settings" });
    }
  });

  // User profile update
  app.patch("/api/user/profile", isAuthenticated, async (req, res) => {
    try {
      const { username, email, currentPassword, newPassword } = req.body;
      const userId = (req.user as any).id;

      // Verifica che username sia valido
      if (!username || username.length < 3) {
        return res.status(400).json({ message: "Il nome utente deve contenere almeno 3 caratteri" });
      }

      // Verifica che la password attuale sia fornita
      if (!currentPassword) {
        return res.status(400).json({ message: "Password attuale richiesta" });
      }

      // Ottieni l'utente attuale dal database
      const currentUser = await storage.getUserLegacy(userId);
      if (!currentUser) {
        return res.status(404).json({ message: "Utente non trovato" });
      }

      // Verifica che la password attuale sia corretta
      const isPasswordValid = await bcrypt.compare(currentPassword, currentUser.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Password attuale non valida" });
      }

      // Verifica se l'username è cambiato e se è già in uso
      if (username !== currentUser.username) {
        const existingUser = await storage.getUserByUsername(username);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: "Nome utente già in uso" });
        }
      }

      // Prepara i dati per l'aggiornamento
      const updateData: { username: string; email: string | null; password?: string; } = {
        username,
        email: email || null
      };

      // Se è stata fornita una nuova password, la aggiorniamo
      if (newPassword) {
        if (newPassword.length < 6) {
          return res.status(400).json({ message: "La nuova password deve contenere almeno 6 caratteri" });
        }
        updateData.password = await bcrypt.hash(newPassword, 10);
      }

      // Aggiorniamo l'utente nel database
      const updatedUser = await storage.updateUser(userId, updateData);

      // Log activity
      await logActivity(
        userId,
        currentUser.username,
        "update_profile",
        `User updated their profile`
      );

      // Restituiamo l'utente aggiornato (rimuoviamo la password)
      const { password, ...safeUser } = updatedUser;
      res.json(safeUser);
    } catch (error) {
      console.error("Profile update error:", error);
      res.status(500).json({ message: "Errore durante l'aggiornamento del profilo" });
    }
  });

  // User management routes (admin and manager)
  app.get("/api/users", isAuthenticated, function(req, res, next) {
    isManagerOrAdmin(req, res, next);
  }, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      // Remove password from response
      const safeUsers = users.map(({ password, ...user }) => ({
        ...user,
        // Assicuriamo che il campo role sia sempre presente
        role: user.role || (user.isAdmin ? schema.UserRole.ADMIN : schema.UserRole.USER)
      }));
      res.json(safeUsers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  // Get single user by ID
  app.get("/api/users/:id", isAuthenticated, function(req, res, next) {
    isManagerOrAdmin(req, res, next);
  }, async (req, res) => {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      const user = await storage.getUserLegacy(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Rimuovi la password dalla risposta
      const { password, ...userWithoutPassword } = user;
      // Assicuriamo che il campo role sia sempre presente
      const userResponse = {
        ...userWithoutPassword,
        role: userWithoutPassword.role || (userWithoutPassword.isAdmin ? schema.UserRole.ADMIN : schema.UserRole.USER)
      };
      res.json(userResponse);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Solo gli admin possono creare nuovi utenti
  app.post("/api/users", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      const newUser = await storage.createUser({
        ...userData,
        password: hashedPassword,
      });

      // Log activity
      const admin = req.user as any;
      logActivity(
        admin.id,
        admin.username,
        "create_user",
        `Admin created new user: ${userData.username}`
      );

      // Remove password from response
      const { password, ...userWithoutPassword } = newUser;
      res.status(201).json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid user data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Solo gli admin possono modificare utenti
  app.patch("/api/users/:id", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      // Check if user exists
      const existingUser = await storage.getUserLegacy(userId);
      if (!existingUser) {
        return res.status(404).json({ message: "User not found" });
      }

      // Prepare update data
      const updateData: any = {};

      // Handle username update
      if (req.body.username && req.body.username !== existingUser.username) {
        // Check if new username already exists
        const userWithSameUsername = await storage.getUserByUsername(req.body.username);
        if (userWithSameUsername && userWithSameUsername.id !== userId) {
          return res.status(400).json({ message: "Username already exists" });
        }
        updateData.username = req.body.username;
      }

      // Handle password update
      if (req.body.password) {
        updateData.password = await bcrypt.hash(req.body.password, 10);
      }

      // Handle email update (deve sempre essere presente nella risposta)
      if (req.body.hasOwnProperty('email')) {
        // Converti stringhe vuote in null esplicitamente
        updateData.email = req.body.email === "" ? null : req.body.email;
        console.log(`Email update in routes.ts: '${req.body.email}' -> '${updateData.email}'`);
      }

      // Debug dell'oggetto updateData
      console.log("Update data prima dell'aggiornamento:", JSON.stringify(updateData));

      // Gestisci l'aggiornamento del ruolo esplicitamente
      if (req.body.hasOwnProperty('role')) {
        // Verifica che il ruolo sia valido
        if (![schema.UserRole.ADMIN, schema.UserRole.MANAGER, schema.UserRole.USER].includes(req.body.role)) {
          return res.status(400).json({ message: "Ruolo non valido" });
        }

        // Ottieni il ruolo precedente per confronto
        const currentRole = existingUser.role || (existingUser.isAdmin ? schema.UserRole.ADMIN : schema.UserRole.USER);
        const newRole = req.body.role;

        // Se si sta declassando un admin all'ultimo admin
        if (currentRole === schema.UserRole.ADMIN && newRole !== schema.UserRole.ADMIN) {
          // Conta quanti admin abbiamo nel sistema
          const allUsers = await storage.getAllUsers();
          const adminCount = allUsers.filter(u => u.role === schema.UserRole.ADMIN || 
                                                 (u.isAdmin && (!u.role || u.role === ''))).length;

          // Se è l'ultimo admin, non permettiamo la rimozione
          if (adminCount <= 1) {
            return res.status(403).json({ 
              message: "Non è possibile rimuovere i privilegi all'ultimo amministratore del sistema" 
            });
          }
        }

        // Aggiorna il campo role
        updateData.role = newRole;

        // Mantieni la retrocompatibilità con il campo isAdmin
        updateData.isAdmin = (newRole === schema.UserRole.ADMIN);

        console.log(`Aggiornamento ruolo: da ${currentRole} a ${newRole}, isAdmin: ${updateData.isAdmin}`);
      }
      // Gestisci aggiornamento legacy con isAdmin - se non è stato impostato un role
      else if (req.body.hasOwnProperty('isAdmin')) {
        // Se stiamo tentando di rimuovere i privilegi di admin ad un admin
        if (existingUser.isAdmin === true && req.body.isAdmin === false) {
          // Conta quanti admin abbiamo nel sistema
          const allUsers = await storage.getAllUsers();
          const adminCount = allUsers.filter(u => u.isAdmin === true).length;

          // Se è l'ultimo admin, non permettiamo la rimozione
          if (adminCount <= 1) {
            return res.status(403).json({ 
              message: "Non è possibile rimuovere i privilegi all'ultimo amministratore del sistema" 
            });
          }
        }

        // Aggiorna isAdmin
        updateData.isAdmin = req.body.isAdmin;

        // Mantieni la coerenza con il campo role
        if (req.body.isAdmin) {
          updateData.role = schema.UserRole.ADMIN;
        } else if (!existingUser.role || existingUser.role === schema.UserRole.ADMIN) {
          // Se era admin, lo rendiamo USER
          updateData.role = schema.UserRole.USER;
        }
        // Altrimenti manteniamo il ruolo precedente (MANAGER o USER)

        console.log(`Aggiornamento isAdmin: ${req.body.isAdmin}, role impostato a: ${updateData.role}`);
      }

      // Update user
      const updatedUser = await storage.updateUser(userId, updateData);

      // Log activity
      const admin = req.user as any;
      logActivity(
        admin.id,
        admin.username,
        "update_user",
        `Admin updated user: ${updatedUser.username}`
      );

      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // Solo gli admin possono eliminare utenti
  app.delete("/api/users/:id", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      // Check if user exists
      const userToDelete = await storage.getUserLegacy(userId);
      if (!userToDelete) {
        return res.status(404).json({ message: "User not found" });
      }

      // Prevent deleting the last admin
      if (userToDelete.isAdmin) {
        const users = await storage.getAllUsers();
        const adminCount = users.filter(user => user.isAdmin).length;
        if (adminCount <= 1) {
          return res.status(400).json({ message: "Cannot delete the last admin user" });
        }
      }

      // Delete user
      await storage.deleteUser(userId);

      // Log activity
      const admin = req.user as any;
      logActivity(
        admin.id,
        admin.username,
        "delete_user",
        `Admin deleted user: ${userToDelete.username}`
      );

      res.status(204).end();
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // Supplier management routes
  app.get("/api/suppliers", isAuthenticated, async (req, res) => {
    try {
      const suppliers = await storage.getAllSuppliers();
      res.json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch suppliers" });
    }
  });

  app.post("/api/suppliers", isAuthenticated, supplierValidation, async (req: Request, res: Response) => {
    try {
      const supplierData = insertSupplierSchema.parse(req.body);
      const user = req.user as any;

      // Check if VAT number already exists
      const existingSupplier = await storage.getSupplierByVatNumber(supplierData.vatNumber);
      if (existingSupplier) {
        return res.status(400).json({ message: "Supplier with this VAT number already exists" });
      }

      const newSupplier = await storage.createSupplier({
        ...supplierData,
        tenantId: user.tenantId,
        createdBy: user.id,
      });

      // Log activity
      logActivity(
        user.id,
        user.username,
        "create_supplier",
        `L'utente ha creato un nuovo fornitore: ${supplierData.companyName}`
      );

      res.status(201).json(newSupplier);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid supplier data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create supplier" });
    }
  });

  app.get("/api/suppliers/:id", isAuthenticated, validateId, async (req: Request, res: Response) => {
    try {
      const supplierId = parseInt(req.params.id, 10);
      if (isNaN(supplierId)) {
        return res.status(400).json({ message: "Invalid supplier ID" });
      }

      const supplier = await storage.getSupplier(supplierId);
      if (!supplier) {
        return res.status(404).json({ message: "Supplier not found" });
      }

      res.json(supplier);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch supplier" });
    }
  });

  app.patch("/api/suppliers/:id", isAuthenticated, validateId, supplierValidation, async (req: Request, res: Response) => {
    try {
      const supplierId = parseInt(req.params.id, 10);
      if (isNaN(supplierId)) {
        return res.status(400).json({ message: "Invalid supplier ID" });
      }

      // Verifica che il fornitore esista
      const existingSupplier = await storage.getSupplier(supplierId);
      if (!existingSupplier) {
        return res.status(404).json({ message: "Supplier not found" });
      }

      // Controllo che la partita IVA non sia già utilizzata da un altro fornitore
      // se è stata modificata
      if (req.body.vatNumber && req.body.vatNumber !== existingSupplier.vatNumber) {
        const supplierWithSameVat = await storage.getSupplierByVatNumber(req.body.vatNumber);
        if (supplierWithSameVat && supplierWithSameVat.id !== supplierId) {
          return res.status(400).json({ message: "Supplier with this P.IVA already exists" });
        }
      }

      // Valida e aggiorna il fornitore
      const updateData = req.body;
      const updatedSupplier = await storage.updateSupplier(supplierId, updateData);

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "update_supplier",
        `User updated supplier: ${updatedSupplier.companyName}`
      );

      res.json(updatedSupplier);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid supplier data", errors: error.errors });
      }
      console.error("Error updating supplier:", error);
      res.status(500).json({ message: "Failed to update supplier" });
    }
  });

  // DDT (delivery note) routes
  app.get("/api/ddt", isAuthenticated, async (req, res) => {
    try {
      const ddts = await storage.getAllDDTs();
      res.json(ddts);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch delivery notes" });
    }
  });

  // Endpoint per ottenere i DDT recenti (ultimo mese)
  app.get("/api/ddt/recent", isAuthenticated, async (req, res) => {
    try {
      // Ottieni tutti i DDT
      const allDdts = await storage.getAllDDTs();

      // Calcola la data di un mese fa
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      // Filtra i DDT piùrecenti di un mese
      const recentDdts = allDdts.filter(ddt => {
        // Converte la data del DDT da formato dd/mm/yyyy a Date object
        const [day, month, year] = ddt.date.split('/').map(Number);
        const ddtDate = new Date(2000 + year, month - 1, day); // Aggiungi 2000 per anni a 2 cifre
        return ddtDate >= oneMonthAgo;
      });

      // Ordina per data, dal più recente
      recentDdts.sort((a, b) => {
        const [dayA, monthA, yearA] = a.date.split('/').map(Number);
        const [dayB, monthB, yearB] = b.date.split('/').map(Number);
        const dateA = new Date(2000 + yearA, monthA - 1, dayA);
        const dateB = new Date(2000 + yearB, monthB - 1, dayB);
        return dateB.getTime() - dateA.getTime();
      });

      // Ottieni le informazioni sui fornitori per arricchire i dati
      const supplierIds = recentDdts.map(ddt => ddt.supplierId).filter(id => id !== null);
      const suppliers = await Promise.all(
        supplierIds.map(id => storage.getSupplier(id as number))
      );

      // Crea una mappa di fornitori per lookup veloce
      const supplierMap = new Map();
      suppliers.forEach(supplier => {
        if (supplier) {
          supplierMap.set(supplier.id, supplier);
        }
      });

      // Arricchisci i dati dei DDT con le informazioni sui fornitori
      const enrichedDdts = recentDdts.map(ddt => ({
        ...ddt,
        supplier: ddt.supplierId ? supplierMap.get(ddt.supplierId) || null : null
      }));

      res.json(enrichedDdts);
    } catch (error) {
      console.error("Error fetching recent DDTs:", error);
      res.status(500).json({ message: "Failed to fetch recent delivery notes" });
    }
  });

  app.get("/api/ddt/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      const ddt = await storage.getDDT(id);
      if (!ddt) {
        return res.status(404).json({ message: "DDT not found" });
      }
      res.json(ddt);
    } catch (error) {
      console.error("Error fetching DDT details:", error);
      res.status(500).json({ message: "Failed to fetch DDT details" });
    }
  });

  app.post("/api/ddt", isAuthenticated, async (req, res) => {
    try {
      console.log("Richiesta POST /api/ddt ricevuta, campi:", Object.keys(req.body));

      // Controlli preliminari sui dati
      const requiredFields = ['companyName', 'vatNumber', 'address', 'number', 'date'];
      for (const field of requiredFields) {
        if (!req.body[field]) {
          console.error(`Campo richiesto mancante: ${field}`);
          return res.status(400).json({ message: `Campo richiesto mancante: ${field}` });
        }
      }

      // Prepara i dati per la validazione
      const requestData = { ...req.body };

      // Se supplierId è null, rimuoviamolo dall'oggetto per evitare errori di validazione
      if (requestData.supplierId === null) {
        delete requestData.supplierId;
      }

      // Validate request body
      const ddtData = insertDDTSchema.parse(requestData);
      const user = req.user as any;

      // Normalize the date to DD-MM-YYYY format before saving
      const normalizedDate = normalizeDateToDDMMYYYY(ddtData.date);
      console.log(`Normalizing DDT date: "${ddtData.date}" -> "${normalizedDate}"`);

      // Check for duplicate DDT (same company, number, and date)
      const existingDDT = await storage.checkDDTDuplicate(
        ddtData.companyName,
        ddtData.number,
        normalizedDate
      );

      if (existingDDT) {
        console.log(`Duplicate DDT detected: Company: ${ddtData.companyName}, Number: ${ddtData.number}, Date: ${normalizedDate}`);
        return res.status(409).json({ 
          message: "DDT duplicato rilevato",
          details: `Un DDT con lo stesso numero (${ddtData.number}), della stessa azienda (${ddtData.companyName}) e con la stessa data (${normalizedDate}) è già presente nel sistema.`,
          duplicateId: existingDDT.id
        });
      }

      // Creiamo prima il DDT con un QR code temporaneo e data normalizzata
      const tempDDT = await storage.createDDT({
        ...ddtData,
        date: normalizedDate, // Use normalized date
        qrCode: "temp", // valore temporaneo
        createdBy: user.id,
      });

      // Ora che abbiamo l'ID reale del database, generiamo il QR code nel formato standard
      const standardQrValue = `ddt:${tempDDT.id}:${tempDDT.number.replace(/[^a-zA-Z0-9]/g, '_')}`;

      // Genera il QR code come data URL
      const qrCode = await generateQRCode(standardQrValue);

      // Aggiorna il DDT con il QR code corretto
      const newDDT = await storage.updateDDT(tempDDT.id, { qrCode });

      // If supplier ID is provided, try to update or create supplier
      if (ddtData.supplierId) {
        try {
          // Check if supplier exists
          const supplier = await storage.getSupplier(ddtData.supplierId);
          if (supplier) {
            // Log that DDT is linked to existing supplier
            logActivity(
              user.id,
              user.username,
              "link_ddt_supplier",
              `DDT #${ddtData.number} linked to supplier: ${supplier.companyName}`
            );
          }
        } catch (error) {
          console.error("Error checking supplier:", error);
          // Continue processing even if supplier check fails
        }
      } else {
        // No supplier ID provided, check if we need to create a new supplier
        try {
          const existingSupplier = await storage.getSupplierByVatNumber(ddtData.vatNumber);

          if (!existingSupplier) {
            // Create new supplier
            const newSupplier = await storage.createSupplier({
              companyName: ddtData.companyName,
              vatNumber: ddtData.vatNumber,
              address: ddtData.address,
              tenantId: user.tenantId,
              createdBy: user.id,
            });

            // Update DDT with new supplier ID
            await storage.updateDDT(newDDT.id, { supplierId: newSupplier.id });

            // Log new supplier creation
            logActivity(
              user.id,
              user.username,
              "create_supplier_from_ddt",
              `New supplier created from DDT: ${ddtData.companyName}`
            );
          } else {
            // IMPORTANTE: Solo colleghiamo il DDT al fornitore esistente 
            // NON modifichiamo MAI i dati anagrafici del fornitore esistente
            console.log(`Associando DDT al fornitore esistente (ID: ${existingSupplier.id}) - NON aggiornando dati anagrafici`);

            // Link DDT to existing supplier, senza modificare dati anagrafici
            await storage.updateDDT(newDDT.id, { supplierId: existingSupplier.id });

            // Se i dati sono diversi, lo logghiamo ma NON aggiorniamo
            if (existingSupplier.companyName !== ddtData.companyName || 
                existingSupplier.address !== ddtData.address) {
              console.log(`NOTA: Dati del fornitore nel DDT (${ddtData.companyName}, ${ddtData.address}) non aggiornati nell'anagrafica.`);
              console.log(`Mantenuti i dati esistenti: ${existingSupplier.companyName}, ${existingSupplier.address}`);
            }

            logActivity(
              user.id,
              user.username,
              "link_ddt_supplier",
              `DDT #${ddtData.number} linked to supplier: ${existingSupplier.companyName}`
            );
          }
        } catch (error) {
          console.error("Error processing supplier from DDT:", error);
          // Continue processing even if supplier creation fails
        }
      }

      // Log activity
      logActivity(
        user.id,
        user.username,
        "create_ddt",
        `L'utente ha aggiunto il DDT #${ddtData.number} da ${ddtData.companyName}`
      );

      res.status(201).json(newDDT);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("Validation error:", error.errors);
        return res.status(400).json({ message: "Invalid DDT data", errors: error.errors });
      }
      console.error("Error creating DDT:", error);
      res.status(500).json({ message: "Failed to create delivery note" });
    }
  });

  // Admin dashboard API routes
  app.get("/api/admin/dashboard/stats", isAdmin, async (req, res) => {
    try {
      const totalTenants = await db.select({ count: count() }).from(schema.tenants);
      const activeTenants = await db.select({ count: count() }).from(schema.tenants).where(eq(schema.tenants.status, 'active'));
      const totalUsers = await db.select({ count: count() }).from(schema.users);
      const activeUsers = await db.select({ count: count() }).from(schema.users).where(eq(schema.users.isActive, true));

      res.json({
        totalTenants: totalTenants[0].count,
        activeTenants: activeTenants[0].count,
        totalUsers: totalUsers[0].count,
        activeUsers: activeUsers[0].count,
      });
    } catch (error) {
      console.error('Dashboard stats error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get("/api/admin/tenants", isAdmin, async (req, res) => {
    try {
      const { search, status, limit } = req.query;
      
      let query = db.select().from(schema.tenants);
      
      if (search) {
        query = query.where(
          or(
            like(schema.tenants.name, `%${search}%`),
            like(schema.tenants.code, `%${search}%`),
            like(schema.tenants.email, `%${search}%`)
          )
        );
      }
      
      if (status) {
        query = query.where(eq(schema.tenants.status, status as string));
      }
      
      if (limit) {
        query = query.limit(parseInt(limit as string, 10));
      }
      
      const tenantList = await query.orderBy(desc(schema.tenants.createdAt));
      
      // Get user count for each tenant
      const enrichedTenants = await Promise.all(
        tenantList.map(async (tenant) => {
          const userCount = await db.select({ count: count() }).from(schema.users).where(eq(schema.users.tenantId, tenant.id));
          return {
            ...tenant,
            userCount: userCount[0].count,
            maxUsers: tenant.maxUsers || 50,
          };
        })
      );
      
      res.json(enrichedTenants);
    } catch (error) {
      console.error('Get tenants error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post("/api/admin/tenants", isAdmin, async (req, res) => {
    try {
      const { name, code, type, email, phone, vatNumber, maxUsers } = req.body;

      if (!name || !code) {
        return res.status(400).json({ message: 'Name and code are required' });
      }

      // Check if code already exists
      const existingTenant = await db
        .select()
        .from(schema.tenants)
        .where(eq(schema.tenants.code, code))
        .limit(1);

      if (existingTenant.length) {
        return res.status(400).json({ message: 'Tenant code already exists' });
      }

      const newTenant = await db
        .insert(schema.tenants)
        .values({
          name,
          code,
          type: type || 'restaurant',
          status: 'active',
          email,
          phone,
          vatNumber,
          maxUsers: maxUsers || 50,
        })
        .returning();

      res.status(201).json(newTenant[0]);
    } catch (error) {
      console.error('Create tenant error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put("/api/admin/tenants/:id", isAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { name, type, email, phone, vatNumber, maxUsers, status } = req.body;

      if (!name) {
        return res.status(400).json({ message: 'Name is required' });
      }

      const updatedTenant = await db
        .update(schema.tenants)
        .set({
          name,
          type,
          email,
          phone,
          vatNumber,
          maxUsers,
          status,
          updatedAt: new Date(),
        })
        .where(eq(schema.tenants.id, id))
        .returning();

      if (!updatedTenant.length) {
        return res.status(404).json({ message: 'Tenant not found' });
      }

      res.json(updatedTenant[0]);
    } catch (error) {
      console.error('Update tenant error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch("/api/admin/tenants/:id/status", isAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!['active', 'suspended', 'inactive'].includes(status)) {
        return res.status(400).json({ message: 'Invalid status' });
      }

      const updatedTenant = await db
        .update(schema.tenants)
        .set({
          status,
          updatedAt: new Date(),
        })
        .where(eq(schema.tenants.id, id))
        .returning();

      if (!updatedTenant.length) {
        return res.status(404).json({ message: 'Tenant not found' });
      }

      res.json(updatedTenant[0]);
    } catch (error) {
      console.error('Update tenant status error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete("/api/admin/tenants/:id", isAdmin, async (req, res) => {
    try {
      const { id } = req.params;

      // Check if tenant exists
      const tenant = await db
        .select()
        .from(schema.tenants)
        .where(eq(schema.tenants.id, id))
        .limit(1);

      if (!tenant.length) {
        return res.status(404).json({ message: 'Tenant not found' });
      }

      // Don't allow deletion of default tenant
      if (tenant[0].code === 'default') {
        return res.status(400).json({ message: 'Cannot delete default tenant' });
      }

      // Delete tenant (this will cascade to related data)
      await db.delete(schema.tenants).where(eq(schema.tenants.id, id));

      res.json({ message: 'Tenant deleted successfully' });
    } catch (error) {
      console.error('Delete tenant error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });



  // Product label routes
  app.get("/api/product-labels", isAuthenticated, async (req, res) => {
    try {
      const productLabels = await storage.getAllProductLabels();
      console.log("API /product-labels: Recuperati", productLabels.length, "prodotti totali");

      // Rimuovi duplicati
      const uniqueProductsMap = new Map();
      productLabels.forEach(product => {
        // Se questo ID prodotto non è ancora stato aggiunto o se questa è la versione più recente 
        // (basandosi sull'ID che è incrementale)
        if (!uniqueProductsMap.has(product.productName) || 
            uniqueProductsMap.get(product.productName).id < product.id) {
          uniqueProductsMap.set(product.productName, product);
        }
      });

      // Converti la Map in un array di prodotti unici
      const uniqueProducts = Array.from(uniqueProductsMap.values());

      res.json(uniqueProducts);
    } catch (error) {
      console.error("Error fetching product labels:", error);
      res.status(500).json({ message: "Failed to fetch product labels" });
    }
  });

  // Endpoint per ottenere i prodotti non scaduti - DEVE VENIRE PRIMA DELLE ROTTE CON PARAMETRI
  app.get("/api/product-labels/valid", isAuthenticated, async (req, res) => {
    try {
      const validProductLabels = await storage.getValidProductLabels();

      // Per mostrare tutti i prodotti validi, inclusi quelli con stessi dati, usiamo l'ID come chiave
      const uniqueProductsMap = new Map();
      validProductLabels.forEach(product => {
        // Usiamo l'ID come chiave unica per non eliminare i duplicati
        const key = `${product.id}`;
        uniqueProductsMap.set(key, product);
      });

      // Converti la Map in un array di prodotti unici
      const uniqueValidProducts = Array.from(uniqueProductsMap.values());

      console.log("API valid: Restituiti", uniqueValidProducts.length, "prodotti validi");
      if (uniqueValidProducts.length > 0) {
        console.log("Primo prodotto:", JSON.stringify(uniqueValidProducts[0]));
      }

      res.json(uniqueValidProducts);
    } catch (error) {
      console.error("Error fetching valid product labels:", error);
      res.status(500).json({ message: "Failed to fetch valid product labels" });
    }
  });

  // Endpoint di debug per visualizzare tutti i prodotti senza filtri
  app.get("/api/debug/products", isAuthenticated, async (req, res) => {
    try {
      // Recupera tutti i prodotti direttamente dallo storage
      const allProducts = await storage.getAllProductLabels();
      console.log("API debug: Recuperati", allProducts.length, "prodotti totali dal database");

      // Aggiungi informazioni diagnostiche alla risposta
      const response = {
        count: allProducts.length,
        products: allProducts,
        diagnostics: {
          timestamp: new Date().toISOString(),
          serverDate: new Date().toLocaleDateString('it-IT'),
        }
      };

      res.json(response);
    } catch (error) {
      console.error("Errore nel recupero di tutti i prodotti:", error);
      res.status(500).json({ 
        message: "Errore nel recupero di tutti i prodotti",
        error: String(error)
      });
    }
  });

  app.get("/api/product-labels/ddt/:ddtId", isAuthenticated, async (req, res) => {
    try {
      const ddtId = parseInt(req.params.ddtId, 10);
      const productLabels = await storage.getProductLabelsByDDT(ddtId);

      // Rimuovi duplicati dal server
      const uniqueProductsMap = new Map();
      productLabels.forEach(product => {
        if (!uniqueProductsMap.has(product.id)) {
          uniqueProductsMap.set(product.id, product);
        }
      });
      const uniqueProductLabels = Array.from(uniqueProductsMap.values());

      res.json(uniqueProductLabels);
    } catch (error) {
      console.error("Error fetching product labels by DDT:", error);
      res.status(500).json({ message: "Failed to fetch product labels for DDT" });
    }
  });

  // Use a different route path to avoid conflicts
  app.get("/api/retired-products", isAuthenticated, async (req, res) => {
    console.log("🔍 RETIRED PRODUCTS ROUTE HIT");
    try {
      console.log("📦 Fetching retired products from storage...");
      const retiredProducts = await storage.getRetiredProducts();
      console.log("✅ Retrieved", retiredProducts.length, "retired products");

      // Filter products retired more than 2 years ago (automatic cleanup)
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);

      const filteredProducts = retiredProducts.filter(product => {
        if (!product.retiredAt) return true; // Keep products without retirement date
        return new Date(product.retiredAt) > twoYearsAgo;
      });

      console.log("🎯 Filtered to", filteredProducts.length, "recent retired products");
      res.json(filteredProducts);
    } catch (error) {
      console.error("💥 Error fetching retired products:", error);
      res.status(500).json({ message: "Failed to fetch retired products" });
    }
  });

  app.get("/api/product-labels/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }
      const productLabel = await storage.getProductLabel(id);
      if (!productLabel) {
        return res.status(404).json({ message: "Product label not found" });
      }
      res.json(productLabel);
    } catch (error) {
      console.error("Error fetching product label details:", error);
      res.status(500).json({ message: "Failed to fetch product label details" });
    }
  });

  // Update product label endpoint
  app.patch("/api/product-labels/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }

      // Check if product exists
      const existingProduct = await storage.getProductLabel(id);
      if (!existingProduct) {
        return res.status(404).json({ message: "Product label not found" });
      }

      // Check if product is retired (retired products should not be editable)
      if (existingProduct.isRetired) {
        return res.status(400).json({ message: "Cannot edit retired product" });
      }

      // Prepare update data
      const updateData = { ...req.body };

      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.createdBy;
      delete updateData.qrCode;
      delete updateData.isRetired;
      delete updateData.retiredAt;
      delete updateData.retiredBy;
      delete updateData.retiredReason;

      // Normalize expiry date if provided
      if (updateData.expiryDate) {
        updateData.expiryDate = normalizeDateToDDMMYYYY(updateData.expiryDate);
      }

      const user = req.user as any;

      // Update the product label
      const updatedProduct = await storage.updateProductLabel(id, updateData);

      // Log activity
      logActivity(
        user.id,
        user.username,
        "UPDATE_PRODUCT_LABEL",
        `Aggiornato prodotto: ${updatedProduct.productName}`,
        { 
          productId: id,
          changes: updateData
        }
      );

      res.json(updatedProduct);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid product data", errors: error.errors });
      }
      console.error("Error updating product label:", error);
      res.status(500).json({ message: "Failed to update product label" });
    }
  });

  app.post("/api/product-labels", isAuthenticated, productLabelValidation, async (req: Request, res: Response) => {
    try {
      console.log("Product label request body:", req.body);

      // Correggiamo la discrepanza storageRequirements vs storageInstructions
      const requestData = {...req.body};

      // Se c'è storageRequirements ma non storageInstructions, copialo
      if (requestData.storageRequirements && !requestData.storageInstructions) {
        requestData.storageInstructions = requestData.storageRequirements;
        delete requestData.storageRequirements; // Rimuovi il campo non necessario
      }

      const productLabelData = insertProductLabelSchema.parse(requestData);
      console.log("Validated product label data:", productLabelData);
      const user = req.user as any;

      // Normalize the expiry date to DD-MM-YYYY format before saving
      const normalizedExpiryDate = normalizeDateToDDMMYYYY(productLabelData.expiryDate);
      console.log(`Normalizing product expiry date: "${productLabelData.expiryDate}" -> "${normalizedExpiryDate}"`);

      // Creiamo prima l'etichetta prodotto con un QR code temporaneo e data normalizzata
      const tempProductLabel = await storage.createProductLabel({
        ...productLabelData,
        expiryDate: normalizedExpiryDate, // Use normalized date
        qrCode: "temp", // valore temporaneo
        createdBy: user.id,
      });

      // Ora che abbiamo l'ID reale del database, generiamo il QR code nel formato standard
      const standardQrValue = `product:${tempProductLabel.id}:${tempProductLabel.productName.replace(/\s+/g, '_')}`;

      // Genera il QR code come data URL
      const qrCode = await generateQRCode(standardQrValue);

      // Aggiorna l'etichetta prodotto con il QR code corretto
      const newProductLabel = await storage.updateProductLabel(tempProductLabel.id, { qrCode });

      // Log activity
      logActivity(
        user.id,
        user.username,
        "create_product_label",
        `L'utente ha aggiunto l'etichetta prodotto: ${productLabelData.productName}`
      );

      res.status(201).json(newProductLabel);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("ZodError:", error.errors);
        return res.status(400).json({ message: "Invalid product label data", errors: error.errors });
      }
      console.error("Error creating product label:", error);
      res.status(500).json({ message: "Failed to create product label" });
    }
  });

  // Test endpoint first
  app.post("/api/test-retirement/:id", async (req, res) => {
    console.log("🧪 TEST RETIREMENT ROUTE HIT - Product:", req.params.id);
    res.json({ success: true, message: "Test retirement route working", productId: req.params.id });
  });

  // Product retirement routes - MUST be before any catch-all routes
  app.post("/api/product-labels/:id/retire", async (req, res) => {
    console.log("🔄 RETIREMENT ROUTE HIT - Product:", req.params.id);
    console.log("📝 Request body:", req.body);

    // Force JSON response header
    res.setHeader('Content-Type', 'application/json');

    try {
      // Check authentication
      if (!req.isAuthenticated() || !req.user) {
        console.log("❌ Authentication failed for retirement request");
        return res.status(401).json({ message: "Authentication required" });
      }

      const productId = parseInt(req.params.id, 10);
      if (isNaN(productId)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }

      const retireData = retireProductSchema.parse(req.body);
      const user = req.user as any;

      // Check if product exists and is not already retired
      const product = await storage.getProductLabel(productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      if (product.isRetired) {
        return res.status(400).json({ message: "Product is already retired" });
      }

      // Remove product from all containers before retiring
      const containerProducts = await storage.getContainerProducts(productId);
      for (const cp of containerProducts) {
        await storage.removeProductFromContainer(cp.id);

        // Log the removal
        await logActivity(
          user.id,
          user.username,
          "REMOVE_PRODUCT_FROM_CONTAINER_ON_RETIRE",
          `Prodotto rimosso automaticamente dal contenitore durante il ritiro: ${product.productName}`,
          { 
            productId: productId,
            containerId: cp.containerId,
            reason: "Product retired"
          }
        );
      }

      // Retire the product
      const retiredProduct = await storage.retireProduct(productId, retireData.reason, user.id);

      // Log activity
      await logActivity(
        user.id,
        user.username,
        "RETIRE_PRODUCT",
        `Prodotto ritirato: ${product.productName} - Motivo: ${retireData.reason}`,
        { 
          productId: productId,
          reason: retireData.reason
        }
      );

      console.log("Product retired successfully:", retiredProduct);
      res.json(retiredProduct);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.log("Validation error in retirement:", error.errors);
        return res.status(400).json({ message: "Invalid retirement data", errors: error.errors });
      }
      console.error("Error retiring product:", error);
      res.status(500).json({ message: "Failed to retire product" });
    }
  });

  app.get("/api/product-labels/active", isAuthenticated, async (req, res) => {
    try {
      const activeProducts = await storage.getActiveProducts();
      res.json(activeProducts);
    } catch (error) {
      console.error("Error fetching active products:", error);
      res.status(500).json({ message: "Failed to fetch active products" });
    }
  });

  // Get available labels for DDT association (non-expired and not already associated with other DDTs)
  app.get("/api/product-labels/available-for-ddt/:ddtId", isAuthenticated, async (req, res) => {
    try {
      const ddtId = parseInt(req.params.ddtId, 10);
      if (isNaN(ddtId)) {
        return res.status(400).json({ message: "Invalid DDT ID" });
      }

      const allLabels = await storage.getAllProductLabels();
      const today = new Date();
      
      // Filter labels that are:
      // 1. Not retired
      // 2. Not expired (if expiry date is set)
      // 3. Not already associated with a DDT (ddtId is null) OR associated with the same DDT we're working on
      const availableLabels = allLabels.filter(label => {
        // Skip retired labels
        if (label.isRetired) return false;
        
        // Check expiry date
        if (label.expiryDate) {
          try {
            const expiryDate = new Date(label.expiryDate);
            if (expiryDate < today) return false;
          } catch (error) {
            // If date parsing fails, skip this label
            console.warn(`Invalid expiry date for label ${label.id}: ${label.expiryDate}`);
            return false;
          }
        }
        
        // Only include labels that are not associated with any DDT, or are associated with the current DDT
        return label.ddtId === null || label.ddtId === ddtId;
      });

      res.json(availableLabels);
    } catch (error) {
      console.error("Error fetching available labels for DDT:", error);
      res.status(500).json({ message: "Failed to fetch available labels" });
    }
  });

  // Associate a label with a DDT
  app.post("/api/product-labels/:id/associate-ddt", isAuthenticated, async (req, res) => {
    try {
      const labelId = parseInt(req.params.id, 10);
      const { ddtId } = req.body;
      const user = req.user as any;

      if (isNaN(labelId) || !ddtId) {
        return res.status(400).json({ message: "Invalid label ID or DDT ID" });
      }

      // Check if label exists and is not retired
      const label = await storage.getProductLabel(labelId);
      if (!label) {
        return res.status(404).json({ message: "Label not found" });
      }

      if (label.isRetired) {
        return res.status(400).json({ message: "Cannot associate retired label" });
      }

      // Check if label is expired
      if (label.expiryDate) {
        try {
          const expiryDate = new Date(label.expiryDate);
          const today = new Date();
          if (expiryDate < today) {
            return res.status(400).json({ message: "Cannot associate expired label" });
          }
        } catch (error) {
          return res.status(400).json({ message: "Invalid expiry date format" });
        }
      }

      // Check if DDT exists
      const ddt = await storage.getDDT(ddtId);
      if (!ddt) {
        return res.status(404).json({ message: "DDT not found" });
      }

      // Check if label is already associated with a different DDT
      if (label.ddtId && label.ddtId !== ddtId) {
        return res.status(400).json({ message: "Label is already associated with another DDT" });
      }

      // Update the label to associate it with the DDT
      const updatedLabel = await storage.updateProductLabel(labelId, { ddtId: ddtId });

      // Log activity
      await logActivity(
        user.id,
        user.username,
        "ASSOCIATE_LABEL_DDT",
        `Etichetta "${label.productName}" associata al DDT #${ddt.number}`,
        { 
          labelId: labelId,
          ddtId: ddtId,
          productName: label.productName,
          ddtNumber: ddt.number
        }
      );

      res.json(updatedLabel);
    } catch (error) {
      console.error("Error associating label with DDT:", error);
      res.status(500).json({ message: "Failed to associate label with DDT" });
    }
  });

  // Container routes
  app.get("/api/containers", isAuthenticated, async (req, res) => {
    try {
      const containers = await storage.getAllContainers();
      res.json(containers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch containers" });
    }
  })

  app.get("/api/containers/:id", async (req, res) => {
    try {
      const containerId = parseInt(req.params.id, 10);
      if (isNaN(containerId)) {
        return res.status(400).json({ message: "Invalid container ID" });
      }

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      res.json(container);
    } catch (error) {
      console.error("Error fetching container details:", error);
      res.status(500).json({ message: "Failed to fetch container details" });
    }
  })

  app.get("/api/containers/:id/products", isAuthenticated, async (req, res) => {
    try {
      const containerId = parseInt(req.params.id, 10);
      if (isNaN(containerId)) {
        return res.status(400).json({ message: "Invalid container ID" });
      }

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      const containerProducts = await storage.getContainerProducts(containerId);
      // Ottieni tutti i productLabelId dal containerProducts
      const productLabelIds = containerProducts.map(cp => cp.productLabelId);

      // Leggi tutti i product labels in una sola query
      const allProducts = await storage.getAllProductLabels();

      // Filtra solo i prodotti che appartengono a questo container
      const containerProductLabels = allProducts.filter(product => 
        productLabelIds.includes(product.id)
      );

      res.json(containerProductLabels);
    } catch (error) {
      console.error("Error fetching container products:", error);
      res.status(500).json({ message: "Failed to fetch container products" });
    }
  });

  app.post("/api/containers", isAuthenticated, containerValidation, async (req: Request, res: Response) => {
    try {
      const containerData = insertContainerSchema.parse(req.body);
      const user = req.user as any;

      // Check if container name already exists
      const existingContainer = await storage.getContainerByName(containerData.name);
      if (existingContainer) {
        return res.status(400).json({ message: "Container with this name already exists" });
      }

      // Creiamo prima il container senza QR code
      const tempContainer = await storage.createContainer({
        ...containerData,
        qrCode: "temp", // valore temporaneo
        createdBy: user.id,
      });

      // Ora che abbiamo l'ID reale del database, generiamo il QR code nel formato standard
      // Questo garantisce la consistenza con il formato usato in altre parti dell'app
      const standardQrValue = `container:${tempContainer.id}:${tempContainer.name.replace(/\s+/g, '_')}`;

      // Genera il QR code come data URL
      const qrCode = await generateQRCode(standardQrValue);

      // Aggiorna il container con il QR code corretto
      const newContainer = await storage.updateContainer(tempContainer.id, { qrCode });

      // Il container è già stato creato e aggiornato con il QR code corretto

      // Log activity
      logActivity(
        user.id,
        user.username,
        "create_container",
        `L'utente ha creato un nuovo contenitore: ${containerData.name}`
      );

      res.status(201).json(newContainer);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid container data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create container" });
    }
  });

  app.patch("/api/containers/:id/archive", isAuthenticated, async (req, res) => {
    try {
      const containerId = parseInt(req.params.id, 10);
      if (isNaN(containerId)) {
        return res.status(400).json({ message: "Invalid container ID" });
      }

      const { isArchived } = req.body;
      if (typeof isArchived !== 'boolean') {
        return res.status(400).json({ message: "isArchived must be a boolean" });
      }

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      const updatedContainer = await storage.updateContainer(containerId, { isArchived });

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        isArchived ? "archive_container" : "unarchive_container",
        `User ${isArchived ? "archived" : "unarchived"} container: ${container.name}`
      );

      res.json(updatedContainer);
    } catch (error) {
      res.status(500).json({ message: "Failed to update container" });
    }
  });

  // Container product association
  app.get("/api/container-products/product/:productLabelId", isAuthenticated, async (req, res) => {
    try {
      const productLabelId = parseInt(req.params.productLabelId, 10);
      const containerProducts = await storage.getContainerProducts(productLabelId);
      res.json(containerProducts);
    } catch (error) {
      console.error("Error fetching container products:", error);
      res.status(500).json({ message: "Failed to fetch container-product associations" });
    }
  });

  // Get all products in a specific container with details
  app.get("/api/container-products/container/:containerId", async (req, res) => {
    try {
      const containerId = parseInt(req.params.containerId, 10);
      if (isNaN(containerId)) {
        return res.status(400).json({ message: "Invalid container ID" });
      }

      // Get the container products
      const containerProducts = await storage.getContainerProducts(containerId);

      // If no products found, return empty array
      if (containerProducts.length === 0) {
        return res.json([]);
      }

      // Get all product labels in a single query
      const productLabels = await storage.getAllProductLabels();

      // Get all users in a single query
      const users = await storage.getAllUsers();

      // Map to dictionary for quick lookups
      const productLabelMap = new Map(
        productLabels.map(label => [label.id, label])
      );

      const userMap = new Map(
        users.map(user => [user.id, user])
      );

      // Combine the data to return detailed information
      const containerProductsWithDetails = containerProducts.map(cp => {
        // Ensure we have a valid user ID for the map lookup
        const createdById = typeof cp.createdBy === 'number' ? cp.createdBy : 0;

        // Assicuriamoci che gli ID siano numeri validi
        const productLabelId = typeof cp.productLabelId === 'number' ? cp.productLabelId : 0;

        // Utilizziamo i default values per evitare null
        const productLabel = productLabelMap.get(productLabelId);
        const user = userMap.get(createdById);

        return {
          ...cp,
          productLabel: productLabel || { 
            id: 0, 
            productName: "Prodotto non disponibile", 
            batchNumber: "N/A", 
            expiryDate: "N/A", 
            storageInstructions: "", 
            notes: "", 
            ddtId: 0, 
            qrCode: "", 
            isUsed: false, 
            createdAt: new Date().toISOString(), 
            createdBy: 0 
          },
          user: user || { 
            id: 0, 
            username: "Utente non disponibile", 
            password: "", 
            isAdmin: false, 
            email: null 
          }
        };
      });

      // Non abbiamo più bisogno di filtrare, poiché forniamo sempre valori predefiniti
      // Tuttavia, potremmo filtrare quelli con ID 0 se vogliamo nascondere gli elementi con dati incompleti
      // Qui decidiamo di mostrare tutto per trasparenza verso l'utente

      res.json(containerProductsWithDetails);
    } catch (error) {
      console.error("Error fetching container products with details:", error);
      res.status(500).json({ message: "Failed to fetch container products" });
    }
  });

  app.post("/api/container-products", isAuthenticated, async (req, res) => {
    try {
      const { containerId, productLabelId } = req.body;
      const user = req.user as any;

      console.log(`POST /api/container-products ricevuto:`, JSON.stringify(req.body));

      // Validazione input
      if (!containerId || !productLabelId) {
        return res.status(400).json({ message: "Container ID and Product Label ID are required" });
      }

      // Trasforma in numeri se necessario
      const containerIdNum = typeof containerId === 'string' ? parseInt(containerId, 10) : containerId;
      const productLabelIdNum = typeof productLabelId === 'string' ? parseInt(productLabelId, 10) : productLabelId;

      // Verifica container
      const container = await storage.getContainer(containerIdNum);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }
      console.log(`Container trovato: ${container.name} (ID: ${container.id}, currentItems: ${container.currentItems})`);

      // Verifica prodotto
      const productLabel = await storage.getProductLabel(productLabelIdNum);
      if (!productLabel) {
        return res.status(404).json({ message: "Product label not found" });
      }

      // Verifica capacità
      if (container.currentItems >= container.maxItems) {
        return res.status(400).json({ message: "Container is at maximum capacity" });
      }

      // Verifica associazione esistente
      const existing = await storage.getContainerProductAssociation(containerIdNum, productLabelIdNum);
      if (existing) {
        return res.status(400).json({ message: "Product is already associated with this container" });
      }

      try {
        // 1. Crea l'associazione
        const association = await storage.addProductToContainer({
          containerId: containerIdNum, 
          productLabelId: productLabelIdNum,
          tenantId: user.tenantId,
          createdBy: user.id,
        });

        // 2. Incrementa il contatore del container (con colonna corretta del DB)
        await pool.query('UPDATE containers SET "current_items" = "current_items" + 1 WHERE id = $1', [containerIdNum]);

        console.log(`Contatore aggiornato: ${container.name} ${container.currentItems} -> ${container.currentItems + 1}`);

        // 3. Log attività
        logActivity(
          user.id,
          user.username,
          "add_product_to_container",
          `L'utente ha aggiunto ${productLabel.productName} al contenitore ${container.name}`
        );

        // 4. Verifica capacità massima
        if (container.currentItems + 1 === container.maxItems) {
          logActivity(
            user.id,
            user.username,
            "container_limit_alert",
            `Container limit reached: ${container.name}`,
            { containerId: containerIdNum, maxItems: container.maxItems }
          );
        }

        res.status(201).json(association);
      } catch (innerError) {
        console.error("Errore durante l'aggiunta del prodotto:", innerError);
        throw innerError;
      }
    } catch (error) {
      console.error("Error adding product to container:", error);
      res.status(500).json({ message: "Failed to associate product with container" });
    }
  });

  // Endpoint per disassociare un prodotto da un container
  app.delete("/api/container-products/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      const user = req.user as any;

      console.log(`DELETE /api/container-products/${id} ricevuto`);
      console.log(`User: ${user?.username || 'sconosciuto'} (ID: ${user?.id || 'n/a'})`);

      if (isNaN(id)) {
        console.error(`ID non valido: ${req.params.id}`);
        return res.status(400).json({ message: "Invalid association ID" });
      }

      // Otteniamo direttamente i dettagli dell'associazione dal database
      // Prima cerca e salva l'associazione
      const containerProductsList = await db
        .select()
        .from(containerProducts)
        .where(eq(containerProducts.id, id));

      const association = containerProductsList.length > 0 ? containerProductsList[0] : null;

      if (!association) {
        console.error(`Associazione con ID ${id} non trovata`);
        return res.status(404).json({ message: "Association not found" });
      }

      // Ottieni i dettagli del prodotto e del container per i log
      let container = null;
      let productLabel = null;

      if (association.containerId !== null && association.containerId !== undefined) {
        container = await storage.getContainer(association.containerId);
      }

      if (association.productLabelId !== null && association.productLabelId !== undefined) {
        productLabel = await storage.getProductLabel(association.productLabelId);
      }

      // Elimina l'associazione
      await storage.removeProductFromContainer(id);
      console.log(`Associazione ${id} eliminata con successo`);

      // Aggiorna il contatore del container decrementandolo se necessario
      if (container && container.currentItems > 0) {
        await storage.updateContainer(container.id, {
          currentItems: container.currentItems - 1
        });
        console.log(`Decrementato contatore per container ${container.name}: ${container.currentItems} -> ${container.currentItems - 1}`);
      }

      // Log activity
      if (container && productLabel) {
        logActivity(
          user.id,
          user.username,
          "remove_product_from_container",
          `User removed ${productLabel.productName} from container ${container.name}`
        );
      } else {
        logActivity(
          user.id,
          user.username,
          "remove_product_from_container",
          `User removed association with ID ${id}`
        );
      }

      res.status(200).json({ message: "Product removed from container successfully" });
    } catch (error) {
      console.error("Error removing product from container:", error);
      res.status(500).json({ message: "Failed to remove product from container" });
    }
  });

  app.post("/api/containers/:containerId/products/:productLabelId", isAuthenticated, async (req, res) => {
    try {
      const containerId = parseInt(req.params.containerId, 10);
      const productLabelId = parseInt(req.params.productLabelId, 10);

      if (isNaN(containerId) || isNaN(productLabelId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }

      const container = await storage.getContainer(containerId);
      if (!container) {
        return res.status(404).json({ message: "Container not found" });
      }

      const productLabel = await storage.getProductLabel(productLabelId);
      if (!productLabel) {
        return res.status(404).json({ message: "Product label not found" });
      }

      // Check if container is at capacity
      if (container.currentItems >= container.maxItems) {
        return res.status(400).json({ message: "Container is at maximum capacity" });
      }

      // Check if association already exists
      const existing = await storage.getContainerProductAssociation(containerId, productLabelId);
      if (existing) {
        return res.status(400).json({ message: "Product is already associated with this container" });
      }

      const user = req.user as any;
      const association = await storage.addProductToContainer({
        containerId,
        productLabelId,
        tenantId: user.tenantId,
        createdBy: user.id,
      });

      // Update container item count
      await storage.updateContainer(containerId, { 
        currentItems: container.currentItems + 1 
      });

      // Log activity
      logActivity(
        user.id,
        user.username,
        "add_product_to_container",
        `L'utente ha aggiunto ${productLabel.productName} al contenitore ${container.name}`
      );

      // Check if container is now at capacity and log alert if so
      const updatedContainer = await storage.getContainer(containerId);
      if (updatedContainer && updatedContainer.currentItems === updatedContainer.maxItems) {
        logActivity(
          user.id,
          user.username,
          "container_limit_alert",
          `Container limit reached: ${container.name}`,
          { containerId, maxItems: container.maxItems }
        );
      }

      res.status(201).json(association);
    } catch (error) {
      res.status(500).json({ message: "Failed to associate product with container" });
    }
  });

  // Activity logs - Endpoint principale per dati JSON
  app.get("/api/activity-logs", isAuthenticated, async (req, res) => {
    try {
      const filters: ActivityLogFilters = {
        startDate: req.query.startDate as string | undefined,
        endDate: req.query.endDate as string | undefined,
        userId: req.query.userId ? parseInt(req.query.userId as string, 10) : undefined,
        containerId: req.query.containerId ? parseInt(req.query.containerId as string, 10) : undefined,
        action: req.query.action as string | undefined
      };

      const logs = await storage.getActivityLogs(Object.keys(filters).length > 0 ? filters : undefined);

      // Risposta normale JSON
      res.json(logs);
    } catch (error) {
      console.error("Errore nel recupero dei log:", error);
      res.status(500).json({ message: "Failed to fetch activity logs" });
    }
  });

  // Endpoint per eliminare i log più vecchi di X anni
  app.post("/api/activity-logs/cleanup", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const { retentionYears } = req.body;

      if (!retentionYears || typeof retentionYears !== 'number' || ![1, 2].includes(retentionYears)) {
        return res.status(400).json({ message: "Il periodo di conservazione deve essere di 1 o 2 anni" });
      }

      // Calcola la data limite per i log da mantenere
      const cutoffDate = new Date();
      cutoffDate.setFullYear(cutoffDate.getFullYear() - retentionYears);

      // Elimina i log più vecchi della data limite
      const deletedCount = await storage.deleteOldActivityLogs(cutoffDate);

      // Log dell'operazione
      const user = req.user as any;
      await logActivity(
        user.id,
        user.username,
        "cleanup_activity_logs",
        `Eliminati ${deletedCount} log di attività più vecchi di ${retentionYears} anni`,
        { retentionYears, cutoffDate: cutoffDate.toISOString(), deletedCount }
      );

      res.json({ 
        success: true, 
        message: `Eliminati ${deletedCount} log di attività più vecchi di ${retentionYears} anni`, 
        deletedCount 
      });
    } catch (error) {
      console.error("Errore nell'eliminazione dei log:", error);
      res.status(500).json({ message: "Errore nell'eliminazione dei log di attività", error: (error as Error).message });
    }
  });

  // Endpoint dedicato per l'esportazione CSV
  app.get("/api/activity-logs/export/csv", isAuthenticated, async (req, res) => {
    try {
      const filters: ActivityLogFilters = {
        startDate: req.query.startDate as string | undefined,
        endDate: req.query.endDate as string | undefined,
        userId: req.query.userId ? parseInt(req.query.userId as string, 10) : undefined,
        containerId: req.query.containerId ? parseInt(req.query.containerId as string, 10) : undefined,
        action: req.query.action as string | undefined
      };

      const logs = await storage.getActivityLogs(Object.keys(filters).length > 0 ? filters : undefined);

      // Prepara i dati per l'esportazione
      const formattedLogs = logs.map(log => ({
        ID: log.id,
        Utente: log.username,
        Azione: log.action,
        Dettagli: log.details,
        Data: new Date(log.timestamp).toLocaleDateString('it-IT'),
        Ora: new Date(log.timestamp).toLocaleTimeString('it-IT')
      }));

      // Imposta gli header per forzare il download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="activity_logs_${new Date().toISOString().split('T')[0]}.csv"`);

      // Genera intestazioni CSV
      const headers = Object.keys(formattedLogs[0]).join(',');
      // Genera righe CSV
      const csvRows = formattedLogs.map(log => 
        Object.values(log).map(val => `"${val}"`).join(',')
      );

      // Invia CSV
      res.send([headers, ...csvRows].join('\n'));
    } catch (error) {
      console.error("Errore nell'esportazione CSV:", error);
      res.status(500).json({ message: "Failed to export CSV" });
    }
  });

  // Endpoint dedicato per l'esportazione Excel usando ExcelJS
  app.get("/api/activity-logs/export/excel", isAuthenticated, async (req, res) => {
    try {
      const filters: ActivityLogFilters = {
        startDate: req.query.startDate as string | undefined,
        endDate: req.query.endDate as string | undefined,
        userId: req.query.userId ? parseInt(req.query.userId as string, 10) : undefined,
        containerId: req.query.containerId ? parseInt(req.query.containerId as string, 10) : undefined,
        action: req.query.action as string | undefined
      };

      const logs = await storage.getActivityLogs(Object.keys(filters).length > 0 ? filters : undefined);

      // Prepara i dati per l'esportazione
      const formattedLogs: Record<string, string | number>[] = logs.map(log => ({
        ID: log.id,
        Utente: log.username,
        Azione: log.action,
        Dettagli: log.details,
        Data: new Date(log.timestamp).toLocaleDateString('it-IT'),
        Ora: new Date(log.timestamp).toLocaleTimeString('it-IT')
      }));

      // Crea un nuovo workbook Excel
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Log Attività');

      // Definisci le colonne
      if (formattedLogs.length > 0) {
        const columns = Object.keys(formattedLogs[0]).map(key => ({
          header: key,
          key: key,
          width: key === 'Dettagli' ? 50 : 20
        }));

        worksheet.columns = columns;

        // Stile per l'intestazione
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };

        // Aggiungi i dati
        formattedLogs.forEach(log => {
          worksheet.addRow(log);
        });
      } else {
        // Se non ci sono log, crea una tabella vuota con intestazioni predefinite
        worksheet.columns = [
          { header: 'ID', key: 'id', width: 10 },
          { header: 'Utente', key: 'username', width: 20 },
          { header: 'Azione', key: 'action', width: 20 },
          { header: 'Dettagli', key: 'details', width: 50 },
          { header: 'Data', key: 'date', width: 15 },
          { header: 'Ora', key: 'time', width: 15 }
        ];

        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };

        // Aggiungi una riga che indica che non ci sono dati
        worksheet.addRow({
          id: '',
          username: '',
          action: 'Nessun dato disponibile',
          details: '',
          date: '',
          time: ''
        });
      }

      // Applica bordi a tutte le celle
      worksheet.eachRow(row => {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // Imposta il content type per Excel
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="activity_logs_${new Date().toISOString().split('T')[0]}.xlsx"`);

      // Scrivi il file direttamente nella risposta
      await workbook.xlsx.write(res);

      // Concludi la risposta
      res.end();
    } catch (error: any) {
      console.error("Errore nell'esportazione Excel:", error);
      res.status(500).json({ message: "Failed to export Excel", error: error.message || 'Unknown error' });
    }
  });

  // Endpoint dedicato per l'esportazione PDF (generazione diretta PDF)
  app.get("/api/activity-logs/export/pdf", isAuthenticated, async (req, res) => {
    try {
      const filters: ActivityLogFilters = {
        startDate: req.query.startDate as string | undefined,
        endDate: req.query.endDate as string | undefined,
        userId: req.query.userId ? parseInt(req.query.userId as string, 10) : undefined,
        containerId: req.query.containerId ? parseInt(req.query.containerId as string, 10) : undefined,
        action: req.query.action as string | undefined
      };

      // Ottieni i dati filtrati
      const logs = await storage.getActivityLogs(Object.keys(filters).length > 0 ? filters : undefined);

      // Formatta i dati per la presentazione
      const formattedLogs = logs.map(log => ({
        ID: log.id,
        Utente: log.username,
        Azione: log.action,
        Dettagli: log.details,
        Data: new Date(log.timestamp).toLocaleDateString('it-IT'),
        Ora: new Date(log.timestamp).toLocaleTimeString('it-IT')
      }));

      // Inizializza il documento PDF - A4 landscape per avere più spazio per i dati
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // Aggiungi titolo
      pdf.setFontSize(18);
      pdf.setTextColor(44, 62, 80); // #2c3e50
      pdf.text('Registro Attività', 14, 15);

      // Aggiungi data di generazione
      pdf.setFontSize(10);
      pdf.setTextColor(127, 140, 141); // #7f8c8d
      const now = new Date();
      pdf.text(`Generato: ${now.toLocaleDateString('it-IT')} ${now.toLocaleTimeString('it-IT')}`, 14, 22);

      // Aggiungi filtri applicati
      pdf.setFontSize(10);
      pdf.setTextColor(44, 62, 80);
      let filtersText = 'Filtri applicati: ';
      if (Object.keys(filters).filter(k => !!filters[k as keyof ActivityLogFilters]).length === 0) {
        filtersText += 'Nessun filtro applicato';
      } else {
        if (filters.startDate) filtersText += `Data inizio: ${filters.startDate} `;
        if (filters.endDate) filtersText += `Data fine: ${filters.endDate} `;
        if (filters.userId) filtersText += `ID Utente: ${filters.userId} `;
        if (filters.containerId) filtersText += `ID Contenitore: ${filters.containerId} `;
        if (filters.action) filtersText += `Azione: ${filters.action} `;
      }
      pdf.text(filtersText, 14, 30);

      // Prepara i dati per la tabella
      if (formattedLogs.length > 0) {
        const columns = Object.keys(formattedLogs[0]).map(header => ({
          header,
          dataKey: header
        }));

        const rows = formattedLogs.map(log => {
          const row: any = {};
          Object.keys(log).forEach(key => {
            // Se la stringa è troppo lunga, tagliala per evitare problemi di spazio
            if (typeof log[key as keyof typeof log] === 'string' && (log[key as keyof typeof log] as string).length > 40) {
              row[key] = (log[key as keyof typeof log] as string).substring(0, 37) + '...';
            } else {
              row[key] = log[key as keyof typeof log];
            }
          });
          return row;
        });

        // Crea la tabella con i dati
        autoTable(pdf, {
          startY: 35,
          head: [columns.map(col => col.header)],
          body: rows.map(row => columns.map(col => row[col.dataKey])),
          theme: 'grid',
          headStyles: {
            fillColor: [52, 152, 219], // #3498db
            textColor: 255,
            fontStyle: 'bold'
          },
          alternateRowStyles: {
            fillColor: [242, 242, 242] // #f2f2f2
          },
          margin: { top: 35 }
        });
      } else {
        // Se non ci sono dati, mostra un messaggio
        pdf.setFontSize(12);
        pdf.setTextColor(44, 62, 80);
        pdf.text('Nessun dato trovato con i filtri applicati.', 14, 40);
      }

      // Aggiungi piè di pagina
      const pageCount = (pdf as any).internal.getNumberOfPages();
      pdf.setFontSize(8);
      pdf.setTextColor(127, 140, 141);
      for (let i = 1; i <= pageCount; i++) {
        pdf.setPage(i);
        pdf.text(`© ${new Date().getFullYear()} HACCP Tracker - Pagina ${i} di ${pageCount}`, pdf.internal.pageSize.width / 2, pdf.internal.pageSize.height - 10, { align: 'center' });
      }

      // Genera il PDF come buffer
      const pdfOutput = pdf.output();
      const buffer = Buffer.from(pdfOutput, 'binary');

      // Configura le intestazioni HTTP per il download diretto del file PDF
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="attivita_report_${new Date().toISOString().split('T')[0]}.pdf"`);
      res.setHeader('Content-Length', buffer.length);

      // Invia il PDF come risposta
      res.send(buffer);

    } catch (error: any) {
      console.error("Errore nell'esportazione PDF:", error);
      res.status(500).json({ 
        message: "Errore durante la generazione del PDF", 
        error: error.message || 'Unknown error' 
      });
    }
  });

  // OCR processing routes
  app.post("/api/ocr/process-ddt", isAuthenticated, async (req, res) => {
    try {
      const { imageData } = req.body;

      if (!imageData || typeof imageData !== 'string') {
        return res.status(400).json({ message: "Invalid image data" });
      }

      // Get AI configuration from unified AI manager
      const user = req.user as any;
      const { aiManager } = await import('./lib/ai-manager');
      const aiConfig = await aiManager.getCurrentAIConfig(user.id);
      const aiProvider = aiConfig.provider;

      let result;

      if (aiProvider === 'gemini') {
        // Import Gemini module and get prompt
        const gemini = await import('./lib/gemini');
        result = await AIErrorHandler.withRetry(
          () => gemini.processDDTWithGemini(imageData),
          {
            provider: 'gemini',
            category: 'ddt',
            imageSize: imageData.length
          },
          3
        );
      } else {
        // Use Claude (default)
        result = await AIErrorHandler.withRetry(
          () => claude.processDDT(imageData),
          {
            provider: 'claude',
            category: 'ddt',
            imageSize: imageData.length
          },
          3
        );
      }

      if (!result.success) {
        // Se il risultato contiene un AIError, usalo per una risposta formattata
        if (result.error && typeof result.error === 'object' && 'type' in result.error && result.error.type) {
          return res.status(500).json(AIErrorHandler.formatForAPI(result.error));
        }
        return res.status(500).json({ 
          success: false,
          error: result.error || "OCR processing failed",
          type: "UNKNOWN_ERROR",
          suggestions: ["Riprovare l'operazione", "Verificare la qualità dell'immagine"] 
        });
      }

      // Log activity
      logActivity(
        user.id,
        user.username,
        "process_ddt_ocr",
        "L'utente ha elaborato un documento di trasporto con OCR"
      );

      res.json(result.data);
    } catch (error: any) {
      console.error("OCR processing error:", error);

      // Gestione errori più dettagliata
      if (error?.error?.error?.message?.includes('invalid base64 data')) {
        return res.status(400).json({
          message: "Invalid base64 image format",
          details: "The image provided is not a valid base64 encoded image"
        });
      }

      res.status(500).json({ 
        message: "Failed to process image",
        details: error?.message || "Unknown error"
      });
    }
  });

  app.post("/api/ocr/process-label", isAuthenticated, async (req, res) => {
    try {
      const { imageData } = req.body;

      if (!imageData || typeof imageData !== 'string') {
        return res.status(400).json({ message: "Invalid image data" });
      }

      console.log("Processing label image - base64 length:", imageData.length);

      // Process image with Claude
      const result = await processProductLabel(imageData);

      // Verifica che il risultato contenga dati validi
      if (!result.success || !result.data) {
        return res.status(500).json({ 
          message: "Failed to extract data from label",
          details: result.error || "No data returned from OCR processing"
        });
      }

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "process_label_ocr",
        "L'utente ha elaborato un'etichetta prodotto con OCR"
      );

      // Standardizza i campi prima di inviarli al client
      const data = result.data;

      // Se c'è storageRequirements ma non storageInstructions, copialo
      if (data && data.storageRequirements && !data.storageInstructions) {
        data.storageInstructions = data.storageRequirements;
      }

      res.json(data);
    } catch (error: any) {
      console.error("OCR processing error:", error);

      // Gestione errori più dettagliata
      if (error?.error?.error?.message?.includes('invalid base64 data')) {
        return res.status(400).json({
          message: "Invalid base64 image format",
          details: "The image provided is not a valid base64 encoded image"
        });
      }

      res.status(500).json({ 
        message: "Failed to process image",
        details: error?.message || "Unknown error"
      });
    }
  });

  app.post("/api/ocr/process-multiple-labels", isAuthenticated, async (req, res) => {
    try {
      const { images } = req.body;

      if (!images || !Array.isArray(images) || images.length === 0) {
        return res.status(400).json({ message: "Invalid images data. Expected non-empty array." });
      }

      if (images.length > 3) {
        return res.status(400).json({ message: "Too many images. Maximum 3 images allowed." });
      }

      // Validate all images are strings
      for (const image of images) {
        if (!image || typeof image !== 'string') {
          return res.status(400).json({ message: "All images must be valid base64 strings" });
        }
      }

      console.log(`Processing ${images.length} label images - lengths:`, images.map(img => img.length));

      // Process all images with Claude
      const result = await processMultipleProductLabels(images);

      // Verifica che il risultato contenga dati validi
      if (!result.success || !result.data) {
        return res.status(500).json({ 
          message: "Failed to extract data from labels",
          details: result.error || "No data returned from OCR processing"
        });
      }

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "process_multiple_labels_ocr",
        `L'utente ha elaborato ${images.length} etichette prodotto con OCR`
      );

      // Standardizza i campi prima di inviarli al client
      const data = result.data;

      // Se c'è storageRequirements ma non storageInstructions, copialo
      if (data && data.storageRequirements && !data.storageInstructions) {
        data.storageInstructions = data.storageRequirements;
      }

      res.json(data);
    } catch (error: any) {
      console.error("Multiple labels OCR processing error:", error);

      // Gestione errori più dettagliata
      if (error?.error?.error?.message?.includes('invalid base64 data')) {
        return res.status(400).json({
          message: "Invalid base64 image format",
          details: "One or more images provided are not valid base64 encoded images"
        });
      }

      res.status(500).json({ 
        message: "Failed to process images",
        details: error?.message || "Unknown error"
      });
    }
  });

  // Prompt management routes
  app.get("/api/prompts", isAuthenticated, async (req, res) => {
    try {
      const prompts = await claude.getAllPrompts();
      // Forza no-cache per evitare problemi di cache del browser
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.set('Pragma', 'no-cache');
      res.set('Expires', '0');
      res.json(prompts);
    } catch (error) {
      console.error("Error in /api/prompts:", error);
      res.status(500).json({ message: "Failed to fetch prompts" });
    }
  });

  app.get("/api/prompts/category/:category", isAuthenticated, async (req, res) => {
    try {
      const { category } = req.params;
      const prompts = await claude.getPromptsByCategory(category);
      res.json(prompts);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch prompts" });
    }
  });

  app.post("/api/prompts", isAuthenticated, async (req, res) => {
    try {
      const { name, content, description, category, isDefault } = req.body;

      if (!name || !content || !category) {
        return res.status(400).json({ message: "Name, content, and category are required" });
      }

      const prompt = claude.addPrompt({
        id: "", // Will be generated
        name,
        content,
        description,
        category: category as "ddt" | "label" | "general",
        isDefault: !!isDefault
      });

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "create_prompt",
        `L'utente ha creato un nuovo modello: ${name}`
      );

      res.status(201).json(prompt);
    } catch (error) {
      res.status(500).json({ message: "Failed to create prompt" });
    }
  });

  app.put("/api/prompts/:id", isAuthenticated, async (req, res) => {
    try {
      const { id } = req.params;
      const { name, content, description, category, isDefault } = req.body;

      if (!name || !content || !category) {
        return res.status(400).json({ message: "Name, content, and category are required" });
      }

      const updatedPrompt = claude.updatePrompt(id, {
        id,
        name,
        content,
        description,
        category: category as "ddt" | "label" | "general",
        isDefault: !!isDefault
      });

      if (!updatedPrompt) {
        return res.status(404).json({ message: "Prompt not found" });
      }

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "update_prompt",
        `User updated prompt: ${name}`
      );

      res.json(updatedPrompt);
    } catch (error) {
      res.status(500).json({ message: "Failed to update prompt" });
    }
  });

  app.delete("/api/prompts/:id", isAuthenticated, async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = claude.deletePrompt(id);

      if (!deleted) {
        return res.status(404).json({ message: "Prompt not found" });
      }

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "delete_prompt",
        `User deleted a prompt`
      );

      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete prompt" });
    }
  });

  app.post("/api/prompts/reset", isAuthenticated, isAdmin, async (req, res) => {
    try {
      claude.resetDefaultPrompts();

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "reset_default_prompts",
        "User reset default prompts"
      );

      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to reset default prompts" });
    }
  });

  // API per i modelli Claude AI - ora utilizza il sistema AI unificato
  app.get("/api/claude/models", isAuthenticated, async (req, res) => {
    try {
      const { aiManager } = await import('./lib/ai-manager');
      const settings = await aiManager.getAISettings();
      
      // Utilizza la nuova funzione per ottenere tutti i modelli disponibili
      const availableModels = claude.getAvailableClaudeModels();

      res.json({
        models: availableModels,
        defaultModel: settings.claudeProvider.defaultModel,
        currentModel: settings.claudeProvider.defaultModel
      });
    } catch (error) {
      console.error("Errore nel recupero modelli Claude:", error);
      res.status(500).json({ message: "Failed to retrieve Claude models" });
    }
  });

  // API per i modelli Gemini AI
  app.get("/api/gemini/models", isAuthenticated, async (req, res) => {
    try {
      const gemini = await import('./lib/gemini');
      const modelsData = await gemini.getGeminiModels();
      res.json(modelsData);
    } catch (error) {
      console.error("Errore nel recupero modelli Gemini:", error);
      res.status(500).json({ message: "Failed to retrieve Gemini models" });
    }
  });

  // API per impostare il modello Claude
  app.post("/api/claude/model", isAuthenticated, async (req, res) => {
    try {
      const { modelId } = req.body;

      if (!modelId) {
        return res.status(400).json({ message: "Model ID is required" });
      }

      // Verifica che il modello sia tra quelli disponibili
      const validModels = Object.values(claude.CLAUDE_MODELS);
      if (!validModels.includes(modelId)) {
        console.log(`Invalid model attempted: ${modelId}. Valid models:`, validModels);
        return res.status(400).json({ message: "Invalid model ID" });
      }

      // Imposta il modello
      const selectedModel = await claude.setSelectedClaudeModel(modelId);

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "set_claude_model",
        `User changed Claude model to: ${modelId}`
      );

      res.json({ success: true, model: selectedModel });
    } catch (error) {
      res.status(500).json({ message: "Failed to set Claude model" });
    }
  });

  // API routes for container types
  app.get("/api/container-types", isAuthenticated, async (req, res) => {
    try {
      console.log("GET /api/container-types chiamato da:", req.user);
      const containerTypes = await storage.getAllContainerTypes();
      console.log("Container types recuperati:", containerTypes);
      res.json(containerTypes);
    } catch (error) {
      console.error("Error fetching container types:", error);
      res.status(500).json({ message: "Failed to fetch container types" });
    }
  });

  app.get("/api/container-types/:id", isAuthenticated, async (req, res) => {
    try {
      const typeId = parseInt(req.params.id, 10);
      if (isNaN(typeId)) {
        return res.status(400).json({ message: "Invalid container type ID" });
      }

      const containerType = await storage.getContainerType(typeId);
      if (!containerType) {
        return res.status(404).json({ message: "Container type not found" });
      }

      res.json(containerType);
    } catch (error) {
      console.error("Error fetching container type details:", error);
      res.status(500).json({ message: "Failed to fetch container type details" });
    }
  });

  app.post("/api/container-types", isAuthenticated, async (req, res) => {
    try {
      const { value, label, description } = req.body;

      if (!value || !label) {
        return res.status(400).json({ message: "Value and label are required" });
      }

      // Check if a type with this value already exists
      const existingType = await storage.getContainerTypeByValue(value);
      if (existingType) {
        return res.status(400).json({ message: "Container type with this value already exists" });
      }

      const user = req.user as any;
      const containerType = await storage.createContainerType({
        value,
        label,
        description,
        isActive: true,
        tenantId: user.tenantId,
        createdBy: user.id
      });

      // Log activity
      logActivity(
        user.id,
        user.username,
        "create_container_type",
        `L'utente ha creato un nuovo tipo di contenitore: ${label}`
      );

      res.status(201).json(containerType);
    } catch (error) {
      console.error("Error creating container type:", error);
      res.status(500).json({ message: "Failed to create container type" });
    }
  });

  app.put("/api/container-types/:id", isAuthenticated, async (req, res) => {
    try {
      const typeId = parseInt(req.params.id, 10);
      if (isNaN(typeId)) {
        return res.status(400).json({ message: "Invalid container type ID" });
      }

      const { value, label, description, isActive } = req.body;

      if (!value || !label) {
        return res.status(400).json({ message: "Value and label are required" });
      }

      // Check if type exists
      const containerType = await storage.getContainerType(typeId);
      if (!containerType) {
        return res.status(404).json({ message: "Container type not found" });
      }

      // If changing value, check that the new value doesn't conflict with another type
      if (value !== containerType.value) {
        const existingType = await storage.getContainerTypeByValue(value);
        if (existingType && existingType.id !== typeId) {
          return res.status(400).json({ message: "Another container type with this value already exists" });
        }
      }

      const updatedType = await storage.updateContainerType(typeId, { 
        value, 
        label, 
        description,
        isActive: isActive !== undefined ? isActive : containerType.isActive
      });

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "update_container_type",
        `User updated container type: ${label}`
      );

      res.json(updatedType);
    } catch (error) {
      console.error("Error updating container type:", error);
      res.status(500).json({ message: "Failed to update container type" });
    }
  });

  // API per elaborare direttamente un'etichetta caricata
  app.post("/api/direct-upload/process-label", isAuthenticated, async (req, res) => {
    try {
      const { imageData } = req.body;

      if (!imageData || typeof imageData !== 'string') {
        return res.status(400).json({ message: "Invalid image data" });
      }

      console.log("Direct upload label processing - base64 length:", imageData.length);

      // Process image with Claude
      const result = await processProductLabel(imageData);

      // Log activity
      const user = req.user as any;
      logActivity(
        user.id,
        user.username,
        "direct_upload_label_ocr",
        "L'utente ha elaborato un'etichetta prodotto tramite caricamento diretto"
      );

      // Standardizza i campi prima di inviarli al client
      const data = result.data;

      // Se c'è storageRequirements ma non storageInstructions, copialo
      if (data.storageRequirements && !data.storageInstructions) {
        data.storageInstructions = data.storageRequirements;
      }

      res.json(data);
    } catch (error: any) {
      console.error("Direct upload OCR processing error:", error);

      res.status(500).json({ 
        message: "Failed to process uploaded image",
        details: error?.message || "Unknown error"
      });
    }
  });

  // API per reset database (solo amministratori)
  const execPromise = promisify(exec);

  app.post("/api/system/reset-database", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const admin = req.user as User;

      // Log dell'inizializzazione dell'operazione
      await logActivity(
        admin.id,
        admin.username,
        "database_reset_initiated",
        `Reset database iniziato da ${admin.username}`
      );

      // Eseguiamo direttamente lo script SQL di reset
      const { stdout, stderr } = await execPromise(`psql "${process.env.DATABASE_URL}" -f reset_database.sql`);

      // Log del completamento dell'operazione
      await logActivity(
        admin.id,
        admin.username,
        "database_reset_completed",
        `Reset database completato`,
        { stdout }
      );

      return res.json({ 
        success: true, 
        message: "Reset database completato con successo",
        details: stdout 
      });
    } catch (error) {
      console.error("Errore durante il reset del database:", error);

      // Log dell'errore
      if (req.user) {
        const admin = req.user as User;
        await logActivity(
          admin.id,
          admin.username,
          "database_reset_failed",
          `Errore durante il reset del database: ${(error as Error).message}`
        );
      }

      return res.status(500).json({ 
        success: false, 
        message: "Errore durante il reset del database",
        error: (error as Error).message
      });
    }
  });

  // Solo in ambiente di sviluppo, aggiungi gli strumenti di sviluppo
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 Strumenti di sviluppo attivati');
    // Gli strumenti di sviluppo sono già registrati all'inizio della funzione registerRoutes
  }

  // User feedback routes
  app.post("/api/feedback", isAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const { message } = req.body;

      if (!message || message.trim().length === 0) {
        return res.status(400).json({ message: "Il messaggio è obbligatorio" });
      }

      const feedback = await storage.createUserFeedback({
        userId: user.id,
        username: user.username,
        message: message.trim(),
        tenantId: user.tenantId
      });

      // Log activity
      logActivity(
        user.id,
        user.username,
        "submit_feedback",
        `L'utente ha inviato un feedback`
      );

      res.status(201).json(feedback);
    } catch (error) {
      console.error("Error creating feedback:", error);
      res.status(500).json({ message: "Errore durante l'invio del feedback" });
    }
  });

  app.get("/api/feedback", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const feedback = await storage.getAllUserFeedback();
      res.json(feedback);
    } catch (error) {
      console.error("Error fetching feedback:", error);
      res.status(500).json({ message: "Errore durante il recupero dei feedback" });
    }
  });

  app.delete("/api/feedback/:id", isAuthenticated, isAdmin, async (req, res) => {
    try {
      const user = req.user as any;
      const feedbackId = parseInt(req.params.id, 10);

      if (isNaN(feedbackId)) {
        return res.status(400).json({ message: "ID feedback non valido" });
      }

      await storage.deleteUserFeedback(feedbackId);

      // Log activity
      logActivity(
        user.id,
        user.username,
        "delete_feedback",
        `L'amministratore ha eliminato il feedback con ID ${feedbackId}`
      );

      res.json({ success: true, message: "Feedback eliminato con successo" });
    } catch (error) {
      console.error("Error deleting feedback:", error);
      res.status(500).json({ message: "Errore durante l'eliminazione del feedback" });
    }
  });

  // Registra le route degli utenti
  registerUserRoutes(app);

  // Registra altre route modulari
  registerClaudeRoutes(app);
  registerAiRoutes(app);
  registerSystemRoutes(app);
  registerContainerRoutes(app);
  
  // Registra le route per lo stato del sistema
  app.use('/api/system', createSystemStatusRoutes());
  
  // Registra le route per monitoring e health checks
  app.use('/api/monitoring', monitoringRoutes);

  const httpServer = createServer(app);
  return httpServer;
}