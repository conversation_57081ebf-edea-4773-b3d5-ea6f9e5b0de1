/**
 * HIGH-RISK SECURITY MIDDLEWARE
 * Comprehensive integration of all high-risk vulnerability fixes
 */

import { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { 
  SecureAP<PERSON><PERSON><PERSON>Manager, 
  SQLInjectionProtector, 
  SecureCORSManager,
  VulnerableDependencyMonitor,
  highRiskSecurityMiddleware,
  xssProtectionMiddleware
} from '../lib/high-risk-security-manager.js';
import { SecureLogger } from '../lib/secure-logger.js';

// Initialize all security managers
let securityInitialized = false;

function initializeHighRiskSecurity() {
  if (securityInitialized) return;
  
  try {
    // Initialize CORS security
    SecureCORSManager.initializeSecureCORS();
    
    // Initialize API key management
    const apiKeyManager = SecureAPIKeyManager.getInstance();
    
    // Check for vulnerable dependencies
    VulnerableDependencyMonitor.checkVulnerabilities();
    
    securityInitialized = true;
    SecureLogger.audit('High-risk security initialization completed', {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    SecureLogger.error('Failed to initialize high-risk security', error);
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Production security initialization failed');
    }
  }
}

// SQL Injection Protection Middleware
const sqlInjectionProtection = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Sanitize all input data
    if (req.body) {
      req.body = SQLInjectionProtector.sanitizeInput(req.body);
    }
    if (req.query) {
      req.query = SQLInjectionProtector.sanitizeInput(req.query);
    }
    if (req.params) {
      req.params = SQLInjectionProtector.sanitizeInput(req.params);
    }
    
    next();
  } catch (error) {
    SecureLogger.error('SQL injection protection failed', error);
    res.status(500).json({ error: 'Security validation failed' });
  }
};

// Secure API Key Validation Middleware
const secureApiKeyValidation = (requiredKeyTypes: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      return res.status(401).json({ 
        error: "API key required",
        code: "MISSING_API_KEY"
      });
    }
    
    try {
      const apiKeyManager = SecureAPIKeyManager.getInstance();
      
      // Validate API key access
      const isValidAccess = apiKeyManager.validateApiKeyAccess(req, 'general');
      if (!isValidAccess) {
        return res.status(403).json({
          error: "API key access denied",
          code: "ACCESS_DENIED"
        });
      }
      
      // For now, we'll implement basic validation
      // In production, this would use the encrypted key system
      const validKeys = {
        'admin': process.env.ADMIN_API_KEY,
        'system': process.env.SYSTEM_API_KEY,
        'ai': process.env.AI_SERVICE_API_KEY
      };
      
      const isValidKey = Object.entries(validKeys).some(([type, key]) => {
        if (!key) return false;
        if (requiredKeyTypes.length > 0 && !requiredKeyTypes.includes(type)) return false;
        return key === apiKey;
      });
      
      if (!isValidKey) {
        SecureLogger.audit('Invalid API key attempt', {
          ip: req.ip,
          userAgent: req.get('User-Agent')?.substring(0, 100),
          timestamp: new Date().toISOString()
        });
        
        return res.status(403).json({ 
          error: "Invalid or unauthorized API key",
          code: "UNAUTHORIZED_API_KEY"
        });
      }
      
      next();
    } catch (error) {
      SecureLogger.error('API key validation error', error);
      res.status(500).json({ error: 'Security validation failed' });
    }
  };
};

// Enhanced Security Headers Middleware
const enhancedSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Basic helmet configuration
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: process.env.NODE_ENV === 'production' 
          ? ["'self'"] 
          : ["'self'", "'unsafe-eval'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: process.env.NODE_ENV === 'production'
          ? ["'self'"]
          : ["'self'", "ws:", "wss:"],
        frameAncestors: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"]
      }
    },
    crossOriginEmbedderPolicy: false // Needed for some React features
  })(req, res, next);
};

// Comprehensive CORS Configuration
const secureCORSConfiguration = () => {
  initializeHighRiskSecurity();
  return cors(SecureCORSManager.getCORSOptions());
};

// Request Logging and Monitoring
const securityRequestLogging = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log incoming request
  SecureLogger.audit('Request received', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent')?.substring(0, 100),
    timestamp: new Date().toISOString()
  });
  
  // Capture response details
  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - startTime;
    
    SecureLogger.audit('Request completed', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });
    
    return originalSend.call(this, data);
  };
  
  next();
};

// Vulnerable Dependency Check Middleware
const vulnerabilityCheckMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Run vulnerability check on first request only
  if (!securityInitialized) {
    try {
      VulnerableDependencyMonitor.checkVulnerabilities();
    } catch (error) {
      SecureLogger.error('Vulnerability check failed', error);
      if (process.env.NODE_ENV === 'production') {
        return res.status(503).json({
          error: 'Service unavailable due to security concerns',
          code: 'SECURITY_BLOCK'
        });
      }
    }
  }
  next();
};

// Complete High-Risk Security Stack
export const completeHighRiskSecurityStack = [
  // 1. Initialize security managers
  (req: Request, res: Response, next: NextFunction) => {
    initializeHighRiskSecurity();
    next();
  },
  
  // 2. Vulnerability check
  vulnerabilityCheckMiddleware,
  
  // 3. Enhanced security headers
  enhancedSecurityHeaders,
  
  // 4. Secure CORS configuration
  secureCORSConfiguration(),
  
  // 5. Request logging
  securityRequestLogging,
  
  // 6. SQL injection protection
  sqlInjectionProtection,
  
  // 7. XSS protection
  xssProtectionMiddleware,
  
  // 8. Rate limiting and general security
  ...highRiskSecurityMiddleware
];

// Export individual components for selective use
export {
  initializeHighRiskSecurity,
  sqlInjectionProtection,
  enhancedSecurityHeaders,
  securityRequestLogging,
  vulnerabilityCheckMiddleware
};