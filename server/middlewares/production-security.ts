/**
 * Production Security Middleware
 * Blocks mock tokens, validates authentication, and enforces security policies
 */

import { Request, Response, NextFunction } from 'express';
import { productionSecurity } from '../lib/production-security-enforcer';
import { SecurityAuditLogger } from '../lib/security-validator';

/**
 * Middleware to block mock tokens and enforce authentication security
 */
export function blockMockTokens(req: Request, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  
  if (authHeader) {
    const token = authHeader.replace('Bearer ', '');
    const validation = productionSecurity.validateAuthToken(token);
    
    if (!validation.isValid) {
      SecurityAuditLogger.logSecurityViolation('invalid_token_attempt', {
        error: validation.error,
        isMock: validation.isMock,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        endpoint: req.path
      });
      
      return res.status(401).json({ 
        error: 'Invalid authentication token',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (validation.isMock && process.env.NODE_ENV === 'production') {
      SecurityAuditLogger.logSecurityViolation('mock_token_blocked', {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        endpoint: req.path
      });
      
      return res.status(403).json({ 
        error: 'Mock tokens are not allowed in production',
        code: 'MOCK_TOKEN_BLOCKED'
      });
    }
  }
  
  next();
}

/**
 * Enhanced authentication middleware with security audit
 */
export function secureAuthentication(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    // Log solo tentativi di accesso a endpoint critici per ridurre rumore
    const isCriticalEndpoint = req.path.includes('/admin') || 
                              req.path.includes('/users') || 
                              req.path.includes('/security') ||
                              req.path.includes('/delete') ||
                              req.path.includes('/modify');
                              
    if (isCriticalEndpoint) {
      SecurityAuditLogger.logSecurityViolation('unauthorized_access_attempt', {
        endpoint: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    return res.status(401).json({ 
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }
  
  // Validate session integrity
  const user = req.user as any;
  if (!user || !user.id || !user.tenantId) {
    SecurityAuditLogger.logSecurityViolation('corrupted_session', {
      userId: user?.id,
      endpoint: req.path,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    return res.status(401).json({ 
      error: 'Session corrupted - please login again',
      code: 'SESSION_CORRUPTED'
    });
  }
  
  next();
}

/**
 * Admin-only access with enhanced security
 */
export function secureAdminOnly(req: Request, res: Response, next: NextFunction) {
  secureAuthentication(req, res, () => {
    const user = req.user as any;
    
    if (!user.isAdmin) {
      SecurityAuditLogger.logSecurityViolation('unauthorized_admin_access', {
        userId: user.id,
        username: user.username,
        endpoint: req.path,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
      
      return res.status(403).json({ 
        error: 'Admin access required',
        code: 'ADMIN_REQUIRED'
      });
    }
    
    next();
  });
}

/**
 * Rate limiting for authentication endpoints
 */
const authAttempts = new Map<string, { count: number; lastAttempt: number }>();

export function authRateLimit(req: Request, res: Response, next: NextFunction) {
  const key = req.ip || 'unknown';
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 5;
  
  const attempts = authAttempts.get(key);
  
  if (attempts) {
    // Reset if window has passed
    if (now - attempts.lastAttempt > windowMs) {
      authAttempts.delete(key);
    } else if (attempts.count >= maxAttempts) {
      SecurityAuditLogger.logSecurityViolation('rate_limit_exceeded', {
        ip: req.ip,
        attempts: attempts.count,
        endpoint: req.path,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(429).json({ 
        error: 'Too many authentication attempts. Please try again later.',
        code: 'RATE_LIMITED',
        retryAfter: Math.ceil((windowMs - (now - attempts.lastAttempt)) / 1000)
      });
    }
  }
  
  // Track this attempt
  const currentAttempts = attempts ? attempts.count + 1 : 1;
  authAttempts.set(key, { count: currentAttempts, lastAttempt: now });
  
  next();
}

/**
 * Enhanced security headers middleware with comprehensive protection
 */
export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  // Core security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Enhanced security headers (Replit-compatible)
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('X-Download-Options', 'noopen');
  // Skip Cross-Origin policies in development to avoid Replit issues
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
  }
  
  // HSTS with enhanced configuration
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  // Enhanced CSP with Replit-compatible configuration
  const cspDirectives = process.env.NODE_ENV === 'production' 
    ? [
        "default-src 'self'",
        "script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval'",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "img-src 'self' data: blob: https:",
        "font-src 'self' https://fonts.gstatic.com",
        "connect-src 'self' wss: ws: https:",
        "media-src 'self'",
        "object-src 'none'",
        "frame-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'"
      ]
    : [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval'",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "img-src 'self' data: blob: https:",
        "font-src 'self' https://fonts.gstatic.com",
        "connect-src 'self' ws: wss: https:",
        "media-src 'self'",
        "object-src 'none'",
        "frame-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'"
      ];
  
  res.setHeader('Content-Security-Policy', cspDirectives.join('; '));
  
  next();
}

/**
 * Request sanitization middleware
 */
export function sanitizeRequest(req: Request, res: Response, next: NextFunction) {
  // Limit request size
  const maxSize = process.env.MAX_REQUEST_SIZE || '10mb';
  
  // Basic input sanitization
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }
  
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }
  
  next();
}

function sanitizeObject(obj: any) {
  for (const key in obj) {
    if (typeof obj[key] === 'string') {
      // Remove potentially dangerous characters
      obj[key] = obj[key].replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
      obj[key] = obj[key].replace(/javascript:/gi, '');
      obj[key] = obj[key].replace(/on\w+\s*=/gi, '');
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      sanitizeObject(obj[key]);
    }
  }
}