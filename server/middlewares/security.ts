import { Request, Response, NextFunction } from "express";
import rateLimit from "express-rate-limit";
import helmet from "helmet";
import { body, validationResult, param, query } from "express-validator";
import fileUpload from "express-fileupload";
import { db } from "../db";
import { apiAnalytics } from "@shared/schema";

// Input validation middleware
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: "Validation failed",
      errors: errors.array()
    });
  }
  next();
};

// Authentication validation rules
export const loginValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_.-]+$/)
    .withMessage('Username can only contain letters, numbers, dots, hyphens and underscores'),
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be between 6 and 128 characters'),
  handleValidationErrors
];

// User creation validation
export const createUserValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_.-]+$/)
    .withMessage('Username can only contain letters, numbers, dots, hyphens and underscores'),
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Must be a valid email address'),
  handleValidationErrors
];

// ID parameter validation
export const validateId = [
  param('id').isInt({ min: 1 }).withMessage('ID must be a positive integer'),
  handleValidationErrors
];

// Enhanced Rate limiters - Much more restrictive
export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'development' ? 1000 : 250, // Higher limit for dev, moderate for production
  message: { error: "Rate limit exceeded. Try again in 15 minutes." },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting in development mode
    return process.env.NODE_ENV === 'development';
  }
});

export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Reduced from 10 to 5 auth attempts per 15min
  message: { error: "Too many authentication attempts. Account temporarily locked." },
  standardHeaders: true,
  legacyHeaders: false,
});

export const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 API calls per minute
  message: { error: "API rate limit exceeded. Wait 1 minute before retrying." },
  standardHeaders: true,
  legacyHeaders: false,
});

export const uploadLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 20, // Limit uploads
  message: { error: "Too many file uploads, please try again later." },
  standardHeaders: true,
  legacyHeaders: false,
});

// Balanced CSP headers - Secure production, functional development
export const securityHeaders = helmet({
  contentSecurityPolicy: process.env.NODE_ENV === 'development' ? false : {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-eval'", // Still needed for production dynamic imports
        "'wasm-unsafe-eval'", // Still needed for WASM in production
        "blob:"
      ],
      scriptSrcAttr: ["'unsafe-hashes'"], // More secure approach for production
      styleSrc: [
        "'self'", 
        "'unsafe-inline'", // Still needed for CSS-in-JS libraries like Tailwind
        "https://fonts.googleapis.com"
      ],
      imgSrc: ["'self'", "data:", "blob:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:", "https:"], // Allow WebSocket and HTTPS connections
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: process.env.NODE_ENV === 'production' ? {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  } : false
});

// File upload security configuration
export const fileUploadConfig = {
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit (reduced from 50MB)
    files: 1 // Only one file at a time
  },
  abortOnLimit: true,
  responseOnLimit: "File size exceeds the 5MB limit",
  useTempFiles: false,
  tempFileDir: undefined,
  uploadTimeout: 30000, // 30 seconds
  createParentPath: false,
  safeFileNames: true,
  preserveExtension: true,
  parseNested: false
};

// Supplier data validation
export const supplierValidation = [
  body('companyName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Company name must be between 2 and 100 characters'),
  body('vatNumber')
    .trim()
    .isLength({ min: 8, max: 20 })
    .withMessage('VAT number must be between 8 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('VAT number can only contain uppercase letters and numbers'),
  body('address')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Address must be between 5 and 200 characters'),
  handleValidationErrors
];

// Product label validation
export const productLabelValidation = [
  body('productName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Product name must be between 1 and 100 characters'),
  body('expirationDate')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid date'),
  body('lotNumber')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Lot number must not exceed 50 characters'),
  handleValidationErrors
];

// Container validation
export const containerValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Container name must be between 1 and 100 characters'),
  body('type')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Container type is required'),
  body('maxCapacity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max capacity must be a positive integer'),
  handleValidationErrors
];

// API key validation for AI services
export const validateApiRequest = (req: Request, res: Response, next: NextFunction) => {
  // Check if request contains potential SQL injection patterns
  const suspiciousPatterns = [
    /('|\\')|(;|--)|(\|)|(\*)/i,
    /(union|select|insert|update|delete|drop|create|alter)/i,
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi
  ];

  const requestBody = JSON.stringify(req.body);
  const queryString = JSON.stringify(req.query);
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestBody) || pattern.test(queryString)) {
      return res.status(400).json({ 
        error: "Invalid input detected. Request blocked for security reasons." 
      });
    }
  }
  
  next();
};

// Image data validation for base64 images
export const validateImageData = (req: Request, res: Response, next: NextFunction) => {
  if (req.body.imageData || req.body.image) {
    const imageData = req.body.imageData || req.body.image;
    
    // Check if it's valid base64
    if (typeof imageData !== 'string') {
      return res.status(400).json({ error: "Image data must be a string" });
    }
    
    // Check base64 format
    const base64Regex = /^data:image\/(jpeg|jpg|png|gif);base64,/;
    if (!base64Regex.test(imageData)) {
      return res.status(400).json({ 
        error: "Invalid image format. Only JPEG, JPG, PNG, and GIF are allowed" 
      });
    }
    
    // Check size (5MB limit for base64)
    const sizeInBytes = (imageData.length * 3) / 4;
    if (sizeInBytes > 5 * 1024 * 1024) {
      return res.status(400).json({ 
        error: "Image size exceeds 5MB limit" 
      });
    }
  }
  
  next();
};

// API Analytics Middleware
export const trackApiAnalytics = async (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Store original send function
  const originalSend = res.send;
  
  res.send = function(data) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Track API call in background (don't await to avoid blocking)
    setImmediate(async () => {
      try {
        const user = (req as any).user;
        const tenantId = user?.tenant_id || null;
        const userId = user?.id || null;
        
        await db.insert(apiAnalytics).values({
          endpoint: req.path,
          method: req.method,
          statusCode: res.statusCode,
          responseTime,
          timestamp: new Date(),
          userAgent: req.get('User-Agent') || null,
          ipAddress: req.ip || req.connection.remoteAddress || null,
          tenantId,
          userId
        });
      } catch (error) {
        console.error('Analytics tracking error:', error);
      }
    });
    
    return originalSend.call(this, data);
  };
  
  next();
};

// Enhanced API Key Security Management
export const secureApiKeyValidation = (requiredKeyTypes: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      return res.status(401).json({ 
        error: "API key required",
        code: "MISSING_API_KEY"
      });
    }
    
    // Validate API key format (should be a secure hash)
    if (!/^[A-Za-z0-9+/=]{32,}$/.test(apiKey)) {
      return res.status(401).json({ 
        error: "Invalid API key format",
        code: "INVALID_API_KEY_FORMAT"
      });
    }
    
    // Check against environment variables (more secure than database storage)
    const validKeys = {
      'admin': process.env.ADMIN_API_KEY,
      'system': process.env.SYSTEM_API_KEY,
      'ai': process.env.AI_SERVICE_API_KEY
    };
    
    const isValidKey = Object.entries(validKeys).some(([type, key]) => {
      if (!key) return false;
      if (requiredKeyTypes.length > 0 && !requiredKeyTypes.includes(type)) return false;
      return key === apiKey;
    });
    
    if (!isValidKey) {
      return res.status(403).json({ 
        error: "Invalid or unauthorized API key",
        code: "UNAUTHORIZED_API_KEY"
      });
    }
    
    next();
  };
};

// Standardized Error Handler
export const standardErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  const timestamp = new Date().toISOString();
  const requestId = req.headers['x-request-id'] || `req_${Date.now()}`;
  
  // Log error with context
  console.error(`[${timestamp}] [${requestId}] Error in ${req.method} ${req.path}:`, {
    error: err.message,
    stack: err.stack,
    user: (req as any).user?.id,
    tenant: (req as any).user?.tenant_id,
    ip: req.ip
  });
  
  // Don't expose internal error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    error: true,
    message: err.message || "Internal server error",
    timestamp,
    requestId,
    ...(isDevelopment && { 
      stack: err.stack,
      details: err 
    })
  };
  
  const statusCode = err.statusCode || err.status || 500;
  res.status(statusCode).json(errorResponse);
};

export { fileUpload };