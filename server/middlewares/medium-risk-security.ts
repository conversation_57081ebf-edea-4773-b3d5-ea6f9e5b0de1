/**
 * MEDIUM-RISK SECURITY MIDDLEWARE
 * Integration middleware for all medium-risk vulnerability fixes
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import {
  EnhancedRateLimiter,
  SecureDataLogger,
  SecureTokenStorage,
  CSRFProtectionManager,
  completeMediumRiskSecurityStack
} from '../lib/medium-risk-security-manager.js';
import { SecureLogger } from '../lib/secure-logger.js';

// Initialize enhanced security on startup
let mediumRiskSecurityInitialized = false;

function initializeMediumRiskSecurity() {
  if (mediumRiskSecurityInitialized) return;
  
  try {
    // Initialize enhanced rate limiters
    const rateLimiters = EnhancedRateLimiter.getTieredRateLimiters();
    
    SecureLogger.audit('Medium-risk security initialization completed', {
      rateLimitersCreated: Object.keys(rateLimiters).length,
      csrfProtectionEnabled: true,
      secureLoggingEnabled: true,
      tokenStorageValidationEnabled: true,
      timestamp: new Date().toISOString()
    });
    
    mediumRiskSecurityInitialized = true;
  } catch (error) {
    SecureLogger.error('Failed to initialize medium-risk security', error);
    throw error;
  }
}

// Enhanced Rate Limiting Middleware
const enhancedRateLimiting = {
  // Authentication endpoints - very strict
  auth: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().authLimiter;
    return limiter(req, res, next);
  },
  
  // API endpoints - moderate
  api: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().apiLimiter;
    return limiter(req, res, next);
  },
  
  // Sensitive operations - strict
  sensitive: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().sensitiveLimiter;
    return limiter(req, res, next);
  },
  
  // Password operations - very strict
  password: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().passwordLimiter;
    return limiter(req, res, next);
  },
  
  // Upload operations
  upload: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().uploadLimiter;
    return limiter(req, res, next);
  },
  
  // General application
  general: (req: Request, res: Response, next: NextFunction) => {
    const limiter = EnhancedRateLimiter.getTieredRateLimiters().generalLimiter;
    return limiter(req, res, next);
  }
};

// Secure Logging Middleware
const secureLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Capture request data securely
  const requestData = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    userId: (req as any).user?.id,
    sessionId: req.sessionID ? req.sessionID.substring(0, 8) + '...' : undefined
  };
  
  SecureDataLogger.logSecurely('audit', 'Request initiated', requestData);
  
  // Override res.json to log responses securely
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    
    SecureDataLogger.logSecurely('audit', 'Request completed', {
      ...requestData,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: JSON.stringify(body).length
    });
    
    return originalJson.call(this, body);
  };
  
  // Handle errors in logging
  res.on('error', (error) => {
    SecureDataLogger.logSecurely('error', 'Response error', {
      ...requestData,
      error: error.message
    });
  });
  
  next();
};

// Token Storage Validation Middleware
const tokenStorageValidationMiddleware = SecureTokenStorage.createSecureStorageMiddleware();

// CSRF Protection Middleware
const csrfProtectionMiddleware = CSRFProtectionManager.createCSRFMiddleware({
  excludePaths: [
    '/api/auth/csrf-token',
    '/api/auth/login',
    '/api/auth/logout',
    '/api/health'
  ],
  methods: ['POST', 'PUT', 'PATCH', 'DELETE']
});

// CSRF Token Provider Middleware
const csrfTokenProviderMiddleware = CSRFProtectionManager.createCSRFTokenProvider();

// Request Sanitization Middleware (Enhanced)
const requestSanitizationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize request body for logging
  if (req.body) {
    const sanitizedBody = SecureDataLogger.sanitizeForLogging(req.body);
    (req as any).sanitizedBody = sanitizedBody;
  }
  
  // Sanitize query parameters
  if (req.query) {
    const sanitizedQuery = SecureDataLogger.sanitizeForLogging(req.query);
    (req as any).sanitizedQuery = sanitizedQuery;
  }
  
  // Add security context to request
  (req as any).securityContext = {
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
    securityLevel: 'medium-risk-protected'
  };
  
  next();
};

// Complete Medium-Risk Security Middleware Stack
const completeMediumRiskSecurityMiddleware = [
  // Initialize security systems
  (req: Request, res: Response, next: NextFunction) => {
    initializeMediumRiskSecurity();
    next();
  },
  
  // Enhanced rate limiting (general)
  enhancedRateLimiting.general,
  
  // Secure logging
  secureLoggingMiddleware,
  
  // Request sanitization
  requestSanitizationMiddleware,
  
  // Token storage validation
  tokenStorageValidationMiddleware,
  
  // CSRF token provider
  csrfTokenProviderMiddleware,
  
  // CSRF protection
  csrfProtectionMiddleware
];

// Route-specific middleware exports
const routeSpecificSecurity = {
  // Authentication routes
  authentication: [
    enhancedRateLimiting.auth,
    enhancedRateLimiting.password,
    secureLoggingMiddleware
  ],
  
  // API routes
  api: [
    enhancedRateLimiting.api,
    secureLoggingMiddleware,
    requestSanitizationMiddleware,
    tokenStorageValidationMiddleware
  ],
  
  // Sensitive operations
  sensitive: [
    enhancedRateLimiting.sensitive,
    secureLoggingMiddleware,
    csrfProtectionMiddleware
  ],
  
  // Upload routes
  upload: [
    enhancedRateLimiting.upload,
    secureLoggingMiddleware,
    requestSanitizationMiddleware
  ]
};

export {
  initializeMediumRiskSecurity,
  enhancedRateLimiting,
  secureLoggingMiddleware,
  tokenStorageValidationMiddleware,
  csrfProtectionMiddleware,
  csrfTokenProviderMiddleware,
  requestSanitizationMiddleware
};