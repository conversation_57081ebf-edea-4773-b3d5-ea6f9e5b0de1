import { Request, Response, NextFunction } from 'express';
import { UserRole } from '../../shared/schema';

// Middleware per verificare se l'utente è autenticato
export function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated()) {
    return next();
  }
  return res.status(401).json({ message: "Unauthorized" });
}

// Middleware per verificare se l'utente è un amministratore
export function isAdmin(req: Request, res: Response, next: NextFunction) {
  const user = req.user as any;
  
  if (req.isAuthenticated() && user && (
    user.isAdmin || 
    user.role === UserRole.ADMIN
  )) {
    return next();
  }
  return res.status(403).json({ message: "Forbidden: Admin access required" });
}

// Middleware per verificare se l'utente è un manager o un amministratore
export function isManagerOrAdmin(req: Request, res: Response, next: NextFunction) {
  const user = req.user as any;
  
  if (req.isAuthenticated() && user && (
    user.isAdmin || 
    user.role === UserRole.ADMIN || 
    user.role === UserRole.MANAGER
  )) {
    return next();
  }
  return res.status(403).json({ message: "Forbidden: Manager or Admin access required" });
}