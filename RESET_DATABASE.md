# Procedura per il Reset Sicuro del Database

Questo documento descrive la procedura per eseguire un reset completo e sicuro del database dell'applicazione HACCP. Seguire attentamente questi passaggi nell'ordine specificato per evitare problemi di integrità referenziale.

## Prerequisiti

- Accesso al database PostgreSQL
- Diritti amministrativi sufficienti
- Backup recente del database (se necessario conservare dati)

## Procedura di Backup

Prima di qualsiasi operazione di reset, è fondamentale creare un backup del database:

```sql
-- Creare una cartella per i backup se non esiste già
mkdir -p db_backups

-- Eseguire il backup completo del database
pg_dump $DATABASE_URL -f db_backups/haccp_backup_$(date +%Y%m%d_%H%M%S).sql
```

## Procedura di Reset (in Ordine)

Per garantire l'integrità referenziale, è necessario eliminare i dati nell'ordine corretto. I comandi SQL seguenti ripristineranno il database allo stato iniziale:

### 1. Eliminazione dei Dati

```sql
-- Eliminare prima le relazioni in container_products
DELETE FROM container_products;

-- Eliminare le etichette prodotto
DELETE FROM product_labels;

-- Eliminare i documenti di trasporto (DDT)
DELETE FROM ddts;

-- Eliminare i fornitori
DELETE FROM suppliers;

-- Eliminare i contenitori
DELETE FROM containers;

-- Eliminare i log di attività (preservando solo login/logout se desiderato)
DELETE FROM activity_logs;
-- OPPURE mantenere solo i log di login/logout
-- DELETE FROM activity_logs WHERE action NOT IN ('login', 'logout');
```

### 2. Reset delle Sequenze ID

Per garantire che i nuovi record inizino dal numero 1:

```sql
-- Resettare le sequenze ID per tutte le tabelle svuotate
ALTER SEQUENCE suppliers_id_seq RESTART WITH 1;
ALTER SEQUENCE ddts_id_seq RESTART WITH 1;
ALTER SEQUENCE product_labels_id_seq RESTART WITH 1;
ALTER SEQUENCE containers_id_seq RESTART WITH 1;
ALTER SEQUENCE container_products_id_seq RESTART WITH 1;
ALTER SEQUENCE activity_logs_id_seq RESTART WITH 1;
```

### 3. Verifica del Reset

Per verificare che tutte le tabelle siano state svuotate correttamente:

```sql
-- Verifica finale dello stato delle tabelle
SELECT 'suppliers' as table_name, COUNT(*) FROM suppliers UNION ALL
SELECT 'ddts', COUNT(*) FROM ddts UNION ALL
SELECT 'product_labels', COUNT(*) FROM product_labels UNION ALL
SELECT 'containers', COUNT(*) FROM containers UNION ALL
SELECT 'container_products', COUNT(*) FROM container_products UNION ALL
SELECT 'activity_logs', COUNT(*) FROM activity_logs;
```

## Nota sui Tipi di Contenitore

Di default, **non** eliminiamo i tipi di contenitore (`container_types`) in quanto rappresentano valori predefiniti del sistema che potrebbero essere necessari per il corretto funzionamento dell'applicazione. Se desideri eliminare anche questi dati, esegui:

```sql
-- OPZIONALE: eliminare i tipi di contenitore (sconsigliato)
DELETE FROM container_types;
ALTER SEQUENCE container_types_id_seq RESTART WITH 1;
```

## Reset delle Notifiche Frontend

Le notifiche non sono memorizzate nel database ma gestite localmente dal frontend. Per resettare le notifiche:

1. Modificare il file `client/src/components/layout/header.tsx`
2. Cambiare la riga `const [notificationsCount, setNotificationsCount] = useState(3);` in `const [notificationsCount, setNotificationsCount] = useState(0);`
3. Aggiornare il contenuto del pannello notifiche sostituendo le notifiche hardcoded con un messaggio "Nessuna notifica"

## Problemi Comuni

### Errore di Foreign Key
Se si riscontrano errori relativi alle chiavi esterne, verificare di aver eliminato i dati nell'ordine corretto (da quelli che dipendono da altre tabelle a quelli indipendenti).

### Errore di ID Duplicato
Se si verificano errori del tipo `duplicate key value violates unique constraint`, assicurarsi di aver resettato tutte le sequenze ID.

## Avvertenze

- Questa procedura eliminerà **PERMANENTEMENTE** tutti i dati specificati
- È fondamentale eseguire un backup prima di procedere
- Verificare che l'applicazione non sia in uso durante il reset
- Dopo il reset potrebbe essere necessario riavviare l'applicazione

## Supporto

In caso di problemi con questa procedura, contattare il team di sviluppo.

---

Documento creato il: 07/05/2025