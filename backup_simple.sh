#!/bin/bash

# Script semplice per backup completo del progetto HACCP Tracker

# Colori per i messaggi
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} Creazione backup completo del progetto HACCP Tracker..."

# Crea directory per i backup
mkdir -p backups

# Nome del file con timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backups/haccp_project_backup_${TIMESTAMP}.tar.gz"

echo -e "${BLUE}[INFO]${NC} File di backup: $BACKUP_FILE"

# Lista dei file e directory da includere
tar -czf "$BACKUP_FILE" \
    --exclude='.git' \
    --exclude='node_modules' \
    --exclude='dist' \
    --exclude='build' \
    --exclude='backups' \
    --exclude='.env' \
    --exclude='.env.local' \
    --exclude='*.log' \
    --exclude='.DS_Store' \
    --exclude='Thumbs.db' \
    --exclude='.replit' \
    --exclude='replit.nix' \
    client/ \
    server/ \
    shared/ \
    db_backups/ \
    *.ts \
    *.js \
    *.json \
    *.md \
    *.sh \
    *.txt \
    *.sql 2>/dev/null

# Verifica che il backup sia stato creato
if [ -f "$BACKUP_FILE" ]; then
    FILE_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
    echo -e "${GREEN}[SUCCESSO]${NC} Backup completato: $BACKUP_FILE ($FILE_SIZE)"
else
    echo -e "\033[0;31m[ERRORE]\033[0m Backup fallito"
    exit 1
fi

echo -e "${BLUE}[INFO]${NC} Backup esistenti:"
ls -lht backups/haccp_project_backup_*.tar.gz 2>/dev/null | head -5

echo -e "${GREEN}[SUCCESSO]${NC} Backup completo del progetto pronto per il download!"