#!/usr/bin/env node

/**
 * Script automatico per incremento versione durante il build
 * Si esegue automaticamente prima del build di produzione
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';

// Legge la versione corrente dal file version.ts
function getCurrentAppVersion() {
  try {
    const content = readFileSync('./client/src/lib/version.ts', 'utf8');
    const match = content.match(/const APP_VERSION = "([^"]+)";/);
    return match ? match[1] : '1.2.16';
  } catch (error) {
    return '1.2.16';
  }
}

// Controlla se siamo in ambiente di build produzione
function isProductionBuild() {
  return process.env.NODE_ENV === 'production' || 
         process.argv.includes('--prod') ||
         process.argv.includes('build');
}

// Controlla se dobbiamo incrementare la versione
function shouldIncrementVersion() {
  // Non incrementare in development
  if (process.env.NODE_ENV === 'development') return false;
  
  // Controlla se abbiamo già incrementato in questa sessione
  const flagFile = './.version-incremented';
  if (existsSync(flagFile)) return false;
  
  return isProductionBuild();
}

// Incrementa la versione patch
function incrementVersion() {
  const currentVersion = getCurrentAppVersion();
  const parts = currentVersion.split('.').map(Number);
  parts[2] = (parts[2] || 0) + 1;
  return parts.join('.');
}

// Aggiorna il file version.ts
function updateVersionFile(version) {
  try {
    let content = readFileSync('./client/src/lib/version.ts', 'utf8');
    
    content = content.replace(
      /const APP_VERSION = "[^"]+";/,
      `const APP_VERSION = "${version}";`
    );
    
    const buildComment = `// Build: ${new Date().toISOString().split('T')[0]}`;
    content = content.replace(
      /\/\/ Build: .*/,
      buildComment
    );
    
    writeFileSync('./client/src/lib/version.ts', content);
    console.log(`Version auto-incremented to ${version}`);
    
    // Crea flag per evitare doppi incrementi
    writeFileSync('./.version-incremented', version);
    
    return true;
  } catch (error) {
    console.error('Error updating version:', error.message);
    return false;
  }
}

// Aggiorna .env
function updateEnvFile(version) {
  const envPath = './.env';
  let envContent = '';
  
  try {
    envContent = readFileSync(envPath, 'utf8');
  } catch (error) {
    // File non esiste
  }
  
  if (envContent.includes('VITE_APP_VERSION=')) {
    envContent = envContent.replace(
      /VITE_APP_VERSION=.*/,
      `VITE_APP_VERSION=${version}`
    );
  } else {
    envContent += `\nVITE_APP_VERSION=${version}\n`;
  }
  
  writeFileSync(envPath, envContent);
}

// Esecuzione principale
function main() {
  if (shouldIncrementVersion()) {
    const newVersion = incrementVersion();
    const success = updateVersionFile(newVersion);
    
    if (success) {
      updateEnvFile(newVersion);
      console.log(`Production build version: ${newVersion}`);
    }
  }
}

main();