<!DOCTYPE html>
<html>
<head>
    <title>Test Tenant Switch</title>
</head>
<body>
    <h1>Test Tenant Switch</h1>
    <p>Clicca il link per testare il cambio tenant:</p>
    <a href="/?tenant=1357&adminAccess=true&timestamp=123456" target="_blank">
        Cambia al tenant Balus (1357)
    </a>
    
    <script>
        // Test anche via JavaScript
        async function testTenantSwitch() {
            try {
                const response = await fetch('/api/admin/switch-tenant/1357', {
                    method: 'POST',
                    credentials: 'include'
                });
                const result = await response.json();
                console.log('Tenant switch result:', result);
                
                // Ottieni dati utente aggiornati
                const userResponse = await fetch('/api/auth/me', {
                    credentials: 'include'
                });
                const userData = await userResponse.json();
                console.log('Updated user data:', userData);
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        // Testa automaticamente dopo 2 secondi
        setTimeout(testTenantSwitch, 2000);
    </script>
</body>
</html>