# Risoluzione dei problemi di Service Worker in ambiente di sviluppo

## Problema

I Service Worker possono interferire con l'ambiente di sviluppo causando:
- Mancato rendering del frontend
- Caching eccessivo delle risorse
- Comportamenti imprevisti nell'applicazione
- Errori di connessione con Vite o Hot Reload

## Soluzioni implementate

### 1. Disattivazione di Service Worker in ambiente di sviluppo (client/src/main.tsx)

```typescript
// Inizializza il sistema di hot reload in ambiente di sviluppo
if (process.env.NODE_ENV === 'development') {
  // Aspetta un secondo per dare tempo alla pagina di caricarsi completamente
  setTimeout(() => {
    initHotReload();
  }, 1000);
  
  // In sviluppo, proviamo a disattivare qualsiasi service worker esistente
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      for (const registration of registrations) {
        console.log('Unregistering service worker in development mode:', registration);
        registration.unregister();
      }
    });
  }
} else {
  // Register service worker for PWA functionality ONLY in production
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
          console.log('Service Worker registered successfully:', registration.scope);
        })
        .catch(error => {
          console.error('Service Worker registration failed:', error);
        });
    });
  }
}
```

### 2. Modifica del comportamento del Service Worker (client/public/service-worker.js)

```javascript
// Verifica se siamo in ambiente di sviluppo (non dovrebbe eseguire questo codice in sviluppo)
const isDevMode = self.location.hostname === 'localhost' || 
                 self.location.hostname === '127.0.0.1' ||
                 self.location.hostname.includes('.replit.dev');

if (isDevMode) {
  console.log('Service Worker in development mode - disabling caching');
  self.addEventListener('install', (event) => {
    self.skipWaiting();
  });
  self.addEventListener('activate', (event) => {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            console.log('Clearing cache in development:', cacheName);
            return caches.delete(cacheName);
          })
        );
      }).then(() => self.clients.claim())
    );
  });
}
```

### 3. Modifica dell'evento fetch nel Service Worker

Nel gestore dell'evento fetch, abbiamo aggiunto un controllo per l'ambiente di sviluppo:

```javascript
// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  // In development mode, just pass through all requests to the network
  if (isDevMode) {
    return;
  }
  
  // Resto della logica di gestione fetch per la produzione
  // ...
});
```

## Procedura di risoluzione in caso di problemi simili

1. **Verificare se ci sono service worker registrati**:
   - Aprire la console del browser
   - Eseguire: `navigator.serviceWorker.getRegistrations().then(regs => console.log(regs))`

2. **Disattivare manualmente i service worker**:
   - Eseguire nella console del browser:
   ```javascript
   navigator.serviceWorker.getRegistrations().then(registrations => {
     for (const registration of registrations) {
       registration.unregister();
       console.log('Service worker unregistered:', registration);
     }
   });
   ```

3. **Pulire le cache**:
   - Eseguire nella console del browser:
   ```javascript
   caches.keys().then(cacheNames => {
     return Promise.all(
       cacheNames.map(cacheName => {
         console.log('Clearing cache:', cacheName);
         return caches.delete(cacheName);
       })
     );
   });
   ```

4. **Riavviare il browser**:
   - A volte è necessario chiudere completamente il browser e riaprirlo

5. **Incorporare controlli di ambiente nei service worker**:
   - Controllare sempre l'ambiente (sviluppo vs produzione) nel service worker
   - Disabilitare il caching in ambiente di sviluppo
   - Usare controlli del tipo `self.location.hostname` per distinguere ambiente di sviluppo e produzione

## Note importanti

- I service worker persistono anche dopo la chiusura della pagina
- L'unregistration non è immediata e potrebbe richiedere la chiusura di tutte le schede che usano quel service worker
- I service worker sono particolarmente utili in produzione per il supporto offline ma possono causare problemi in sviluppo
- Per un'esperienza di sviluppo ottimale, è preferibile disabilitare completamente i service worker in ambiente di sviluppo

## Log di riferimento

Se vedi log come questi, ci potrebbe essere un problema con il service worker:

```
Dev-reload: Avviato in ambiente Replit
Dev-reload: Pulsante di ricarica aggiunto
Dev-reload: Rilevato Service Worker attivo, potrebbe essere necessario disattivarlo in sviluppo
```