#!/usr/bin/env tsx

/**
 * Security Validation Test Runner
 * Comprehensive security testing automation script
 */

import { execSync } from 'child_process';
import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

interface TestResult {
  suite: string;
  status: 'PASS' | 'FAIL' | 'ERROR';
  duration: number;
  tests: {
    name: string;
    status: 'PASS' | 'FAIL';
    duration: number;
    error?: string;
  }[];
}

interface ValidationReport {
  timestamp: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  duration: number;
  results: TestResult[];
  securityScore: number;
  recommendations: string[];
}

class SecurityValidationRunner {
  private results: TestResult[] = [];
  private startTime: number = 0;

  constructor() {
    console.log('🛡️  Starting Security Validation Suite');
    console.log('=====================================\n');
  }

  async runValidation(): Promise<ValidationReport> {
    this.startTime = Date.now();

    try {
      // 1. Security Penetration Testing
      await this.runTestSuite(
        'Security Penetration Testing',
        'tests/security/penetration-testing.test.ts'
      );

      // 2. Storage Security Audit
      await this.runTestSuite(
        'Storage Security Audit',
        'tests/security/storage-audit.test.ts'
      );

      // 3. XSS Vulnerability Scanning
      await this.runTestSuite(
        'XSS Vulnerability Scanner',
        'tests/security/xss-vulnerability-scanner.test.ts'
      );

      // 4. Performance Impact Assessment
      await this.runTestSuite(
        'Performance Impact Assessment',
        'tests/security/performance-impact-assessment.test.ts'
      );

      // Generate comprehensive report
      return this.generateReport();

    } catch (error) {
      console.error('❌ Security validation failed:', error);
      throw error;
    }
  }

  private async runTestSuite(suiteName: string, testFile: string): Promise<void> {
    console.log(`📋 Running ${suiteName}...`);
    
    const startTime = Date.now();
    
    try {
      // Run Jest test for specific file
      const output = execSync(
        `npx jest ${testFile} --verbose --json --testTimeout=30000`,
        { 
          encoding: 'utf8',
          timeout: 120000 // 2 minute timeout
        }
      );

      const result = JSON.parse(output);
      const duration = Date.now() - startTime;

      const testResult: TestResult = {
        suite: suiteName,
        status: result.success ? 'PASS' : 'FAIL',
        duration,
        tests: result.testResults[0]?.assertionResults?.map((test: any) => ({
          name: test.title,
          status: test.status === 'passed' ? 'PASS' : 'FAIL',
          duration: test.duration || 0,
          error: test.failureMessages?.[0]
        })) || []
      };

      this.results.push(testResult);

      if (testResult.status === 'PASS') {
        console.log(`✅ ${suiteName} completed successfully (${duration}ms)`);
      } else {
        console.log(`❌ ${suiteName} failed (${duration}ms)`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        suite: suiteName,
        status: 'ERROR',
        duration,
        tests: []
      });

      console.log(`💥 ${suiteName} encountered error (${duration}ms):`, String(error).substring(0, 200));
    }

    console.log('');
  }

  private generateReport(): ValidationReport {
    const totalDuration = Date.now() - this.startTime;
    const totalTests = this.results.reduce((sum, result) => sum + result.tests.length, 0);
    const passedTests = this.results.reduce((sum, result) => 
      sum + result.tests.filter(test => test.status === 'PASS').length, 0
    );
    const failedTests = totalTests - passedTests;

    // Calculate security score (0-100)
    const securityScore = this.calculateSecurityScore();
    
    // Generate recommendations
    const recommendations = this.generateRecommendations();

    const report: ValidationReport = {
      timestamp: new Date().toISOString(),
      totalTests,
      passedTests,
      failedTests,
      duration: totalDuration,
      results: this.results,
      securityScore,
      recommendations
    };

    // Save detailed report
    this.saveReport(report);
    
    // Print summary
    this.printSummary(report);

    return report;
  }

  private calculateSecurityScore(): number {
    let score = 100;
    
    for (const result of this.results) {
      if (result.status === 'FAIL') {
        score -= 20; // Major deduction for failed suite
      } else if (result.status === 'ERROR') {
        score -= 15; // Deduction for suite errors
      }

      // Deduct points for individual test failures
      const failedTestCount = result.tests.filter(test => test.status === 'FAIL').length;
      score -= failedTestCount * 2;
    }

    return Math.max(0, score);
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    for (const result of this.results) {
      if (result.status === 'FAIL' || result.status === 'ERROR') {
        switch (result.suite) {
          case 'Security Penetration Testing':
            recommendations.push('Review authentication mechanisms and rate limiting configuration');
            recommendations.push('Strengthen input validation and SQL injection prevention');
            break;
          case 'Storage Security Audit':
            recommendations.push('Enhance tenant isolation mechanisms in database queries');
            recommendations.push('Review data sanitization and validation processes');
            break;
          case 'XSS Vulnerability Scanner':
            recommendations.push('Implement comprehensive XSS filtering and output encoding');
            recommendations.push('Review Content Security Policy configuration');
            break;
          case 'Performance Impact Assessment':
            recommendations.push('Optimize security middleware performance');
            recommendations.push('Review database query efficiency with security constraints');
            break;
        }
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('All security tests passed - maintain current security posture');
      recommendations.push('Consider implementing additional security monitoring');
      recommendations.push('Schedule regular security audits and penetration testing');
    }

    return recommendations;
  }

  private saveReport(report: ValidationReport): void {
    const reportPath = join(process.cwd(), 'SECURITY_VALIDATION_REPORT.json');
    writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Also save markdown version
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = join(process.cwd(), 'SECURITY_VALIDATION_REPORT.md');
    writeFileSync(markdownPath, markdownReport);
    
    console.log(`📄 Detailed report saved to: ${reportPath}`);
    console.log(`📝 Markdown report saved to: ${markdownPath}`);
  }

  private generateMarkdownReport(report: ValidationReport): string {
    let markdown = `# Security Validation Report\n\n`;
    markdown += `**Generated:** ${report.timestamp}\n`;
    markdown += `**Duration:** ${(report.duration / 1000).toFixed(2)}s\n`;
    markdown += `**Security Score:** ${report.securityScore}/100\n\n`;

    // Summary
    markdown += `## Summary\n\n`;
    markdown += `- **Total Tests:** ${report.totalTests}\n`;
    markdown += `- **Passed:** ${report.passedTests} ✅\n`;
    markdown += `- **Failed:** ${report.failedTests} ❌\n`;
    markdown += `- **Success Rate:** ${((report.passedTests / report.totalTests) * 100).toFixed(1)}%\n\n`;

    // Test Suite Results
    markdown += `## Test Suite Results\n\n`;
    for (const result of report.results) {
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '💥';
      markdown += `### ${statusIcon} ${result.suite}\n\n`;
      markdown += `- **Status:** ${result.status}\n`;
      markdown += `- **Duration:** ${result.duration}ms\n`;
      markdown += `- **Tests:** ${result.tests.length}\n`;
      
      if (result.tests.length > 0) {
        const passed = result.tests.filter(t => t.status === 'PASS').length;
        const failed = result.tests.filter(t => t.status === 'FAIL').length;
        markdown += `- **Passed:** ${passed}\n`;
        markdown += `- **Failed:** ${failed}\n`;
      }
      
      markdown += `\n`;
    }

    // Recommendations
    markdown += `## Security Recommendations\n\n`;
    for (let i = 0; i < report.recommendations.length; i++) {
      markdown += `${i + 1}. ${report.recommendations[i]}\n`;
    }

    return markdown;
  }

  private printSummary(report: ValidationReport): void {
    console.log('🛡️  SECURITY VALIDATION SUMMARY');
    console.log('================================');
    console.log(`Total Tests: ${report.totalTests}`);
    console.log(`Passed: ${report.passedTests} ✅`);
    console.log(`Failed: ${report.failedTests} ❌`);
    console.log(`Success Rate: ${((report.passedTests / report.totalTests) * 100).toFixed(1)}%`);
    console.log(`Security Score: ${report.securityScore}/100`);
    console.log(`Duration: ${(report.duration / 1000).toFixed(2)}s`);
    console.log('');

    // Print suite status
    console.log('Test Suite Status:');
    for (const result of report.results) {
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '💥';
      console.log(`  ${statusIcon} ${result.suite} (${result.duration}ms)`);
    }
    console.log('');

    // Security assessment
    if (report.securityScore >= 90) {
      console.log('🎉 EXCELLENT: Security posture is excellent');
    } else if (report.securityScore >= 75) {
      console.log('👍 GOOD: Security posture is good with minor improvements needed');
    } else if (report.securityScore >= 60) {
      console.log('⚠️  MODERATE: Security improvements required');
    } else {
      console.log('🚩 CRITICAL: Immediate security attention required');
    }
    console.log('');
  }
}

// Run validation if called directly
if (require.main === module) {
  const runner = new SecurityValidationRunner();
  
  runner.runValidation()
    .then((report) => {
      console.log('✅ Security validation completed successfully');
      process.exit(report.securityScore >= 75 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Security validation failed:', error);
      process.exit(1);
    });
}

export { SecurityValidationRunner, ValidationReport };