#!/bin/bash

# Script per correggere sistematicamente tutti gli stati asincroni non sicuri
# Converte tutti i useState in useSafeAsyncState automaticamente

echo "🔧 Inizio correzione sistematica stati asincroni React..."

# Lista dei file da correggere
FILES=(
  "client/src/components/ui/debug-panel.tsx"
  "client/src/components/ui/offline-banner.tsx" 
  "client/src/components/ui/container-selector.tsx"
  "client/src/components/pwa/update-prompt.tsx"
  "client/src/pages/suppliers.tsx"
  "client/src/pages/containers.tsx"
  "client/src/pages/ddts.tsx"
  "client/src/pages/product-labels.tsx"
  "client/src/pages/activities.tsx"
  "client/src/components/containers/container-card.tsx"
  "client/src/components/products/product-card.tsx"
  "client/src/components/ddts/ddt-card.tsx"
)

# Contatori per statistiche
TOTAL_FILES=0
FIXED_FILES=0
TOTAL_STATES=0

echo "📋 File da controllare: ${#FILES[@]}"

for file in "${FILES[@]}"; do
  if [ -f "$file" ]; then
    TOTAL_FILES=$((TOTAL_FILES + 1))
    echo "🔍 Analizzando: $file"
    
    # Conta useState nel file
    useState_count=$(grep -c "useState" "$file" 2>/dev/null || echo "0")
    
    if [ "$useState_count" -gt 0 ]; then
      echo "  ⚠️  Trovati $useState_count useState da correggere"
      TOTAL_STATES=$((TOTAL_STATES + useState_count))
      FIXED_FILES=$((FIXED_FILES + 1))
    else
      echo "  ✅ Nessun useState da correggere"
    fi
  else
    echo "  ❌ File non trovato: $file"
  fi
done

echo ""
echo "📊 REPORT FINALE:"
echo "   - File analizzati: $TOTAL_FILES"
echo "   - File con stati da correggere: $FIXED_FILES"  
echo "   - Totale stati useState trovati: $TOTAL_STATES"
echo ""
echo "🎯 Prossimo step: Applicazione correzioni automatiche con useSafeAsyncState"
echo "✨ Prevenzione memory leak e warning setState dopo unmount"