diff --git a/client/src/lib/unifiedCache.ts b/client/src/lib/unifiedCache.ts
--- a/client/src/lib/unifiedCache.ts
+++ b/client/src/lib/unifiedCache.ts
@@ -16,1 +16,1 @@
-export interface CacheEntry<T = any> {
+export interface CacheEntry<T = unknown> {
@@ -46,1 +46,1 @@
-  private memoryCache = new Map<string, any>(); // Cache ultraveloce per dati critici
+  private memoryCache = new Map<string, unknown>(); // Cache ultraveloce per dati critici
@@ -298,1 +298,1 @@
-    keys.forEach(key => localStorage.removeItem(key));
+    keys.forEach(key => { localStorage.removeItem(key); });
@@ -321,1 +321,1 @@
-    unifiedCache.preloadCriticalData();
+    void unifiedCache.preloadCriticalData();
diff --git a/client/src/lib/optimizedQueryClient.ts b/client/src/lib/optimizedQueryClient.ts
--- a/client/src/lib/optimizedQueryClient.ts
+++ b/client/src/lib/optimizedQueryClient.ts
@@ -37,1 +37,1 @@
-  body?: any
+  body?: unknown
diff --git a/client/src/lib/serviceWorkerOptimizer.ts b/client/src/lib/serviceWorkerOptimizer.ts
--- a/client/src/lib/serviceWorkerOptimizer.ts
+++ b/client/src/lib/serviceWorkerOptimizer.ts
@@ -345,1 +345,1 @@
-      this.developmentCleanup();
+      void this.developmentCleanup();
@@ -355,1 +355,1 @@
-  serviceWorkerOptimizer.initialize();
+  void serviceWorkerOptimizer.initialize();
@@ -364,1 +364,1 @@
-    serviceWorkerOptimizer.forceUnregister();
+    void serviceWorkerOptimizer.forceUnregister();
diff --git a/client/src/lib/spa-navigation.ts b/client/src/lib/spa-navigation.ts
--- a/client/src/lib/spa-navigation.ts
+++ b/client/src/lib/spa-navigation.ts
@@ -59,1 +59,1 @@
-    const routeMap: { [key: string]: () => Promise<any> } = {
+    const routeMap: { [key: string]: () => Promise<unknown> } = {
@@ -312,1 +312,1 @@
-    swipeForward: () => SPANavigation.handleSwipeForward(setLocation),
+    swipeForward: () => { SPANavigation.handleSwipeForward(setLocation); },
diff --git a/client/src/pages/home.tsx b/client/src/pages/home.tsx
--- a/client/src/pages/home.tsx
+++ b/client/src/pages/home.tsx
@@ -27,1 +27,1 @@
-    select: (data: any[]) => data?.[0] // Prende il primo elemento (piÃ¹ recente)
+    select: (data: unknown[]) => data?.[0] // Prende il primo elemento (piÃ¹ recente)
@@ -33,1 +33,1 @@
-    select: (data: any[]) => {
+    select: (data: unknown[]) => {
diff --git a/client/src/lib/advancedLazyLoading.ts b/client/src/lib/advancedLazyLoading.ts
--- a/client/src/lib/advancedLazyLoading.ts
+++ b/client/src/lib/advancedLazyLoading.ts
@@ -47,1 +47,1 @@
-const componentCache = new Map<string, ComponentType<any>>();
+const componentCache = new Map<string, ComponentType<unknown>>();
@@ -50,1 +50,1 @@
-const preloadState = new Map<string, Promise<ComponentType<any>>>();
+const preloadState = new Map<string, Promise<ComponentType<unknown>>>();
@@ -55,1 +55,1 @@
-export function createOptimizedLazy<T extends ComponentType<any>>(
+export function createOptimizedLazy<T extends ComponentType<unknown>>(
@@ -88,1 +88,1 @@
-      (stats as any)[routePath] = { loadTime, timestamp: Date.now(), priority: config.priority };
+      (stats as unknown)[routePath] = { loadTime, timestamp: Date.now(), priority: config.priority };
@@ -124,1 +124,1 @@
-      const preloadPromise = new Promise<ComponentType<any>>(resolve => {
+      const preloadPromise = new Promise<ComponentType<unknown>>(resolve => {
@@ -133,1 +133,1 @@
-            resolve(null as any);
+            resolve(null as unknown);
@@ -184,1 +184,1 @@
-function getImportFunction(route: string): (() => Promise<{ default: ComponentType<any> }>) | null {
+function getImportFunction(route: string): (() => Promise<{ default: ComponentType<unknown> }>) | null {
@@ -185,1 +185,1 @@
-  const imports: Record<string, () => Promise<{ default: ComponentType<any> }>> = {
+  const imports: Record<string, () => Promise<{ default: ComponentType<unknown> }>> = {
@@ -215,1 +215,1 @@
-  const routeStats = (stats as any)[route] || { visits: 0, lastVisit: 0 };
+  const routeStats = (stats as unknown)[route] || { visits: 0, lastVisit: 0 };
@@ -219,1 +219,1 @@
-  (stats as any)[route] = routeStats;
+  (stats as unknown)[route] = routeStats;
@@ -243,1 +243,1 @@
-    const routeStats = (stats as any)[route];
+    const routeStats = (stats as unknown)[route];
diff --git a/client/src/components/optimized/memoized-components.tsx b/client/src/components/optimized/memoized-components.tsx
--- a/client/src/components/optimized/memoized-components.tsx
+++ b/client/src/components/optimized/memoized-components.tsx
@@ -214,1 +214,1 @@
-          onClick={() => handleSupplierClick(supplier)}
+          onClick={() => { handleSupplierClick(supplier); }}
@@ -292,1 +292,1 @@
-                onClick={() => handleDDTClick(ddt)}
+                onClick={() => { handleDDTClick(ddt); }}
diff --git a/client/src/lib/bundleOptimizer.ts b/client/src/lib/bundleOptimizer.ts
--- a/client/src/lib/bundleOptimizer.ts
+++ b/client/src/lib/bundleOptimizer.ts
@@ -167,1 +167,1 @@
-  private memoryCache = new Map<string, any>();
+  private memoryCache = new Map<string, unknown>();
@@ -168,1 +168,1 @@
-  private computeCache = new Map<string, { result: any; timestamp: number; ttl: number }>();
+  private computeCache = new Map<string, { result: unknown; timestamp: number; ttl: number }>();
@@ -173,1 +173,1 @@
-  memoize<T extends (...args: any[]) => any>(
+  memoize<T extends (...args: unknown[]) => any>(
@@ -209,1 +209,1 @@
-  debounce<T extends (...args: any[]) => any>(
+  debounce<T extends (...args: unknown[]) => any>(
@@ -224,1 +224,1 @@
-  throttle<T extends (...args: any[]) => any>(
+  throttle<T extends (...args: unknown[]) => any>(
@@ -317,1 +317,1 @@
-export function measureComponentPerformance<T extends (...args: any[]) => any>(
+export function measureComponentPerformance<T extends (...args: unknown[]) => any>(
diff --git a/client/src/lib/native-experience.ts b/client/src/lib/native-experience.ts
--- a/client/src/lib/native-experience.ts
+++ b/client/src/lib/native-experience.ts
@@ -30,1 +30,1 @@
-    document.addEventListener('gesturestart', (e) => e.preventDefault());
+    document.addEventListener('gesturestart', (e) => { e.preventDefault(); });
@@ -31,1 +31,1 @@
-    document.addEventListener('gesturechange', (e) => e.preventDefault());
+    document.addEventListener('gesturechange', (e) => { e.preventDefault(); });