[AUTH-AUDIT] 2025-07-27T20:53:38.235Z - session_deserialization - User: c2f26b17-b4f9-4920-8b41-f30fbd332920 { username: 'admin', tenant: '3596a193-0d3e-4e61-b683-a5313bf58719' }
🚨 SECURITY: Blocked CORS request from unauthorized origin: http://127.0.0.1:5000
[2025-07-27T20:53:38.255Z] [req_1753649618255] Error in GET /@vite/client: {
  error: 'Not allowed by CORS',
  stack: 'Error: Not allowed by CORS\n' +
    '    at origin (/home/<USER>/workspace/server/index.ts:60:16)\n' +
    '    at /home/<USER>/workspace/node_modules/cors/lib/index.js:219:13\n' +
    '    at optionsCallback (/home/<USER>/workspace/node_modules/cors/lib/index.js:199:9)\n' +
    '    at corsMiddleware (/home/<USER>/workspace/node_modules/cors/lib/index.js:204:7)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)',
  user: undefined,
  tenant: undefined,
  ip: '127.0.0.1'
}
🚨 SECURITY: Blocked CORS request from unauthorized origin: http://127.0.0.1:5000
[2025-07-27T20:53:38.259Z] [req_1753649618259] Error in GET /src/main.tsx: {
  error: 'Not allowed by CORS',
  stack: 'Error: Not allowed by CORS\n' +
    '    at origin (/home/<USER>/workspace/server/index.ts:60:16)\n' +
    '    at /home/<USER>/workspace/node_modules/cors/lib/index.js:219:13\n' +
    '    at optionsCallback (/home/<USER>/workspace/node_modules/cors/lib/index.js:199:9)\n' +
    '    at corsMiddleware (/home/<USER>/workspace/node_modules/cors/lib/index.js:204:7)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)',
  user: undefined,
  tenant: undefined,
  ip: '127.0.0.1'
}
🚨 SECURITY: Blocked CORS request from unauthorized origin: http://127.0.0.1:5000
[2025-07-27T20:53:38.503Z] [req_1753649618503] Error in GET /@react-refresh: {
  error: 'Not allowed by CORS',
  stack: 'Error: Not allowed by CORS\n' +
    '    at origin (/home/<USER>/workspace/server/index.ts:60:16)\n' +
    '    at /home/<USER>/workspace/node_modules/cors/lib/index.js:219:13\n' +
    '    at optionsCallback (/home/<USER>/workspace/node_modules/cors/lib/index.js:199:9)\n' +
    '    at corsMiddleware (/home/<USER>/workspace/node_modules/cors/lib/index.js:204:7)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)\n' +
    '    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)\n' +
    '    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)\n' +
    '    at Server.emit (node:events:524:28)\n' +
    '    at parserOnIncoming (node:_http_server:1141:12)\n' +
    '    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)',
  user: undefined,
  tenant: undefined,
  ip: '127.0.0.1'