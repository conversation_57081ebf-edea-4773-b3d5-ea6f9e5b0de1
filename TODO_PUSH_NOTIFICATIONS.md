# TODO: Progetti e Ottimizzazioni HACCP Tracker

## 1. PERFORMANCE OPTIMIZATION CONTINUA ⚡ [PRIORITY: ALTA]
- **Status**: IN CORSO - Parzialmente completato
- **Data creazione**: 27 Luglio 2025
- **Obiettivo**: Raggiungere FCP <3000ms (attualmente 8160ms)
- **Progresso**: 
  - ✅ FCP migliorato da 306324ms a 8160ms (97.3% riduzione)
  - ✅ Bundle Splitting Optimizer implementato
  - ✅ API Cache Optimizer con TTL aggressive
  - ✅ Final Performance Optimizer con render blocking elimination
  - 🔄 **RIMANENTE**: FCP 8160ms → <3000ms (63% improvement ancora necessario)

### Prossimi step per performance optimization:
- [ ] Implementare Server-Side Rendering (SSR) o Static Site Generation
- [ ] Ottimizzare ulteriormente il critical CSS path
- [ ] Implementare lazy loading più aggressivo per componenti non-critical
- [ ] Ottimizzare le query del database per ridurre i tempi di risposta API
- [ ] Considerare CDN per assets statici
- [ ] Analizzare e ottimizzare le immagini (logo.png che carica in 1100ms+)

---

## 2. Implementazione Notifiche Push

## Stato Attuale
Le notifiche push sono configurate nell'interfaccia ma non completamente implementate.

## Cosa Funziona Ora:
- ✅ Richiesta permessi per notifiche push
- ✅ Registrazione service worker PWA  
- ✅ Interfaccia per scegliere tipi di notifiche
- ✅ UI componenti in `client/src/components/ui/pwa-installer.tsx`

## Cosa Manca per Implementazione Completa:

### 1. Server di Notifiche Push
- [ ] Configurare Firebase Cloud Messaging o servizio simile
- [ ] Aggiungere credenziali e configurazione nel backend
- [ ] Implementare endpoint per registrazione dispositivi

### 2. Logica Backend
- [ ] Sistema di monitoraggio scadenze prodotti (24h prima)
- [ ] Controllo stock in esaurimento
- [ ] Notifiche per nuovi DDT da processare
- [ ] Aggiornamenti di sistema

### 3. Integrazione Database
- [ ] Query per prodotti in scadenza
- [ ] Query per stock bassi
- [ ] Trigger per nuovi DDT
- [ ] Scheduler per controlli periodici

### 4. Tipi di Notifiche da Implementare:
1. **Prodotti in scadenza** (24h prima)
2. **Stock in esaurimento** 
3. **Nuovi DDT da processare**
4. **Aggiornamenti di sistema**

## File Coinvolti:
- `client/src/components/ui/pwa-installer.tsx` - UI notifiche
- `server/` - Backend per invio notifiche
- Database schema per tracking

## Note Implementazione:
- Richiedere API keys/secrets per servizio notifiche
- Implementare fallback per dispositivi non supportati
- Gestire permessi utente e privacy
- Testing su diversi dispositivi/browser

---
*Promemoria salvato il 27/05/2025*