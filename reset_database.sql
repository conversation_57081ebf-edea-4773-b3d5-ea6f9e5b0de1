-- Script per il reset completo del database HACCP
-- ATTENZIONE: Questo script eliminerà PERMANENTEMENTE tutti i dati specificati
-- Assicurarsi di avere un backup prima di eseguire questo script!

-- 1. Eliminare prima le relazioni
DELETE FROM container_products;

-- 2. Eliminare le etichette prodotto
DELETE FROM product_labels;

-- 3. Eliminare i documenti di trasporto (DDT)
DELETE FROM ddts;

-- 4. Eliminare i fornitori
DELETE FROM suppliers;

-- 5. Eliminare i contenitori
DELETE FROM containers;

-- 6. Eliminare i log di attività
-- Elimina tutti i log:
DELETE FROM activity_logs;
-- Oppure mantieni solo login/logout:
-- DELETE FROM activity_logs WHERE action NOT IN ('login', 'logout');

-- 7. Resettare le sequenze ID
ALTER SEQUENCE suppliers_id_seq RESTART WITH 1;
ALTER SEQUENCE ddts_id_seq RESTART WITH 1;
ALTER SEQUENCE product_labels_id_seq RESTART WITH 1;
ALTER SEQUENCE containers_id_seq RESTART WITH 1;
ALTER SEQUENCE container_products_id_seq RESTART WITH 1;
ALTER SEQUENCE activity_logs_id_seq RESTART WITH 1;

-- 8. Verifica finale (commenta questa parte se preferisci non visualizzare)
SELECT 'suppliers' as table_name, COUNT(*) FROM suppliers UNION ALL
SELECT 'ddts', COUNT(*) FROM ddts UNION ALL
SELECT 'product_labels', COUNT(*) FROM product_labels UNION ALL
SELECT 'containers', COUNT(*) FROM containers UNION ALL
SELECT 'container_products', COUNT(*) FROM container_products UNION ALL
SELECT 'container_types', COUNT(*) FROM container_types UNION ALL
SELECT 'activity_logs', COUNT(*) FROM activity_logs;

-- NOTA: Questo script non elimina i tipi di contenitore (container_types)
-- in quanto sono considerati valori predefiniti dell'applicazione.
-- Se desideri eliminarli, decommenta queste righe:

-- DELETE FROM container_types;
-- ALTER SEQUENCE container_types_id_seq RESTART WITH 1;