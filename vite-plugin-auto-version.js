import { readFileSync, writeFileSync, existsSync } from 'fs';

/**
 * Plugin Vite per auto-incremento versione in build produzione
 */
export function autoVersionPlugin() {
  let hasIncremented = false;

  return {
    name: 'auto-version',
    buildStart() {
      // Solo in build produzione e solo una volta
      if (process.env.NODE_ENV === 'production' && !hasIncremented) {
        this.incrementVersion();
        hasIncremented = true;
      }
    },

    incrementVersion() {
      try {
        const currentVersion = this.getCurrentVersion();
        const newVersion = this.bumpVersion(currentVersion);
        
        this.updateVersionFile(newVersion);
        this.updateEnvFile(newVersion);
        
        console.log(`Auto-incremented version: ${currentVersion} → ${newVersion}`);
      } catch (error) {
        console.warn('Could not auto-increment version:', error.message);
      }
    },

    getCurrentVersion() {
      const content = readFileSync('./client/src/lib/version.ts', 'utf8');
      const match = content.match(/const APP_VERSION = "([^"]+)";/);
      return match ? match[1] : '1.2.16';
    },

    bumpVersion(version) {
      const parts = version.split('.').map(Number);
      parts[2] = (parts[2] || 0) + 1;
      return parts.join('.');
    },

    updateVersionFile(version) {
      let content = readFileSync('./client/src/lib/version.ts', 'utf8');
      
      content = content.replace(
        /const APP_VERSION = "[^"]+";/,
        `const APP_VERSION = "${version}";`
      );
      
      const buildComment = `// Build: ${new Date().toISOString().split('T')[0]}`;
      content = content.replace(
        /\/\/ Build: .*/,
        buildComment
      );
      
      writeFileSync('./client/src/lib/version.ts', content);
    },

    updateEnvFile(version) {
      const envPath = './.env';
      let envContent = '';
      
      try {
        envContent = readFileSync(envPath, 'utf8');
      } catch (error) {
        // File non esiste
      }
      
      if (envContent.includes('VITE_APP_VERSION=')) {
        envContent = envContent.replace(
          /VITE_APP_VERSION=.*/,
          `VITE_APP_VERSION=${version}`
        );
      } else {
        envContent += `\nVITE_APP_VERSION=${version}\n`;
      }
      
      writeFileSync(envPath, envContent);
    }
  };
}