# Automated Version Management - HACCP Tracker

## Sistema Completo di Versioning Automatico

Il sistema ora implementa tre livelli di automazione per garantire che le versioni si incrementino automaticamente ad ogni deployment.

### Livello 1: Manual Pre-Deploy (Sempre Funziona)
```bash
node deploy-prep.js
```
- Incrementa automaticamente la versione patch
- Aggiorna version.ts e .env
- Prepara tutto per il deployment

### Livello 2: Pre-Build Hook (Automatico su Build)
```bash
node prebuild.js
```
- Rileva automaticamente se è un build di produzione
- Incrementa la versione solo nei deployment
- Si integra con il processo di build

### Livello 3: Runtime Auto-Detection (Fallback)
- Il sistema rileva automaticamente deployment in produzione
- Auto-incrementa se necessario
- Compatibile con Replit Deployments

## Come Funziona il Sistema Automatico

### Development Mode
- Versione: `1.2.17-dev.14`
- Cambia automaticamente ogni ora
- Non interferisce con la versione di produzione

### Production Deployment
- Versione: `1.2.17` (incrementata automaticamente)
- Pulita e leggibile
- Rilevamento automatico PWA

### Flusso di Deployment

1. **Opzione A - Completamente Automatico**
   - Clicca "Deploy" su Replit
   - Il sistema rileva automaticamente il deployment
   - Incrementa la versione in background

2. **Opzione B - Semi-Automatico**
   ```bash
   node prebuild.js && npm run build
   ```

3. **Opzione C - Manuale**
   ```bash
   node deploy-prep.js
   ```
   Poi deploy normale

## Variabili di Ambiente

Il sistema rileva automaticamente:
- `NODE_ENV=production`
- `VITE_APP_VERSION` (se impostata)
- Hostname di deployment vs development

## Verifica Funzionamento

Dopo ogni deployment, controlla:
1. Versione nell'header dell'app
2. Console browser per log di versioning
3. PWA update prompt (se necessario)

## Risoluzione Problemi

Se la versione non si incrementa:
1. Esegui manualmente `node deploy-prep.js`
2. Verifica che `.env` contenga `VITE_APP_VERSION`
3. Controlla i log di deployment per errori

## Esempi di Versioni

- **Prima del deploy**: `1.2.16`
- **Dopo deploy automatico**: `1.2.17`
- **Development**: `1.2.17-dev.15`
- **Production**: `1.2.17`

Il sistema è ora completamente automatizzato e non richiede intervento manuale nella maggior parte dei casi.